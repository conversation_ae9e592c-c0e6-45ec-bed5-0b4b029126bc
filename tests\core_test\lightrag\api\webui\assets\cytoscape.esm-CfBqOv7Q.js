function cs(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,a=Array(e);r<e;r++)a[r]=t[r];return a}function Nf(t){if(Array.isArray(t))return t}function Ff(t){if(Array.isArray(t))return cs(t)}function or(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function zf(t,e){for(var r=0;r<e.length;r++){var a=e[r];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(t,Ol(a.key),a)}}function ur(t,e,r){return e&&zf(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function Pt(t,e){var r=typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=Rs(t))||e){r&&(t=r);var a=0,n=function(){};return{s:n,n:function(){return a>=t.length?{done:!0}:{done:!1,value:t[a++]}},e:function(l){throw l},f:n}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var i,s=!0,o=!1;return{s:function(){r=r.call(t)},n:function(){var l=r.next();return s=l.done,l},e:function(l){o=!0,i=l},f:function(){try{s||r.return==null||r.return()}finally{if(o)throw i}}}}function Ll(t,e,r){return(e=Ol(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function qf(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function Vf(t,e){var r=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(r!=null){var a,n,i,s,o=[],l=!0,u=!1;try{if(i=(r=r.call(t)).next,e===0){if(Object(r)!==r)return;l=!1}else for(;!(l=(a=i.call(r)).done)&&(o.push(a.value),o.length!==e);l=!0);}catch(v){u=!0,n=v}finally{try{if(!l&&r.return!=null&&(s=r.return(),Object(s)!==s))return}finally{if(u)throw n}}return o}}function _f(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Gf(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function je(t,e){return Nf(t)||Vf(t,e)||Rs(t,e)||_f()}function Il(t){return Ff(t)||qf(t)||Rs(t)||Gf()}function Hf(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var a=r.call(t,e);if(typeof a!="object")return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}function Ol(t){var e=Hf(t,"string");return typeof e=="symbol"?e:e+""}function We(t){"@babel/helpers - typeof";return We=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},We(t)}function Rs(t,e){if(t){if(typeof t=="string")return cs(t,e);var r={}.toString.call(t).slice(8,-1);return r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set"?Array.from(t):r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?cs(t,e):void 0}}var Ke=typeof window>"u"?null:window,so=Ke?Ke.navigator:null;Ke&&Ke.document;var Kf=We(""),Nl=We({}),$f=We(function(){}),Wf=typeof HTMLElement>"u"?"undefined":We(HTMLElement),Ca=function(e){return e&&e.instanceString&&_e(e.instanceString)?e.instanceString():null},fe=function(e){return e!=null&&We(e)==Kf},_e=function(e){return e!=null&&We(e)===$f},Le=function(e){return!bt(e)&&(Array.isArray?Array.isArray(e):e!=null&&e instanceof Array)},ke=function(e){return e!=null&&We(e)===Nl&&!Le(e)&&e.constructor===Object},Uf=function(e){return e!=null&&We(e)===Nl},ae=function(e){return e!=null&&We(e)===We(1)&&!isNaN(e)},Yf=function(e){return ae(e)&&Math.floor(e)===e},un=function(e){if(Wf!=="undefined")return e!=null&&e instanceof HTMLElement},bt=function(e){return Ta(e)||Fl(e)},Ta=function(e){return Ca(e)==="collection"&&e._private.single},Fl=function(e){return Ca(e)==="collection"&&!e._private.single},Ms=function(e){return Ca(e)==="core"},zl=function(e){return Ca(e)==="stylesheet"},Xf=function(e){return Ca(e)==="event"},tr=function(e){return e==null?!0:!!(e===""||e.match(/^\s+$/))},Zf=function(e){return typeof HTMLElement>"u"?!1:e instanceof HTMLElement},Qf=function(e){return ke(e)&&ae(e.x1)&&ae(e.x2)&&ae(e.y1)&&ae(e.y2)},Jf=function(e){return Uf(e)&&_e(e.then)},jf=function(){return so&&so.userAgent.match(/msie|trident|edge/i)},Vr=function(e,r){r||(r=function(){if(arguments.length===1)return arguments[0];if(arguments.length===0)return"undefined";for(var i=[],s=0;s<arguments.length;s++)i.push(arguments[s]);return i.join("$")});var a=function(){var i=this,s=arguments,o,l=r.apply(i,s),u=a.cache;return(o=u[l])||(o=u[l]=e.apply(i,s)),o};return a.cache={},a},Ls=Vr(function(t){return t.replace(/([A-Z])/g,function(e){return"-"+e.toLowerCase()})}),xn=Vr(function(t){return t.replace(/(-\w)/g,function(e){return e[1].toUpperCase()})}),ql=Vr(function(t,e){return t+e[0].toUpperCase()+e.substring(1)},function(t,e){return t+"$"+e}),oo=function(e){return tr(e)?e:e.charAt(0).toUpperCase()+e.substring(1)},$e="(?:[-+]?(?:(?:\\d+|\\d*\\.\\d+)(?:[Ee][+-]?\\d+)?))",ec="rgb[a]?\\(("+$e+"[%]?)\\s*,\\s*("+$e+"[%]?)\\s*,\\s*("+$e+"[%]?)(?:\\s*,\\s*("+$e+"))?\\)",tc="rgb[a]?\\((?:"+$e+"[%]?)\\s*,\\s*(?:"+$e+"[%]?)\\s*,\\s*(?:"+$e+"[%]?)(?:\\s*,\\s*(?:"+$e+"))?\\)",rc="hsl[a]?\\(("+$e+")\\s*,\\s*("+$e+"[%])\\s*,\\s*("+$e+"[%])(?:\\s*,\\s*("+$e+"))?\\)",ac="hsl[a]?\\((?:"+$e+")\\s*,\\s*(?:"+$e+"[%])\\s*,\\s*(?:"+$e+"[%])(?:\\s*,\\s*(?:"+$e+"))?\\)",nc="\\#[0-9a-fA-F]{3}",ic="\\#[0-9a-fA-F]{6}",Vl=function(e,r){return e<r?-1:e>r?1:0},sc=function(e,r){return-1*Vl(e,r)},ge=Object.assign!=null?Object.assign.bind(Object):function(t){for(var e=arguments,r=1;r<e.length;r++){var a=e[r];if(a!=null)for(var n=Object.keys(a),i=0;i<n.length;i++){var s=n[i];t[s]=a[s]}}return t},oc=function(e){if(!(!(e.length===4||e.length===7)||e[0]!=="#")){var r=e.length===4,a,n,i,s=16;return r?(a=parseInt(e[1]+e[1],s),n=parseInt(e[2]+e[2],s),i=parseInt(e[3]+e[3],s)):(a=parseInt(e[1]+e[2],s),n=parseInt(e[3]+e[4],s),i=parseInt(e[5]+e[6],s)),[a,n,i]}},uc=function(e){var r,a,n,i,s,o,l,u;function v(d,y,g){return g<0&&(g+=1),g>1&&(g-=1),g<1/6?d+(y-d)*6*g:g<1/2?y:g<2/3?d+(y-d)*(2/3-g)*6:d}var f=new RegExp("^"+rc+"$").exec(e);if(f){if(a=parseInt(f[1]),a<0?a=(360- -1*a%360)%360:a>360&&(a=a%360),a/=360,n=parseFloat(f[2]),n<0||n>100||(n=n/100,i=parseFloat(f[3]),i<0||i>100)||(i=i/100,s=f[4],s!==void 0&&(s=parseFloat(s),s<0||s>1)))return;if(n===0)o=l=u=Math.round(i*255);else{var c=i<.5?i*(1+n):i+n-i*n,h=2*i-c;o=Math.round(255*v(h,c,a+1/3)),l=Math.round(255*v(h,c,a)),u=Math.round(255*v(h,c,a-1/3))}r=[o,l,u,s]}return r},lc=function(e){var r,a=new RegExp("^"+ec+"$").exec(e);if(a){r=[];for(var n=[],i=1;i<=3;i++){var s=a[i];if(s[s.length-1]==="%"&&(n[i]=!0),s=parseFloat(s),n[i]&&(s=s/100*255),s<0||s>255)return;r.push(Math.floor(s))}var o=n[1]||n[2]||n[3],l=n[1]&&n[2]&&n[3];if(o&&!l)return;var u=a[4];if(u!==void 0){if(u=parseFloat(u),u<0||u>1)return;r.push(u)}}return r},vc=function(e){return fc[e.toLowerCase()]},_l=function(e){return(Le(e)?e:null)||vc(e)||oc(e)||lc(e)||uc(e)},fc={transparent:[0,0,0,0],aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],grey:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]},Gl=function(e){for(var r=e.map,a=e.keys,n=a.length,i=0;i<n;i++){var s=a[i];if(ke(s))throw Error("Tried to set map with object key");i<a.length-1?(r[s]==null&&(r[s]={}),r=r[s]):r[s]=e.value}},Hl=function(e){for(var r=e.map,a=e.keys,n=a.length,i=0;i<n;i++){var s=a[i];if(ke(s))throw Error("Tried to get map with object key");if(r=r[s],r==null)return r}return r},Fa=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Sa(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}var _n,uo;function Da(){if(uo)return _n;uo=1;function t(e){var r=typeof e;return e!=null&&(r=="object"||r=="function")}return _n=t,_n}var Gn,lo;function cc(){if(lo)return Gn;lo=1;var t=typeof Fa=="object"&&Fa&&Fa.Object===Object&&Fa;return Gn=t,Gn}var Hn,vo;function En(){if(vo)return Hn;vo=1;var t=cc(),e=typeof self=="object"&&self&&self.Object===Object&&self,r=t||e||Function("return this")();return Hn=r,Hn}var Kn,fo;function dc(){if(fo)return Kn;fo=1;var t=En(),e=function(){return t.Date.now()};return Kn=e,Kn}var $n,co;function hc(){if(co)return $n;co=1;var t=/\s/;function e(r){for(var a=r.length;a--&&t.test(r.charAt(a)););return a}return $n=e,$n}var Wn,ho;function gc(){if(ho)return Wn;ho=1;var t=hc(),e=/^\s+/;function r(a){return a&&a.slice(0,t(a)+1).replace(e,"")}return Wn=r,Wn}var Un,go;function Is(){if(go)return Un;go=1;var t=En(),e=t.Symbol;return Un=e,Un}var Yn,po;function pc(){if(po)return Yn;po=1;var t=Is(),e=Object.prototype,r=e.hasOwnProperty,a=e.toString,n=t?t.toStringTag:void 0;function i(s){var o=r.call(s,n),l=s[n];try{s[n]=void 0;var u=!0}catch{}var v=a.call(s);return u&&(o?s[n]=l:delete s[n]),v}return Yn=i,Yn}var Xn,yo;function yc(){if(yo)return Xn;yo=1;var t=Object.prototype,e=t.toString;function r(a){return e.call(a)}return Xn=r,Xn}var Zn,mo;function Kl(){if(mo)return Zn;mo=1;var t=Is(),e=pc(),r=yc(),a="[object Null]",n="[object Undefined]",i=t?t.toStringTag:void 0;function s(o){return o==null?o===void 0?n:a:i&&i in Object(o)?e(o):r(o)}return Zn=s,Zn}var Qn,bo;function mc(){if(bo)return Qn;bo=1;function t(e){return e!=null&&typeof e=="object"}return Qn=t,Qn}var Jn,wo;function ka(){if(wo)return Jn;wo=1;var t=Kl(),e=mc(),r="[object Symbol]";function a(n){return typeof n=="symbol"||e(n)&&t(n)==r}return Jn=a,Jn}var jn,xo;function bc(){if(xo)return jn;xo=1;var t=gc(),e=Da(),r=ka(),a=NaN,n=/^[-+]0x[0-9a-f]+$/i,i=/^0b[01]+$/i,s=/^0o[0-7]+$/i,o=parseInt;function l(u){if(typeof u=="number")return u;if(r(u))return a;if(e(u)){var v=typeof u.valueOf=="function"?u.valueOf():u;u=e(v)?v+"":v}if(typeof u!="string")return u===0?u:+u;u=t(u);var f=i.test(u);return f||s.test(u)?o(u.slice(2),f?2:8):n.test(u)?a:+u}return jn=l,jn}var ei,Eo;function wc(){if(Eo)return ei;Eo=1;var t=Da(),e=dc(),r=bc(),a="Expected a function",n=Math.max,i=Math.min;function s(o,l,u){var v,f,c,h,d,y,g=0,p=!1,m=!1,b=!0;if(typeof o!="function")throw new TypeError(a);l=r(l)||0,t(u)&&(p=!!u.leading,m="maxWait"in u,c=m?n(r(u.maxWait)||0,l):c,b="trailing"in u?!!u.trailing:b);function w(P){var R=v,L=f;return v=f=void 0,g=P,h=o.apply(L,R),h}function E(P){return g=P,d=setTimeout(S,l),p?w(P):h}function C(P){var R=P-y,L=P-g,I=l-R;return m?i(I,c-L):I}function x(P){var R=P-y,L=P-g;return y===void 0||R>=l||R<0||m&&L>=c}function S(){var P=e();if(x(P))return k(P);d=setTimeout(S,C(P))}function k(P){return d=void 0,b&&v?w(P):(v=f=void 0,h)}function B(){d!==void 0&&clearTimeout(d),g=0,v=y=f=d=void 0}function D(){return d===void 0?h:k(e())}function A(){var P=e(),R=x(P);if(v=arguments,f=this,y=P,R){if(d===void 0)return E(y);if(m)return clearTimeout(d),d=setTimeout(S,l),w(y)}return d===void 0&&(d=setTimeout(S,l)),h}return A.cancel=B,A.flush=D,A}return ei=s,ei}var xc=wc(),Pa=Sa(xc),ti=Ke?Ke.performance:null,$l=ti&&ti.now?function(){return ti.now()}:function(){return Date.now()},Ec=function(){if(Ke){if(Ke.requestAnimationFrame)return function(t){Ke.requestAnimationFrame(t)};if(Ke.mozRequestAnimationFrame)return function(t){Ke.mozRequestAnimationFrame(t)};if(Ke.webkitRequestAnimationFrame)return function(t){Ke.webkitRequestAnimationFrame(t)};if(Ke.msRequestAnimationFrame)return function(t){Ke.msRequestAnimationFrame(t)}}return function(t){t&&setTimeout(function(){t($l())},1e3/60)}}(),ln=function(e){return Ec(e)},$t=$l,Mr=9261,Wl=65599,sa=5381,Ul=function(e){for(var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Mr,a=r,n;n=e.next(),!n.done;)a=a*Wl+n.value|0;return a},da=function(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Mr;return r*Wl+e|0},ha=function(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:sa;return(r<<5)+r+e|0},Cc=function(e,r){return e*2097152+r},Xt=function(e){return e[0]*2097152+e[1]},za=function(e,r){return[da(e[0],r[0]),ha(e[1],r[1])]},Co=function(e,r){var a={value:0,done:!1},n=0,i=e.length,s={next:function(){return n<i?a.value=e[n++]:a.done=!0,a}};return Ul(s,r)},rr=function(e,r){var a={value:0,done:!1},n=0,i=e.length,s={next:function(){return n<i?a.value=e.charCodeAt(n++):a.done=!0,a}};return Ul(s,r)},Yl=function(){return Tc(arguments)},Tc=function(e){for(var r,a=0;a<e.length;a++){var n=e[a];a===0?r=rr(n):r=rr(n,r)}return r},To=!0,Sc=console.warn!=null,Dc=console.trace!=null,Os=Number.MAX_SAFE_INTEGER||9007199254740991,Xl=function(){return!0},vn=function(){return!1},So=function(){return 0},Ns=function(){},Ve=function(e){throw new Error(e)},Zl=function(e){if(e!==void 0)To=!!e;else return To},Re=function(e){Zl()&&(Sc?console.warn(e):(console.log(e),Dc&&console.trace()))},kc=function(e){return ge({},e)},Nt=function(e){return e==null?e:Le(e)?e.slice():ke(e)?kc(e):e},Pc=function(e){return e.slice()},Ql=function(e,r){for(r=e="";e++<36;r+=e*51&52?(e^15?8^Math.random()*(e^20?16:4):4).toString(16):"-");return r},Bc={},Jl=function(){return Bc},Ue=function(e){var r=Object.keys(e);return function(a){for(var n={},i=0;i<r.length;i++){var s=r[i],o=a==null?void 0:a[s];n[s]=o===void 0?e[s]:o}return n}},ar=function(e,r,a){for(var n=e.length-1;n>=0;n--)e[n]===r&&e.splice(n,1)},Fs=function(e){e.splice(0,e.length)},Ac=function(e,r){for(var a=0;a<r.length;a++){var n=r[a];e.push(n)}},Et=function(e,r,a){return a&&(r=ql(a,r)),e[r]},Ht=function(e,r,a,n){a&&(r=ql(a,r)),e[r]=n},Rc=function(){function t(){or(this,t),this._obj={}}return ur(t,[{key:"set",value:function(r,a){return this._obj[r]=a,this}},{key:"delete",value:function(r){return this._obj[r]=void 0,this}},{key:"clear",value:function(){this._obj={}}},{key:"has",value:function(r){return this._obj[r]!==void 0}},{key:"get",value:function(r){return this._obj[r]}}])}(),Kt=typeof Map<"u"?Map:Rc,Mc="undefined",Lc=function(){function t(e){if(or(this,t),this._obj=Object.create(null),this.size=0,e!=null){var r;e.instanceString!=null&&e.instanceString()===this.instanceString()?r=e.toArray():r=e;for(var a=0;a<r.length;a++)this.add(r[a])}}return ur(t,[{key:"instanceString",value:function(){return"set"}},{key:"add",value:function(r){var a=this._obj;a[r]!==1&&(a[r]=1,this.size++)}},{key:"delete",value:function(r){var a=this._obj;a[r]===1&&(a[r]=0,this.size--)}},{key:"clear",value:function(){this._obj=Object.create(null)}},{key:"has",value:function(r){return this._obj[r]===1}},{key:"toArray",value:function(){var r=this;return Object.keys(this._obj).filter(function(a){return r.has(a)})}},{key:"forEach",value:function(r,a){return this.toArray().forEach(r,a)}}])}(),$r=(typeof Set>"u"?"undefined":We(Set))!==Mc?Set:Lc,Cn=function(e,r){var a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;if(e===void 0||r===void 0||!Ms(e)){Ve("An element must have a core reference and parameters set");return}var n=r.group;if(n==null&&(r.data&&r.data.source!=null&&r.data.target!=null?n="edges":n="nodes"),n!=="nodes"&&n!=="edges"){Ve("An element must be of type `nodes` or `edges`; you specified `"+n+"`");return}this.length=1,this[0]=this;var i=this._private={cy:e,single:!0,data:r.data||{},position:r.position||{x:0,y:0},autoWidth:void 0,autoHeight:void 0,autoPadding:void 0,compoundBoundsClean:!1,listeners:[],group:n,style:{},rstyle:{},styleCxts:[],styleKeys:{},removed:!0,selected:!!r.selected,selectable:r.selectable===void 0?!0:!!r.selectable,locked:!!r.locked,grabbed:!1,grabbable:r.grabbable===void 0?!0:!!r.grabbable,pannable:r.pannable===void 0?n==="edges":!!r.pannable,active:!1,classes:new $r,animation:{current:[],queue:[]},rscratch:{},scratch:r.scratch||{},edges:[],children:[],parent:r.parent&&r.parent.isNode()?r.parent:null,traversalCache:{},backgrounding:!1,bbCache:null,bbCacheShift:{x:0,y:0},bodyBounds:null,overlayBounds:null,labelBounds:{all:null,source:null,target:null,main:null},arrowBounds:{source:null,target:null,"mid-source":null,"mid-target":null}};if(i.position.x==null&&(i.position.x=0),i.position.y==null&&(i.position.y=0),r.renderedPosition){var s=r.renderedPosition,o=e.pan(),l=e.zoom();i.position={x:(s.x-o.x)/l,y:(s.y-o.y)/l}}var u=[];Le(r.classes)?u=r.classes:fe(r.classes)&&(u=r.classes.split(/\s+/));for(var v=0,f=u.length;v<f;v++){var c=u[v];!c||c===""||i.classes.add(c)}this.createEmitter(),(a===void 0||a)&&this.restore();var h=r.style||r.css;h&&(Re("Setting a `style` bypass at element creation should be done only when absolutely necessary.  Try to use the stylesheet instead."),this.style(h))},Do=function(e){return e={bfs:e.bfs||!e.dfs,dfs:e.dfs||!e.bfs},function(a,n,i){var s;ke(a)&&!bt(a)&&(s=a,a=s.roots||s.root,n=s.visit,i=s.directed),i=arguments.length===2&&!_e(n)?n:i,n=_e(n)?n:function(){};for(var o=this._private.cy,l=a=fe(a)?this.filter(a):a,u=[],v=[],f={},c={},h={},d=0,y,g=this.byGroup(),p=g.nodes,m=g.edges,b=0;b<l.length;b++){var w=l[b],E=w.id();w.isNode()&&(u.unshift(w),e.bfs&&(h[E]=!0,v.push(w)),c[E]=0)}for(var C=function(){var P=e.bfs?u.shift():u.pop(),R=P.id();if(e.dfs){if(h[R])return 0;h[R]=!0,v.push(P)}var L=c[R],I=f[R],M=I!=null?I.source():null,O=I!=null?I.target():null,_=I==null?void 0:P.same(M)?O[0]:M[0],H;if(H=n(P,I,_,d++,L),H===!0)return y=P,1;if(H===!1)return 1;for(var F=P.connectedEdges().filter(function(Q){return(!i||Q.source().same(P))&&m.has(Q)}),G=0;G<F.length;G++){var U=F[G],X=U.connectedNodes().filter(function(Q){return!Q.same(P)&&p.has(Q)}),Z=X.id();X.length!==0&&!h[Z]&&(X=X[0],u.push(X),e.bfs&&(h[Z]=!0,v.push(X)),f[Z]=U,c[Z]=c[R]+1)}},x;u.length!==0&&(x=C(),!(x!==0&&x===1)););for(var S=o.collection(),k=0;k<v.length;k++){var B=v[k],D=f[B.id()];D!=null&&S.push(D),S.push(B)}return{path:o.collection(S),found:o.collection(y)}}},ga={breadthFirstSearch:Do({bfs:!0}),depthFirstSearch:Do({dfs:!0})};ga.bfs=ga.breadthFirstSearch;ga.dfs=ga.depthFirstSearch;var Za={exports:{}},Ic=Za.exports,ko;function Oc(){return ko||(ko=1,function(t,e){(function(){var r,a,n,i,s,o,l,u,v,f,c,h,d,y,g;n=Math.floor,f=Math.min,a=function(p,m){return p<m?-1:p>m?1:0},v=function(p,m,b,w,E){var C;if(b==null&&(b=0),E==null&&(E=a),b<0)throw new Error("lo must be non-negative");for(w==null&&(w=p.length);b<w;)C=n((b+w)/2),E(m,p[C])<0?w=C:b=C+1;return[].splice.apply(p,[b,b-b].concat(m)),m},o=function(p,m,b){return b==null&&(b=a),p.push(m),y(p,0,p.length-1,b)},s=function(p,m){var b,w;return m==null&&(m=a),b=p.pop(),p.length?(w=p[0],p[0]=b,g(p,0,m)):w=b,w},u=function(p,m,b){var w;return b==null&&(b=a),w=p[0],p[0]=m,g(p,0,b),w},l=function(p,m,b){var w;return b==null&&(b=a),p.length&&b(p[0],m)<0&&(w=[p[0],m],m=w[0],p[0]=w[1],g(p,0,b)),m},i=function(p,m){var b,w,E,C,x,S;for(m==null&&(m=a),C=(function(){S=[];for(var k=0,B=n(p.length/2);0<=B?k<B:k>B;0<=B?k++:k--)S.push(k);return S}).apply(this).reverse(),x=[],w=0,E=C.length;w<E;w++)b=C[w],x.push(g(p,b,m));return x},d=function(p,m,b){var w;if(b==null&&(b=a),w=p.indexOf(m),w!==-1)return y(p,0,w,b),g(p,w,b)},c=function(p,m,b){var w,E,C,x,S;if(b==null&&(b=a),E=p.slice(0,m),!E.length)return E;for(i(E,b),S=p.slice(m),C=0,x=S.length;C<x;C++)w=S[C],l(E,w,b);return E.sort(b).reverse()},h=function(p,m,b){var w,E,C,x,S,k,B,D,A;if(b==null&&(b=a),m*10<=p.length){if(C=p.slice(0,m).sort(b),!C.length)return C;for(E=C[C.length-1],B=p.slice(m),x=0,k=B.length;x<k;x++)w=B[x],b(w,E)<0&&(v(C,w,0,null,b),C.pop(),E=C[C.length-1]);return C}for(i(p,b),A=[],S=0,D=f(m,p.length);0<=D?S<D:S>D;0<=D?++S:--S)A.push(s(p,b));return A},y=function(p,m,b,w){var E,C,x;for(w==null&&(w=a),E=p[b];b>m;){if(x=b-1>>1,C=p[x],w(E,C)<0){p[b]=C,b=x;continue}break}return p[b]=E},g=function(p,m,b){var w,E,C,x,S;for(b==null&&(b=a),E=p.length,S=m,C=p[m],w=2*m+1;w<E;)x=w+1,x<E&&!(b(p[w],p[x])<0)&&(w=x),p[m]=p[w],m=w,w=2*m+1;return p[m]=C,y(p,S,m,b)},r=function(){p.push=o,p.pop=s,p.replace=u,p.pushpop=l,p.heapify=i,p.updateItem=d,p.nlargest=c,p.nsmallest=h;function p(m){this.cmp=m??a,this.nodes=[]}return p.prototype.push=function(m){return o(this.nodes,m,this.cmp)},p.prototype.pop=function(){return s(this.nodes,this.cmp)},p.prototype.peek=function(){return this.nodes[0]},p.prototype.contains=function(m){return this.nodes.indexOf(m)!==-1},p.prototype.replace=function(m){return u(this.nodes,m,this.cmp)},p.prototype.pushpop=function(m){return l(this.nodes,m,this.cmp)},p.prototype.heapify=function(){return i(this.nodes,this.cmp)},p.prototype.updateItem=function(m){return d(this.nodes,m,this.cmp)},p.prototype.clear=function(){return this.nodes=[]},p.prototype.empty=function(){return this.nodes.length===0},p.prototype.size=function(){return this.nodes.length},p.prototype.clone=function(){var m;return m=new p,m.nodes=this.nodes.slice(0),m},p.prototype.toArray=function(){return this.nodes.slice(0)},p.prototype.insert=p.prototype.push,p.prototype.top=p.prototype.peek,p.prototype.front=p.prototype.peek,p.prototype.has=p.prototype.contains,p.prototype.copy=p.prototype.clone,p}(),function(p,m){return t.exports=m()}(this,function(){return r})}).call(Ic)}(Za)),Za.exports}var ri,Po;function Nc(){return Po||(Po=1,ri=Oc()),ri}var Fc=Nc(),Ba=Sa(Fc),zc=Ue({root:null,weight:function(e){return 1},directed:!1}),qc={dijkstra:function(e){if(!ke(e)){var r=arguments;e={root:r[0],weight:r[1],directed:r[2]}}var a=zc(e),n=a.root,i=a.weight,s=a.directed,o=this,l=i,u=fe(n)?this.filter(n)[0]:n[0],v={},f={},c={},h=this.byGroup(),d=h.nodes,y=h.edges;y.unmergeBy(function(L){return L.isLoop()});for(var g=function(I){return v[I.id()]},p=function(I,M){v[I.id()]=M,m.updateItem(I)},m=new Ba(function(L,I){return g(L)-g(I)}),b=0;b<d.length;b++){var w=d[b];v[w.id()]=w.same(u)?0:1/0,m.push(w)}for(var E=function(I,M){for(var O=(s?I.edgesTo(M):I.edgesWith(M)).intersect(y),_=1/0,H,F=0;F<O.length;F++){var G=O[F],U=l(G);(U<_||!H)&&(_=U,H=G)}return{edge:H,dist:_}};m.size()>0;){var C=m.pop(),x=g(C),S=C.id();if(c[S]=x,x!==1/0)for(var k=C.neighborhood().intersect(d),B=0;B<k.length;B++){var D=k[B],A=D.id(),P=E(C,D),R=x+P.dist;R<g(D)&&(p(D,R),f[A]={node:C,edge:P.edge})}}return{distanceTo:function(I){var M=fe(I)?d.filter(I)[0]:I[0];return c[M.id()]},pathTo:function(I){var M=fe(I)?d.filter(I)[0]:I[0],O=[],_=M,H=_.id();if(M.length>0)for(O.unshift(M);f[H];){var F=f[H];O.unshift(F.edge),O.unshift(F.node),_=F.node,H=_.id()}return o.spawn(O)}}}},Vc={kruskal:function(e){e=e||function(b){return 1};for(var r=this.byGroup(),a=r.nodes,n=r.edges,i=a.length,s=new Array(i),o=a,l=function(w){for(var E=0;E<s.length;E++){var C=s[E];if(C.has(w))return E}},u=0;u<i;u++)s[u]=this.spawn(a[u]);for(var v=n.sort(function(b,w){return e(b)-e(w)}),f=0;f<v.length;f++){var c=v[f],h=c.source()[0],d=c.target()[0],y=l(h),g=l(d),p=s[y],m=s[g];y!==g&&(o.merge(c),p.merge(m),s.splice(g,1))}return o}},_c=Ue({root:null,goal:null,weight:function(e){return 1},heuristic:function(e){return 0},directed:!1}),Gc={aStar:function(e){var r=this.cy(),a=_c(e),n=a.root,i=a.goal,s=a.heuristic,o=a.directed,l=a.weight;n=r.collection(n)[0],i=r.collection(i)[0];var u=n.id(),v=i.id(),f={},c={},h={},d=new Ba(function(H,F){return c[H.id()]-c[F.id()]}),y=new $r,g={},p={},m=function(F,G){d.push(F),y.add(G)},b,w,E=function(){b=d.pop(),w=b.id(),y.delete(w)},C=function(F){return y.has(F)};m(n,u),f[u]=0,c[u]=s(n);for(var x=0;d.size()>0;){if(E(),x++,w===v){for(var S=[],k=i,B=v,D=p[B];S.unshift(k),D!=null&&S.unshift(D),k=g[B],k!=null;)B=k.id(),D=p[B];return{found:!0,distance:f[w],path:this.spawn(S),steps:x}}h[w]=!0;for(var A=b._private.edges,P=0;P<A.length;P++){var R=A[P];if(this.hasElementWithId(R.id())&&!(o&&R.data("source")!==w)){var L=R.source(),I=R.target(),M=L.id()!==w?L:I,O=M.id();if(this.hasElementWithId(O)&&!h[O]){var _=f[w]+l(R);if(!C(O)){f[O]=_,c[O]=_+s(M),m(M,O),g[O]=b,p[O]=R;continue}_<f[O]&&(f[O]=_,c[O]=_+s(M),g[O]=b,p[O]=R)}}}}return{found:!1,distance:void 0,path:void 0,steps:x}}},Hc=Ue({weight:function(e){return 1},directed:!1}),Kc={floydWarshall:function(e){for(var r=this.cy(),a=Hc(e),n=a.weight,i=a.directed,s=n,o=this.byGroup(),l=o.nodes,u=o.edges,v=l.length,f=v*v,c=function(U){return l.indexOf(U)},h=function(U){return l[U]},d=new Array(f),y=0;y<f;y++){var g=y%v,p=(y-g)/v;p===g?d[y]=0:d[y]=1/0}for(var m=new Array(f),b=new Array(f),w=0;w<u.length;w++){var E=u[w],C=E.source()[0],x=E.target()[0];if(C!==x){var S=c(C),k=c(x),B=S*v+k,D=s(E);if(d[B]>D&&(d[B]=D,m[B]=k,b[B]=E),!i){var A=k*v+S;!i&&d[A]>D&&(d[A]=D,m[A]=S,b[A]=E)}}}for(var P=0;P<v;P++)for(var R=0;R<v;R++)for(var L=R*v+P,I=0;I<v;I++){var M=R*v+I,O=P*v+I;d[L]+d[O]<d[M]&&(d[M]=d[L]+d[O],m[M]=m[L])}var _=function(U){return(fe(U)?r.filter(U):U)[0]},H=function(U){return c(_(U))},F={distance:function(U,X){var Z=H(U),Q=H(X);return d[Z*v+Q]},path:function(U,X){var Z=H(U),Q=H(X),ee=h(Z);if(Z===Q)return ee.collection();if(m[Z*v+Q]==null)return r.collection();var te=r.collection(),K=Z,N;for(te.merge(ee);Z!==Q;)K=Z,Z=m[Z*v+Q],N=b[K*v+Z],te.merge(N),te.merge(h(Z));return te}};return F}},$c=Ue({weight:function(e){return 1},directed:!1,root:null}),Wc={bellmanFord:function(e){var r=this,a=$c(e),n=a.weight,i=a.directed,s=a.root,o=n,l=this,u=this.cy(),v=this.byGroup(),f=v.edges,c=v.nodes,h=c.length,d=new Kt,y=!1,g=[];s=u.collection(s)[0],f.unmergeBy(function(Be){return Be.isLoop()});for(var p=f.length,m=function(se){var ue=d.get(se.id());return ue||(ue={},d.set(se.id(),ue)),ue},b=function(se){return(fe(se)?u.$(se):se)[0]},w=function(se){return m(b(se)).dist},E=function(se){for(var ue=arguments.length>1&&arguments[1]!==void 0?arguments[1]:s,de=b(se),ye=[],he=de;;){if(he==null)return r.spawn();var me=m(he),Ce=me.edge,Se=me.pred;if(ye.unshift(he[0]),he.same(ue)&&ye.length>0)break;Ce!=null&&ye.unshift(Ce),he=Se}return l.spawn(ye)},C=0;C<h;C++){var x=c[C],S=m(x);x.same(s)?S.dist=0:S.dist=1/0,S.pred=null,S.edge=null}for(var k=!1,B=function(se,ue,de,ye,he,me){var Ce=ye.dist+me;Ce<he.dist&&!de.same(ye.edge)&&(he.dist=Ce,he.pred=se,he.edge=de,k=!0)},D=1;D<h;D++){k=!1;for(var A=0;A<p;A++){var P=f[A],R=P.source(),L=P.target(),I=o(P),M=m(R),O=m(L);B(R,L,P,M,O,I),i||B(L,R,P,O,M,I)}if(!k)break}if(k)for(var _=[],H=0;H<p;H++){var F=f[H],G=F.source(),U=F.target(),X=o(F),Z=m(G).dist,Q=m(U).dist;if(Z+X<Q||!i&&Q+X<Z)if(y||(Re("Graph contains a negative weight cycle for Bellman-Ford"),y=!0),e.findNegativeWeightCycles!==!1){var ee=[];Z+X<Q&&ee.push(G),!i&&Q+X<Z&&ee.push(U);for(var te=ee.length,K=0;K<te;K++){var N=ee[K],$=[N];$.push(m(N).edge);for(var J=m(N).pred;$.indexOf(J)===-1;)$.push(J),$.push(m(J).edge),J=m(J).pred;$=$.slice($.indexOf(J));for(var re=$[0].id(),le=0,xe=2;xe<$.length;xe+=2)$[xe].id()<re&&(re=$[xe].id(),le=xe);$=$.slice(le).concat($.slice(0,le)),$.push($[0]);var Ie=$.map(function(Be){return Be.id()}).join(",");_.indexOf(Ie)===-1&&(g.push(l.spawn($)),_.push(Ie))}}else break}return{distanceTo:w,pathTo:E,hasNegativeWeightCycle:y,negativeWeightCycles:g}}},Uc=Math.sqrt(2),Yc=function(e,r,a){a.length===0&&Ve("Karger-Stein must be run on a connected (sub)graph");for(var n=a[e],i=n[1],s=n[2],o=r[i],l=r[s],u=a,v=u.length-1;v>=0;v--){var f=u[v],c=f[1],h=f[2];(r[c]===o&&r[h]===l||r[c]===l&&r[h]===o)&&u.splice(v,1)}for(var d=0;d<u.length;d++){var y=u[d];y[1]===l?(u[d]=y.slice(),u[d][1]=o):y[2]===l&&(u[d]=y.slice(),u[d][2]=o)}for(var g=0;g<r.length;g++)r[g]===l&&(r[g]=o);return u},ai=function(e,r,a,n){for(;a>n;){var i=Math.floor(Math.random()*r.length);r=Yc(i,e,r),a--}return r},Xc={kargerStein:function(){var e=this,r=this.byGroup(),a=r.nodes,n=r.edges;n.unmergeBy(function(O){return O.isLoop()});var i=a.length,s=n.length,o=Math.ceil(Math.pow(Math.log(i)/Math.LN2,2)),l=Math.floor(i/Uc);if(i<2){Ve("At least 2 nodes are required for Karger-Stein algorithm");return}for(var u=[],v=0;v<s;v++){var f=n[v];u.push([v,a.indexOf(f.source()),a.indexOf(f.target())])}for(var c=1/0,h=[],d=new Array(i),y=new Array(i),g=new Array(i),p=function(_,H){for(var F=0;F<i;F++)H[F]=_[F]},m=0;m<=o;m++){for(var b=0;b<i;b++)y[b]=b;var w=ai(y,u.slice(),i,l),E=w.slice();p(y,g);var C=ai(y,w,l,2),x=ai(g,E,l,2);C.length<=x.length&&C.length<c?(c=C.length,h=C,p(y,d)):x.length<=C.length&&x.length<c&&(c=x.length,h=x,p(g,d))}for(var S=this.spawn(h.map(function(O){return n[O[0]]})),k=this.spawn(),B=this.spawn(),D=d[0],A=0;A<d.length;A++){var P=d[A],R=a[A];P===D?k.merge(R):B.merge(R)}var L=function(_){var H=e.spawn();return _.forEach(function(F){H.merge(F),F.connectedEdges().forEach(function(G){e.contains(G)&&!S.contains(G)&&H.merge(G)})}),H},I=[L(k),L(B)],M={cut:S,components:I,partition1:k,partition2:B};return M}},Zc=function(e){return{x:e.x,y:e.y}},Tn=function(e,r,a){return{x:e.x*r+a.x,y:e.y*r+a.y}},jl=function(e,r,a){return{x:(e.x-a.x)/r,y:(e.y-a.y)/r}},Lr=function(e){return{x:e[0],y:e[1]}},Qc=function(e){for(var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:e.length,n=1/0,i=r;i<a;i++){var s=e[i];isFinite(s)&&(n=Math.min(s,n))}return n},Jc=function(e){for(var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:e.length,n=-1/0,i=r;i<a;i++){var s=e[i];isFinite(s)&&(n=Math.max(s,n))}return n},jc=function(e){for(var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:e.length,n=0,i=0,s=r;s<a;s++){var o=e[s];isFinite(o)&&(n+=o,i++)}return n/i},ed=function(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:e.length,n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!0,i=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!0,s=arguments.length>5&&arguments[5]!==void 0?arguments[5]:!0;n?e=e.slice(r,a):(a<e.length&&e.splice(a,e.length-a),r>0&&e.splice(0,r));for(var o=0,l=e.length-1;l>=0;l--){var u=e[l];s?isFinite(u)||(e[l]=-1/0,o++):e.splice(l,1)}i&&e.sort(function(c,h){return c-h});var v=e.length,f=Math.floor(v/2);return v%2!==0?e[f+1+o]:(e[f-1+o]+e[f+o])/2},td=function(e){return Math.PI*e/180},qa=function(e,r){return Math.atan2(r,e)-Math.PI/2},zs=Math.log2||function(t){return Math.log(t)/Math.log(2)},ev=function(e){return e>0?1:e<0?-1:0},yr=function(e,r){return Math.sqrt(cr(e,r))},cr=function(e,r){var a=r.x-e.x,n=r.y-e.y;return a*a+n*n},rd=function(e){for(var r=e.length,a=0,n=0;n<r;n++)a+=e[n];for(var i=0;i<r;i++)e[i]=e[i]/a;return e},Je=function(e,r,a,n){return(1-n)*(1-n)*e+2*(1-n)*n*r+n*n*a},Nr=function(e,r,a,n){return{x:Je(e.x,r.x,a.x,n),y:Je(e.y,r.y,a.y,n)}},ad=function(e,r,a,n){var i={x:r.x-e.x,y:r.y-e.y},s=yr(e,r),o={x:i.x/s,y:i.y/s};return a=a??0,n=n??a*s,{x:e.x+o.x*n,y:e.y+o.y*n}},pa=function(e,r,a){return Math.max(e,Math.min(a,r))},pt=function(e){if(e==null)return{x1:1/0,y1:1/0,x2:-1/0,y2:-1/0,w:0,h:0};if(e.x1!=null&&e.y1!=null){if(e.x2!=null&&e.y2!=null&&e.x2>=e.x1&&e.y2>=e.y1)return{x1:e.x1,y1:e.y1,x2:e.x2,y2:e.y2,w:e.x2-e.x1,h:e.y2-e.y1};if(e.w!=null&&e.h!=null&&e.w>=0&&e.h>=0)return{x1:e.x1,y1:e.y1,x2:e.x1+e.w,y2:e.y1+e.h,w:e.w,h:e.h}}},nd=function(e){return{x1:e.x1,x2:e.x2,w:e.w,y1:e.y1,y2:e.y2,h:e.h}},id=function(e){e.x1=1/0,e.y1=1/0,e.x2=-1/0,e.y2=-1/0,e.w=0,e.h=0},sd=function(e,r,a){return{x1:e.x1+r,x2:e.x2+r,y1:e.y1+a,y2:e.y2+a,w:e.w,h:e.h}},tv=function(e,r){e.x1=Math.min(e.x1,r.x1),e.x2=Math.max(e.x2,r.x2),e.w=e.x2-e.x1,e.y1=Math.min(e.y1,r.y1),e.y2=Math.max(e.y2,r.y2),e.h=e.y2-e.y1},od=function(e,r,a){e.x1=Math.min(e.x1,r),e.x2=Math.max(e.x2,r),e.w=e.x2-e.x1,e.y1=Math.min(e.y1,a),e.y2=Math.max(e.y2,a),e.h=e.y2-e.y1},Qa=function(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return e.x1-=r,e.x2+=r,e.y1-=r,e.y2+=r,e.w=e.x2-e.x1,e.h=e.y2-e.y1,e},Ja=function(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[0],a,n,i,s;if(r.length===1)a=n=i=s=r[0];else if(r.length===2)a=i=r[0],s=n=r[1];else if(r.length===4){var o=je(r,4);a=o[0],n=o[1],i=o[2],s=o[3]}return e.x1-=s,e.x2+=n,e.y1-=a,e.y2+=i,e.w=e.x2-e.x1,e.h=e.y2-e.y1,e},Bo=function(e,r){e.x1=r.x1,e.y1=r.y1,e.x2=r.x2,e.y2=r.y2,e.w=e.x2-e.x1,e.h=e.y2-e.y1},qs=function(e,r){return!(e.x1>r.x2||r.x1>e.x2||e.x2<r.x1||r.x2<e.x1||e.y2<r.y1||r.y2<e.y1||e.y1>r.y2||r.y1>e.y2)},_r=function(e,r,a){return e.x1<=r&&r<=e.x2&&e.y1<=a&&a<=e.y2},ud=function(e,r){return _r(e,r.x,r.y)},rv=function(e,r){return _r(e,r.x1,r.y1)&&_r(e,r.x2,r.y2)},av=function(e,r,a,n,i,s,o){var l=arguments.length>7&&arguments[7]!==void 0?arguments[7]:"auto",u=l==="auto"?mr(i,s):l,v=i/2,f=s/2;u=Math.min(u,v,f);var c=u!==v,h=u!==f,d;if(c){var y=a-v+u-o,g=n-f-o,p=a+v-u+o,m=g;if(d=Jt(e,r,a,n,y,g,p,m,!1),d.length>0)return d}if(h){var b=a+v+o,w=n-f+u-o,E=b,C=n+f-u+o;if(d=Jt(e,r,a,n,b,w,E,C,!1),d.length>0)return d}if(c){var x=a-v+u-o,S=n+f+o,k=a+v-u+o,B=S;if(d=Jt(e,r,a,n,x,S,k,B,!1),d.length>0)return d}if(h){var D=a-v-o,A=n-f+u-o,P=D,R=n+f-u+o;if(d=Jt(e,r,a,n,D,A,P,R,!1),d.length>0)return d}var L;{var I=a-v+u,M=n-f+u;if(L=oa(e,r,a,n,I,M,u+o),L.length>0&&L[0]<=I&&L[1]<=M)return[L[0],L[1]]}{var O=a+v-u,_=n-f+u;if(L=oa(e,r,a,n,O,_,u+o),L.length>0&&L[0]>=O&&L[1]<=_)return[L[0],L[1]]}{var H=a+v-u,F=n+f-u;if(L=oa(e,r,a,n,H,F,u+o),L.length>0&&L[0]>=H&&L[1]>=F)return[L[0],L[1]]}{var G=a-v+u,U=n+f-u;if(L=oa(e,r,a,n,G,U,u+o),L.length>0&&L[0]<=G&&L[1]>=U)return[L[0],L[1]]}return[]},ld=function(e,r,a,n,i,s,o){var l=o,u=Math.min(a,i),v=Math.max(a,i),f=Math.min(n,s),c=Math.max(n,s);return u-l<=e&&e<=v+l&&f-l<=r&&r<=c+l},vd=function(e,r,a,n,i,s,o,l,u){var v={x1:Math.min(a,o,i)-u,x2:Math.max(a,o,i)+u,y1:Math.min(n,l,s)-u,y2:Math.max(n,l,s)+u};return!(e<v.x1||e>v.x2||r<v.y1||r>v.y2)},fd=function(e,r,a,n){a-=n;var i=r*r-4*e*a;if(i<0)return[];var s=Math.sqrt(i),o=2*e,l=(-r+s)/o,u=(-r-s)/o;return[l,u]},cd=function(e,r,a,n,i){var s=1e-5;e===0&&(e=s),r/=e,a/=e,n/=e;var o,l,u,v,f,c,h,d;if(l=(3*a-r*r)/9,u=-(27*n)+r*(9*a-2*(r*r)),u/=54,o=l*l*l+u*u,i[1]=0,h=r/3,o>0){f=u+Math.sqrt(o),f=f<0?-Math.pow(-f,1/3):Math.pow(f,1/3),c=u-Math.sqrt(o),c=c<0?-Math.pow(-c,1/3):Math.pow(c,1/3),i[0]=-h+f+c,h+=(f+c)/2,i[4]=i[2]=-h,h=Math.sqrt(3)*(-c+f)/2,i[3]=h,i[5]=-h;return}if(i[5]=i[3]=0,o===0){d=u<0?-Math.pow(-u,1/3):Math.pow(u,1/3),i[0]=-h+2*d,i[4]=i[2]=-(d+h);return}l=-l,v=l*l*l,v=Math.acos(u/Math.sqrt(v)),d=2*Math.sqrt(l),i[0]=-h+d*Math.cos(v/3),i[2]=-h+d*Math.cos((v+2*Math.PI)/3),i[4]=-h+d*Math.cos((v+4*Math.PI)/3)},dd=function(e,r,a,n,i,s,o,l){var u=1*a*a-4*a*i+2*a*o+4*i*i-4*i*o+o*o+n*n-4*n*s+2*n*l+4*s*s-4*s*l+l*l,v=1*9*a*i-3*a*a-3*a*o-6*i*i+3*i*o+9*n*s-3*n*n-3*n*l-6*s*s+3*s*l,f=1*3*a*a-6*a*i+a*o-a*e+2*i*i+2*i*e-o*e+3*n*n-6*n*s+n*l-n*r+2*s*s+2*s*r-l*r,c=1*a*i-a*a+a*e-i*e+n*s-n*n+n*r-s*r,h=[];cd(u,v,f,c,h);for(var d=1e-7,y=[],g=0;g<6;g+=2)Math.abs(h[g+1])<d&&h[g]>=0&&h[g]<=1&&y.push(h[g]);y.push(1),y.push(0);for(var p=-1,m,b,w,E=0;E<y.length;E++)m=Math.pow(1-y[E],2)*a+2*(1-y[E])*y[E]*i+y[E]*y[E]*o,b=Math.pow(1-y[E],2)*n+2*(1-y[E])*y[E]*s+y[E]*y[E]*l,w=Math.pow(m-e,2)+Math.pow(b-r,2),p>=0?w<p&&(p=w):p=w;return p},hd=function(e,r,a,n,i,s){var o=[e-a,r-n],l=[i-a,s-n],u=l[0]*l[0]+l[1]*l[1],v=o[0]*o[0]+o[1]*o[1],f=o[0]*l[0]+o[1]*l[1],c=f*f/u;return f<0?v:c>u?(e-i)*(e-i)+(r-s)*(r-s):v-c},gt=function(e,r,a){for(var n,i,s,o,l,u=0,v=0;v<a.length/2;v++)if(n=a[v*2],i=a[v*2+1],v+1<a.length/2?(s=a[(v+1)*2],o=a[(v+1)*2+1]):(s=a[(v+1-a.length/2)*2],o=a[(v+1-a.length/2)*2+1]),!(n==e&&s==e))if(n>=e&&e>=s||n<=e&&e<=s)l=(e-n)/(s-n)*(o-i)+i,l>r&&u++;else continue;return u%2!==0},Wt=function(e,r,a,n,i,s,o,l,u){var v=new Array(a.length),f;l[0]!=null?(f=Math.atan(l[1]/l[0]),l[0]<0?f=f+Math.PI/2:f=-f-Math.PI/2):f=l;for(var c=Math.cos(-f),h=Math.sin(-f),d=0;d<v.length/2;d++)v[d*2]=s/2*(a[d*2]*c-a[d*2+1]*h),v[d*2+1]=o/2*(a[d*2+1]*c+a[d*2]*h),v[d*2]+=n,v[d*2+1]+=i;var y;if(u>0){var g=cn(v,-u);y=fn(g)}else y=v;return gt(e,r,y)},gd=function(e,r,a,n,i,s,o,l){for(var u=new Array(a.length*2),v=0;v<l.length;v++){var f=l[v];u[v*4+0]=f.startX,u[v*4+1]=f.startY,u[v*4+2]=f.stopX,u[v*4+3]=f.stopY;var c=Math.pow(f.cx-e,2)+Math.pow(f.cy-r,2);if(c<=Math.pow(f.radius,2))return!0}return gt(e,r,u)},fn=function(e){for(var r=new Array(e.length/2),a,n,i,s,o,l,u,v,f=0;f<e.length/4;f++){a=e[f*4],n=e[f*4+1],i=e[f*4+2],s=e[f*4+3],f<e.length/4-1?(o=e[(f+1)*4],l=e[(f+1)*4+1],u=e[(f+1)*4+2],v=e[(f+1)*4+3]):(o=e[0],l=e[1],u=e[2],v=e[3]);var c=Jt(a,n,i,s,o,l,u,v,!0);r[f*2]=c[0],r[f*2+1]=c[1]}return r},cn=function(e,r){for(var a=new Array(e.length*2),n,i,s,o,l=0;l<e.length/2;l++){n=e[l*2],i=e[l*2+1],l<e.length/2-1?(s=e[(l+1)*2],o=e[(l+1)*2+1]):(s=e[0],o=e[1]);var u=o-i,v=-(s-n),f=Math.sqrt(u*u+v*v),c=u/f,h=v/f;a[l*4]=n+c*r,a[l*4+1]=i+h*r,a[l*4+2]=s+c*r,a[l*4+3]=o+h*r}return a},pd=function(e,r,a,n,i,s){var o=a-e,l=n-r;o/=i,l/=s;var u=Math.sqrt(o*o+l*l),v=u-1;if(v<0)return[];var f=v/u;return[(a-e)*f+e,(n-r)*f+r]},pr=function(e,r,a,n,i,s,o){return e-=i,r-=s,e/=a/2+o,r/=n/2+o,e*e+r*r<=1},oa=function(e,r,a,n,i,s,o){var l=[a-e,n-r],u=[e-i,r-s],v=l[0]*l[0]+l[1]*l[1],f=2*(u[0]*l[0]+u[1]*l[1]),c=u[0]*u[0]+u[1]*u[1]-o*o,h=f*f-4*v*c;if(h<0)return[];var d=(-f+Math.sqrt(h))/(2*v),y=(-f-Math.sqrt(h))/(2*v),g=Math.min(d,y),p=Math.max(d,y),m=[];if(g>=0&&g<=1&&m.push(g),p>=0&&p<=1&&m.push(p),m.length===0)return[];var b=m[0]*l[0]+e,w=m[0]*l[1]+r;if(m.length>1){if(m[0]==m[1])return[b,w];var E=m[1]*l[0]+e,C=m[1]*l[1]+r;return[b,w,E,C]}else return[b,w]},ni=function(e,r,a){return r<=e&&e<=a||a<=e&&e<=r?e:e<=r&&r<=a||a<=r&&r<=e?r:a},Jt=function(e,r,a,n,i,s,o,l,u){var v=e-i,f=a-e,c=o-i,h=r-s,d=n-r,y=l-s,g=c*h-y*v,p=f*h-d*v,m=y*f-c*d;if(m!==0){var b=g/m,w=p/m,E=.001,C=0-E,x=1+E;return C<=b&&b<=x&&C<=w&&w<=x?[e+b*f,r+b*d]:u?[e+b*f,r+b*d]:[]}else return g===0||p===0?ni(e,a,o)===o?[o,l]:ni(e,a,i)===i?[i,s]:ni(i,o,a)===a?[a,n]:[]:[]},ya=function(e,r,a,n,i,s,o,l){var u=[],v,f=new Array(a.length),c=!0;s==null&&(c=!1);var h;if(c){for(var d=0;d<f.length/2;d++)f[d*2]=a[d*2]*s+n,f[d*2+1]=a[d*2+1]*o+i;if(l>0){var y=cn(f,-l);h=fn(y)}else h=f}else h=a;for(var g,p,m,b,w=0;w<h.length/2;w++)g=h[w*2],p=h[w*2+1],w<h.length/2-1?(m=h[(w+1)*2],b=h[(w+1)*2+1]):(m=h[0],b=h[1]),v=Jt(e,r,n,i,g,p,m,b),v.length!==0&&u.push(v[0],v[1]);return u},yd=function(e,r,a,n,i,s,o,l,u){var v=[],f,c=new Array(a.length*2);u.forEach(function(m,b){b===0?(c[c.length-2]=m.startX,c[c.length-1]=m.startY):(c[b*4-2]=m.startX,c[b*4-1]=m.startY),c[b*4]=m.stopX,c[b*4+1]=m.stopY,f=oa(e,r,n,i,m.cx,m.cy,m.radius),f.length!==0&&v.push(f[0],f[1])});for(var h=0;h<c.length/4;h++)f=Jt(e,r,n,i,c[h*4],c[h*4+1],c[h*4+2],c[h*4+3],!1),f.length!==0&&v.push(f[0],f[1]);if(v.length>2){for(var d=[v[0],v[1]],y=Math.pow(d[0]-e,2)+Math.pow(d[1]-r,2),g=1;g<v.length/2;g++){var p=Math.pow(v[g*2]-e,2)+Math.pow(v[g*2+1]-r,2);p<=y&&(d[0]=v[g*2],d[1]=v[g*2+1],y=p)}return d}return v},Va=function(e,r,a){var n=[e[0]-r[0],e[1]-r[1]],i=Math.sqrt(n[0]*n[0]+n[1]*n[1]),s=(i-a)/i;return s<0&&(s=1e-5),[r[0]+s*n[0],r[1]+s*n[1]]},ct=function(e,r){var a=ds(e,r);return a=nv(a),a},nv=function(e){for(var r,a,n=e.length/2,i=1/0,s=1/0,o=-1/0,l=-1/0,u=0;u<n;u++)r=e[2*u],a=e[2*u+1],i=Math.min(i,r),o=Math.max(o,r),s=Math.min(s,a),l=Math.max(l,a);for(var v=2/(o-i),f=2/(l-s),c=0;c<n;c++)r=e[2*c]=e[2*c]*v,a=e[2*c+1]=e[2*c+1]*f,i=Math.min(i,r),o=Math.max(o,r),s=Math.min(s,a),l=Math.max(l,a);if(s<-1)for(var h=0;h<n;h++)a=e[2*h+1]=e[2*h+1]+(-1-s);return e},ds=function(e,r){var a=1/e*2*Math.PI,n=e%2===0?Math.PI/2+a/2:Math.PI/2;n+=r;for(var i=new Array(e*2),s,o=0;o<e;o++)s=o*a+n,i[2*o]=Math.cos(s),i[2*o+1]=Math.sin(-s);return i},mr=function(e,r){return Math.min(e/4,r/4,8)},iv=function(e,r){return Math.min(e/10,r/10,8)},Vs=function(){return 8},md=function(e,r,a){return[e-2*r+a,2*(r-e),e]},hs=function(e,r){return{heightOffset:Math.min(15,.05*r),widthOffset:Math.min(100,.25*e),ctrlPtOffsetPct:.05}},bd=Ue({dampingFactor:.8,precision:1e-6,iterations:200,weight:function(e){return 1}}),wd={pageRank:function(e){for(var r=bd(e),a=r.dampingFactor,n=r.precision,i=r.iterations,s=r.weight,o=this._private.cy,l=this.byGroup(),u=l.nodes,v=l.edges,f=u.length,c=f*f,h=v.length,d=new Array(c),y=new Array(f),g=(1-a)/f,p=0;p<f;p++){for(var m=0;m<f;m++){var b=p*f+m;d[b]=0}y[p]=0}for(var w=0;w<h;w++){var E=v[w],C=E.data("source"),x=E.data("target");if(C!==x){var S=u.indexOfId(C),k=u.indexOfId(x),B=s(E),D=k*f+S;d[D]+=B,y[S]+=B}}for(var A=1/f+g,P=0;P<f;P++)if(y[P]===0)for(var R=0;R<f;R++){var L=R*f+P;d[L]=A}else for(var I=0;I<f;I++){var M=I*f+P;d[M]=d[M]/y[P]+g}for(var O=new Array(f),_=new Array(f),H,F=0;F<f;F++)O[F]=1;for(var G=0;G<i;G++){for(var U=0;U<f;U++)_[U]=0;for(var X=0;X<f;X++)for(var Z=0;Z<f;Z++){var Q=X*f+Z;_[X]+=d[Q]*O[Z]}rd(_),H=O,O=_,_=H;for(var ee=0,te=0;te<f;te++){var K=H[te]-O[te];ee+=K*K}if(ee<n)break}var N={rank:function(J){return J=o.collection(J)[0],O[u.indexOf(J)]}};return N}},Ao=Ue({root:null,weight:function(e){return 1},directed:!1,alpha:0}),Fr={degreeCentralityNormalized:function(e){e=Ao(e);var r=this.cy(),a=this.nodes(),n=a.length;if(e.directed){for(var v={},f={},c=0,h=0,d=0;d<n;d++){var y=a[d],g=y.id();e.root=y;var p=this.degreeCentrality(e);c<p.indegree&&(c=p.indegree),h<p.outdegree&&(h=p.outdegree),v[g]=p.indegree,f[g]=p.outdegree}return{indegree:function(b){return c==0?0:(fe(b)&&(b=r.filter(b)),v[b.id()]/c)},outdegree:function(b){return h===0?0:(fe(b)&&(b=r.filter(b)),f[b.id()]/h)}}}else{for(var i={},s=0,o=0;o<n;o++){var l=a[o];e.root=l;var u=this.degreeCentrality(e);s<u.degree&&(s=u.degree),i[l.id()]=u.degree}return{degree:function(b){return s===0?0:(fe(b)&&(b=r.filter(b)),i[b.id()]/s)}}}},degreeCentrality:function(e){e=Ao(e);var r=this.cy(),a=this,n=e,i=n.root,s=n.weight,o=n.directed,l=n.alpha;if(i=r.collection(i)[0],o){for(var h=i.connectedEdges(),d=h.filter(function(C){return C.target().same(i)&&a.has(C)}),y=h.filter(function(C){return C.source().same(i)&&a.has(C)}),g=d.length,p=y.length,m=0,b=0,w=0;w<d.length;w++)m+=s(d[w]);for(var E=0;E<y.length;E++)b+=s(y[E]);return{indegree:Math.pow(g,1-l)*Math.pow(m,l),outdegree:Math.pow(p,1-l)*Math.pow(b,l)}}else{for(var u=i.connectedEdges().intersection(a),v=u.length,f=0,c=0;c<u.length;c++)f+=s(u[c]);return{degree:Math.pow(v,1-l)*Math.pow(f,l)}}}};Fr.dc=Fr.degreeCentrality;Fr.dcn=Fr.degreeCentralityNormalised=Fr.degreeCentralityNormalized;var Ro=Ue({harmonic:!0,weight:function(){return 1},directed:!1,root:null}),zr={closenessCentralityNormalized:function(e){for(var r=Ro(e),a=r.harmonic,n=r.weight,i=r.directed,s=this.cy(),o={},l=0,u=this.nodes(),v=this.floydWarshall({weight:n,directed:i}),f=0;f<u.length;f++){for(var c=0,h=u[f],d=0;d<u.length;d++)if(f!==d){var y=v.distance(h,u[d]);a?c+=1/y:c+=y}a||(c=1/c),l<c&&(l=c),o[h.id()]=c}return{closeness:function(p){return l==0?0:(fe(p)?p=s.filter(p)[0].id():p=p.id(),o[p]/l)}}},closenessCentrality:function(e){var r=Ro(e),a=r.root,n=r.weight,i=r.directed,s=r.harmonic;a=this.filter(a)[0];for(var o=this.dijkstra({root:a,weight:n,directed:i}),l=0,u=this.nodes(),v=0;v<u.length;v++){var f=u[v];if(!f.same(a)){var c=o.distanceTo(f);s?l+=1/c:l+=c}}return s?l:1/l}};zr.cc=zr.closenessCentrality;zr.ccn=zr.closenessCentralityNormalised=zr.closenessCentralityNormalized;var xd=Ue({weight:null,directed:!1}),gs={betweennessCentrality:function(e){for(var r=xd(e),a=r.directed,n=r.weight,i=n!=null,s=this.cy(),o=this.nodes(),l={},u={},v=0,f={set:function(b,w){u[b]=w,w>v&&(v=w)},get:function(b){return u[b]}},c=0;c<o.length;c++){var h=o[c],d=h.id();a?l[d]=h.outgoers().nodes():l[d]=h.openNeighborhood().nodes(),f.set(d,0)}for(var y=function(){for(var b=o[g].id(),w=[],E={},C={},x={},S=new Ba(function(X,Z){return x[X]-x[Z]}),k=0;k<o.length;k++){var B=o[k].id();E[B]=[],C[B]=0,x[B]=1/0}for(C[b]=1,x[b]=0,S.push(b);!S.empty();){var D=S.pop();if(w.push(D),i)for(var A=0;A<l[D].length;A++){var P=l[D][A],R=s.getElementById(D),L=void 0;R.edgesTo(P).length>0?L=R.edgesTo(P)[0]:L=P.edgesTo(R)[0];var I=n(L);P=P.id(),x[P]>x[D]+I&&(x[P]=x[D]+I,S.nodes.indexOf(P)<0?S.push(P):S.updateItem(P),C[P]=0,E[P]=[]),x[P]==x[D]+I&&(C[P]=C[P]+C[D],E[P].push(D))}else for(var M=0;M<l[D].length;M++){var O=l[D][M].id();x[O]==1/0&&(S.push(O),x[O]=x[D]+1),x[O]==x[D]+1&&(C[O]=C[O]+C[D],E[O].push(D))}}for(var _={},H=0;H<o.length;H++)_[o[H].id()]=0;for(;w.length>0;){for(var F=w.pop(),G=0;G<E[F].length;G++){var U=E[F][G];_[U]=_[U]+C[U]/C[F]*(1+_[F])}F!=o[g].id()&&f.set(F,f.get(F)+_[F])}},g=0;g<o.length;g++)y();var p={betweenness:function(b){var w=s.collection(b).id();return f.get(w)},betweennessNormalized:function(b){if(v==0)return 0;var w=s.collection(b).id();return f.get(w)/v}};return p.betweennessNormalised=p.betweennessNormalized,p}};gs.bc=gs.betweennessCentrality;var Ed=Ue({expandFactor:2,inflateFactor:2,multFactor:1,maxIterations:20,attributes:[function(t){return 1}]}),Cd=function(e){return Ed(e)},Td=function(e,r){for(var a=0,n=0;n<r.length;n++)a+=r[n](e);return a},Sd=function(e,r,a){for(var n=0;n<r;n++)e[n*r+n]=a},sv=function(e,r){for(var a,n=0;n<r;n++){a=0;for(var i=0;i<r;i++)a+=e[i*r+n];for(var s=0;s<r;s++)e[s*r+n]=e[s*r+n]/a}},Dd=function(e,r,a){for(var n=new Array(a*a),i=0;i<a;i++){for(var s=0;s<a;s++)n[i*a+s]=0;for(var o=0;o<a;o++)for(var l=0;l<a;l++)n[i*a+l]+=e[i*a+o]*r[o*a+l]}return n},kd=function(e,r,a){for(var n=e.slice(0),i=1;i<a;i++)e=Dd(e,n,r);return e},Pd=function(e,r,a){for(var n=new Array(r*r),i=0;i<r*r;i++)n[i]=Math.pow(e[i],a);return sv(n,r),n},Bd=function(e,r,a,n){for(var i=0;i<a;i++){var s=Math.round(e[i]*Math.pow(10,n))/Math.pow(10,n),o=Math.round(r[i]*Math.pow(10,n))/Math.pow(10,n);if(s!==o)return!1}return!0},Ad=function(e,r,a,n){for(var i=[],s=0;s<r;s++){for(var o=[],l=0;l<r;l++)Math.round(e[s*r+l]*1e3)/1e3>0&&o.push(a[l]);o.length!==0&&i.push(n.collection(o))}return i},Rd=function(e,r){for(var a=0;a<e.length;a++)if(!r[a]||e[a].id()!==r[a].id())return!1;return!0},Md=function(e){for(var r=0;r<e.length;r++)for(var a=0;a<e.length;a++)r!=a&&Rd(e[r],e[a])&&e.splice(a,1);return e},Mo=function(e){for(var r=this.nodes(),a=this.edges(),n=this.cy(),i=Cd(e),s={},o=0;o<r.length;o++)s[r[o].id()]=o;for(var l=r.length,u=l*l,v=new Array(u),f,c=0;c<u;c++)v[c]=0;for(var h=0;h<a.length;h++){var d=a[h],y=s[d.source().id()],g=s[d.target().id()],p=Td(d,i.attributes);v[y*l+g]+=p,v[g*l+y]+=p}Sd(v,l,i.multFactor),sv(v,l);for(var m=!0,b=0;m&&b<i.maxIterations;)m=!1,f=kd(v,l,i.expandFactor),v=Pd(f,l,i.inflateFactor),Bd(v,f,u,4)||(m=!0),b++;var w=Ad(v,l,r,n);return w=Md(w),w},Ld={markovClustering:Mo,mcl:Mo},Id=function(e){return e},ov=function(e,r){return Math.abs(r-e)},Lo=function(e,r,a){return e+ov(r,a)},Io=function(e,r,a){return e+Math.pow(a-r,2)},Od=function(e){return Math.sqrt(e)},Nd=function(e,r,a){return Math.max(e,ov(r,a))},ea=function(e,r,a,n,i){for(var s=arguments.length>5&&arguments[5]!==void 0?arguments[5]:Id,o=n,l,u,v=0;v<e;v++)l=r(v),u=a(v),o=i(o,l,u);return s(o)},Gr={euclidean:function(e,r,a){return e>=2?ea(e,r,a,0,Io,Od):ea(e,r,a,0,Lo)},squaredEuclidean:function(e,r,a){return ea(e,r,a,0,Io)},manhattan:function(e,r,a){return ea(e,r,a,0,Lo)},max:function(e,r,a){return ea(e,r,a,-1/0,Nd)}};Gr["squared-euclidean"]=Gr.squaredEuclidean;Gr.squaredeuclidean=Gr.squaredEuclidean;function Sn(t,e,r,a,n,i){var s;return _e(t)?s=t:s=Gr[t]||Gr.euclidean,e===0&&_e(t)?s(n,i):s(e,r,a,n,i)}var Fd=Ue({k:2,m:2,sensitivityThreshold:1e-4,distance:"euclidean",maxIterations:10,attributes:[],testMode:!1,testCentroids:null}),_s=function(e){return Fd(e)},dn=function(e,r,a,n,i){var s=i!=="kMedoids",o=s?function(f){return a[f]}:function(f){return n[f](a)},l=function(c){return n[c](r)},u=a,v=r;return Sn(e,n.length,o,l,u,v)},ii=function(e,r,a){for(var n=a.length,i=new Array(n),s=new Array(n),o=new Array(r),l=null,u=0;u<n;u++)i[u]=e.min(a[u]).value,s[u]=e.max(a[u]).value;for(var v=0;v<r;v++){l=[];for(var f=0;f<n;f++)l[f]=Math.random()*(s[f]-i[f])+i[f];o[v]=l}return o},uv=function(e,r,a,n,i){for(var s=1/0,o=0,l=0;l<r.length;l++){var u=dn(a,e,r[l],n,i);u<s&&(s=u,o=l)}return o},lv=function(e,r,a){for(var n=[],i=null,s=0;s<r.length;s++)i=r[s],a[i.id()]===e&&n.push(i);return n},zd=function(e,r,a){return Math.abs(r-e)<=a},qd=function(e,r,a){for(var n=0;n<e.length;n++)for(var i=0;i<e[n].length;i++){var s=Math.abs(e[n][i]-r[n][i]);if(s>a)return!1}return!0},Vd=function(e,r,a){for(var n=0;n<a;n++)if(e===r[n])return!0;return!1},Oo=function(e,r){var a=new Array(r);if(e.length<50)for(var n=0;n<r;n++){for(var i=e[Math.floor(Math.random()*e.length)];Vd(i,a,n);)i=e[Math.floor(Math.random()*e.length)];a[n]=i}else for(var s=0;s<r;s++)a[s]=e[Math.floor(Math.random()*e.length)];return a},No=function(e,r,a){for(var n=0,i=0;i<r.length;i++)n+=dn("manhattan",r[i],e,a,"kMedoids");return n},_d=function(e){var r=this.cy(),a=this.nodes(),n=null,i=_s(e),s=new Array(i.k),o={},l;i.testMode?typeof i.testCentroids=="number"?(i.testCentroids,l=ii(a,i.k,i.attributes)):We(i.testCentroids)==="object"?l=i.testCentroids:l=ii(a,i.k,i.attributes):l=ii(a,i.k,i.attributes);for(var u=!0,v=0;u&&v<i.maxIterations;){for(var f=0;f<a.length;f++)n=a[f],o[n.id()]=uv(n,l,i.distance,i.attributes,"kMeans");u=!1;for(var c=0;c<i.k;c++){var h=lv(c,a,o);if(h.length!==0){for(var d=i.attributes.length,y=l[c],g=new Array(d),p=new Array(d),m=0;m<d;m++){p[m]=0;for(var b=0;b<h.length;b++)n=h[b],p[m]+=i.attributes[m](n);g[m]=p[m]/h.length,zd(g[m],y[m],i.sensitivityThreshold)||(u=!0)}l[c]=g,s[c]=r.collection(h)}}v++}return s},Gd=function(e){var r=this.cy(),a=this.nodes(),n=null,i=_s(e),s=new Array(i.k),o,l={},u,v=new Array(i.k);i.testMode?typeof i.testCentroids=="number"||(We(i.testCentroids)==="object"?o=i.testCentroids:o=Oo(a,i.k)):o=Oo(a,i.k);for(var f=!0,c=0;f&&c<i.maxIterations;){for(var h=0;h<a.length;h++)n=a[h],l[n.id()]=uv(n,o,i.distance,i.attributes,"kMedoids");f=!1;for(var d=0;d<o.length;d++){var y=lv(d,a,l);if(y.length!==0){v[d]=No(o[d],y,i.attributes);for(var g=0;g<y.length;g++)u=No(y[g],y,i.attributes),u<v[d]&&(v[d]=u,o[d]=y[g],f=!0);s[d]=r.collection(y)}}c++}return s},Hd=function(e,r,a,n,i){for(var s,o,l=0;l<r.length;l++)for(var u=0;u<e.length;u++)n[l][u]=Math.pow(a[l][u],i.m);for(var v=0;v<e.length;v++)for(var f=0;f<i.attributes.length;f++){s=0,o=0;for(var c=0;c<r.length;c++)s+=n[c][v]*i.attributes[f](r[c]),o+=n[c][v];e[v][f]=s/o}},Kd=function(e,r,a,n,i){for(var s=0;s<e.length;s++)r[s]=e[s].slice();for(var o,l,u,v=2/(i.m-1),f=0;f<a.length;f++)for(var c=0;c<n.length;c++){o=0;for(var h=0;h<a.length;h++)l=dn(i.distance,n[c],a[f],i.attributes,"cmeans"),u=dn(i.distance,n[c],a[h],i.attributes,"cmeans"),o+=Math.pow(l/u,v);e[c][f]=1/o}},$d=function(e,r,a,n){for(var i=new Array(a.k),s=0;s<i.length;s++)i[s]=[];for(var o,l,u=0;u<r.length;u++){o=-1/0,l=-1;for(var v=0;v<r[0].length;v++)r[u][v]>o&&(o=r[u][v],l=v);i[l].push(e[u])}for(var f=0;f<i.length;f++)i[f]=n.collection(i[f]);return i},Fo=function(e){var r=this.cy(),a=this.nodes(),n=_s(e),i,s,o,l,u;l=new Array(a.length);for(var v=0;v<a.length;v++)l[v]=new Array(n.k);o=new Array(a.length);for(var f=0;f<a.length;f++)o[f]=new Array(n.k);for(var c=0;c<a.length;c++){for(var h=0,d=0;d<n.k;d++)o[c][d]=Math.random(),h+=o[c][d];for(var y=0;y<n.k;y++)o[c][y]=o[c][y]/h}s=new Array(n.k);for(var g=0;g<n.k;g++)s[g]=new Array(n.attributes.length);u=new Array(a.length);for(var p=0;p<a.length;p++)u[p]=new Array(n.k);for(var m=!0,b=0;m&&b<n.maxIterations;)m=!1,Hd(s,a,o,u,n),Kd(o,l,s,a,n),qd(o,l,n.sensitivityThreshold)||(m=!0),b++;return i=$d(a,o,n,r),{clusters:i,degreeOfMembership:o}},Wd={kMeans:_d,kMedoids:Gd,fuzzyCMeans:Fo,fcm:Fo},Ud=Ue({distance:"euclidean",linkage:"min",mode:"threshold",threshold:1/0,addDendrogram:!1,dendrogramDepth:0,attributes:[]}),Yd={single:"min",complete:"max"},Xd=function(e){var r=Ud(e),a=Yd[r.linkage];return a!=null&&(r.linkage=a),r},zo=function(e,r,a,n,i){for(var s=0,o=1/0,l,u=i.attributes,v=function(k,B){return Sn(i.distance,u.length,function(D){return u[D](k)},function(D){return u[D](B)},k,B)},f=0;f<e.length;f++){var c=e[f].key,h=a[c][n[c]];h<o&&(s=c,o=h)}if(i.mode==="threshold"&&o>=i.threshold||i.mode==="dendrogram"&&e.length===1)return!1;var d=r[s],y=r[n[s]],g;i.mode==="dendrogram"?g={left:d,right:y,key:d.key}:g={value:d.value.concat(y.value),key:d.key},e[d.index]=g,e.splice(y.index,1),r[d.key]=g;for(var p=0;p<e.length;p++){var m=e[p];d.key===m.key?l=1/0:i.linkage==="min"?(l=a[d.key][m.key],a[d.key][m.key]>a[y.key][m.key]&&(l=a[y.key][m.key])):i.linkage==="max"?(l=a[d.key][m.key],a[d.key][m.key]<a[y.key][m.key]&&(l=a[y.key][m.key])):i.linkage==="mean"?l=(a[d.key][m.key]*d.size+a[y.key][m.key]*y.size)/(d.size+y.size):i.mode==="dendrogram"?l=v(m.value,d.value):l=v(m.value[0],d.value[0]),a[d.key][m.key]=a[m.key][d.key]=l}for(var b=0;b<e.length;b++){var w=e[b].key;if(n[w]===d.key||n[w]===y.key){for(var E=w,C=0;C<e.length;C++){var x=e[C].key;a[w][x]<a[w][E]&&(E=x)}n[w]=E}e[b].index=b}return d.key=y.key=d.index=y.index=null,!0},Ir=function(e,r,a){e&&(e.value?r.push(e.value):(e.left&&Ir(e.left,r),e.right&&Ir(e.right,r)))},ps=function(e,r){if(!e)return"";if(e.left&&e.right){var a=ps(e.left,r),n=ps(e.right,r),i=r.add({group:"nodes",data:{id:a+","+n}});return r.add({group:"edges",data:{source:a,target:i.id()}}),r.add({group:"edges",data:{source:n,target:i.id()}}),i.id()}else if(e.value)return e.value.id()},ys=function(e,r,a){if(!e)return[];var n=[],i=[],s=[];return r===0?(e.left&&Ir(e.left,n),e.right&&Ir(e.right,i),s=n.concat(i),[a.collection(s)]):r===1?e.value?[a.collection(e.value)]:(e.left&&Ir(e.left,n),e.right&&Ir(e.right,i),[a.collection(n),a.collection(i)]):e.value?[a.collection(e.value)]:(e.left&&(n=ys(e.left,r-1,a)),e.right&&(i=ys(e.right,r-1,a)),n.concat(i))},qo=function(e){for(var r=this.cy(),a=this.nodes(),n=Xd(e),i=n.attributes,s=function(b,w){return Sn(n.distance,i.length,function(E){return i[E](b)},function(E){return i[E](w)},b,w)},o=[],l=[],u=[],v=[],f=0;f<a.length;f++){var c={value:n.mode==="dendrogram"?a[f]:[a[f]],key:f,index:f};o[f]=c,v[f]=c,l[f]=[],u[f]=0}for(var h=0;h<o.length;h++)for(var d=0;d<=h;d++){var y=void 0;n.mode==="dendrogram"?y=h===d?1/0:s(o[h].value,o[d].value):y=h===d?1/0:s(o[h].value[0],o[d].value[0]),l[h][d]=y,l[d][h]=y,y<l[h][u[h]]&&(u[h]=d)}for(var g=zo(o,v,l,u,n);g;)g=zo(o,v,l,u,n);var p;return n.mode==="dendrogram"?(p=ys(o[0],n.dendrogramDepth,r),n.addDendrogram&&ps(o[0],r)):(p=new Array(o.length),o.forEach(function(m,b){m.key=m.index=null,p[b]=r.collection(m.value)})),p},Zd={hierarchicalClustering:qo,hca:qo},Qd=Ue({distance:"euclidean",preference:"median",damping:.8,maxIterations:1e3,minIterations:100,attributes:[]}),Jd=function(e){var r=e.damping,a=e.preference;.5<=r&&r<1||Ve("Damping must range on [0.5, 1).  Got: ".concat(r));var n=["median","mean","min","max"];return n.some(function(i){return i===a})||ae(a)||Ve("Preference must be one of [".concat(n.map(function(i){return"'".concat(i,"'")}).join(", "),"] or a number.  Got: ").concat(a)),Qd(e)},jd=function(e,r,a,n){var i=function(o,l){return n[l](o)};return-Sn(e,n.length,function(s){return i(r,s)},function(s){return i(a,s)},r,a)},eh=function(e,r){var a=null;return r==="median"?a=ed(e):r==="mean"?a=jc(e):r==="min"?a=Qc(e):r==="max"?a=Jc(e):a=r,a},th=function(e,r,a){for(var n=[],i=0;i<e;i++)r[i*e+i]+a[i*e+i]>0&&n.push(i);return n},Vo=function(e,r,a){for(var n=[],i=0;i<e;i++){for(var s=-1,o=-1/0,l=0;l<a.length;l++){var u=a[l];r[i*e+u]>o&&(s=u,o=r[i*e+u])}s>0&&n.push(s)}for(var v=0;v<a.length;v++)n[a[v]]=a[v];return n},rh=function(e,r,a){for(var n=Vo(e,r,a),i=0;i<a.length;i++){for(var s=[],o=0;o<n.length;o++)n[o]===a[i]&&s.push(o);for(var l=-1,u=-1/0,v=0;v<s.length;v++){for(var f=0,c=0;c<s.length;c++)f+=r[s[c]*e+s[v]];f>u&&(l=v,u=f)}a[i]=s[l]}return n=Vo(e,r,a),n},_o=function(e){for(var r=this.cy(),a=this.nodes(),n=Jd(e),i={},s=0;s<a.length;s++)i[a[s].id()]=s;var o,l,u,v,f,c;o=a.length,l=o*o,u=new Array(l);for(var h=0;h<l;h++)u[h]=-1/0;for(var d=0;d<o;d++)for(var y=0;y<o;y++)d!==y&&(u[d*o+y]=jd(n.distance,a[d],a[y],n.attributes));v=eh(u,n.preference);for(var g=0;g<o;g++)u[g*o+g]=v;f=new Array(l);for(var p=0;p<l;p++)f[p]=0;c=new Array(l);for(var m=0;m<l;m++)c[m]=0;for(var b=new Array(o),w=new Array(o),E=new Array(o),C=0;C<o;C++)b[C]=0,w[C]=0,E[C]=0;for(var x=new Array(o*n.minIterations),S=0;S<x.length;S++)x[S]=0;var k;for(k=0;k<n.maxIterations;k++){for(var B=0;B<o;B++){for(var D=-1/0,A=-1/0,P=-1,R=0,L=0;L<o;L++)b[L]=f[B*o+L],R=c[B*o+L]+u[B*o+L],R>=D?(A=D,D=R,P=L):R>A&&(A=R);for(var I=0;I<o;I++)f[B*o+I]=(1-n.damping)*(u[B*o+I]-D)+n.damping*b[I];f[B*o+P]=(1-n.damping)*(u[B*o+P]-A)+n.damping*b[P]}for(var M=0;M<o;M++){for(var O=0,_=0;_<o;_++)b[_]=c[_*o+M],w[_]=Math.max(0,f[_*o+M]),O+=w[_];O-=w[M],w[M]=f[M*o+M],O+=w[M];for(var H=0;H<o;H++)c[H*o+M]=(1-n.damping)*Math.min(0,O-w[H])+n.damping*b[H];c[M*o+M]=(1-n.damping)*(O-w[M])+n.damping*b[M]}for(var F=0,G=0;G<o;G++){var U=c[G*o+G]+f[G*o+G]>0?1:0;x[k%n.minIterations*o+G]=U,F+=U}if(F>0&&(k>=n.minIterations-1||k==n.maxIterations-1)){for(var X=0,Z=0;Z<o;Z++){E[Z]=0;for(var Q=0;Q<n.minIterations;Q++)E[Z]+=x[Q*o+Z];(E[Z]===0||E[Z]===n.minIterations)&&X++}if(X===o)break}}for(var ee=th(o,f,c),te=rh(o,u,ee),K={},N=0;N<ee.length;N++)K[ee[N]]=[];for(var $=0;$<a.length;$++){var J=i[a[$].id()],re=te[J];re!=null&&K[re].push(a[$])}for(var le=new Array(ee.length),xe=0;xe<ee.length;xe++)le[xe]=r.collection(K[ee[xe]]);return le},ah={affinityPropagation:_o,ap:_o},nh=Ue({root:void 0,directed:!1}),ih={hierholzer:function(e){if(!ke(e)){var r=arguments;e={root:r[0],directed:r[1]}}var a=nh(e),n=a.root,i=a.directed,s=this,o=!1,l,u,v;n&&(v=fe(n)?this.filter(n)[0].id():n[0].id());var f={},c={};i?s.forEach(function(m){var b=m.id();if(m.isNode()){var w=m.indegree(!0),E=m.outdegree(!0),C=w-E,x=E-w;C==1?l?o=!0:l=b:x==1?u?o=!0:u=b:(x>1||C>1)&&(o=!0),f[b]=[],m.outgoers().forEach(function(S){S.isEdge()&&f[b].push(S.id())})}else c[b]=[void 0,m.target().id()]}):s.forEach(function(m){var b=m.id();if(m.isNode()){var w=m.degree(!0);w%2&&(l?u?o=!0:u=b:l=b),f[b]=[],m.connectedEdges().forEach(function(E){return f[b].push(E.id())})}else c[b]=[m.source().id(),m.target().id()]});var h={found:!1,trail:void 0};if(o)return h;if(u&&l)if(i){if(v&&u!=v)return h;v=u}else{if(v&&u!=v&&l!=v)return h;v||(v=u)}else v||(v=s[0].id());var d=function(b){for(var w=b,E=[b],C,x,S;f[w].length;)C=f[w].shift(),x=c[C][0],S=c[C][1],w!=S?(f[S]=f[S].filter(function(k){return k!=C}),w=S):!i&&w!=x&&(f[x]=f[x].filter(function(k){return k!=C}),w=x),E.unshift(C),E.unshift(w);return E},y=[],g=[];for(g=d(v);g.length!=1;)f[g[0]].length==0?(y.unshift(s.getElementById(g.shift())),y.unshift(s.getElementById(g.shift()))):g=d(g.shift()).concat(g);y.unshift(s.getElementById(g.shift()));for(var p in f)if(f[p].length)return h;return h.found=!0,h.trail=this.spawn(y,!0),h}},_a=function(){var e=this,r={},a=0,n=0,i=[],s=[],o={},l=function(c,h){for(var d=s.length-1,y=[],g=e.spawn();s[d].x!=c||s[d].y!=h;)y.push(s.pop().edge),d--;y.push(s.pop().edge),y.forEach(function(p){var m=p.connectedNodes().intersection(e);g.merge(p),m.forEach(function(b){var w=b.id(),E=b.connectedEdges().intersection(e);g.merge(b),r[w].cutVertex?g.merge(E.filter(function(C){return C.isLoop()})):g.merge(E)})}),i.push(g)},u=function(c,h,d){c===d&&(n+=1),r[h]={id:a,low:a++,cutVertex:!1};var y=e.getElementById(h).connectedEdges().intersection(e);if(y.size()===0)i.push(e.spawn(e.getElementById(h)));else{var g,p,m,b;y.forEach(function(w){g=w.source().id(),p=w.target().id(),m=g===h?p:g,m!==d&&(b=w.id(),o[b]||(o[b]=!0,s.push({x:h,y:m,edge:w})),m in r?r[h].low=Math.min(r[h].low,r[m].id):(u(c,m,h),r[h].low=Math.min(r[h].low,r[m].low),r[h].id<=r[m].low&&(r[h].cutVertex=!0,l(h,m))))})}};e.forEach(function(f){if(f.isNode()){var c=f.id();c in r||(n=0,u(c,c),r[c].cutVertex=n>1)}});var v=Object.keys(r).filter(function(f){return r[f].cutVertex}).map(function(f){return e.getElementById(f)});return{cut:e.spawn(v),components:i}},sh={hopcroftTarjanBiconnected:_a,htbc:_a,htb:_a,hopcroftTarjanBiconnectedComponents:_a},Ga=function(){var e=this,r={},a=0,n=[],i=[],s=e.spawn(e),o=function(u){i.push(u),r[u]={index:a,low:a++,explored:!1};var v=e.getElementById(u).connectedEdges().intersection(e);if(v.forEach(function(y){var g=y.target().id();g!==u&&(g in r||o(g),r[g].explored||(r[u].low=Math.min(r[u].low,r[g].low)))}),r[u].index===r[u].low){for(var f=e.spawn();;){var c=i.pop();if(f.merge(e.getElementById(c)),r[c].low=r[u].index,r[c].explored=!0,c===u)break}var h=f.edgesWith(f),d=f.merge(h);n.push(d),s=s.difference(d)}};return e.forEach(function(l){if(l.isNode()){var u=l.id();u in r||o(u)}}),{cut:s,components:n}},oh={tarjanStronglyConnected:Ga,tsc:Ga,tscc:Ga,tarjanStronglyConnectedComponents:Ga},vv={};[ga,qc,Vc,Gc,Kc,Wc,Xc,wd,Fr,zr,gs,Ld,Wd,Zd,ah,ih,sh,oh].forEach(function(t){ge(vv,t)});/*!
Embeddable Minimum Strictly-Compliant Promises/A+ 1.1.1 Thenable
Copyright (c) 2013-2014 Ralf S. Engelschall (http://engelschall.com)
Licensed under The MIT License (http://opensource.org/licenses/MIT)
*/var fv=0,cv=1,dv=2,At=function(e){if(!(this instanceof At))return new At(e);this.id="Thenable/1.0.7",this.state=fv,this.fulfillValue=void 0,this.rejectReason=void 0,this.onFulfilled=[],this.onRejected=[],this.proxy={then:this.then.bind(this)},typeof e=="function"&&e.call(this,this.fulfill.bind(this),this.reject.bind(this))};At.prototype={fulfill:function(e){return Go(this,cv,"fulfillValue",e)},reject:function(e){return Go(this,dv,"rejectReason",e)},then:function(e,r){var a=this,n=new At;return a.onFulfilled.push(Ko(e,n,"fulfill")),a.onRejected.push(Ko(r,n,"reject")),hv(a),n.proxy}};var Go=function(e,r,a,n){return e.state===fv&&(e.state=r,e[a]=n,hv(e)),e},hv=function(e){e.state===cv?Ho(e,"onFulfilled",e.fulfillValue):e.state===dv&&Ho(e,"onRejected",e.rejectReason)},Ho=function(e,r,a){if(e[r].length!==0){var n=e[r];e[r]=[];var i=function(){for(var o=0;o<n.length;o++)n[o](a)};typeof setImmediate=="function"?setImmediate(i):setTimeout(i,0)}},Ko=function(e,r,a){return function(n){if(typeof e!="function")r[a].call(r,n);else{var i;try{i=e(n)}catch(s){r.reject(s);return}gv(r,i)}}},gv=function(e,r){if(e===r||e.proxy===r){e.reject(new TypeError("cannot resolve promise with itself"));return}var a;if(We(r)==="object"&&r!==null||typeof r=="function")try{a=r.then}catch(i){e.reject(i);return}if(typeof a=="function"){var n=!1;try{a.call(r,function(i){n||(n=!0,i===r?e.reject(new TypeError("circular thenable chain")):gv(e,i))},function(i){n||(n=!0,e.reject(i))})}catch(i){n||e.reject(i)}return}e.fulfill(r)};At.all=function(t){return new At(function(e,r){for(var a=new Array(t.length),n=0,i=function(l,u){a[l]=u,n++,n===t.length&&e(a)},s=0;s<t.length;s++)(function(o){var l=t[o],u=l!=null&&l.then!=null;if(u)l.then(function(f){i(o,f)},function(f){r(f)});else{var v=l;i(o,v)}})(s)})};At.resolve=function(t){return new At(function(e,r){e(t)})};At.reject=function(t){return new At(function(e,r){r(t)})};var Wr=typeof Promise<"u"?Promise:At,ms=function(e,r,a){var n=Ms(e),i=!n,s=this._private=ge({duration:1e3},r,a);if(s.target=e,s.style=s.style||s.css,s.started=!1,s.playing=!1,s.hooked=!1,s.applying=!1,s.progress=0,s.completes=[],s.frames=[],s.complete&&_e(s.complete)&&s.completes.push(s.complete),i){var o=e.position();s.startPosition=s.startPosition||{x:o.x,y:o.y},s.startStyle=s.startStyle||e.cy().style().getAnimationStartStyle(e,s.style)}if(n){var l=e.pan();s.startPan={x:l.x,y:l.y},s.startZoom=e.zoom()}this.length=1,this[0]=this},br=ms.prototype;ge(br,{instanceString:function(){return"animation"},hook:function(){var e=this._private;if(!e.hooked){var r,a=e.target._private.animation;e.queue?r=a.queue:r=a.current,r.push(this),bt(e.target)&&e.target.cy().addToAnimationPool(e.target),e.hooked=!0}return this},play:function(){var e=this._private;return e.progress===1&&(e.progress=0),e.playing=!0,e.started=!1,e.stopped=!1,this.hook(),this},playing:function(){return this._private.playing},apply:function(){var e=this._private;return e.applying=!0,e.started=!1,e.stopped=!1,this.hook(),this},applying:function(){return this._private.applying},pause:function(){var e=this._private;return e.playing=!1,e.started=!1,this},stop:function(){var e=this._private;return e.playing=!1,e.started=!1,e.stopped=!0,this},rewind:function(){return this.progress(0)},fastforward:function(){return this.progress(1)},time:function(e){var r=this._private;return e===void 0?r.progress*r.duration:this.progress(e/r.duration)},progress:function(e){var r=this._private,a=r.playing;return e===void 0?r.progress:(a&&this.pause(),r.progress=e,r.started=!1,a&&this.play(),this)},completed:function(){return this._private.progress===1},reverse:function(){var e=this._private,r=e.playing;r&&this.pause(),e.progress=1-e.progress,e.started=!1;var a=function(u,v){var f=e[u];f!=null&&(e[u]=e[v],e[v]=f)};if(a("zoom","startZoom"),a("pan","startPan"),a("position","startPosition"),e.style)for(var n=0;n<e.style.length;n++){var i=e.style[n],s=i.name,o=e.startStyle[s];e.startStyle[s]=i,e.style[n]=o}return r&&this.play(),this},promise:function(e){var r=this._private,a;switch(e){case"frame":a=r.frames;break;default:case"complete":case"completed":a=r.completes}return new Wr(function(n,i){a.push(function(){n()})})}});br.complete=br.completed;br.run=br.play;br.running=br.playing;var uh={animated:function(){return function(){var r=this,a=r.length!==void 0,n=a?r:[r],i=this._private.cy||this;if(!i.styleEnabled())return!1;var s=n[0];if(s)return s._private.animation.current.length>0}},clearQueue:function(){return function(){var r=this,a=r.length!==void 0,n=a?r:[r],i=this._private.cy||this;if(!i.styleEnabled())return this;for(var s=0;s<n.length;s++){var o=n[s];o._private.animation.queue=[]}return this}},delay:function(){return function(r,a){var n=this._private.cy||this;return n.styleEnabled()?this.animate({delay:r,duration:r,complete:a}):this}},delayAnimation:function(){return function(r,a){var n=this._private.cy||this;return n.styleEnabled()?this.animation({delay:r,duration:r,complete:a}):this}},animation:function(){return function(r,a){var n=this,i=n.length!==void 0,s=i?n:[n],o=this._private.cy||this,l=!i,u=!l;if(!o.styleEnabled())return this;var v=o.style();r=ge({},r,a);var f=Object.keys(r).length===0;if(f)return new ms(s[0],r);switch(r.duration===void 0&&(r.duration=400),r.duration){case"slow":r.duration=600;break;case"fast":r.duration=200;break}if(u&&(r.style=v.getPropsList(r.style||r.css),r.css=void 0),u&&r.renderedPosition!=null){var c=r.renderedPosition,h=o.pan(),d=o.zoom();r.position=jl(c,d,h)}if(l&&r.panBy!=null){var y=r.panBy,g=o.pan();r.pan={x:g.x+y.x,y:g.y+y.y}}var p=r.center||r.centre;if(l&&p!=null){var m=o.getCenterPan(p.eles,r.zoom);m!=null&&(r.pan=m)}if(l&&r.fit!=null){var b=r.fit,w=o.getFitViewport(b.eles||b.boundingBox,b.padding);w!=null&&(r.pan=w.pan,r.zoom=w.zoom)}if(l&&ke(r.zoom)){var E=o.getZoomedViewport(r.zoom);E!=null?(E.zoomed&&(r.zoom=E.zoom),E.panned&&(r.pan=E.pan)):r.zoom=null}return new ms(s[0],r)}},animate:function(){return function(r,a){var n=this,i=n.length!==void 0,s=i?n:[n],o=this._private.cy||this;if(!o.styleEnabled())return this;a&&(r=ge({},r,a));for(var l=0;l<s.length;l++){var u=s[l],v=u.animated()&&(r.queue===void 0||r.queue),f=u.animation(r,v?{queue:!0}:void 0);f.play()}return this}},stop:function(){return function(r,a){var n=this,i=n.length!==void 0,s=i?n:[n],o=this._private.cy||this;if(!o.styleEnabled())return this;for(var l=0;l<s.length;l++){for(var u=s[l],v=u._private,f=v.animation.current,c=0;c<f.length;c++){var h=f[c],d=h._private;a&&(d.duration=0)}r&&(v.animation.queue=[]),a||(v.animation.current=[])}return o.notify("draw"),this}}},si,$o;function Dn(){if($o)return si;$o=1;var t=Array.isArray;return si=t,si}var oi,Wo;function lh(){if(Wo)return oi;Wo=1;var t=Dn(),e=ka(),r=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;function n(i,s){if(t(i))return!1;var o=typeof i;return o=="number"||o=="symbol"||o=="boolean"||i==null||e(i)?!0:a.test(i)||!r.test(i)||s!=null&&i in Object(s)}return oi=n,oi}var ui,Uo;function vh(){if(Uo)return ui;Uo=1;var t=Kl(),e=Da(),r="[object AsyncFunction]",a="[object Function]",n="[object GeneratorFunction]",i="[object Proxy]";function s(o){if(!e(o))return!1;var l=t(o);return l==a||l==n||l==r||l==i}return ui=s,ui}var li,Yo;function fh(){if(Yo)return li;Yo=1;var t=En(),e=t["__core-js_shared__"];return li=e,li}var vi,Xo;function ch(){if(Xo)return vi;Xo=1;var t=fh(),e=function(){var a=/[^.]+$/.exec(t&&t.keys&&t.keys.IE_PROTO||"");return a?"Symbol(src)_1."+a:""}();function r(a){return!!e&&e in a}return vi=r,vi}var fi,Zo;function dh(){if(Zo)return fi;Zo=1;var t=Function.prototype,e=t.toString;function r(a){if(a!=null){try{return e.call(a)}catch{}try{return a+""}catch{}}return""}return fi=r,fi}var ci,Qo;function hh(){if(Qo)return ci;Qo=1;var t=vh(),e=ch(),r=Da(),a=dh(),n=/[\\^$.*+?()[\]{}|]/g,i=/^\[object .+?Constructor\]$/,s=Function.prototype,o=Object.prototype,l=s.toString,u=o.hasOwnProperty,v=RegExp("^"+l.call(u).replace(n,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function f(c){if(!r(c)||e(c))return!1;var h=t(c)?v:i;return h.test(a(c))}return ci=f,ci}var di,Jo;function gh(){if(Jo)return di;Jo=1;function t(e,r){return e==null?void 0:e[r]}return di=t,di}var hi,jo;function Gs(){if(jo)return hi;jo=1;var t=hh(),e=gh();function r(a,n){var i=e(a,n);return t(i)?i:void 0}return hi=r,hi}var gi,eu;function kn(){if(eu)return gi;eu=1;var t=Gs(),e=t(Object,"create");return gi=e,gi}var pi,tu;function ph(){if(tu)return pi;tu=1;var t=kn();function e(){this.__data__=t?t(null):{},this.size=0}return pi=e,pi}var yi,ru;function yh(){if(ru)return yi;ru=1;function t(e){var r=this.has(e)&&delete this.__data__[e];return this.size-=r?1:0,r}return yi=t,yi}var mi,au;function mh(){if(au)return mi;au=1;var t=kn(),e="__lodash_hash_undefined__",r=Object.prototype,a=r.hasOwnProperty;function n(i){var s=this.__data__;if(t){var o=s[i];return o===e?void 0:o}return a.call(s,i)?s[i]:void 0}return mi=n,mi}var bi,nu;function bh(){if(nu)return bi;nu=1;var t=kn(),e=Object.prototype,r=e.hasOwnProperty;function a(n){var i=this.__data__;return t?i[n]!==void 0:r.call(i,n)}return bi=a,bi}var wi,iu;function wh(){if(iu)return wi;iu=1;var t=kn(),e="__lodash_hash_undefined__";function r(a,n){var i=this.__data__;return this.size+=this.has(a)?0:1,i[a]=t&&n===void 0?e:n,this}return wi=r,wi}var xi,su;function xh(){if(su)return xi;su=1;var t=ph(),e=yh(),r=mh(),a=bh(),n=wh();function i(s){var o=-1,l=s==null?0:s.length;for(this.clear();++o<l;){var u=s[o];this.set(u[0],u[1])}}return i.prototype.clear=t,i.prototype.delete=e,i.prototype.get=r,i.prototype.has=a,i.prototype.set=n,xi=i,xi}var Ei,ou;function Eh(){if(ou)return Ei;ou=1;function t(){this.__data__=[],this.size=0}return Ei=t,Ei}var Ci,uu;function pv(){if(uu)return Ci;uu=1;function t(e,r){return e===r||e!==e&&r!==r}return Ci=t,Ci}var Ti,lu;function Pn(){if(lu)return Ti;lu=1;var t=pv();function e(r,a){for(var n=r.length;n--;)if(t(r[n][0],a))return n;return-1}return Ti=e,Ti}var Si,vu;function Ch(){if(vu)return Si;vu=1;var t=Pn(),e=Array.prototype,r=e.splice;function a(n){var i=this.__data__,s=t(i,n);if(s<0)return!1;var o=i.length-1;return s==o?i.pop():r.call(i,s,1),--this.size,!0}return Si=a,Si}var Di,fu;function Th(){if(fu)return Di;fu=1;var t=Pn();function e(r){var a=this.__data__,n=t(a,r);return n<0?void 0:a[n][1]}return Di=e,Di}var ki,cu;function Sh(){if(cu)return ki;cu=1;var t=Pn();function e(r){return t(this.__data__,r)>-1}return ki=e,ki}var Pi,du;function Dh(){if(du)return Pi;du=1;var t=Pn();function e(r,a){var n=this.__data__,i=t(n,r);return i<0?(++this.size,n.push([r,a])):n[i][1]=a,this}return Pi=e,Pi}var Bi,hu;function kh(){if(hu)return Bi;hu=1;var t=Eh(),e=Ch(),r=Th(),a=Sh(),n=Dh();function i(s){var o=-1,l=s==null?0:s.length;for(this.clear();++o<l;){var u=s[o];this.set(u[0],u[1])}}return i.prototype.clear=t,i.prototype.delete=e,i.prototype.get=r,i.prototype.has=a,i.prototype.set=n,Bi=i,Bi}var Ai,gu;function Ph(){if(gu)return Ai;gu=1;var t=Gs(),e=En(),r=t(e,"Map");return Ai=r,Ai}var Ri,pu;function Bh(){if(pu)return Ri;pu=1;var t=xh(),e=kh(),r=Ph();function a(){this.size=0,this.__data__={hash:new t,map:new(r||e),string:new t}}return Ri=a,Ri}var Mi,yu;function Ah(){if(yu)return Mi;yu=1;function t(e){var r=typeof e;return r=="string"||r=="number"||r=="symbol"||r=="boolean"?e!=="__proto__":e===null}return Mi=t,Mi}var Li,mu;function Bn(){if(mu)return Li;mu=1;var t=Ah();function e(r,a){var n=r.__data__;return t(a)?n[typeof a=="string"?"string":"hash"]:n.map}return Li=e,Li}var Ii,bu;function Rh(){if(bu)return Ii;bu=1;var t=Bn();function e(r){var a=t(this,r).delete(r);return this.size-=a?1:0,a}return Ii=e,Ii}var Oi,wu;function Mh(){if(wu)return Oi;wu=1;var t=Bn();function e(r){return t(this,r).get(r)}return Oi=e,Oi}var Ni,xu;function Lh(){if(xu)return Ni;xu=1;var t=Bn();function e(r){return t(this,r).has(r)}return Ni=e,Ni}var Fi,Eu;function Ih(){if(Eu)return Fi;Eu=1;var t=Bn();function e(r,a){var n=t(this,r),i=n.size;return n.set(r,a),this.size+=n.size==i?0:1,this}return Fi=e,Fi}var zi,Cu;function Oh(){if(Cu)return zi;Cu=1;var t=Bh(),e=Rh(),r=Mh(),a=Lh(),n=Ih();function i(s){var o=-1,l=s==null?0:s.length;for(this.clear();++o<l;){var u=s[o];this.set(u[0],u[1])}}return i.prototype.clear=t,i.prototype.delete=e,i.prototype.get=r,i.prototype.has=a,i.prototype.set=n,zi=i,zi}var qi,Tu;function Nh(){if(Tu)return qi;Tu=1;var t=Oh(),e="Expected a function";function r(a,n){if(typeof a!="function"||n!=null&&typeof n!="function")throw new TypeError(e);var i=function(){var s=arguments,o=n?n.apply(this,s):s[0],l=i.cache;if(l.has(o))return l.get(o);var u=a.apply(this,s);return i.cache=l.set(o,u)||l,u};return i.cache=new(r.Cache||t),i}return r.Cache=t,qi=r,qi}var Vi,Su;function Fh(){if(Su)return Vi;Su=1;var t=Nh(),e=500;function r(a){var n=t(a,function(s){return i.size===e&&i.clear(),s}),i=n.cache;return n}return Vi=r,Vi}var _i,Du;function yv(){if(Du)return _i;Du=1;var t=Fh(),e=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,r=/\\(\\)?/g,a=t(function(n){var i=[];return n.charCodeAt(0)===46&&i.push(""),n.replace(e,function(s,o,l,u){i.push(l?u.replace(r,"$1"):o||s)}),i});return _i=a,_i}var Gi,ku;function mv(){if(ku)return Gi;ku=1;function t(e,r){for(var a=-1,n=e==null?0:e.length,i=Array(n);++a<n;)i[a]=r(e[a],a,e);return i}return Gi=t,Gi}var Hi,Pu;function zh(){if(Pu)return Hi;Pu=1;var t=Is(),e=mv(),r=Dn(),a=ka(),n=t?t.prototype:void 0,i=n?n.toString:void 0;function s(o){if(typeof o=="string")return o;if(r(o))return e(o,s)+"";if(a(o))return i?i.call(o):"";var l=o+"";return l=="0"&&1/o==-1/0?"-0":l}return Hi=s,Hi}var Ki,Bu;function bv(){if(Bu)return Ki;Bu=1;var t=zh();function e(r){return r==null?"":t(r)}return Ki=e,Ki}var $i,Au;function wv(){if(Au)return $i;Au=1;var t=Dn(),e=lh(),r=yv(),a=bv();function n(i,s){return t(i)?i:e(i,s)?[i]:r(a(i))}return $i=n,$i}var Wi,Ru;function Hs(){if(Ru)return Wi;Ru=1;var t=ka();function e(r){if(typeof r=="string"||t(r))return r;var a=r+"";return a=="0"&&1/r==-1/0?"-0":a}return Wi=e,Wi}var Ui,Mu;function qh(){if(Mu)return Ui;Mu=1;var t=wv(),e=Hs();function r(a,n){n=t(n,a);for(var i=0,s=n.length;a!=null&&i<s;)a=a[e(n[i++])];return i&&i==s?a:void 0}return Ui=r,Ui}var Yi,Lu;function Vh(){if(Lu)return Yi;Lu=1;var t=qh();function e(r,a,n){var i=r==null?void 0:t(r,a);return i===void 0?n:i}return Yi=e,Yi}var _h=Vh(),Gh=Sa(_h),Xi,Iu;function Hh(){if(Iu)return Xi;Iu=1;var t=Gs(),e=function(){try{var r=t(Object,"defineProperty");return r({},"",{}),r}catch{}}();return Xi=e,Xi}var Zi,Ou;function Kh(){if(Ou)return Zi;Ou=1;var t=Hh();function e(r,a,n){a=="__proto__"&&t?t(r,a,{configurable:!0,enumerable:!0,value:n,writable:!0}):r[a]=n}return Zi=e,Zi}var Qi,Nu;function $h(){if(Nu)return Qi;Nu=1;var t=Kh(),e=pv(),r=Object.prototype,a=r.hasOwnProperty;function n(i,s,o){var l=i[s];(!(a.call(i,s)&&e(l,o))||o===void 0&&!(s in i))&&t(i,s,o)}return Qi=n,Qi}var Ji,Fu;function Wh(){if(Fu)return Ji;Fu=1;var t=9007199254740991,e=/^(?:0|[1-9]\d*)$/;function r(a,n){var i=typeof a;return n=n??t,!!n&&(i=="number"||i!="symbol"&&e.test(a))&&a>-1&&a%1==0&&a<n}return Ji=r,Ji}var ji,zu;function Uh(){if(zu)return ji;zu=1;var t=$h(),e=wv(),r=Wh(),a=Da(),n=Hs();function i(s,o,l,u){if(!a(s))return s;o=e(o,s);for(var v=-1,f=o.length,c=f-1,h=s;h!=null&&++v<f;){var d=n(o[v]),y=l;if(d==="__proto__"||d==="constructor"||d==="prototype")return s;if(v!=c){var g=h[d];y=u?u(g,d,h):void 0,y===void 0&&(y=a(g)?g:r(o[v+1])?[]:{})}t(h,d,y),h=h[d]}return s}return ji=i,ji}var es,qu;function Yh(){if(qu)return es;qu=1;var t=Uh();function e(r,a,n){return r==null?r:t(r,a,n)}return es=e,es}var Xh=Yh(),Zh=Sa(Xh),ts,Vu;function Qh(){if(Vu)return ts;Vu=1;function t(e,r){var a=-1,n=e.length;for(r||(r=Array(n));++a<n;)r[a]=e[a];return r}return ts=t,ts}var rs,_u;function Jh(){if(_u)return rs;_u=1;var t=mv(),e=Qh(),r=Dn(),a=ka(),n=yv(),i=Hs(),s=bv();function o(l){return r(l)?t(l,i):a(l)?[l]:e(n(s(l)))}return rs=o,rs}var jh=Jh(),eg=Sa(jh),tg={data:function(e){var r={field:"data",bindingEvent:"data",allowBinding:!1,allowSetting:!1,allowGetting:!1,settingEvent:"data",settingTriggersEvent:!1,triggerFnName:"trigger",immutableKeys:{},updateStyle:!1,beforeGet:function(n){},beforeSet:function(n,i){},onSet:function(n){},canSet:function(n){return!0}};return e=ge({},r,e),function(n,i){var s=e,o=this,l=o.length!==void 0,u=l?o:[o],v=l?o[0]:o;if(fe(n)){var f=n.indexOf(".")!==-1,c=f&&eg(n);if(s.allowGetting&&i===void 0){var h;return v&&(s.beforeGet(v),c&&v._private[s.field][n]===void 0?h=Gh(v._private[s.field],c):h=v._private[s.field][n]),h}else if(s.allowSetting&&i!==void 0){var d=!s.immutableKeys[n];if(d){var y=Ll({},n,i);s.beforeSet(o,y);for(var g=0,p=u.length;g<p;g++){var m=u[g];s.canSet(m)&&(c&&v._private[s.field][n]===void 0?Zh(m._private[s.field],c,i):m._private[s.field][n]=i)}s.updateStyle&&o.updateStyle(),s.onSet(o),s.settingTriggersEvent&&o[s.triggerFnName](s.settingEvent)}}}else if(s.allowSetting&&ke(n)){var b=n,w,E,C=Object.keys(b);s.beforeSet(o,b);for(var x=0;x<C.length;x++){w=C[x],E=b[w];var S=!s.immutableKeys[w];if(S)for(var k=0;k<u.length;k++){var B=u[k];s.canSet(B)&&(B._private[s.field][w]=E)}}s.updateStyle&&o.updateStyle(),s.onSet(o),s.settingTriggersEvent&&o[s.triggerFnName](s.settingEvent)}else if(s.allowBinding&&_e(n)){var D=n;o.on(s.bindingEvent,D)}else if(s.allowGetting&&n===void 0){var A;return v&&(s.beforeGet(v),A=v._private[s.field]),A}return o}},removeData:function(e){var r={field:"data",event:"data",triggerFnName:"trigger",triggerEvent:!1,immutableKeys:{}};return e=ge({},r,e),function(n){var i=e,s=this,o=s.length!==void 0,l=o?s:[s];if(fe(n)){for(var u=n.split(/\s+/),v=u.length,f=0;f<v;f++){var c=u[f];if(!tr(c)){var h=!i.immutableKeys[c];if(h)for(var d=0,y=l.length;d<y;d++)l[d]._private[i.field][c]=void 0}}i.triggerEvent&&s[i.triggerFnName](i.event)}else if(n===void 0){for(var g=0,p=l.length;g<p;g++)for(var m=l[g]._private[i.field],b=Object.keys(m),w=0;w<b.length;w++){var E=b[w],C=!i.immutableKeys[E];C&&(m[E]=void 0)}i.triggerEvent&&s[i.triggerFnName](i.event)}return s}}},rg={eventAliasesOn:function(e){var r=e;r.addListener=r.listen=r.bind=r.on,r.unlisten=r.unbind=r.off=r.removeListener,r.trigger=r.emit,r.pon=r.promiseOn=function(a,n){var i=this,s=Array.prototype.slice.call(arguments,0);return new Wr(function(o,l){var u=function(h){i.off.apply(i,f),o(h)},v=s.concat([u]),f=v.concat([]);i.on.apply(i,v)})}}},Ae={};[uh,tg,rg].forEach(function(t){ge(Ae,t)});var ag={animate:Ae.animate(),animation:Ae.animation(),animated:Ae.animated(),clearQueue:Ae.clearQueue(),delay:Ae.delay(),delayAnimation:Ae.delayAnimation(),stop:Ae.stop()},ja={classes:function(e){var r=this;if(e===void 0){var a=[];return r[0]._private.classes.forEach(function(d){return a.push(d)}),a}else Le(e)||(e=(e||"").match(/\S+/g)||[]);for(var n=[],i=new $r(e),s=0;s<r.length;s++){for(var o=r[s],l=o._private,u=l.classes,v=!1,f=0;f<e.length;f++){var c=e[f],h=u.has(c);if(!h){v=!0;break}}v||(v=u.size!==e.length),v&&(l.classes=i,n.push(o))}return n.length>0&&this.spawn(n).updateStyle().emit("class"),r},addClass:function(e){return this.toggleClass(e,!0)},hasClass:function(e){var r=this[0];return r!=null&&r._private.classes.has(e)},toggleClass:function(e,r){Le(e)||(e=e.match(/\S+/g)||[]);for(var a=this,n=r===void 0,i=[],s=0,o=a.length;s<o;s++)for(var l=a[s],u=l._private.classes,v=!1,f=0;f<e.length;f++){var c=e[f],h=u.has(c),d=!1;r||n&&!h?(u.add(c),d=!0):(!r||n&&h)&&(u.delete(c),d=!0),!v&&d&&(i.push(l),v=!0)}return i.length>0&&this.spawn(i).updateStyle().emit("class"),a},removeClass:function(e){return this.toggleClass(e,!1)},flashClass:function(e,r){var a=this;if(r==null)r=250;else if(r===0)return a;return a.addClass(e),setTimeout(function(){a.removeClass(e)},r),a}};ja.className=ja.classNames=ja.classes;var De={metaChar:"[\\!\\\"\\#\\$\\%\\&\\'\\(\\)\\*\\+\\,\\.\\/\\:\\;\\<\\=\\>\\?\\@\\[\\]\\^\\`\\{\\|\\}\\~]",comparatorOp:"=|\\!=|>|>=|<|<=|\\$=|\\^=|\\*=",boolOp:"\\?|\\!|\\^",string:`"(?:\\\\"|[^"])*"|'(?:\\\\'|[^'])*'`,number:$e,meta:"degree|indegree|outdegree",separator:"\\s*,\\s*",descendant:"\\s+",child:"\\s+>\\s+",subject:"\\$",group:"node|edge|\\*",directedEdge:"\\s+->\\s+",undirectedEdge:"\\s+<->\\s+"};De.variable="(?:[\\w-.]|(?:\\\\"+De.metaChar+"))+";De.className="(?:[\\w-]|(?:\\\\"+De.metaChar+"))+";De.value=De.string+"|"+De.number;De.id=De.variable;(function(){var t,e,r;for(t=De.comparatorOp.split("|"),r=0;r<t.length;r++)e=t[r],De.comparatorOp+="|@"+e;for(t=De.comparatorOp.split("|"),r=0;r<t.length;r++)e=t[r],!(e.indexOf("!")>=0)&&e!=="="&&(De.comparatorOp+="|\\!"+e)})();var Me=function(){return{checks:[]}},oe={GROUP:0,COLLECTION:1,FILTER:2,DATA_COMPARE:3,DATA_EXIST:4,DATA_BOOL:5,META_COMPARE:6,STATE:7,ID:8,CLASS:9,UNDIRECTED_EDGE:10,DIRECTED_EDGE:11,NODE_SOURCE:12,NODE_TARGET:13,NODE_NEIGHBOR:14,CHILD:15,DESCENDANT:16,PARENT:17,ANCESTOR:18,COMPOUND_SPLIT:19,TRUE:20},bs=[{selector:":selected",matches:function(e){return e.selected()}},{selector:":unselected",matches:function(e){return!e.selected()}},{selector:":selectable",matches:function(e){return e.selectable()}},{selector:":unselectable",matches:function(e){return!e.selectable()}},{selector:":locked",matches:function(e){return e.locked()}},{selector:":unlocked",matches:function(e){return!e.locked()}},{selector:":visible",matches:function(e){return e.visible()}},{selector:":hidden",matches:function(e){return!e.visible()}},{selector:":transparent",matches:function(e){return e.transparent()}},{selector:":grabbed",matches:function(e){return e.grabbed()}},{selector:":free",matches:function(e){return!e.grabbed()}},{selector:":removed",matches:function(e){return e.removed()}},{selector:":inside",matches:function(e){return!e.removed()}},{selector:":grabbable",matches:function(e){return e.grabbable()}},{selector:":ungrabbable",matches:function(e){return!e.grabbable()}},{selector:":animated",matches:function(e){return e.animated()}},{selector:":unanimated",matches:function(e){return!e.animated()}},{selector:":parent",matches:function(e){return e.isParent()}},{selector:":childless",matches:function(e){return e.isChildless()}},{selector:":child",matches:function(e){return e.isChild()}},{selector:":orphan",matches:function(e){return e.isOrphan()}},{selector:":nonorphan",matches:function(e){return e.isChild()}},{selector:":compound",matches:function(e){return e.isNode()?e.isParent():e.source().isParent()||e.target().isParent()}},{selector:":loop",matches:function(e){return e.isLoop()}},{selector:":simple",matches:function(e){return e.isSimple()}},{selector:":active",matches:function(e){return e.active()}},{selector:":inactive",matches:function(e){return!e.active()}},{selector:":backgrounding",matches:function(e){return e.backgrounding()}},{selector:":nonbackgrounding",matches:function(e){return!e.backgrounding()}}].sort(function(t,e){return sc(t.selector,e.selector)}),ng=function(){for(var t={},e,r=0;r<bs.length;r++)e=bs[r],t[e.selector]=e.matches;return t}(),ig=function(e,r){return ng[e](r)},sg="("+bs.map(function(t){return t.selector}).join("|")+")",Tr=function(e){return e.replace(new RegExp("\\\\("+De.metaChar+")","g"),function(r,a){return a})},Zt=function(e,r,a){e[e.length-1]=a},ws=[{name:"group",query:!0,regex:"("+De.group+")",populate:function(e,r,a){var n=je(a,1),i=n[0];r.checks.push({type:oe.GROUP,value:i==="*"?i:i+"s"})}},{name:"state",query:!0,regex:sg,populate:function(e,r,a){var n=je(a,1),i=n[0];r.checks.push({type:oe.STATE,value:i})}},{name:"id",query:!0,regex:"\\#("+De.id+")",populate:function(e,r,a){var n=je(a,1),i=n[0];r.checks.push({type:oe.ID,value:Tr(i)})}},{name:"className",query:!0,regex:"\\.("+De.className+")",populate:function(e,r,a){var n=je(a,1),i=n[0];r.checks.push({type:oe.CLASS,value:Tr(i)})}},{name:"dataExists",query:!0,regex:"\\[\\s*("+De.variable+")\\s*\\]",populate:function(e,r,a){var n=je(a,1),i=n[0];r.checks.push({type:oe.DATA_EXIST,field:Tr(i)})}},{name:"dataCompare",query:!0,regex:"\\[\\s*("+De.variable+")\\s*("+De.comparatorOp+")\\s*("+De.value+")\\s*\\]",populate:function(e,r,a){var n=je(a,3),i=n[0],s=n[1],o=n[2],l=new RegExp("^"+De.string+"$").exec(o)!=null;l?o=o.substring(1,o.length-1):o=parseFloat(o),r.checks.push({type:oe.DATA_COMPARE,field:Tr(i),operator:s,value:o})}},{name:"dataBool",query:!0,regex:"\\[\\s*("+De.boolOp+")\\s*("+De.variable+")\\s*\\]",populate:function(e,r,a){var n=je(a,2),i=n[0],s=n[1];r.checks.push({type:oe.DATA_BOOL,field:Tr(s),operator:i})}},{name:"metaCompare",query:!0,regex:"\\[\\[\\s*("+De.meta+")\\s*("+De.comparatorOp+")\\s*("+De.number+")\\s*\\]\\]",populate:function(e,r,a){var n=je(a,3),i=n[0],s=n[1],o=n[2];r.checks.push({type:oe.META_COMPARE,field:Tr(i),operator:s,value:parseFloat(o)})}},{name:"nextQuery",separator:!0,regex:De.separator,populate:function(e,r){var a=e.currentSubject,n=e.edgeCount,i=e.compoundCount,s=e[e.length-1];a!=null&&(s.subject=a,e.currentSubject=null),s.edgeCount=n,s.compoundCount=i,e.edgeCount=0,e.compoundCount=0;var o=e[e.length++]=Me();return o}},{name:"directedEdge",separator:!0,regex:De.directedEdge,populate:function(e,r){if(e.currentSubject==null){var a=Me(),n=r,i=Me();return a.checks.push({type:oe.DIRECTED_EDGE,source:n,target:i}),Zt(e,r,a),e.edgeCount++,i}else{var s=Me(),o=r,l=Me();return s.checks.push({type:oe.NODE_SOURCE,source:o,target:l}),Zt(e,r,s),e.edgeCount++,l}}},{name:"undirectedEdge",separator:!0,regex:De.undirectedEdge,populate:function(e,r){if(e.currentSubject==null){var a=Me(),n=r,i=Me();return a.checks.push({type:oe.UNDIRECTED_EDGE,nodes:[n,i]}),Zt(e,r,a),e.edgeCount++,i}else{var s=Me(),o=r,l=Me();return s.checks.push({type:oe.NODE_NEIGHBOR,node:o,neighbor:l}),Zt(e,r,s),l}}},{name:"child",separator:!0,regex:De.child,populate:function(e,r){if(e.currentSubject==null){var a=Me(),n=Me(),i=e[e.length-1];return a.checks.push({type:oe.CHILD,parent:i,child:n}),Zt(e,r,a),e.compoundCount++,n}else if(e.currentSubject===r){var s=Me(),o=e[e.length-1],l=Me(),u=Me(),v=Me(),f=Me();return s.checks.push({type:oe.COMPOUND_SPLIT,left:o,right:l,subject:u}),u.checks=r.checks,r.checks=[{type:oe.TRUE}],f.checks.push({type:oe.TRUE}),l.checks.push({type:oe.PARENT,parent:f,child:v}),Zt(e,o,s),e.currentSubject=u,e.compoundCount++,v}else{var c=Me(),h=Me(),d=[{type:oe.PARENT,parent:c,child:h}];return c.checks=r.checks,r.checks=d,e.compoundCount++,h}}},{name:"descendant",separator:!0,regex:De.descendant,populate:function(e,r){if(e.currentSubject==null){var a=Me(),n=Me(),i=e[e.length-1];return a.checks.push({type:oe.DESCENDANT,ancestor:i,descendant:n}),Zt(e,r,a),e.compoundCount++,n}else if(e.currentSubject===r){var s=Me(),o=e[e.length-1],l=Me(),u=Me(),v=Me(),f=Me();return s.checks.push({type:oe.COMPOUND_SPLIT,left:o,right:l,subject:u}),u.checks=r.checks,r.checks=[{type:oe.TRUE}],f.checks.push({type:oe.TRUE}),l.checks.push({type:oe.ANCESTOR,ancestor:f,descendant:v}),Zt(e,o,s),e.currentSubject=u,e.compoundCount++,v}else{var c=Me(),h=Me(),d=[{type:oe.ANCESTOR,ancestor:c,descendant:h}];return c.checks=r.checks,r.checks=d,e.compoundCount++,h}}},{name:"subject",modifier:!0,regex:De.subject,populate:function(e,r){if(e.currentSubject!=null&&e.currentSubject!==r)return Re("Redefinition of subject in selector `"+e.toString()+"`"),!1;e.currentSubject=r;var a=e[e.length-1],n=a.checks[0],i=n==null?null:n.type;i===oe.DIRECTED_EDGE?n.type=oe.NODE_TARGET:i===oe.UNDIRECTED_EDGE&&(n.type=oe.NODE_NEIGHBOR,n.node=n.nodes[1],n.neighbor=n.nodes[0],n.nodes=null)}}];ws.forEach(function(t){return t.regexObj=new RegExp("^"+t.regex)});var og=function(e){for(var r,a,n,i=0;i<ws.length;i++){var s=ws[i],o=s.name,l=e.match(s.regexObj);if(l!=null){a=l,r=s,n=o;var u=l[0];e=e.substring(u.length);break}}return{expr:r,match:a,name:n,remaining:e}},ug=function(e){var r=e.match(/^\s+/);if(r){var a=r[0];e=e.substring(a.length)}return e},lg=function(e){var r=this,a=r.inputText=e,n=r[0]=Me();for(r.length=1,a=ug(a);;){var i=og(a);if(i.expr==null)return Re("The selector `"+e+"`is invalid"),!1;var s=i.match.slice(1),o=i.expr.populate(r,n,s);if(o===!1)return!1;if(o!=null&&(n=o),a=i.remaining,a.match(/^\s*$/))break}var l=r[r.length-1];r.currentSubject!=null&&(l.subject=r.currentSubject),l.edgeCount=r.edgeCount,l.compoundCount=r.compoundCount;for(var u=0;u<r.length;u++){var v=r[u];if(v.compoundCount>0&&v.edgeCount>0)return Re("The selector `"+e+"` is invalid because it uses both a compound selector and an edge selector"),!1;if(v.edgeCount>1)return Re("The selector `"+e+"` is invalid because it uses multiple edge selectors"),!1;v.edgeCount===1&&Re("The selector `"+e+"` is deprecated.  Edge selectors do not take effect on changes to source and target nodes after an edge is added, for performance reasons.  Use a class or data selector on edges instead, updating the class or data of an edge when your app detects a change in source or target nodes.")}return!0},vg=function(){if(this.toStringCache!=null)return this.toStringCache;for(var e=function(v){return v??""},r=function(v){return fe(v)?'"'+v+'"':e(v)},a=function(v){return" "+v+" "},n=function(v,f){var c=v.type,h=v.value;switch(c){case oe.GROUP:{var d=e(h);return d.substring(0,d.length-1)}case oe.DATA_COMPARE:{var y=v.field,g=v.operator;return"["+y+a(e(g))+r(h)+"]"}case oe.DATA_BOOL:{var p=v.operator,m=v.field;return"["+e(p)+m+"]"}case oe.DATA_EXIST:{var b=v.field;return"["+b+"]"}case oe.META_COMPARE:{var w=v.operator,E=v.field;return"[["+E+a(e(w))+r(h)+"]]"}case oe.STATE:return h;case oe.ID:return"#"+h;case oe.CLASS:return"."+h;case oe.PARENT:case oe.CHILD:return i(v.parent,f)+a(">")+i(v.child,f);case oe.ANCESTOR:case oe.DESCENDANT:return i(v.ancestor,f)+" "+i(v.descendant,f);case oe.COMPOUND_SPLIT:{var C=i(v.left,f),x=i(v.subject,f),S=i(v.right,f);return C+(C.length>0?" ":"")+x+S}case oe.TRUE:return""}},i=function(v,f){return v.checks.reduce(function(c,h,d){return c+(f===v&&d===0?"$":"")+n(h,f)},"")},s="",o=0;o<this.length;o++){var l=this[o];s+=i(l,l.subject),this.length>1&&o<this.length-1&&(s+=", ")}return this.toStringCache=s,s},fg={parse:lg,toString:vg},xv=function(e,r,a){var n,i=fe(e),s=ae(e),o=fe(a),l,u,v=!1,f=!1,c=!1;switch(r.indexOf("!")>=0&&(r=r.replace("!",""),f=!0),r.indexOf("@")>=0&&(r=r.replace("@",""),v=!0),(i||o||v)&&(l=!i&&!s?"":""+e,u=""+a),v&&(e=l=l.toLowerCase(),a=u=u.toLowerCase()),r){case"*=":n=l.indexOf(u)>=0;break;case"$=":n=l.indexOf(u,l.length-u.length)>=0;break;case"^=":n=l.indexOf(u)===0;break;case"=":n=e===a;break;case">":c=!0,n=e>a;break;case">=":c=!0,n=e>=a;break;case"<":c=!0,n=e<a;break;case"<=":c=!0,n=e<=a;break;default:n=!1;break}return f&&(e!=null||!c)&&(n=!n),n},cg=function(e,r){switch(r){case"?":return!!e;case"!":return!e;case"^":return e===void 0}},dg=function(e){return e!==void 0},Ks=function(e,r){return e.data(r)},hg=function(e,r){return e[r]()},Ge=[],ze=function(e,r){return e.checks.every(function(a){return Ge[a.type](a,r)})};Ge[oe.GROUP]=function(t,e){var r=t.value;return r==="*"||r===e.group()};Ge[oe.STATE]=function(t,e){var r=t.value;return ig(r,e)};Ge[oe.ID]=function(t,e){var r=t.value;return e.id()===r};Ge[oe.CLASS]=function(t,e){var r=t.value;return e.hasClass(r)};Ge[oe.META_COMPARE]=function(t,e){var r=t.field,a=t.operator,n=t.value;return xv(hg(e,r),a,n)};Ge[oe.DATA_COMPARE]=function(t,e){var r=t.field,a=t.operator,n=t.value;return xv(Ks(e,r),a,n)};Ge[oe.DATA_BOOL]=function(t,e){var r=t.field,a=t.operator;return cg(Ks(e,r),a)};Ge[oe.DATA_EXIST]=function(t,e){var r=t.field;return t.operator,dg(Ks(e,r))};Ge[oe.UNDIRECTED_EDGE]=function(t,e){var r=t.nodes[0],a=t.nodes[1],n=e.source(),i=e.target();return ze(r,n)&&ze(a,i)||ze(a,n)&&ze(r,i)};Ge[oe.NODE_NEIGHBOR]=function(t,e){return ze(t.node,e)&&e.neighborhood().some(function(r){return r.isNode()&&ze(t.neighbor,r)})};Ge[oe.DIRECTED_EDGE]=function(t,e){return ze(t.source,e.source())&&ze(t.target,e.target())};Ge[oe.NODE_SOURCE]=function(t,e){return ze(t.source,e)&&e.outgoers().some(function(r){return r.isNode()&&ze(t.target,r)})};Ge[oe.NODE_TARGET]=function(t,e){return ze(t.target,e)&&e.incomers().some(function(r){return r.isNode()&&ze(t.source,r)})};Ge[oe.CHILD]=function(t,e){return ze(t.child,e)&&ze(t.parent,e.parent())};Ge[oe.PARENT]=function(t,e){return ze(t.parent,e)&&e.children().some(function(r){return ze(t.child,r)})};Ge[oe.DESCENDANT]=function(t,e){return ze(t.descendant,e)&&e.ancestors().some(function(r){return ze(t.ancestor,r)})};Ge[oe.ANCESTOR]=function(t,e){return ze(t.ancestor,e)&&e.descendants().some(function(r){return ze(t.descendant,r)})};Ge[oe.COMPOUND_SPLIT]=function(t,e){return ze(t.subject,e)&&ze(t.left,e)&&ze(t.right,e)};Ge[oe.TRUE]=function(){return!0};Ge[oe.COLLECTION]=function(t,e){var r=t.value;return r.has(e)};Ge[oe.FILTER]=function(t,e){var r=t.value;return r(e)};var gg=function(e){var r=this;if(r.length===1&&r[0].checks.length===1&&r[0].checks[0].type===oe.ID)return e.getElementById(r[0].checks[0].value).collection();var a=function(i){for(var s=0;s<r.length;s++){var o=r[s];if(ze(o,i))return!0}return!1};return r.text()==null&&(a=function(){return!0}),e.filter(a)},pg=function(e){for(var r=this,a=0;a<r.length;a++){var n=r[a];if(ze(n,e))return!0}return!1},yg={matches:pg,filter:gg},nr=function(e){this.inputText=e,this.currentSubject=null,this.compoundCount=0,this.edgeCount=0,this.length=0,e==null||fe(e)&&e.match(/^\s*$/)||(bt(e)?this.addQuery({checks:[{type:oe.COLLECTION,value:e.collection()}]}):_e(e)?this.addQuery({checks:[{type:oe.FILTER,value:e}]}):fe(e)?this.parse(e)||(this.invalid=!0):Ve("A selector must be created from a string; found "))},ir=nr.prototype;[fg,yg].forEach(function(t){return ge(ir,t)});ir.text=function(){return this.inputText};ir.size=function(){return this.length};ir.eq=function(t){return this[t]};ir.sameText=function(t){return!this.invalid&&!t.invalid&&this.text()===t.text()};ir.addQuery=function(t){this[this.length++]=t};ir.selector=ir.toString;var jt={allAre:function(e){var r=new nr(e);return this.every(function(a){return r.matches(a)})},is:function(e){var r=new nr(e);return this.some(function(a){return r.matches(a)})},some:function(e,r){for(var a=0;a<this.length;a++){var n=r?e.apply(r,[this[a],a,this]):e(this[a],a,this);if(n)return!0}return!1},every:function(e,r){for(var a=0;a<this.length;a++){var n=r?e.apply(r,[this[a],a,this]):e(this[a],a,this);if(!n)return!1}return!0},same:function(e){if(this===e)return!0;e=this.cy().collection(e);var r=this.length,a=e.length;return r!==a?!1:r===1?this[0]===e[0]:this.every(function(n){return e.hasElementWithId(n.id())})},anySame:function(e){return e=this.cy().collection(e),this.some(function(r){return e.hasElementWithId(r.id())})},allAreNeighbors:function(e){e=this.cy().collection(e);var r=this.neighborhood();return e.every(function(a){return r.hasElementWithId(a.id())})},contains:function(e){e=this.cy().collection(e);var r=this;return e.every(function(a){return r.hasElementWithId(a.id())})}};jt.allAreNeighbours=jt.allAreNeighbors;jt.has=jt.contains;jt.equal=jt.equals=jt.same;var Tt=function(e,r){return function(n,i,s,o){var l=n,u=this,v;if(l==null?v="":bt(l)&&l.length===1&&(v=l.id()),u.length===1&&v){var f=u[0]._private,c=f.traversalCache=f.traversalCache||{},h=c[r]=c[r]||[],d=rr(v),y=h[d];return y||(h[d]=e.call(u,n,i,s,o))}else return e.call(u,n,i,s,o)}},Hr={parent:function(e){var r=[];if(this.length===1){var a=this[0]._private.parent;if(a)return a}for(var n=0;n<this.length;n++){var i=this[n],s=i._private.parent;s&&r.push(s)}return this.spawn(r,!0).filter(e)},parents:function(e){for(var r=[],a=this.parent();a.nonempty();){for(var n=0;n<a.length;n++){var i=a[n];r.push(i)}a=a.parent()}return this.spawn(r,!0).filter(e)},commonAncestors:function(e){for(var r,a=0;a<this.length;a++){var n=this[a],i=n.parents();r=r||i,r=r.intersect(i)}return r.filter(e)},orphans:function(e){return this.stdFilter(function(r){return r.isOrphan()}).filter(e)},nonorphans:function(e){return this.stdFilter(function(r){return r.isChild()}).filter(e)},children:Tt(function(t){for(var e=[],r=0;r<this.length;r++)for(var a=this[r],n=a._private.children,i=0;i<n.length;i++)e.push(n[i]);return this.spawn(e,!0).filter(t)},"children"),siblings:function(e){return this.parent().children().not(this).filter(e)},isParent:function(){var e=this[0];if(e)return e.isNode()&&e._private.children.length!==0},isChildless:function(){var e=this[0];if(e)return e.isNode()&&e._private.children.length===0},isChild:function(){var e=this[0];if(e)return e.isNode()&&e._private.parent!=null},isOrphan:function(){var e=this[0];if(e)return e.isNode()&&e._private.parent==null},descendants:function(e){var r=[];function a(n){for(var i=0;i<n.length;i++){var s=n[i];r.push(s),s.children().nonempty()&&a(s.children())}}return a(this.children()),this.spawn(r,!0).filter(e)}};function $s(t,e,r,a){for(var n=[],i=new $r,s=t.cy(),o=s.hasCompoundNodes(),l=0;l<t.length;l++){var u=t[l];r?n.push(u):o&&a(n,i,u)}for(;n.length>0;){var v=n.shift();e(v),i.add(v.id()),o&&a(n,i,v)}return t}function Ev(t,e,r){if(r.isParent())for(var a=r._private.children,n=0;n<a.length;n++){var i=a[n];e.has(i.id())||t.push(i)}}Hr.forEachDown=function(t){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;return $s(this,t,e,Ev)};function Cv(t,e,r){if(r.isChild()){var a=r._private.parent;e.has(a.id())||t.push(a)}}Hr.forEachUp=function(t){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;return $s(this,t,e,Cv)};function mg(t,e,r){Cv(t,e,r),Ev(t,e,r)}Hr.forEachUpAndDown=function(t){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;return $s(this,t,e,mg)};Hr.ancestors=Hr.parents;var ma,Tv;ma=Tv={data:Ae.data({field:"data",bindingEvent:"data",allowBinding:!0,allowSetting:!0,settingEvent:"data",settingTriggersEvent:!0,triggerFnName:"trigger",allowGetting:!0,immutableKeys:{id:!0,source:!0,target:!0,parent:!0},updateStyle:!0}),removeData:Ae.removeData({field:"data",event:"data",triggerFnName:"trigger",triggerEvent:!0,immutableKeys:{id:!0,source:!0,target:!0,parent:!0},updateStyle:!0}),scratch:Ae.data({field:"scratch",bindingEvent:"scratch",allowBinding:!0,allowSetting:!0,settingEvent:"scratch",settingTriggersEvent:!0,triggerFnName:"trigger",allowGetting:!0,updateStyle:!0}),removeScratch:Ae.removeData({field:"scratch",event:"scratch",triggerFnName:"trigger",triggerEvent:!0,updateStyle:!0}),rscratch:Ae.data({field:"rscratch",allowBinding:!1,allowSetting:!0,settingTriggersEvent:!1,allowGetting:!0}),removeRscratch:Ae.removeData({field:"rscratch",triggerEvent:!1}),id:function(){var e=this[0];if(e)return e._private.data.id}};ma.attr=ma.data;ma.removeAttr=ma.removeData;var bg=Tv,An={};function as(t){return function(e){var r=this;if(e===void 0&&(e=!0),r.length!==0)if(r.isNode()&&!r.removed()){for(var a=0,n=r[0],i=n._private.edges,s=0;s<i.length;s++){var o=i[s];!e&&o.isLoop()||(a+=t(n,o))}return a}else return}}ge(An,{degree:as(function(t,e){return e.source().same(e.target())?2:1}),indegree:as(function(t,e){return e.target().same(t)?1:0}),outdegree:as(function(t,e){return e.source().same(t)?1:0})});function Sr(t,e){return function(r){for(var a,n=this.nodes(),i=0;i<n.length;i++){var s=n[i],o=s[t](r);o!==void 0&&(a===void 0||e(o,a))&&(a=o)}return a}}ge(An,{minDegree:Sr("degree",function(t,e){return t<e}),maxDegree:Sr("degree",function(t,e){return t>e}),minIndegree:Sr("indegree",function(t,e){return t<e}),maxIndegree:Sr("indegree",function(t,e){return t>e}),minOutdegree:Sr("outdegree",function(t,e){return t<e}),maxOutdegree:Sr("outdegree",function(t,e){return t>e})});ge(An,{totalDegree:function(e){for(var r=0,a=this.nodes(),n=0;n<a.length;n++)r+=a[n].degree(e);return r}});var Bt,Sv,Dv=function(e,r,a){for(var n=0;n<e.length;n++){var i=e[n];if(!i.locked()){var s=i._private.position,o={x:r.x!=null?r.x-s.x:0,y:r.y!=null?r.y-s.y:0};i.isParent()&&!(o.x===0&&o.y===0)&&i.children().shift(o,a),i.dirtyBoundingBoxCache()}}},Gu={field:"position",bindingEvent:"position",allowBinding:!0,allowSetting:!0,settingEvent:"position",settingTriggersEvent:!0,triggerFnName:"emitAndNotify",allowGetting:!0,validKeys:["x","y"],beforeGet:function(e){e.updateCompoundBounds()},beforeSet:function(e,r){Dv(e,r,!1)},onSet:function(e){e.dirtyCompoundBoundsCache()},canSet:function(e){return!e.locked()}};Bt=Sv={position:Ae.data(Gu),silentPosition:Ae.data(ge({},Gu,{allowBinding:!1,allowSetting:!0,settingTriggersEvent:!1,allowGetting:!1,beforeSet:function(e,r){Dv(e,r,!0)},onSet:function(e){e.dirtyCompoundBoundsCache()}})),positions:function(e,r){if(ke(e))r?this.silentPosition(e):this.position(e);else if(_e(e)){var a=e,n=this.cy();n.startBatch();for(var i=0;i<this.length;i++){var s=this[i],o=void 0;(o=a(s,i))&&(r?s.silentPosition(o):s.position(o))}n.endBatch()}return this},silentPositions:function(e){return this.positions(e,!0)},shift:function(e,r,a){var n;if(ke(e)?(n={x:ae(e.x)?e.x:0,y:ae(e.y)?e.y:0},a=r):fe(e)&&ae(r)&&(n={x:0,y:0},n[e]=r),n!=null){var i=this.cy();i.startBatch();for(var s=0;s<this.length;s++){var o=this[s];if(!(i.hasCompoundNodes()&&o.isChild()&&o.ancestors().anySame(this))){var l=o.position(),u={x:l.x+n.x,y:l.y+n.y};a?o.silentPosition(u):o.position(u)}}i.endBatch()}return this},silentShift:function(e,r){return ke(e)?this.shift(e,!0):fe(e)&&ae(r)&&this.shift(e,r,!0),this},renderedPosition:function(e,r){var a=this[0],n=this.cy(),i=n.zoom(),s=n.pan(),o=ke(e)?e:void 0,l=o!==void 0||r!==void 0&&fe(e);if(a&&a.isNode())if(l)for(var u=0;u<this.length;u++){var v=this[u];r!==void 0?v.position(e,(r-s[e])/i):o!==void 0&&v.position(jl(o,i,s))}else{var f=a.position();return o=Tn(f,i,s),e===void 0?o:o[e]}else if(!l)return;return this},relativePosition:function(e,r){var a=this[0],n=this.cy(),i=ke(e)?e:void 0,s=i!==void 0||r!==void 0&&fe(e),o=n.hasCompoundNodes();if(a&&a.isNode())if(s)for(var l=0;l<this.length;l++){var u=this[l],v=o?u.parent():null,f=v&&v.length>0,c=f;f&&(v=v[0]);var h=c?v.position():{x:0,y:0};r!==void 0?u.position(e,r+h[e]):i!==void 0&&u.position({x:i.x+h.x,y:i.y+h.y})}else{var d=a.position(),y=o?a.parent():null,g=y&&y.length>0,p=g;g&&(y=y[0]);var m=p?y.position():{x:0,y:0};return i={x:d.x-m.x,y:d.y-m.y},e===void 0?i:i[e]}else if(!s)return;return this}};Bt.modelPosition=Bt.point=Bt.position;Bt.modelPositions=Bt.points=Bt.positions;Bt.renderedPoint=Bt.renderedPosition;Bt.relativePoint=Bt.relativePosition;var wg=Sv,qr,lr;qr=lr={};lr.renderedBoundingBox=function(t){var e=this.boundingBox(t),r=this.cy(),a=r.zoom(),n=r.pan(),i=e.x1*a+n.x,s=e.x2*a+n.x,o=e.y1*a+n.y,l=e.y2*a+n.y;return{x1:i,x2:s,y1:o,y2:l,w:s-i,h:l-o}};lr.dirtyCompoundBoundsCache=function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,e=this.cy();return!e.styleEnabled()||!e.hasCompoundNodes()?this:(this.forEachUp(function(r){if(r.isParent()){var a=r._private;a.compoundBoundsClean=!1,a.bbCache=null,t||r.emitAndNotify("bounds")}}),this)};lr.updateCompoundBounds=function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,e=this.cy();if(!e.styleEnabled()||!e.hasCompoundNodes())return this;if(!t&&e.batching())return this;function r(s){if(!s.isParent())return;var o=s._private,l=s.children(),u=s.pstyle("compound-sizing-wrt-labels").value==="include",v={width:{val:s.pstyle("min-width").pfValue,left:s.pstyle("min-width-bias-left"),right:s.pstyle("min-width-bias-right")},height:{val:s.pstyle("min-height").pfValue,top:s.pstyle("min-height-bias-top"),bottom:s.pstyle("min-height-bias-bottom")}},f=l.boundingBox({includeLabels:u,includeOverlays:!1,useCache:!1}),c=o.position;(f.w===0||f.h===0)&&(f={w:s.pstyle("width").pfValue,h:s.pstyle("height").pfValue},f.x1=c.x-f.w/2,f.x2=c.x+f.w/2,f.y1=c.y-f.h/2,f.y2=c.y+f.h/2);function h(k,B,D){var A=0,P=0,R=B+D;return k>0&&R>0&&(A=B/R*k,P=D/R*k),{biasDiff:A,biasComplementDiff:P}}function d(k,B,D,A){if(D.units==="%")switch(A){case"width":return k>0?D.pfValue*k:0;case"height":return B>0?D.pfValue*B:0;case"average":return k>0&&B>0?D.pfValue*(k+B)/2:0;case"min":return k>0&&B>0?k>B?D.pfValue*B:D.pfValue*k:0;case"max":return k>0&&B>0?k>B?D.pfValue*k:D.pfValue*B:0;default:return 0}else return D.units==="px"?D.pfValue:0}var y=v.width.left.value;v.width.left.units==="px"&&v.width.val>0&&(y=y*100/v.width.val);var g=v.width.right.value;v.width.right.units==="px"&&v.width.val>0&&(g=g*100/v.width.val);var p=v.height.top.value;v.height.top.units==="px"&&v.height.val>0&&(p=p*100/v.height.val);var m=v.height.bottom.value;v.height.bottom.units==="px"&&v.height.val>0&&(m=m*100/v.height.val);var b=h(v.width.val-f.w,y,g),w=b.biasDiff,E=b.biasComplementDiff,C=h(v.height.val-f.h,p,m),x=C.biasDiff,S=C.biasComplementDiff;o.autoPadding=d(f.w,f.h,s.pstyle("padding"),s.pstyle("padding-relative-to").value),o.autoWidth=Math.max(f.w,v.width.val),c.x=(-w+f.x1+f.x2+E)/2,o.autoHeight=Math.max(f.h,v.height.val),c.y=(-x+f.y1+f.y2+S)/2}for(var a=0;a<this.length;a++){var n=this[a],i=n._private;(!i.compoundBoundsClean||t)&&(r(n),e.batching()||(i.compoundBoundsClean=!0))}return this};var Ct=function(e){return e===1/0||e===-1/0?0:e},kt=function(e,r,a,n,i){n-r===0||i-a===0||r==null||a==null||n==null||i==null||(e.x1=r<e.x1?r:e.x1,e.x2=n>e.x2?n:e.x2,e.y1=a<e.y1?a:e.y1,e.y2=i>e.y2?i:e.y2,e.w=e.x2-e.x1,e.h=e.y2-e.y1)},dr=function(e,r){return r==null?e:kt(e,r.x1,r.y1,r.x2,r.y2)},ta=function(e,r,a){return Et(e,r,a)},Ha=function(e,r,a){if(!r.cy().headless()){var n=r._private,i=n.rstyle,s=i.arrowWidth/2,o=r.pstyle(a+"-arrow-shape").value,l,u;if(o!=="none"){a==="source"?(l=i.srcX,u=i.srcY):a==="target"?(l=i.tgtX,u=i.tgtY):(l=i.midX,u=i.midY);var v=n.arrowBounds=n.arrowBounds||{},f=v[a]=v[a]||{};f.x1=l-s,f.y1=u-s,f.x2=l+s,f.y2=u+s,f.w=f.x2-f.x1,f.h=f.y2-f.y1,Qa(f,1),kt(e,f.x1,f.y1,f.x2,f.y2)}}},ns=function(e,r,a){if(!r.cy().headless()){var n;a?n=a+"-":n="";var i=r._private,s=i.rstyle,o=r.pstyle(n+"label").strValue;if(o){var l=r.pstyle("text-halign"),u=r.pstyle("text-valign"),v=ta(s,"labelWidth",a),f=ta(s,"labelHeight",a),c=ta(s,"labelX",a),h=ta(s,"labelY",a),d=r.pstyle(n+"text-margin-x").pfValue,y=r.pstyle(n+"text-margin-y").pfValue,g=r.isEdge(),p=r.pstyle(n+"text-rotation"),m=r.pstyle("text-outline-width").pfValue,b=r.pstyle("text-border-width").pfValue,w=b/2,E=r.pstyle("text-background-padding").pfValue,C=2,x=f,S=v,k=S/2,B=x/2,D,A,P,R;if(g)D=c-k,A=c+k,P=h-B,R=h+B;else{switch(l.value){case"left":D=c-S,A=c;break;case"center":D=c-k,A=c+k;break;case"right":D=c,A=c+S;break}switch(u.value){case"top":P=h-x,R=h;break;case"center":P=h-B,R=h+B;break;case"bottom":P=h,R=h+x;break}}var L=d-Math.max(m,w)-E-C,I=d+Math.max(m,w)+E+C,M=y-Math.max(m,w)-E-C,O=y+Math.max(m,w)+E+C;D+=L,A+=I,P+=M,R+=O;var _=a||"main",H=i.labelBounds,F=H[_]=H[_]||{};F.x1=D,F.y1=P,F.x2=A,F.y2=R,F.w=A-D,F.h=R-P,F.leftPad=L,F.rightPad=I,F.topPad=M,F.botPad=O;var G=g&&p.strValue==="autorotate",U=p.pfValue!=null&&p.pfValue!==0;if(G||U){var X=G?ta(i.rstyle,"labelAngle",a):p.pfValue,Z=Math.cos(X),Q=Math.sin(X),ee=(D+A)/2,te=(P+R)/2;if(!g){switch(l.value){case"left":ee=A;break;case"right":ee=D;break}switch(u.value){case"top":te=R;break;case"bottom":te=P;break}}var K=function(Be,se){return Be=Be-ee,se=se-te,{x:Be*Z-se*Q+ee,y:Be*Q+se*Z+te}},N=K(D,P),$=K(D,R),J=K(A,P),re=K(A,R);D=Math.min(N.x,$.x,J.x,re.x),A=Math.max(N.x,$.x,J.x,re.x),P=Math.min(N.y,$.y,J.y,re.y),R=Math.max(N.y,$.y,J.y,re.y)}var le=_+"Rot",xe=H[le]=H[le]||{};xe.x1=D,xe.y1=P,xe.x2=A,xe.y2=R,xe.w=A-D,xe.h=R-P,kt(e,D,P,A,R),kt(i.labelBounds.all,D,P,A,R)}return e}},xg=function(e,r){if(!r.cy().headless()){var a=r.pstyle("outline-opacity").value,n=r.pstyle("outline-width").value;if(a>0&&n>0){var i=r.pstyle("outline-offset").value,s=r.pstyle("shape").value,o=n+i,l=(e.w+o*2)/e.w,u=(e.h+o*2)/e.h,v=0,f=0;["diamond","pentagon","round-triangle"].includes(s)?(l=(e.w+o*2.4)/e.w,f=-o/3.6):["concave-hexagon","rhomboid","right-rhomboid"].includes(s)?l=(e.w+o*2.4)/e.w:s==="star"?(l=(e.w+o*2.8)/e.w,u=(e.h+o*2.6)/e.h,f=-o/3.8):s==="triangle"?(l=(e.w+o*2.8)/e.w,u=(e.h+o*2.4)/e.h,f=-o/1.4):s==="vee"&&(l=(e.w+o*4.4)/e.w,u=(e.h+o*3.8)/e.h,f=-o*.5);var c=e.h*u-e.h,h=e.w*l-e.w;if(Ja(e,[Math.ceil(c/2),Math.ceil(h/2)]),v!=0||f!==0){var d=sd(e,v,f);tv(e,d)}}}},Eg=function(e,r){var a=e._private.cy,n=a.styleEnabled(),i=a.headless(),s=pt(),o=e._private,l=e.isNode(),u=e.isEdge(),v,f,c,h,d,y,g=o.rstyle,p=l&&n?e.pstyle("bounds-expansion").pfValue:[0],m=function(Ie){return Ie.pstyle("display").value!=="none"},b=!n||m(e)&&(!u||m(e.source())&&m(e.target()));if(b){var w=0,E=0;n&&r.includeOverlays&&(w=e.pstyle("overlay-opacity").value,w!==0&&(E=e.pstyle("overlay-padding").value));var C=0,x=0;n&&r.includeUnderlays&&(C=e.pstyle("underlay-opacity").value,C!==0&&(x=e.pstyle("underlay-padding").value));var S=Math.max(E,x),k=0,B=0;if(n&&(k=e.pstyle("width").pfValue,B=k/2),l&&r.includeNodes){var D=e.position();d=D.x,y=D.y;var A=e.outerWidth(),P=A/2,R=e.outerHeight(),L=R/2;v=d-P,f=d+P,c=y-L,h=y+L,kt(s,v,c,f,h),n&&r.includeOutlines&&xg(s,e)}else if(u&&r.includeEdges)if(n&&!i){var I=e.pstyle("curve-style").strValue;if(v=Math.min(g.srcX,g.midX,g.tgtX),f=Math.max(g.srcX,g.midX,g.tgtX),c=Math.min(g.srcY,g.midY,g.tgtY),h=Math.max(g.srcY,g.midY,g.tgtY),v-=B,f+=B,c-=B,h+=B,kt(s,v,c,f,h),I==="haystack"){var M=g.haystackPts;if(M&&M.length===2){if(v=M[0].x,c=M[0].y,f=M[1].x,h=M[1].y,v>f){var O=v;v=f,f=O}if(c>h){var _=c;c=h,h=_}kt(s,v-B,c-B,f+B,h+B)}}else if(I==="bezier"||I==="unbundled-bezier"||I.endsWith("segments")||I.endsWith("taxi")){var H;switch(I){case"bezier":case"unbundled-bezier":H=g.bezierPts;break;case"segments":case"taxi":case"round-segments":case"round-taxi":H=g.linePts;break}if(H!=null)for(var F=0;F<H.length;F++){var G=H[F];v=G.x-B,f=G.x+B,c=G.y-B,h=G.y+B,kt(s,v,c,f,h)}}}else{var U=e.source(),X=U.position(),Z=e.target(),Q=Z.position();if(v=X.x,f=Q.x,c=X.y,h=Q.y,v>f){var ee=v;v=f,f=ee}if(c>h){var te=c;c=h,h=te}v-=B,f+=B,c-=B,h+=B,kt(s,v,c,f,h)}if(n&&r.includeEdges&&u&&(Ha(s,e,"mid-source"),Ha(s,e,"mid-target"),Ha(s,e,"source"),Ha(s,e,"target")),n){var K=e.pstyle("ghost").value==="yes";if(K){var N=e.pstyle("ghost-offset-x").pfValue,$=e.pstyle("ghost-offset-y").pfValue;kt(s,s.x1+N,s.y1+$,s.x2+N,s.y2+$)}}var J=o.bodyBounds=o.bodyBounds||{};Bo(J,s),Ja(J,p),Qa(J,1),n&&(v=s.x1,f=s.x2,c=s.y1,h=s.y2,kt(s,v-S,c-S,f+S,h+S));var re=o.overlayBounds=o.overlayBounds||{};Bo(re,s),Ja(re,p),Qa(re,1);var le=o.labelBounds=o.labelBounds||{};le.all!=null?id(le.all):le.all=pt(),n&&r.includeLabels&&(r.includeMainLabels&&ns(s,e,null),u&&(r.includeSourceLabels&&ns(s,e,"source"),r.includeTargetLabels&&ns(s,e,"target")))}return s.x1=Ct(s.x1),s.y1=Ct(s.y1),s.x2=Ct(s.x2),s.y2=Ct(s.y2),s.w=Ct(s.x2-s.x1),s.h=Ct(s.y2-s.y1),s.w>0&&s.h>0&&b&&(Ja(s,p),Qa(s,1)),s},kv=function(e){var r=0,a=function(s){return(s?1:0)<<r++},n=0;return n+=a(e.incudeNodes),n+=a(e.includeEdges),n+=a(e.includeLabels),n+=a(e.includeMainLabels),n+=a(e.includeSourceLabels),n+=a(e.includeTargetLabels),n+=a(e.includeOverlays),n+=a(e.includeOutlines),n},Pv=function(e){var r=function(o){return Math.round(o)};if(e.isEdge()){var a=e.source().position(),n=e.target().position();return Co([r(a.x),r(a.y),r(n.x),r(n.y)])}else{var i=e.position();return Co([r(i.x),r(i.y)])}},Hu=function(e,r){var a=e._private,n,i=e.isEdge(),s=r==null?Ku:kv(r),o=s===Ku;if(a.bbCache==null?(n=Eg(e,ba),a.bbCache=n,a.bbCachePosKey=Pv(e)):n=a.bbCache,!o){var l=e.isNode();n=pt(),(r.includeNodes&&l||r.includeEdges&&!l)&&(r.includeOverlays?dr(n,a.overlayBounds):dr(n,a.bodyBounds)),r.includeLabels&&(r.includeMainLabels&&(!i||r.includeSourceLabels&&r.includeTargetLabels)?dr(n,a.labelBounds.all):(r.includeMainLabels&&dr(n,a.labelBounds.mainRot),r.includeSourceLabels&&dr(n,a.labelBounds.sourceRot),r.includeTargetLabels&&dr(n,a.labelBounds.targetRot))),n.w=n.x2-n.x1,n.h=n.y2-n.y1}return n},ba={includeNodes:!0,includeEdges:!0,includeLabels:!0,includeMainLabels:!0,includeSourceLabels:!0,includeTargetLabels:!0,includeOverlays:!0,includeUnderlays:!0,includeOutlines:!0,useCache:!0},Ku=kv(ba),$u=Ue(ba);lr.boundingBox=function(t){var e,r=t===void 0||t.useCache===void 0||t.useCache===!0,a=Vr(function(v){var f=v._private;return f.bbCache==null||f.styleDirty||f.bbCachePosKey!==Pv(v)},function(v){return v.id()});if(r&&this.length===1&&!a(this[0]))t===void 0?t=ba:t=$u(t),e=Hu(this[0],t);else{e=pt(),t=t||ba;var n=$u(t),i=this,s=i.cy(),o=s.styleEnabled();this.edges().forEach(a),this.nodes().forEach(a),o&&this.recalculateRenderedStyle(r),this.updateCompoundBounds(!r);for(var l=0;l<i.length;l++){var u=i[l];a(u)&&u.dirtyBoundingBoxCache(),dr(e,Hu(u,n))}}return e.x1=Ct(e.x1),e.y1=Ct(e.y1),e.x2=Ct(e.x2),e.y2=Ct(e.y2),e.w=Ct(e.x2-e.x1),e.h=Ct(e.y2-e.y1),e};lr.dirtyBoundingBoxCache=function(){for(var t=0;t<this.length;t++){var e=this[t]._private;e.bbCache=null,e.bbCachePosKey=null,e.bodyBounds=null,e.overlayBounds=null,e.labelBounds.all=null,e.labelBounds.source=null,e.labelBounds.target=null,e.labelBounds.main=null,e.labelBounds.sourceRot=null,e.labelBounds.targetRot=null,e.labelBounds.mainRot=null,e.arrowBounds.source=null,e.arrowBounds.target=null,e.arrowBounds["mid-source"]=null,e.arrowBounds["mid-target"]=null}return this.emitAndNotify("bounds"),this};lr.boundingBoxAt=function(t){var e=this.nodes(),r=this.cy(),a=r.hasCompoundNodes(),n=r.collection();if(a&&(n=e.filter(function(u){return u.isParent()}),e=e.not(n)),ke(t)){var i=t;t=function(){return i}}var s=function(v,f){return v._private.bbAtOldPos=t(v,f)},o=function(v){return v._private.bbAtOldPos};r.startBatch(),e.forEach(s).silentPositions(t),a&&(n.dirtyCompoundBoundsCache(),n.dirtyBoundingBoxCache(),n.updateCompoundBounds(!0));var l=nd(this.boundingBox({useCache:!1}));return e.silentPositions(o),a&&(n.dirtyCompoundBoundsCache(),n.dirtyBoundingBoxCache(),n.updateCompoundBounds(!0)),r.endBatch(),l};qr.boundingbox=qr.bb=qr.boundingBox;qr.renderedBoundingbox=qr.renderedBoundingBox;var Cg=lr,ua,Aa;ua=Aa={};var Bv=function(e){e.uppercaseName=oo(e.name),e.autoName="auto"+e.uppercaseName,e.labelName="label"+e.uppercaseName,e.outerName="outer"+e.uppercaseName,e.uppercaseOuterName=oo(e.outerName),ua[e.name]=function(){var a=this[0],n=a._private,i=n.cy,s=i._private.styleEnabled;if(a)if(s){if(a.isParent())return a.updateCompoundBounds(),n[e.autoName]||0;var o=a.pstyle(e.name);switch(o.strValue){case"label":return a.recalculateRenderedStyle(),n.rstyle[e.labelName]||0;default:return o.pfValue}}else return 1},ua["outer"+e.uppercaseName]=function(){var a=this[0],n=a._private,i=n.cy,s=i._private.styleEnabled;if(a)if(s){var o=a[e.name](),l=a.pstyle("border-width").pfValue,u=2*a.padding();return o+l+u}else return 1},ua["rendered"+e.uppercaseName]=function(){var a=this[0];if(a){var n=a[e.name]();return n*this.cy().zoom()}},ua["rendered"+e.uppercaseOuterName]=function(){var a=this[0];if(a){var n=a[e.outerName]();return n*this.cy().zoom()}}};Bv({name:"width"});Bv({name:"height"});Aa.padding=function(){var t=this[0],e=t._private;return t.isParent()?(t.updateCompoundBounds(),e.autoPadding!==void 0?e.autoPadding:t.pstyle("padding").pfValue):t.pstyle("padding").pfValue};Aa.paddedHeight=function(){var t=this[0];return t.height()+2*t.padding()};Aa.paddedWidth=function(){var t=this[0];return t.width()+2*t.padding()};var Tg=Aa,Sg=function(e,r){if(e.isEdge()&&e.takesUpSpace())return r(e)},Dg=function(e,r){if(e.isEdge()&&e.takesUpSpace()){var a=e.cy();return Tn(r(e),a.zoom(),a.pan())}},kg=function(e,r){if(e.isEdge()&&e.takesUpSpace()){var a=e.cy(),n=a.pan(),i=a.zoom();return r(e).map(function(s){return Tn(s,i,n)})}},Pg=function(e){return e.renderer().getControlPoints(e)},Bg=function(e){return e.renderer().getSegmentPoints(e)},Ag=function(e){return e.renderer().getSourceEndpoint(e)},Rg=function(e){return e.renderer().getTargetEndpoint(e)},Mg=function(e){return e.renderer().getEdgeMidpoint(e)},Wu={controlPoints:{get:Pg,mult:!0},segmentPoints:{get:Bg,mult:!0},sourceEndpoint:{get:Ag},targetEndpoint:{get:Rg},midpoint:{get:Mg}},Lg=function(e){return"rendered"+e[0].toUpperCase()+e.substr(1)},Ig=Object.keys(Wu).reduce(function(t,e){var r=Wu[e],a=Lg(e);return t[e]=function(){return Sg(this,r.get)},r.mult?t[a]=function(){return kg(this,r.get)}:t[a]=function(){return Dg(this,r.get)},t},{}),Og=ge({},wg,Cg,Tg,Ig);/*!
Event object based on jQuery events, MIT license

https://jquery.org/license/
https://tldrlegal.com/license/mit-license
https://github.com/jquery/jquery/blob/master/src/event.js
*/var Av=function(e,r){this.recycle(e,r)};function ra(){return!1}function Ka(){return!0}Av.prototype={instanceString:function(){return"event"},recycle:function(e,r){if(this.isImmediatePropagationStopped=this.isPropagationStopped=this.isDefaultPrevented=ra,e!=null&&e.preventDefault?(this.type=e.type,this.isDefaultPrevented=e.defaultPrevented?Ka:ra):e!=null&&e.type?r=e:this.type=e,r!=null&&(this.originalEvent=r.originalEvent,this.type=r.type!=null?r.type:this.type,this.cy=r.cy,this.target=r.target,this.position=r.position,this.renderedPosition=r.renderedPosition,this.namespace=r.namespace,this.layout=r.layout),this.cy!=null&&this.position!=null&&this.renderedPosition==null){var a=this.position,n=this.cy.zoom(),i=this.cy.pan();this.renderedPosition={x:a.x*n+i.x,y:a.y*n+i.y}}this.timeStamp=e&&e.timeStamp||Date.now()},preventDefault:function(){this.isDefaultPrevented=Ka;var e=this.originalEvent;e&&e.preventDefault&&e.preventDefault()},stopPropagation:function(){this.isPropagationStopped=Ka;var e=this.originalEvent;e&&e.stopPropagation&&e.stopPropagation()},stopImmediatePropagation:function(){this.isImmediatePropagationStopped=Ka,this.stopPropagation()},isDefaultPrevented:ra,isPropagationStopped:ra,isImmediatePropagationStopped:ra};var Rv=/^([^.]+)(\.(?:[^.]+))?$/,Ng=".*",Mv={qualifierCompare:function(e,r){return e===r},eventMatches:function(){return!0},addEventFields:function(){},callbackContext:function(e){return e},beforeEmit:function(){},afterEmit:function(){},bubble:function(){return!1},parent:function(){return null},context:null},Uu=Object.keys(Mv),Fg={};function Rn(){for(var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:Fg,e=arguments.length>1?arguments[1]:void 0,r=0;r<Uu.length;r++){var a=Uu[r];this[a]=t[a]||Mv[a]}this.context=e||this.context,this.listeners=[],this.emitting=0}var sr=Rn.prototype,Lv=function(e,r,a,n,i,s,o){_e(n)&&(i=n,n=null),o&&(s==null?s=o:s=ge({},s,o));for(var l=Le(a)?a:a.split(/\s+/),u=0;u<l.length;u++){var v=l[u];if(!tr(v)){var f=v.match(Rv);if(f){var c=f[1],h=f[2]?f[2]:null,d=r(e,v,c,h,n,i,s);if(d===!1)break}}}},Yu=function(e,r){return e.addEventFields(e.context,r),new Av(r.type,r)},zg=function(e,r,a){if(Xf(a)){r(e,a);return}else if(ke(a)){r(e,Yu(e,a));return}for(var n=Le(a)?a:a.split(/\s+/),i=0;i<n.length;i++){var s=n[i];if(!tr(s)){var o=s.match(Rv);if(o){var l=o[1],u=o[2]?o[2]:null,v=Yu(e,{type:l,namespace:u,target:e.context});r(e,v)}}}};sr.on=sr.addListener=function(t,e,r,a,n){return Lv(this,function(i,s,o,l,u,v,f){_e(v)&&i.listeners.push({event:s,callback:v,type:o,namespace:l,qualifier:u,conf:f})},t,e,r,a,n),this};sr.one=function(t,e,r,a){return this.on(t,e,r,a,{one:!0})};sr.removeListener=sr.off=function(t,e,r,a){var n=this;this.emitting!==0&&(this.listeners=Pc(this.listeners));for(var i=this.listeners,s=function(u){var v=i[u];Lv(n,function(f,c,h,d,y,g){if((v.type===h||t==="*")&&(!d&&v.namespace!==".*"||v.namespace===d)&&(!y||f.qualifierCompare(v.qualifier,y))&&(!g||v.callback===g))return i.splice(u,1),!1},t,e,r,a)},o=i.length-1;o>=0;o--)s(o);return this};sr.removeAllListeners=function(){return this.removeListener("*")};sr.emit=sr.trigger=function(t,e,r){var a=this.listeners,n=a.length;return this.emitting++,Le(e)||(e=[e]),zg(this,function(i,s){r!=null&&(a=[{event:s.event,type:s.type,namespace:s.namespace,callback:r}],n=a.length);for(var o=function(){var v=a[l];if(v.type===s.type&&(!v.namespace||v.namespace===s.namespace||v.namespace===Ng)&&i.eventMatches(i.context,v,s)){var f=[s];e!=null&&Ac(f,e),i.beforeEmit(i.context,v,s),v.conf&&v.conf.one&&(i.listeners=i.listeners.filter(function(d){return d!==v}));var c=i.callbackContext(i.context,v,s),h=v.callback.apply(c,f);i.afterEmit(i.context,v,s),h===!1&&(s.stopPropagation(),s.preventDefault())}},l=0;l<n;l++)o();i.bubble(i.context)&&!s.isPropagationStopped()&&i.parent(i.context).emit(s,e)},t),this.emitting--,this};var qg={qualifierCompare:function(e,r){return e==null||r==null?e==null&&r==null:e.sameText(r)},eventMatches:function(e,r,a){var n=r.qualifier;return n!=null?e!==a.target&&Ta(a.target)&&n.matches(a.target):!0},addEventFields:function(e,r){r.cy=e.cy(),r.target=e},callbackContext:function(e,r,a){return r.qualifier!=null?a.target:e},beforeEmit:function(e,r){r.conf&&r.conf.once&&r.conf.onceCollection.removeListener(r.event,r.qualifier,r.callback)},bubble:function(){return!0},parent:function(e){return e.isChild()?e.parent():e.cy()}},$a=function(e){return fe(e)?new nr(e):e},Iv={createEmitter:function(){for(var e=0;e<this.length;e++){var r=this[e],a=r._private;a.emitter||(a.emitter=new Rn(qg,r))}return this},emitter:function(){return this._private.emitter},on:function(e,r,a){for(var n=$a(r),i=0;i<this.length;i++){var s=this[i];s.emitter().on(e,n,a)}return this},removeListener:function(e,r,a){for(var n=$a(r),i=0;i<this.length;i++){var s=this[i];s.emitter().removeListener(e,n,a)}return this},removeAllListeners:function(){for(var e=0;e<this.length;e++){var r=this[e];r.emitter().removeAllListeners()}return this},one:function(e,r,a){for(var n=$a(r),i=0;i<this.length;i++){var s=this[i];s.emitter().one(e,n,a)}return this},once:function(e,r,a){for(var n=$a(r),i=0;i<this.length;i++){var s=this[i];s.emitter().on(e,n,a,{once:!0,onceCollection:this})}},emit:function(e,r){for(var a=0;a<this.length;a++){var n=this[a];n.emitter().emit(e,r)}return this},emitAndNotify:function(e,r){if(this.length!==0)return this.cy().notify(e,this),this.emit(e,r),this}};Ae.eventAliasesOn(Iv);var Ov={nodes:function(e){return this.filter(function(r){return r.isNode()}).filter(e)},edges:function(e){return this.filter(function(r){return r.isEdge()}).filter(e)},byGroup:function(){for(var e=this.spawn(),r=this.spawn(),a=0;a<this.length;a++){var n=this[a];n.isNode()?e.push(n):r.push(n)}return{nodes:e,edges:r}},filter:function(e,r){if(e===void 0)return this;if(fe(e)||bt(e))return new nr(e).filter(this);if(_e(e)){for(var a=this.spawn(),n=this,i=0;i<n.length;i++){var s=n[i],o=r?e.apply(r,[s,i,n]):e(s,i,n);o&&a.push(s)}return a}return this.spawn()},not:function(e){if(e){fe(e)&&(e=this.filter(e));for(var r=this.spawn(),a=0;a<this.length;a++){var n=this[a],i=e.has(n);i||r.push(n)}return r}else return this},absoluteComplement:function(){var e=this.cy();return e.mutableElements().not(this)},intersect:function(e){if(fe(e)){var r=e;return this.filter(r)}for(var a=this.spawn(),n=this,i=e,s=this.length<e.length,o=s?n:i,l=s?i:n,u=0;u<o.length;u++){var v=o[u];l.has(v)&&a.push(v)}return a},xor:function(e){var r=this._private.cy;fe(e)&&(e=r.$(e));var a=this.spawn(),n=this,i=e,s=function(l,u){for(var v=0;v<l.length;v++){var f=l[v],c=f._private.data.id,h=u.hasElementWithId(c);h||a.push(f)}};return s(n,i),s(i,n),a},diff:function(e){var r=this._private.cy;fe(e)&&(e=r.$(e));var a=this.spawn(),n=this.spawn(),i=this.spawn(),s=this,o=e,l=function(v,f,c){for(var h=0;h<v.length;h++){var d=v[h],y=d._private.data.id,g=f.hasElementWithId(y);g?i.merge(d):c.push(d)}};return l(s,o,a),l(o,s,n),{left:a,right:n,both:i}},add:function(e){var r=this._private.cy;if(!e)return this;if(fe(e)){var a=e;e=r.mutableElements().filter(a)}for(var n=this.spawnSelf(),i=0;i<e.length;i++){var s=e[i],o=!this.has(s);o&&n.push(s)}return n},merge:function(e){var r=this._private,a=r.cy;if(!e)return this;if(e&&fe(e)){var n=e;e=a.mutableElements().filter(n)}for(var i=r.map,s=0;s<e.length;s++){var o=e[s],l=o._private.data.id,u=!i.has(l);if(u){var v=this.length++;this[v]=o,i.set(l,{ele:o,index:v})}}return this},unmergeAt:function(e){var r=this[e],a=r.id(),n=this._private,i=n.map;this[e]=void 0,i.delete(a);var s=e===this.length-1;if(this.length>1&&!s){var o=this.length-1,l=this[o],u=l._private.data.id;this[o]=void 0,this[e]=l,i.set(u,{ele:l,index:e})}return this.length--,this},unmergeOne:function(e){e=e[0];var r=this._private,a=e._private.data.id,n=r.map,i=n.get(a);if(!i)return this;var s=i.index;return this.unmergeAt(s),this},unmerge:function(e){var r=this._private.cy;if(!e)return this;if(e&&fe(e)){var a=e;e=r.mutableElements().filter(a)}for(var n=0;n<e.length;n++)this.unmergeOne(e[n]);return this},unmergeBy:function(e){for(var r=this.length-1;r>=0;r--){var a=this[r];e(a)&&this.unmergeAt(r)}return this},map:function(e,r){for(var a=[],n=this,i=0;i<n.length;i++){var s=n[i],o=r?e.apply(r,[s,i,n]):e(s,i,n);a.push(o)}return a},reduce:function(e,r){for(var a=r,n=this,i=0;i<n.length;i++)a=e(a,n[i],i,n);return a},max:function(e,r){for(var a=-1/0,n,i=this,s=0;s<i.length;s++){var o=i[s],l=r?e.apply(r,[o,s,i]):e(o,s,i);l>a&&(a=l,n=o)}return{value:a,ele:n}},min:function(e,r){for(var a=1/0,n,i=this,s=0;s<i.length;s++){var o=i[s],l=r?e.apply(r,[o,s,i]):e(o,s,i);l<a&&(a=l,n=o)}return{value:a,ele:n}}},Pe=Ov;Pe.u=Pe["|"]=Pe["+"]=Pe.union=Pe.or=Pe.add;Pe["\\"]=Pe["!"]=Pe["-"]=Pe.difference=Pe.relativeComplement=Pe.subtract=Pe.not;Pe.n=Pe["&"]=Pe["."]=Pe.and=Pe.intersection=Pe.intersect;Pe["^"]=Pe["(+)"]=Pe["(-)"]=Pe.symmetricDifference=Pe.symdiff=Pe.xor;Pe.fnFilter=Pe.filterFn=Pe.stdFilter=Pe.filter;Pe.complement=Pe.abscomp=Pe.absoluteComplement;var Vg={isNode:function(){return this.group()==="nodes"},isEdge:function(){return this.group()==="edges"},isLoop:function(){return this.isEdge()&&this.source()[0]===this.target()[0]},isSimple:function(){return this.isEdge()&&this.source()[0]!==this.target()[0]},group:function(){var e=this[0];if(e)return e._private.group}},Nv=function(e,r){var a=e.cy(),n=a.hasCompoundNodes();function i(v){var f=v.pstyle("z-compound-depth");return f.value==="auto"?n?v.zDepth():0:f.value==="bottom"?-1:f.value==="top"?Os:0}var s=i(e)-i(r);if(s!==0)return s;function o(v){var f=v.pstyle("z-index-compare");return f.value==="auto"&&v.isNode()?1:0}var l=o(e)-o(r);if(l!==0)return l;var u=e.pstyle("z-index").value-r.pstyle("z-index").value;return u!==0?u:e.poolIndex()-r.poolIndex()},hn={forEach:function(e,r){if(_e(e))for(var a=this.length,n=0;n<a;n++){var i=this[n],s=r?e.apply(r,[i,n,this]):e(i,n,this);if(s===!1)break}return this},toArray:function(){for(var e=[],r=0;r<this.length;r++)e.push(this[r]);return e},slice:function(e,r){var a=[],n=this.length;r==null&&(r=n),e==null&&(e=0),e<0&&(e=n+e),r<0&&(r=n+r);for(var i=e;i>=0&&i<r&&i<n;i++)a.push(this[i]);return this.spawn(a)},size:function(){return this.length},eq:function(e){return this[e]||this.spawn()},first:function(){return this[0]||this.spawn()},last:function(){return this[this.length-1]||this.spawn()},empty:function(){return this.length===0},nonempty:function(){return!this.empty()},sort:function(e){if(!_e(e))return this;var r=this.toArray().sort(e);return this.spawn(r)},sortByZIndex:function(){return this.sort(Nv)},zDepth:function(){var e=this[0];if(e){var r=e._private,a=r.group;if(a==="nodes"){var n=r.data.parent?e.parents().size():0;return e.isParent()?n:Os-1}else{var i=r.source,s=r.target,o=i.zDepth(),l=s.zDepth();return Math.max(o,l,0)}}}};hn.each=hn.forEach;var _g=function(){var e="undefined",r=(typeof Symbol>"u"?"undefined":We(Symbol))!=e&&We(Symbol.iterator)!=e;r&&(hn[Symbol.iterator]=function(){var a=this,n={value:void 0,done:!1},i=0,s=this.length;return Ll({next:function(){return i<s?n.value=a[i++]:(n.value=void 0,n.done=!0),n}},Symbol.iterator,function(){return this})})};_g();var Gg=Ue({nodeDimensionsIncludeLabels:!1}),en={layoutDimensions:function(e){e=Gg(e);var r;if(!this.takesUpSpace())r={w:0,h:0};else if(e.nodeDimensionsIncludeLabels){var a=this.boundingBox();r={w:a.w,h:a.h}}else r={w:this.outerWidth(),h:this.outerHeight()};return(r.w===0||r.h===0)&&(r.w=r.h=1),r},layoutPositions:function(e,r,a){var n=this.nodes().filter(function(E){return!E.isParent()}),i=this.cy(),s=r.eles,o=function(C){return C.id()},l=Vr(a,o);e.emit({type:"layoutstart",layout:e}),e.animations=[];var u=function(C,x,S){var k={x:x.x1+x.w/2,y:x.y1+x.h/2},B={x:(S.x-k.x)*C,y:(S.y-k.y)*C};return{x:k.x+B.x,y:k.y+B.y}},v=r.spacingFactor&&r.spacingFactor!==1,f=function(){if(!v)return null;for(var C=pt(),x=0;x<n.length;x++){var S=n[x],k=l(S,x);od(C,k.x,k.y)}return C},c=f(),h=Vr(function(E,C){var x=l(E,C);if(v){var S=Math.abs(r.spacingFactor);x=u(S,c,x)}return r.transform!=null&&(x=r.transform(E,x)),x},o);if(r.animate){for(var d=0;d<n.length;d++){var y=n[d],g=h(y,d),p=r.animateFilter==null||r.animateFilter(y,d);if(p){var m=y.animation({position:g,duration:r.animationDuration,easing:r.animationEasing});e.animations.push(m)}else y.position(g)}if(r.fit){var b=i.animation({fit:{boundingBox:s.boundingBoxAt(h),padding:r.padding},duration:r.animationDuration,easing:r.animationEasing});e.animations.push(b)}else if(r.zoom!==void 0&&r.pan!==void 0){var w=i.animation({zoom:r.zoom,pan:r.pan,duration:r.animationDuration,easing:r.animationEasing});e.animations.push(w)}e.animations.forEach(function(E){return E.play()}),e.one("layoutready",r.ready),e.emit({type:"layoutready",layout:e}),Wr.all(e.animations.map(function(E){return E.promise()})).then(function(){e.one("layoutstop",r.stop),e.emit({type:"layoutstop",layout:e})})}else n.positions(h),r.fit&&i.fit(r.eles,r.padding),r.zoom!=null&&i.zoom(r.zoom),r.pan&&i.pan(r.pan),e.one("layoutready",r.ready),e.emit({type:"layoutready",layout:e}),e.one("layoutstop",r.stop),e.emit({type:"layoutstop",layout:e});return this},layout:function(e){var r=this.cy();return r.makeLayout(ge({},e,{eles:this}))}};en.createLayout=en.makeLayout=en.layout;function Fv(t,e,r){var a=r._private,n=a.styleCache=a.styleCache||[],i;return(i=n[t])!=null||(i=n[t]=e(r)),i}function Mn(t,e){return t=rr(t),function(a){return Fv(t,e,a)}}function Ln(t,e){t=rr(t);var r=function(n){return e.call(n)};return function(){var n=this[0];if(n)return Fv(t,r,n)}}var at={recalculateRenderedStyle:function(e){var r=this.cy(),a=r.renderer(),n=r.styleEnabled();return a&&n&&a.recalculateRenderedStyle(this,e),this},dirtyStyleCache:function(){var e=this.cy(),r=function(i){return i._private.styleCache=null};if(e.hasCompoundNodes()){var a;a=this.spawnSelf().merge(this.descendants()).merge(this.parents()),a.merge(a.connectedEdges()),a.forEach(r)}else this.forEach(function(n){r(n),n.connectedEdges().forEach(r)});return this},updateStyle:function(e){var r=this._private.cy;if(!r.styleEnabled())return this;if(r.batching()){var a=r._private.batchStyleEles;return a.merge(this),this}var n=r.hasCompoundNodes(),i=this;e=!!(e||e===void 0),n&&(i=this.spawnSelf().merge(this.descendants()).merge(this.parents()));var s=i;return e?s.emitAndNotify("style"):s.emit("style"),i.forEach(function(o){return o._private.styleDirty=!0}),this},cleanStyle:function(){var e=this.cy();if(e.styleEnabled())for(var r=0;r<this.length;r++){var a=this[r];a._private.styleDirty&&(a._private.styleDirty=!1,e.style().apply(a))}},parsedStyle:function(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,a=this[0],n=a.cy();if(n.styleEnabled()&&a){a._private.styleDirty&&(a._private.styleDirty=!1,n.style().apply(a));var i=a._private.style[e];return i??(r?n.style().getDefaultProperty(e):null)}},numericStyle:function(e){var r=this[0];if(r.cy().styleEnabled()&&r){var a=r.pstyle(e);return a.pfValue!==void 0?a.pfValue:a.value}},numericStyleUnits:function(e){var r=this[0];if(r.cy().styleEnabled()&&r)return r.pstyle(e).units},renderedStyle:function(e){var r=this.cy();if(!r.styleEnabled())return this;var a=this[0];if(a)return r.style().getRenderedStyle(a,e)},style:function(e,r){var a=this.cy();if(!a.styleEnabled())return this;var n=!1,i=a.style();if(ke(e)){var s=e;i.applyBypass(this,s,n),this.emitAndNotify("style")}else if(fe(e))if(r===void 0){var o=this[0];return o?i.getStylePropertyValue(o,e):void 0}else i.applyBypass(this,e,r,n),this.emitAndNotify("style");else if(e===void 0){var l=this[0];return l?i.getRawStyle(l):void 0}return this},removeStyle:function(e){var r=this.cy();if(!r.styleEnabled())return this;var a=!1,n=r.style(),i=this;if(e===void 0)for(var s=0;s<i.length;s++){var o=i[s];n.removeAllBypasses(o,a)}else{e=e.split(/\s+/);for(var l=0;l<i.length;l++){var u=i[l];n.removeBypasses(u,e,a)}}return this.emitAndNotify("style"),this},show:function(){return this.css("display","element"),this},hide:function(){return this.css("display","none"),this},effectiveOpacity:function(){var e=this.cy();if(!e.styleEnabled())return 1;var r=e.hasCompoundNodes(),a=this[0];if(a){var n=a._private,i=a.pstyle("opacity").value;if(!r)return i;var s=n.data.parent?a.parents():null;if(s)for(var o=0;o<s.length;o++){var l=s[o],u=l.pstyle("opacity").value;i=u*i}return i}},transparent:function(){var e=this.cy();if(!e.styleEnabled())return!1;var r=this[0],a=r.cy().hasCompoundNodes();if(r)return a?r.effectiveOpacity()===0:r.pstyle("opacity").value===0},backgrounding:function(){var e=this.cy();if(!e.styleEnabled())return!1;var r=this[0];return!!r._private.backgrounding}};function is(t,e){var r=t._private,a=r.data.parent?t.parents():null;if(a)for(var n=0;n<a.length;n++){var i=a[n];if(!e(i))return!1}return!0}function Ws(t){var e=t.ok,r=t.edgeOkViaNode||t.ok,a=t.parentOk||t.ok;return function(){var n=this.cy();if(!n.styleEnabled())return!0;var i=this[0],s=n.hasCompoundNodes();if(i){var o=i._private;if(!e(i))return!1;if(i.isNode())return!s||is(i,a);var l=o.source,u=o.target;return r(l)&&(!s||is(l,r))&&(l===u||r(u)&&(!s||is(u,r)))}}}var Ur=Mn("eleTakesUpSpace",function(t){return t.pstyle("display").value==="element"&&t.width()!==0&&(t.isNode()?t.height()!==0:!0)});at.takesUpSpace=Ln("takesUpSpace",Ws({ok:Ur}));var Hg=Mn("eleInteractive",function(t){return t.pstyle("events").value==="yes"&&t.pstyle("visibility").value==="visible"&&Ur(t)}),Kg=Mn("parentInteractive",function(t){return t.pstyle("visibility").value==="visible"&&Ur(t)});at.interactive=Ln("interactive",Ws({ok:Hg,parentOk:Kg,edgeOkViaNode:Ur}));at.noninteractive=function(){var t=this[0];if(t)return!t.interactive()};var $g=Mn("eleVisible",function(t){return t.pstyle("visibility").value==="visible"&&t.pstyle("opacity").pfValue!==0&&Ur(t)}),Wg=Ur;at.visible=Ln("visible",Ws({ok:$g,edgeOkViaNode:Wg}));at.hidden=function(){var t=this[0];if(t)return!t.visible()};at.isBundledBezier=Ln("isBundledBezier",function(){return this.cy().styleEnabled()?!this.removed()&&this.pstyle("curve-style").value==="bezier"&&this.takesUpSpace():!1});at.bypass=at.css=at.style;at.renderedCss=at.renderedStyle;at.removeBypass=at.removeCss=at.removeStyle;at.pstyle=at.parsedStyle;var er={};function Xu(t){return function(){var e=arguments,r=[];if(e.length===2){var a=e[0],n=e[1];this.on(t.event,a,n)}else if(e.length===1&&_e(e[0])){var i=e[0];this.on(t.event,i)}else if(e.length===0||e.length===1&&Le(e[0])){for(var s=e.length===1?e[0]:null,o=0;o<this.length;o++){var l=this[o],u=!t.ableField||l._private[t.ableField],v=l._private[t.field]!=t.value;if(t.overrideAble){var f=t.overrideAble(l);if(f!==void 0&&(u=f,!f))return this}u&&(l._private[t.field]=t.value,v&&r.push(l))}var c=this.spawn(r);c.updateStyle(),c.emit(t.event),s&&c.emit(s)}return this}}function Yr(t){er[t.field]=function(){var e=this[0];if(e){if(t.overrideField){var r=t.overrideField(e);if(r!==void 0)return r}return e._private[t.field]}},er[t.on]=Xu({event:t.on,field:t.field,ableField:t.ableField,overrideAble:t.overrideAble,value:!0}),er[t.off]=Xu({event:t.off,field:t.field,ableField:t.ableField,overrideAble:t.overrideAble,value:!1})}Yr({field:"locked",overrideField:function(e){return e.cy().autolock()?!0:void 0},on:"lock",off:"unlock"});Yr({field:"grabbable",overrideField:function(e){return e.cy().autoungrabify()||e.pannable()?!1:void 0},on:"grabify",off:"ungrabify"});Yr({field:"selected",ableField:"selectable",overrideAble:function(e){return e.cy().autounselectify()?!1:void 0},on:"select",off:"unselect"});Yr({field:"selectable",overrideField:function(e){return e.cy().autounselectify()?!1:void 0},on:"selectify",off:"unselectify"});er.deselect=er.unselect;er.grabbed=function(){var t=this[0];if(t)return t._private.grabbed};Yr({field:"active",on:"activate",off:"unactivate"});Yr({field:"pannable",on:"panify",off:"unpanify"});er.inactive=function(){var t=this[0];if(t)return!t._private.active};var ot={},Zu=function(e){return function(a){for(var n=this,i=[],s=0;s<n.length;s++){var o=n[s];if(o.isNode()){for(var l=!1,u=o.connectedEdges(),v=0;v<u.length;v++){var f=u[v],c=f.source(),h=f.target();if(e.noIncomingEdges&&h===o&&c!==o||e.noOutgoingEdges&&c===o&&h!==o){l=!0;break}}l||i.push(o)}}return this.spawn(i,!0).filter(a)}},Qu=function(e){return function(r){for(var a=this,n=[],i=0;i<a.length;i++){var s=a[i];if(s.isNode())for(var o=s.connectedEdges(),l=0;l<o.length;l++){var u=o[l],v=u.source(),f=u.target();e.outgoing&&v===s?(n.push(u),n.push(f)):e.incoming&&f===s&&(n.push(u),n.push(v))}}return this.spawn(n,!0).filter(r)}},Ju=function(e){return function(r){for(var a=this,n=[],i={};;){var s=e.outgoing?a.outgoers():a.incomers();if(s.length===0)break;for(var o=!1,l=0;l<s.length;l++){var u=s[l],v=u.id();i[v]||(i[v]=!0,n.push(u),o=!0)}if(!o)break;a=s}return this.spawn(n,!0).filter(r)}};ot.clearTraversalCache=function(){for(var t=0;t<this.length;t++)this[t]._private.traversalCache=null};ge(ot,{roots:Zu({noIncomingEdges:!0}),leaves:Zu({noOutgoingEdges:!0}),outgoers:Tt(Qu({outgoing:!0}),"outgoers"),successors:Ju({outgoing:!0}),incomers:Tt(Qu({incoming:!0}),"incomers"),predecessors:Ju({})});ge(ot,{neighborhood:Tt(function(t){for(var e=[],r=this.nodes(),a=0;a<r.length;a++)for(var n=r[a],i=n.connectedEdges(),s=0;s<i.length;s++){var o=i[s],l=o.source(),u=o.target(),v=n===l?u:l;v.length>0&&e.push(v[0]),e.push(o[0])}return this.spawn(e,!0).filter(t)},"neighborhood"),closedNeighborhood:function(e){return this.neighborhood().add(this).filter(e)},openNeighborhood:function(e){return this.neighborhood(e)}});ot.neighbourhood=ot.neighborhood;ot.closedNeighbourhood=ot.closedNeighborhood;ot.openNeighbourhood=ot.openNeighborhood;ge(ot,{source:Tt(function(e){var r=this[0],a;return r&&(a=r._private.source||r.cy().collection()),a&&e?a.filter(e):a},"source"),target:Tt(function(e){var r=this[0],a;return r&&(a=r._private.target||r.cy().collection()),a&&e?a.filter(e):a},"target"),sources:ju({attr:"source"}),targets:ju({attr:"target"})});function ju(t){return function(r){for(var a=[],n=0;n<this.length;n++){var i=this[n],s=i._private[t.attr];s&&a.push(s)}return this.spawn(a,!0).filter(r)}}ge(ot,{edgesWith:Tt(el(),"edgesWith"),edgesTo:Tt(el({thisIsSrc:!0}),"edgesTo")});function el(t){return function(r){var a=[],n=this._private.cy,i=t||{};fe(r)&&(r=n.$(r));for(var s=0;s<r.length;s++)for(var o=r[s]._private.edges,l=0;l<o.length;l++){var u=o[l],v=u._private.data,f=this.hasElementWithId(v.source)&&r.hasElementWithId(v.target),c=r.hasElementWithId(v.source)&&this.hasElementWithId(v.target),h=f||c;h&&((i.thisIsSrc||i.thisIsTgt)&&(i.thisIsSrc&&!f||i.thisIsTgt&&!c)||a.push(u))}return this.spawn(a,!0)}}ge(ot,{connectedEdges:Tt(function(t){for(var e=[],r=this,a=0;a<r.length;a++){var n=r[a];if(n.isNode())for(var i=n._private.edges,s=0;s<i.length;s++){var o=i[s];e.push(o)}}return this.spawn(e,!0).filter(t)},"connectedEdges"),connectedNodes:Tt(function(t){for(var e=[],r=this,a=0;a<r.length;a++){var n=r[a];n.isEdge()&&(e.push(n.source()[0]),e.push(n.target()[0]))}return this.spawn(e,!0).filter(t)},"connectedNodes"),parallelEdges:Tt(tl(),"parallelEdges"),codirectedEdges:Tt(tl({codirected:!0}),"codirectedEdges")});function tl(t){var e={codirected:!1};return t=ge({},e,t),function(a){for(var n=[],i=this.edges(),s=t,o=0;o<i.length;o++)for(var l=i[o],u=l._private,v=u.source,f=v._private.data.id,c=u.data.target,h=v._private.edges,d=0;d<h.length;d++){var y=h[d],g=y._private.data,p=g.target,m=g.source,b=p===c&&m===f,w=f===p&&c===m;(s.codirected&&b||!s.codirected&&(b||w))&&n.push(y)}return this.spawn(n,!0).filter(a)}}ge(ot,{components:function(e){var r=this,a=r.cy(),n=a.collection(),i=e==null?r.nodes():e.nodes(),s=[];e!=null&&i.empty()&&(i=e.sources());var o=function(v,f){n.merge(v),i.unmerge(v),f.merge(v)};if(i.empty())return r.spawn();var l=function(){var v=a.collection();s.push(v);var f=i[0];o(f,v),r.bfs({directed:!1,roots:f,visit:function(h){return o(h,v)}}),v.forEach(function(c){c.connectedEdges().forEach(function(h){r.has(h)&&v.has(h.source())&&v.has(h.target())&&v.merge(h)})})};do l();while(i.length>0);return s},component:function(){var e=this[0];return e.cy().mutableElements().components(e)[0]}});ot.componentsOf=ot.components;var nt=function(e,r){var a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;if(e===void 0){Ve("A collection must have a reference to the core");return}var i=new Kt,s=!1;if(!r)r=[];else if(r.length>0&&ke(r[0])&&!Ta(r[0])){s=!0;for(var o=[],l=new $r,u=0,v=r.length;u<v;u++){var f=r[u];f.data==null&&(f.data={});var c=f.data;if(c.id==null)c.id=Ql();else if(e.hasElementWithId(c.id)||l.has(c.id))continue;var h=new Cn(e,f,!1);o.push(h),l.add(c.id)}r=o}this.length=0;for(var d=0,y=r.length;d<y;d++){var g=r[d][0];if(g!=null){var p=g._private.data.id;(!a||!i.has(p))&&(a&&i.set(p,{index:this.length,ele:g}),this[this.length]=g,this.length++)}}this._private={eles:this,cy:e,get map(){return this.lazyMap==null&&this.rebuildMap(),this.lazyMap},set map(m){this.lazyMap=m},rebuildMap:function(){for(var b=this.lazyMap=new Kt,w=this.eles,E=0;E<w.length;E++){var C=w[E];b.set(C.id(),{index:E,ele:C})}}},a&&(this._private.map=i),s&&!n&&this.restore()},Fe=Cn.prototype=nt.prototype=Object.create(Array.prototype);Fe.instanceString=function(){return"collection"};Fe.spawn=function(t,e){return new nt(this.cy(),t,e)};Fe.spawnSelf=function(){return this.spawn(this)};Fe.cy=function(){return this._private.cy};Fe.renderer=function(){return this._private.cy.renderer()};Fe.element=function(){return this[0]};Fe.collection=function(){return Fl(this)?this:new nt(this._private.cy,[this])};Fe.unique=function(){return new nt(this._private.cy,this,!0)};Fe.hasElementWithId=function(t){return t=""+t,this._private.map.has(t)};Fe.getElementById=function(t){t=""+t;var e=this._private.cy,r=this._private.map.get(t);return r?r.ele:new nt(e)};Fe.$id=Fe.getElementById;Fe.poolIndex=function(){var t=this._private.cy,e=t._private.elements,r=this[0]._private.data.id;return e._private.map.get(r).index};Fe.indexOf=function(t){var e=t[0]._private.data.id;return this._private.map.get(e).index};Fe.indexOfId=function(t){return t=""+t,this._private.map.get(t).index};Fe.json=function(t){var e=this.element(),r=this.cy();if(e==null&&t)return this;if(e!=null){var a=e._private;if(ke(t)){if(r.startBatch(),t.data){e.data(t.data);var n=a.data;if(e.isEdge()){var i=!1,s={},o=t.data.source,l=t.data.target;o!=null&&o!=n.source&&(s.source=""+o,i=!0),l!=null&&l!=n.target&&(s.target=""+l,i=!0),i&&(e=e.move(s))}else{var u="parent"in t.data,v=t.data.parent;u&&(v!=null||n.parent!=null)&&v!=n.parent&&(v===void 0&&(v=null),v!=null&&(v=""+v),e=e.move({parent:v}))}}t.position&&e.position(t.position);var f=function(y,g,p){var m=t[y];m!=null&&m!==a[y]&&(m?e[g]():e[p]())};return f("removed","remove","restore"),f("selected","select","unselect"),f("selectable","selectify","unselectify"),f("locked","lock","unlock"),f("grabbable","grabify","ungrabify"),f("pannable","panify","unpanify"),t.classes!=null&&e.classes(t.classes),r.endBatch(),this}else if(t===void 0){var c={data:Nt(a.data),position:Nt(a.position),group:a.group,removed:a.removed,selected:a.selected,selectable:a.selectable,locked:a.locked,grabbable:a.grabbable,pannable:a.pannable,classes:null};c.classes="";var h=0;return a.classes.forEach(function(d){return c.classes+=h++===0?d:" "+d}),c}}};Fe.jsons=function(){for(var t=[],e=0;e<this.length;e++){var r=this[e],a=r.json();t.push(a)}return t};Fe.clone=function(){for(var t=this.cy(),e=[],r=0;r<this.length;r++){var a=this[r],n=a.json(),i=new Cn(t,n,!1);e.push(i)}return new nt(t,e)};Fe.copy=Fe.clone;Fe.restore=function(){for(var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,r=this,a=r.cy(),n=a._private,i=[],s=[],o,l=0,u=r.length;l<u;l++){var v=r[l];e&&!v.removed()||(v.isNode()?i.push(v):s.push(v))}o=i.concat(s);var f,c=function(){o.splice(f,1),f--};for(f=0;f<o.length;f++){var h=o[f],d=h._private,y=d.data;if(h.clearTraversalCache(),!(!e&&!d.removed)){if(y.id===void 0)y.id=Ql();else if(ae(y.id))y.id=""+y.id;else if(tr(y.id)||!fe(y.id)){Ve("Can not create element with invalid string ID `"+y.id+"`"),c();continue}else if(a.hasElementWithId(y.id)){Ve("Can not create second element with ID `"+y.id+"`"),c();continue}}var g=y.id;if(h.isNode()){var p=d.position;p.x==null&&(p.x=0),p.y==null&&(p.y=0)}if(h.isEdge()){for(var m=h,b=["source","target"],w=b.length,E=!1,C=0;C<w;C++){var x=b[C],S=y[x];ae(S)&&(S=y[x]=""+y[x]),S==null||S===""?(Ve("Can not create edge `"+g+"` with unspecified "+x),E=!0):a.hasElementWithId(S)||(Ve("Can not create edge `"+g+"` with nonexistant "+x+" `"+S+"`"),E=!0)}if(E){c();continue}var k=a.getElementById(y.source),B=a.getElementById(y.target);k.same(B)?k._private.edges.push(m):(k._private.edges.push(m),B._private.edges.push(m)),m._private.source=k,m._private.target=B}d.map=new Kt,d.map.set(g,{ele:h,index:0}),d.removed=!1,e&&a.addToPool(h)}for(var D=0;D<i.length;D++){var A=i[D],P=A._private.data;ae(P.parent)&&(P.parent=""+P.parent);var R=P.parent,L=R!=null;if(L||A._private.parent){var I=A._private.parent?a.collection().merge(A._private.parent):a.getElementById(R);if(I.empty())P.parent=void 0;else if(I[0].removed())Re("Node added with missing parent, reference to parent removed"),P.parent=void 0,A._private.parent=null;else{for(var M=!1,O=I;!O.empty();){if(A.same(O)){M=!0,P.parent=void 0;break}O=O.parent()}M||(I[0]._private.children.push(A),A._private.parent=I[0],n.hasCompoundNodes=!0)}}}if(o.length>0){for(var _=o.length===r.length?r:new nt(a,o),H=0;H<_.length;H++){var F=_[H];F.isNode()||(F.parallelEdges().clearTraversalCache(),F.source().clearTraversalCache(),F.target().clearTraversalCache())}var G;n.hasCompoundNodes?G=a.collection().merge(_).merge(_.connectedNodes()).merge(_.parent()):G=_,G.dirtyCompoundBoundsCache().dirtyBoundingBoxCache().updateStyle(t),t?_.emitAndNotify("add"):e&&_.emit("add")}return r};Fe.removed=function(){var t=this[0];return t&&t._private.removed};Fe.inside=function(){var t=this[0];return t&&!t._private.removed};Fe.remove=function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,r=this,a=[],n={},i=r._private.cy;function s(R){for(var L=R._private.edges,I=0;I<L.length;I++)l(L[I])}function o(R){for(var L=R._private.children,I=0;I<L.length;I++)l(L[I])}function l(R){var L=n[R.id()];e&&R.removed()||L||(n[R.id()]=!0,R.isNode()?(a.push(R),s(R),o(R)):a.unshift(R))}for(var u=0,v=r.length;u<v;u++){var f=r[u];l(f)}function c(R,L){var I=R._private.edges;ar(I,L),R.clearTraversalCache()}function h(R){R.clearTraversalCache()}var d=[];d.ids={};function y(R,L){L=L[0],R=R[0];var I=R._private.children,M=R.id();ar(I,L),L._private.parent=null,d.ids[M]||(d.ids[M]=!0,d.push(R))}r.dirtyCompoundBoundsCache(),e&&i.removeFromPool(a);for(var g=0;g<a.length;g++){var p=a[g];if(p.isEdge()){var m=p.source()[0],b=p.target()[0];c(m,p),c(b,p);for(var w=p.parallelEdges(),E=0;E<w.length;E++){var C=w[E];h(C),C.isBundledBezier()&&C.dirtyBoundingBoxCache()}}else{var x=p.parent();x.length!==0&&y(x,p)}e&&(p._private.removed=!0)}var S=i._private.elements;i._private.hasCompoundNodes=!1;for(var k=0;k<S.length;k++){var B=S[k];if(B.isParent()){i._private.hasCompoundNodes=!0;break}}var D=new nt(this.cy(),a);D.size()>0&&(t?D.emitAndNotify("remove"):e&&D.emit("remove"));for(var A=0;A<d.length;A++){var P=d[A];(!e||!P.removed())&&P.updateStyle()}return D};Fe.move=function(t){var e=this._private.cy,r=this,a=!1,n=!1,i=function(d){return d==null?d:""+d};if(t.source!==void 0||t.target!==void 0){var s=i(t.source),o=i(t.target),l=s!=null&&e.hasElementWithId(s),u=o!=null&&e.hasElementWithId(o);(l||u)&&(e.batch(function(){r.remove(a,n),r.emitAndNotify("moveout");for(var h=0;h<r.length;h++){var d=r[h],y=d._private.data;d.isEdge()&&(l&&(y.source=s),u&&(y.target=o))}r.restore(a,n)}),r.emitAndNotify("move"))}else if(t.parent!==void 0){var v=i(t.parent),f=v===null||e.hasElementWithId(v);if(f){var c=v===null?void 0:v;e.batch(function(){var h=r.remove(a,n);h.emitAndNotify("moveout");for(var d=0;d<r.length;d++){var y=r[d],g=y._private.data;y.isNode()&&(g.parent=c)}h.restore(a,n)}),r.emitAndNotify("move")}}return this};[vv,ag,ja,jt,Hr,bg,An,Og,Iv,Ov,Vg,hn,en,at,er,ot].forEach(function(t){ge(Fe,t)});var Ug={add:function(e){var r,a=this;if(bt(e)){var n=e;if(n._private.cy===a)r=n.restore();else{for(var i=[],s=0;s<n.length;s++){var o=n[s];i.push(o.json())}r=new nt(a,i)}}else if(Le(e)){var l=e;r=new nt(a,l)}else if(ke(e)&&(Le(e.nodes)||Le(e.edges))){for(var u=e,v=[],f=["nodes","edges"],c=0,h=f.length;c<h;c++){var d=f[c],y=u[d];if(Le(y))for(var g=0,p=y.length;g<p;g++){var m=ge({group:d},y[g]);v.push(m)}}r=new nt(a,v)}else{var b=e;r=new Cn(a,b).collection()}return r},remove:function(e){if(!bt(e)){if(fe(e)){var r=e;e=this.$(r)}}return e.remove()}};/*! Bezier curve function generator. Copyright Gaetan Renaudeau. MIT License: http://en.wikipedia.org/wiki/MIT_License */function Yg(t,e,r,a){var n=4,i=.001,s=1e-7,o=10,l=11,u=1/(l-1),v=typeof Float32Array<"u";if(arguments.length!==4)return!1;for(var f=0;f<4;++f)if(typeof arguments[f]!="number"||isNaN(arguments[f])||!isFinite(arguments[f]))return!1;t=Math.min(t,1),r=Math.min(r,1),t=Math.max(t,0),r=Math.max(r,0);var c=v?new Float32Array(l):new Array(l);function h(B,D){return 1-3*D+3*B}function d(B,D){return 3*D-6*B}function y(B){return 3*B}function g(B,D,A){return((h(D,A)*B+d(D,A))*B+y(D))*B}function p(B,D,A){return 3*h(D,A)*B*B+2*d(D,A)*B+y(D)}function m(B,D){for(var A=0;A<n;++A){var P=p(D,t,r);if(P===0)return D;var R=g(D,t,r)-B;D-=R/P}return D}function b(){for(var B=0;B<l;++B)c[B]=g(B*u,t,r)}function w(B,D,A){var P,R,L=0;do R=D+(A-D)/2,P=g(R,t,r)-B,P>0?A=R:D=R;while(Math.abs(P)>s&&++L<o);return R}function E(B){for(var D=0,A=1,P=l-1;A!==P&&c[A]<=B;++A)D+=u;--A;var R=(B-c[A])/(c[A+1]-c[A]),L=D+R*u,I=p(L,t,r);return I>=i?m(B,L):I===0?L:w(B,D,D+u)}var C=!1;function x(){C=!0,(t!==e||r!==a)&&b()}var S=function(D){return C||x(),t===e&&r===a?D:D===0?0:D===1?1:g(E(D),e,a)};S.getControlPoints=function(){return[{x:t,y:e},{x:r,y:a}]};var k="generateBezier("+[t,e,r,a]+")";return S.toString=function(){return k},S}/*! Runge-Kutta spring physics function generator. Adapted from Framer.js, copyright Koen Bok. MIT License: http://en.wikipedia.org/wiki/MIT_License */var Xg=function(){function t(a){return-a.tension*a.x-a.friction*a.v}function e(a,n,i){var s={x:a.x+i.dx*n,v:a.v+i.dv*n,tension:a.tension,friction:a.friction};return{dx:s.v,dv:t(s)}}function r(a,n){var i={dx:a.v,dv:t(a)},s=e(a,n*.5,i),o=e(a,n*.5,s),l=e(a,n,o),u=1/6*(i.dx+2*(s.dx+o.dx)+l.dx),v=1/6*(i.dv+2*(s.dv+o.dv)+l.dv);return a.x=a.x+u*n,a.v=a.v+v*n,a}return function a(n,i,s){var o={x:-1,v:0,tension:null,friction:null},l=[0],u=0,v=1/1e4,f=16/1e3,c,h,d;for(n=parseFloat(n)||500,i=parseFloat(i)||20,s=s||null,o.tension=n,o.friction=i,c=s!==null,c?(u=a(n,i),h=u/s*f):h=f;d=r(d||o,h),l.push(1+d.x),u+=16,Math.abs(d.x)>v&&Math.abs(d.v)>v;);return c?function(y){return l[y*(l.length-1)|0]}:u}}(),Ne=function(e,r,a,n){var i=Yg(e,r,a,n);return function(s,o,l){return s+(o-s)*i(l)}},tn={linear:function(e,r,a){return e+(r-e)*a},ease:Ne(.25,.1,.25,1),"ease-in":Ne(.42,0,1,1),"ease-out":Ne(0,0,.58,1),"ease-in-out":Ne(.42,0,.58,1),"ease-in-sine":Ne(.47,0,.745,.715),"ease-out-sine":Ne(.39,.575,.565,1),"ease-in-out-sine":Ne(.445,.05,.55,.95),"ease-in-quad":Ne(.55,.085,.68,.53),"ease-out-quad":Ne(.25,.46,.45,.94),"ease-in-out-quad":Ne(.455,.03,.515,.955),"ease-in-cubic":Ne(.55,.055,.675,.19),"ease-out-cubic":Ne(.215,.61,.355,1),"ease-in-out-cubic":Ne(.645,.045,.355,1),"ease-in-quart":Ne(.895,.03,.685,.22),"ease-out-quart":Ne(.165,.84,.44,1),"ease-in-out-quart":Ne(.77,0,.175,1),"ease-in-quint":Ne(.755,.05,.855,.06),"ease-out-quint":Ne(.23,1,.32,1),"ease-in-out-quint":Ne(.86,0,.07,1),"ease-in-expo":Ne(.95,.05,.795,.035),"ease-out-expo":Ne(.19,1,.22,1),"ease-in-out-expo":Ne(1,0,0,1),"ease-in-circ":Ne(.6,.04,.98,.335),"ease-out-circ":Ne(.075,.82,.165,1),"ease-in-out-circ":Ne(.785,.135,.15,.86),spring:function(e,r,a){if(a===0)return tn.linear;var n=Xg(e,r,a);return function(i,s,o){return i+(s-i)*n(o)}},"cubic-bezier":Ne};function rl(t,e,r,a,n){if(a===1||e===r)return r;var i=n(e,r,a);return t==null||((t.roundValue||t.color)&&(i=Math.round(i)),t.min!==void 0&&(i=Math.max(i,t.min)),t.max!==void 0&&(i=Math.min(i,t.max))),i}function al(t,e){return t.pfValue!=null||t.value!=null?t.pfValue!=null&&(e==null||e.type.units!=="%")?t.pfValue:t.value:t}function Dr(t,e,r,a,n){var i=n!=null?n.type:null;r<0?r=0:r>1&&(r=1);var s=al(t,n),o=al(e,n);if(ae(s)&&ae(o))return rl(i,s,o,r,a);if(Le(s)&&Le(o)){for(var l=[],u=0;u<o.length;u++){var v=s[u],f=o[u];if(v!=null&&f!=null){var c=rl(i,v,f,r,a);l.push(c)}else l.push(f)}return l}}function Zg(t,e,r,a){var n=!a,i=t._private,s=e._private,o=s.easing,l=s.startTime,u=a?t:t.cy(),v=u.style();if(!s.easingImpl)if(o==null)s.easingImpl=tn.linear;else{var f;if(fe(o)){var c=v.parse("transition-timing-function",o);f=c.value}else f=o;var h,d;fe(f)?(h=f,d=[]):(h=f[1],d=f.slice(2).map(function(_){return+_})),d.length>0?(h==="spring"&&d.push(s.duration),s.easingImpl=tn[h].apply(null,d)):s.easingImpl=tn[h]}var y=s.easingImpl,g;if(s.duration===0?g=1:g=(r-l)/s.duration,s.applying&&(g=s.progress),g<0?g=0:g>1&&(g=1),s.delay==null){var p=s.startPosition,m=s.position;if(m&&n&&!t.locked()){var b={};aa(p.x,m.x)&&(b.x=Dr(p.x,m.x,g,y)),aa(p.y,m.y)&&(b.y=Dr(p.y,m.y,g,y)),t.position(b)}var w=s.startPan,E=s.pan,C=i.pan,x=E!=null&&a;x&&(aa(w.x,E.x)&&(C.x=Dr(w.x,E.x,g,y)),aa(w.y,E.y)&&(C.y=Dr(w.y,E.y,g,y)),t.emit("pan"));var S=s.startZoom,k=s.zoom,B=k!=null&&a;B&&(aa(S,k)&&(i.zoom=pa(i.minZoom,Dr(S,k,g,y),i.maxZoom)),t.emit("zoom")),(x||B)&&t.emit("viewport");var D=s.style;if(D&&D.length>0&&n){for(var A=0;A<D.length;A++){var P=D[A],R=P.name,L=P,I=s.startStyle[R],M=v.properties[I.name],O=Dr(I,L,g,y,M);v.overrideBypass(t,R,O)}t.emit("style")}}return s.progress=g,g}function aa(t,e){return t==null||e==null?!1:ae(t)&&ae(e)?!0:!!(t&&e)}function Qg(t,e,r,a){var n=e._private;n.started=!0,n.startTime=r-n.progress*n.duration}function nl(t,e){var r=e._private.aniEles,a=[];function n(v,f){var c=v._private,h=c.animation.current,d=c.animation.queue,y=!1;if(h.length===0){var g=d.shift();g&&h.push(g)}for(var p=function(C){for(var x=C.length-1;x>=0;x--){var S=C[x];S()}C.splice(0,C.length)},m=h.length-1;m>=0;m--){var b=h[m],w=b._private;if(w.stopped){h.splice(m,1),w.hooked=!1,w.playing=!1,w.started=!1,p(w.frames);continue}!w.playing&&!w.applying||(w.playing&&w.applying&&(w.applying=!1),w.started||Qg(v,b,t),Zg(v,b,t,f),w.applying&&(w.applying=!1),p(w.frames),w.step!=null&&w.step(t),b.completed()&&(h.splice(m,1),w.hooked=!1,w.playing=!1,w.started=!1,p(w.completes)),y=!0)}return!f&&h.length===0&&d.length===0&&a.push(v),y}for(var i=!1,s=0;s<r.length;s++){var o=r[s],l=n(o);i=i||l}var u=n(e,!0);(i||u)&&(r.length>0?e.notify("draw",r):e.notify("draw")),r.unmerge(a),e.emit("step")}var Jg={animate:Ae.animate(),animation:Ae.animation(),animated:Ae.animated(),clearQueue:Ae.clearQueue(),delay:Ae.delay(),delayAnimation:Ae.delayAnimation(),stop:Ae.stop(),addToAnimationPool:function(e){var r=this;r.styleEnabled()&&r._private.aniEles.merge(e)},stopAnimationLoop:function(){this._private.animationsRunning=!1},startAnimationLoop:function(){var e=this;if(e._private.animationsRunning=!0,!e.styleEnabled())return;function r(){e._private.animationsRunning&&ln(function(i){nl(i,e),r()})}var a=e.renderer();a&&a.beforeRender?a.beforeRender(function(i,s){nl(s,e)},a.beforeRenderPriorities.animations):r()}},jg={qualifierCompare:function(e,r){return e==null||r==null?e==null&&r==null:e.sameText(r)},eventMatches:function(e,r,a){var n=r.qualifier;return n!=null?e!==a.target&&Ta(a.target)&&n.matches(a.target):!0},addEventFields:function(e,r){r.cy=e,r.target=e},callbackContext:function(e,r,a){return r.qualifier!=null?a.target:e}},Wa=function(e){return fe(e)?new nr(e):e},zv={createEmitter:function(){var e=this._private;return e.emitter||(e.emitter=new Rn(jg,this)),this},emitter:function(){return this._private.emitter},on:function(e,r,a){return this.emitter().on(e,Wa(r),a),this},removeListener:function(e,r,a){return this.emitter().removeListener(e,Wa(r),a),this},removeAllListeners:function(){return this.emitter().removeAllListeners(),this},one:function(e,r,a){return this.emitter().one(e,Wa(r),a),this},once:function(e,r,a){return this.emitter().one(e,Wa(r),a),this},emit:function(e,r){return this.emitter().emit(e,r),this},emitAndNotify:function(e,r){return this.emit(e),this.notify(e,r),this}};Ae.eventAliasesOn(zv);var xs={png:function(e){var r=this._private.renderer;return e=e||{},r.png(e)},jpg:function(e){var r=this._private.renderer;return e=e||{},e.bg=e.bg||"#fff",r.jpg(e)}};xs.jpeg=xs.jpg;var rn={layout:function(e){var r=this;if(e==null){Ve("Layout options must be specified to make a layout");return}if(e.name==null){Ve("A `name` must be specified to make a layout");return}var a=e.name,n=r.extension("layout",a);if(n==null){Ve("No such layout `"+a+"` found.  Did you forget to import it and `cytoscape.use()` it?");return}var i;fe(e.eles)?i=r.$(e.eles):i=e.eles!=null?e.eles:r.$();var s=new n(ge({},e,{cy:r,eles:i}));return s}};rn.createLayout=rn.makeLayout=rn.layout;var ep={notify:function(e,r){var a=this._private;if(this.batching()){a.batchNotifications=a.batchNotifications||{};var n=a.batchNotifications[e]=a.batchNotifications[e]||this.collection();r!=null&&n.merge(r);return}if(a.notificationsEnabled){var i=this.renderer();this.destroyed()||!i||i.notify(e,r)}},notifications:function(e){var r=this._private;return e===void 0?r.notificationsEnabled:(r.notificationsEnabled=!!e,this)},noNotifications:function(e){this.notifications(!1),e(),this.notifications(!0)},batching:function(){return this._private.batchCount>0},startBatch:function(){var e=this._private;return e.batchCount==null&&(e.batchCount=0),e.batchCount===0&&(e.batchStyleEles=this.collection(),e.batchNotifications={}),e.batchCount++,this},endBatch:function(){var e=this._private;if(e.batchCount===0)return this;if(e.batchCount--,e.batchCount===0){e.batchStyleEles.updateStyle();var r=this.renderer();Object.keys(e.batchNotifications).forEach(function(a){var n=e.batchNotifications[a];n.empty()?r.notify(a):r.notify(a,n)})}return this},batch:function(e){return this.startBatch(),e(),this.endBatch(),this},batchData:function(e){var r=this;return this.batch(function(){for(var a=Object.keys(e),n=0;n<a.length;n++){var i=a[n],s=e[i],o=r.getElementById(i);o.data(s)}})}},tp=Ue({hideEdgesOnViewport:!1,textureOnViewport:!1,motionBlur:!1,motionBlurOpacity:.05,pixelRatio:void 0,desktopTapThreshold:4,touchTapThreshold:8,wheelSensitivity:1,debug:!1,showFps:!1,webgl:!1,webglDebug:!1,webglDebugShowAtlases:!1,webglTexSize:2048,webglTexRows:36,webglTexRowsNodes:18,webglBatchSize:2048,webglTexPerBatch:14,webglBgColor:[255,255,255]}),Es={renderTo:function(e,r,a,n){var i=this._private.renderer;return i.renderTo(e,r,a,n),this},renderer:function(){return this._private.renderer},forceRender:function(){return this.notify("draw"),this},resize:function(){return this.invalidateSize(),this.emitAndNotify("resize"),this},initRenderer:function(e){var r=this,a=r.extension("renderer",e.name);if(a==null){Ve("Can not initialise: No such renderer `".concat(e.name,"` found. Did you forget to import it and `cytoscape.use()` it?"));return}e.wheelSensitivity!==void 0&&Re("You have set a custom wheel sensitivity.  This will make your app zoom unnaturally when using mainstream mice.  You should change this value from the default only if you can guarantee that all your users will use the same hardware and OS configuration as your current machine.");var n=tp(e);n.cy=r,r._private.renderer=new a(n),this.notify("init")},destroyRenderer:function(){var e=this;e.notify("destroy");var r=e.container();if(r)for(r._cyreg=null;r.childNodes.length>0;)r.removeChild(r.childNodes[0]);e._private.renderer=null,e.mutableElements().forEach(function(a){var n=a._private;n.rscratch={},n.rstyle={},n.animation.current=[],n.animation.queue=[]})},onRender:function(e){return this.on("render",e)},offRender:function(e){return this.off("render",e)}};Es.invalidateDimensions=Es.resize;var an={collection:function(e,r){return fe(e)?this.$(e):bt(e)?e.collection():Le(e)?(r||(r={}),new nt(this,e,r.unique,r.removed)):new nt(this)},nodes:function(e){var r=this.$(function(a){return a.isNode()});return e?r.filter(e):r},edges:function(e){var r=this.$(function(a){return a.isEdge()});return e?r.filter(e):r},$:function(e){var r=this._private.elements;return e?r.filter(e):r.spawnSelf()},mutableElements:function(){return this._private.elements}};an.elements=an.filter=an.$;var tt={},va="t",rp="f";tt.apply=function(t){for(var e=this,r=e._private,a=r.cy,n=a.collection(),i=0;i<t.length;i++){var s=t[i],o=e.getContextMeta(s);if(!o.empty){var l=e.getContextStyle(o),u=e.applyContextStyle(o,l,s);s._private.appliedInitStyle?e.updateTransitions(s,u.diffProps):s._private.appliedInitStyle=!0;var v=e.updateStyleHints(s);v&&n.push(s)}}return n};tt.getPropertiesDiff=function(t,e){var r=this,a=r._private.propDiffs=r._private.propDiffs||{},n=t+"-"+e,i=a[n];if(i)return i;for(var s=[],o={},l=0;l<r.length;l++){var u=r[l],v=t[l]===va,f=e[l]===va,c=v!==f,h=u.mappedProperties.length>0;if(c||f&&h){var d=void 0;c&&h||c?d=u.properties:h&&(d=u.mappedProperties);for(var y=0;y<d.length;y++){for(var g=d[y],p=g.name,m=!1,b=l+1;b<r.length;b++){var w=r[b],E=e[b]===va;if(E&&(m=w.properties[g.name]!=null,m))break}!o[p]&&!m&&(o[p]=!0,s.push(p))}}}return a[n]=s,s};tt.getContextMeta=function(t){for(var e=this,r="",a,n=t._private.styleCxtKey||"",i=0;i<e.length;i++){var s=e[i],o=s.selector&&s.selector.matches(t);o?r+=va:r+=rp}return a=e.getPropertiesDiff(n,r),t._private.styleCxtKey=r,{key:r,diffPropNames:a,empty:a.length===0}};tt.getContextStyle=function(t){var e=t.key,r=this,a=this._private.contextStyles=this._private.contextStyles||{};if(a[e])return a[e];for(var n={_private:{key:e}},i=0;i<r.length;i++){var s=r[i],o=e[i]===va;if(o)for(var l=0;l<s.properties.length;l++){var u=s.properties[l];n[u.name]=u}}return a[e]=n,n};tt.applyContextStyle=function(t,e,r){for(var a=this,n=t.diffPropNames,i={},s=a.types,o=0;o<n.length;o++){var l=n[o],u=e[l],v=r.pstyle(l);if(!u)if(v)v.bypass?u={name:l,deleteBypassed:!0}:u={name:l,delete:!0};else continue;if(v!==u){if(u.mapped===s.fn&&v!=null&&v.mapping!=null&&v.mapping.value===u.value){var f=v.mapping,c=f.fnValue=u.value(r);if(c===f.prevFnValue)continue}var h=i[l]={prev:v};a.applyParsedProperty(r,u),h.next=r.pstyle(l),h.next&&h.next.bypass&&(h.next=h.next.bypassed)}}return{diffProps:i}};tt.updateStyleHints=function(t){var e=t._private,r=this,a=r.propertyGroupNames,n=r.propertyGroupKeys,i=function($,J,re){return r.getPropertiesHash($,J,re)},s=e.styleKey;if(t.removed())return!1;var o=e.group==="nodes",l=t._private.style;a=Object.keys(l);for(var u=0;u<n.length;u++){var v=n[u];e.styleKeys[v]=[Mr,sa]}for(var f=function($,J){return e.styleKeys[J][0]=da($,e.styleKeys[J][0])},c=function($,J){return e.styleKeys[J][1]=ha($,e.styleKeys[J][1])},h=function($,J){f($,J),c($,J)},d=function($,J){for(var re=0;re<$.length;re++){var le=$.charCodeAt(re);f(le,J),c(le,J)}},y=2e9,g=function($){return-128<$&&$<128&&Math.floor($)!==$?y-($*1024|0):$},p=0;p<a.length;p++){var m=a[p],b=l[m];if(b!=null){var w=this.properties[m],E=w.type,C=w.groupKey,x=void 0;w.hashOverride!=null?x=w.hashOverride(t,b):b.pfValue!=null&&(x=b.pfValue);var S=w.enums==null?b.value:null,k=x!=null,B=S!=null,D=k||B,A=b.units;if(E.number&&D&&!E.multiple){var P=k?x:S;h(g(P),C),!k&&A!=null&&d(A,C)}else d(b.strValue,C)}}for(var R=[Mr,sa],L=0;L<n.length;L++){var I=n[L],M=e.styleKeys[I];R[0]=da(M[0],R[0]),R[1]=ha(M[1],R[1])}e.styleKey=Cc(R[0],R[1]);var O=e.styleKeys;e.labelDimsKey=Xt(O.labelDimensions);var _=i(t,["label"],O.labelDimensions);if(e.labelKey=Xt(_),e.labelStyleKey=Xt(za(O.commonLabel,_)),!o){var H=i(t,["source-label"],O.labelDimensions);e.sourceLabelKey=Xt(H),e.sourceLabelStyleKey=Xt(za(O.commonLabel,H));var F=i(t,["target-label"],O.labelDimensions);e.targetLabelKey=Xt(F),e.targetLabelStyleKey=Xt(za(O.commonLabel,F))}if(o){var G=e.styleKeys,U=G.nodeBody,X=G.nodeBorder,Z=G.nodeOutline,Q=G.backgroundImage,ee=G.compound,te=G.pie,K=[U,X,Z,Q,ee,te].filter(function(N){return N!=null}).reduce(za,[Mr,sa]);e.nodeKey=Xt(K),e.hasPie=te!=null&&te[0]!==Mr&&te[1]!==sa}return s!==e.styleKey};tt.clearStyleHints=function(t){var e=t._private;e.styleCxtKey="",e.styleKeys={},e.styleKey=null,e.labelKey=null,e.labelStyleKey=null,e.sourceLabelKey=null,e.sourceLabelStyleKey=null,e.targetLabelKey=null,e.targetLabelStyleKey=null,e.nodeKey=null,e.hasPie=null};tt.applyParsedProperty=function(t,e){var r=this,a=e,n=t._private.style,i,s=r.types,o=r.properties[a.name].type,l=a.bypass,u=n[a.name],v=u&&u.bypass,f=t._private,c="mapping",h=function(U){return U==null?null:U.pfValue!=null?U.pfValue:U.value},d=function(){var U=h(u),X=h(a);r.checkTriggers(t,a.name,U,X)};if(e.name==="curve-style"&&t.isEdge()&&(e.value!=="bezier"&&t.isLoop()||e.value==="haystack"&&(t.source().isParent()||t.target().isParent()))&&(a=e=this.parse(e.name,"bezier",l)),a.delete)return n[a.name]=void 0,d(),!0;if(a.deleteBypassed)return u?u.bypass?(u.bypassed=void 0,d(),!0):!1:(d(),!0);if(a.deleteBypass)return u?u.bypass?(n[a.name]=u.bypassed,d(),!0):!1:(d(),!0);var y=function(){Re("Do not assign mappings to elements without corresponding data (i.e. ele `"+t.id()+"` has no mapping for property `"+a.name+"` with data field `"+a.field+"`); try a `["+a.field+"]` selector to limit scope to elements with `"+a.field+"` defined")};switch(a.mapped){case s.mapData:{for(var g=a.field.split("."),p=f.data,m=0;m<g.length&&p;m++){var b=g[m];p=p[b]}if(p==null)return y(),!1;var w;if(ae(p)){var E=a.fieldMax-a.fieldMin;E===0?w=0:w=(p-a.fieldMin)/E}else return Re("Do not use continuous mappers without specifying numeric data (i.e. `"+a.field+": "+p+"` for `"+t.id()+"` is non-numeric)"),!1;if(w<0?w=0:w>1&&(w=1),o.color){var C=a.valueMin[0],x=a.valueMax[0],S=a.valueMin[1],k=a.valueMax[1],B=a.valueMin[2],D=a.valueMax[2],A=a.valueMin[3]==null?1:a.valueMin[3],P=a.valueMax[3]==null?1:a.valueMax[3],R=[Math.round(C+(x-C)*w),Math.round(S+(k-S)*w),Math.round(B+(D-B)*w),Math.round(A+(P-A)*w)];i={bypass:a.bypass,name:a.name,value:R,strValue:"rgb("+R[0]+", "+R[1]+", "+R[2]+")"}}else if(o.number){var L=a.valueMin+(a.valueMax-a.valueMin)*w;i=this.parse(a.name,L,a.bypass,c)}else return!1;if(!i)return y(),!1;i.mapping=a,a=i;break}case s.data:{for(var I=a.field.split("."),M=f.data,O=0;O<I.length&&M;O++){var _=I[O];M=M[_]}if(M!=null&&(i=this.parse(a.name,M,a.bypass,c)),!i)return y(),!1;i.mapping=a,a=i;break}case s.fn:{var H=a.value,F=a.fnValue!=null?a.fnValue:H(t);if(a.prevFnValue=F,F==null)return Re("Custom function mappers may not return null (i.e. `"+a.name+"` for ele `"+t.id()+"` is null)"),!1;if(i=this.parse(a.name,F,a.bypass,c),!i)return Re("Custom function mappers may not return invalid values for the property type (i.e. `"+a.name+"` for ele `"+t.id()+"` is invalid)"),!1;i.mapping=Nt(a),a=i;break}case void 0:break;default:return!1}return l?(v?a.bypassed=u.bypassed:a.bypassed=u,n[a.name]=a):v?u.bypassed=a:n[a.name]=a,d(),!0};tt.cleanElements=function(t,e){for(var r=0;r<t.length;r++){var a=t[r];if(this.clearStyleHints(a),a.dirtyCompoundBoundsCache(),a.dirtyBoundingBoxCache(),!e)a._private.style={};else for(var n=a._private.style,i=Object.keys(n),s=0;s<i.length;s++){var o=i[s],l=n[o];l!=null&&(l.bypass?l.bypassed=null:n[o]=null)}}};tt.update=function(){var t=this._private.cy,e=t.mutableElements();e.updateStyle()};tt.updateTransitions=function(t,e){var r=this,a=t._private,n=t.pstyle("transition-property").value,i=t.pstyle("transition-duration").pfValue,s=t.pstyle("transition-delay").pfValue;if(n.length>0&&i>0){for(var o={},l=!1,u=0;u<n.length;u++){var v=n[u],f=t.pstyle(v),c=e[v];if(c){var h=c.prev,d=h,y=c.next!=null?c.next:f,g=!1,p=void 0,m=1e-6;d&&(ae(d.pfValue)&&ae(y.pfValue)?(g=y.pfValue-d.pfValue,p=d.pfValue+m*g):ae(d.value)&&ae(y.value)?(g=y.value-d.value,p=d.value+m*g):Le(d.value)&&Le(y.value)&&(g=d.value[0]!==y.value[0]||d.value[1]!==y.value[1]||d.value[2]!==y.value[2],p=d.strValue),g&&(o[v]=y.strValue,this.applyBypass(t,v,p),l=!0))}}if(!l)return;a.transitioning=!0,new Wr(function(b){s>0?t.delayAnimation(s).play().promise().then(b):b()}).then(function(){return t.animation({style:o,duration:i,easing:t.pstyle("transition-timing-function").value,queue:!1}).play().promise()}).then(function(){r.removeBypasses(t,n),t.emitAndNotify("style"),a.transitioning=!1})}else a.transitioning&&(this.removeBypasses(t,n),t.emitAndNotify("style"),a.transitioning=!1)};tt.checkTrigger=function(t,e,r,a,n,i){var s=this.properties[e],o=n(s);t.removed()||o!=null&&o(r,a,t)&&i(s)};tt.checkZOrderTrigger=function(t,e,r,a){var n=this;this.checkTrigger(t,e,r,a,function(i){return i.triggersZOrder},function(){n._private.cy.notify("zorder",t)})};tt.checkBoundsTrigger=function(t,e,r,a){this.checkTrigger(t,e,r,a,function(n){return n.triggersBounds},function(n){t.dirtyCompoundBoundsCache(),t.dirtyBoundingBoxCache()})};tt.checkConnectedEdgesBoundsTrigger=function(t,e,r,a){this.checkTrigger(t,e,r,a,function(n){return n.triggersBoundsOfConnectedEdges},function(n){t.connectedEdges().forEach(function(i){i.dirtyBoundingBoxCache()})})};tt.checkParallelEdgesBoundsTrigger=function(t,e,r,a){this.checkTrigger(t,e,r,a,function(n){return n.triggersBoundsOfParallelEdges},function(n){t.parallelEdges().forEach(function(i){i.dirtyBoundingBoxCache()})})};tt.checkTriggers=function(t,e,r,a){t.dirtyStyleCache(),this.checkZOrderTrigger(t,e,r,a),this.checkBoundsTrigger(t,e,r,a),this.checkConnectedEdgesBoundsTrigger(t,e,r,a),this.checkParallelEdgesBoundsTrigger(t,e,r,a)};var Ra={};Ra.applyBypass=function(t,e,r,a){var n=this,i=[],s=!0;if(e==="*"||e==="**"){if(r!==void 0)for(var o=0;o<n.properties.length;o++){var l=n.properties[o],u=l.name,v=this.parse(u,r,!0);v&&i.push(v)}}else if(fe(e)){var f=this.parse(e,r,!0);f&&i.push(f)}else if(ke(e)){var c=e;a=r;for(var h=Object.keys(c),d=0;d<h.length;d++){var y=h[d],g=c[y];if(g===void 0&&(g=c[xn(y)]),g!==void 0){var p=this.parse(y,g,!0);p&&i.push(p)}}}else return!1;if(i.length===0)return!1;for(var m=!1,b=0;b<t.length;b++){for(var w=t[b],E={},C=void 0,x=0;x<i.length;x++){var S=i[x];if(a){var k=w.pstyle(S.name);C=E[S.name]={prev:k}}m=this.applyParsedProperty(w,Nt(S))||m,a&&(C.next=w.pstyle(S.name))}m&&this.updateStyleHints(w),a&&this.updateTransitions(w,E,s)}return m};Ra.overrideBypass=function(t,e,r){e=Ls(e);for(var a=0;a<t.length;a++){var n=t[a],i=n._private.style[e],s=this.properties[e].type,o=s.color,l=s.mutiple,u=i?i.pfValue!=null?i.pfValue:i.value:null;!i||!i.bypass?this.applyBypass(n,e,r):(i.value=r,i.pfValue!=null&&(i.pfValue=r),o?i.strValue="rgb("+r.join(",")+")":l?i.strValue=r.join(" "):i.strValue=""+r,this.updateStyleHints(n)),this.checkTriggers(n,e,u,r)}};Ra.removeAllBypasses=function(t,e){return this.removeBypasses(t,this.propertyNames,e)};Ra.removeBypasses=function(t,e,r){for(var a=!0,n=0;n<t.length;n++){for(var i=t[n],s={},o=0;o<e.length;o++){var l=e[o],u=this.properties[l],v=i.pstyle(u.name);if(!(!v||!v.bypass)){var f="",c=this.parse(l,f,!0),h=s[u.name]={prev:v};this.applyParsedProperty(i,c),h.next=i.pstyle(u.name)}}this.updateStyleHints(i),r&&this.updateTransitions(i,s,a)}};var Us={};Us.getEmSizeInPixels=function(){var t=this.containerCss("font-size");return t!=null?parseFloat(t):1};Us.containerCss=function(t){var e=this._private.cy,r=e.container(),a=e.window();if(a&&r&&a.getComputedStyle)return a.getComputedStyle(r).getPropertyValue(t)};var Ft={};Ft.getRenderedStyle=function(t,e){return e?this.getStylePropertyValue(t,e,!0):this.getRawStyle(t,!0)};Ft.getRawStyle=function(t,e){var r=this;if(t=t[0],t){for(var a={},n=0;n<r.properties.length;n++){var i=r.properties[n],s=r.getStylePropertyValue(t,i.name,e);s!=null&&(a[i.name]=s,a[xn(i.name)]=s)}return a}};Ft.getIndexedStyle=function(t,e,r,a){var n=t.pstyle(e)[r][a];return n??t.cy().style().getDefaultProperty(e)[r][0]};Ft.getStylePropertyValue=function(t,e,r){var a=this;if(t=t[0],t){var n=a.properties[e];n.alias&&(n=n.pointsTo);var i=n.type,s=t.pstyle(n.name);if(s){var o=s.value,l=s.units,u=s.strValue;if(r&&i.number&&o!=null&&ae(o)){var v=t.cy().zoom(),f=function(g){return g*v},c=function(g,p){return f(g)+p},h=Le(o),d=h?l.every(function(y){return y!=null}):l!=null;return d?h?o.map(function(y,g){return c(y,l[g])}).join(" "):c(o,l):h?o.map(function(y){return fe(y)?y:""+f(y)}).join(" "):""+f(o)}else if(u!=null)return u}return null}};Ft.getAnimationStartStyle=function(t,e){for(var r={},a=0;a<e.length;a++){var n=e[a],i=n.name,s=t.pstyle(i);s!==void 0&&(ke(s)?s=this.parse(i,s.strValue):s=this.parse(i,s)),s&&(r[i]=s)}return r};Ft.getPropsList=function(t){var e=this,r=[],a=t,n=e.properties;if(a)for(var i=Object.keys(a),s=0;s<i.length;s++){var o=i[s],l=a[o],u=n[o]||n[Ls(o)],v=this.parse(u.name,l);v&&r.push(v)}return r};Ft.getNonDefaultPropertiesHash=function(t,e,r){var a=r.slice(),n,i,s,o,l,u;for(l=0;l<e.length;l++)if(n=e[l],i=t.pstyle(n,!1),i!=null)if(i.pfValue!=null)a[0]=da(o,a[0]),a[1]=ha(o,a[1]);else for(s=i.strValue,u=0;u<s.length;u++)o=s.charCodeAt(u),a[0]=da(o,a[0]),a[1]=ha(o,a[1]);return a};Ft.getPropertiesHash=Ft.getNonDefaultPropertiesHash;var In={};In.appendFromJson=function(t){for(var e=this,r=0;r<t.length;r++){var a=t[r],n=a.selector,i=a.style||a.css,s=Object.keys(i);e.selector(n);for(var o=0;o<s.length;o++){var l=s[o],u=i[l];e.css(l,u)}}return e};In.fromJson=function(t){var e=this;return e.resetToDefault(),e.appendFromJson(t),e};In.json=function(){for(var t=[],e=this.defaultLength;e<this.length;e++){for(var r=this[e],a=r.selector,n=r.properties,i={},s=0;s<n.length;s++){var o=n[s];i[o.name]=o.strValue}t.push({selector:a?a.toString():"core",style:i})}return t};var Ys={};Ys.appendFromString=function(t){var e=this,r=this,a=""+t,n,i,s;a=a.replace(/[/][*](\s|.)+?[*][/]/g,"");function o(){a.length>n.length?a=a.substr(n.length):a=""}function l(){i.length>s.length?i=i.substr(s.length):i=""}for(;;){var u=a.match(/^\s*$/);if(u)break;var v=a.match(/^\s*((?:.|\s)+?)\s*\{((?:.|\s)+?)\}/);if(!v){Re("Halting stylesheet parsing: String stylesheet contains more to parse but no selector and block found in: "+a);break}n=v[0];var f=v[1];if(f!=="core"){var c=new nr(f);if(c.invalid){Re("Skipping parsing of block: Invalid selector found in string stylesheet: "+f),o();continue}}var h=v[2],d=!1;i=h;for(var y=[];;){var g=i.match(/^\s*$/);if(g)break;var p=i.match(/^\s*(.+?)\s*:\s*(.+?)(?:\s*;|\s*$)/);if(!p){Re("Skipping parsing of block: Invalid formatting of style property and value definitions found in:"+h),d=!0;break}s=p[0];var m=p[1],b=p[2],w=e.properties[m];if(!w){Re("Skipping property: Invalid property name in: "+s),l();continue}var E=r.parse(m,b);if(!E){Re("Skipping property: Invalid property definition in: "+s),l();continue}y.push({name:m,val:b}),l()}if(d){o();break}r.selector(f);for(var C=0;C<y.length;C++){var x=y[C];r.css(x.name,x.val)}o()}return r};Ys.fromString=function(t){var e=this;return e.resetToDefault(),e.appendFromString(t),e};var rt={};(function(){var t=$e,e=tc,r=ac,a=nc,n=ic,i=function(K){return"^"+K+"\\s*\\(\\s*([\\w\\.]+)\\s*\\)$"},s=function(K){var N=t+"|\\w+|"+e+"|"+r+"|"+a+"|"+n;return"^"+K+"\\s*\\(([\\w\\.]+)\\s*\\,\\s*("+t+")\\s*\\,\\s*("+t+")\\s*,\\s*("+N+")\\s*\\,\\s*("+N+")\\)$"},o=[`^url\\s*\\(\\s*['"]?(.+?)['"]?\\s*\\)$`,"^(none)$","^(.+)$"];rt.types={time:{number:!0,min:0,units:"s|ms",implicitUnits:"ms"},percent:{number:!0,min:0,max:100,units:"%",implicitUnits:"%"},percentages:{number:!0,min:0,max:100,units:"%",implicitUnits:"%",multiple:!0},zeroOneNumber:{number:!0,min:0,max:1,unitless:!0},zeroOneNumbers:{number:!0,min:0,max:1,unitless:!0,multiple:!0},nOneOneNumber:{number:!0,min:-1,max:1,unitless:!0},nonNegativeInt:{number:!0,min:0,integer:!0,unitless:!0},nonNegativeNumber:{number:!0,min:0,unitless:!0},position:{enums:["parent","origin"]},nodeSize:{number:!0,min:0,enums:["label"]},number:{number:!0,unitless:!0},numbers:{number:!0,unitless:!0,multiple:!0},positiveNumber:{number:!0,unitless:!0,min:0,strictMin:!0},size:{number:!0,min:0},bidirectionalSize:{number:!0},bidirectionalSizeMaybePercent:{number:!0,allowPercent:!0},bidirectionalSizes:{number:!0,multiple:!0},sizeMaybePercent:{number:!0,min:0,allowPercent:!0},axisDirection:{enums:["horizontal","leftward","rightward","vertical","upward","downward","auto"]},paddingRelativeTo:{enums:["width","height","average","min","max"]},bgWH:{number:!0,min:0,allowPercent:!0,enums:["auto"],multiple:!0},bgPos:{number:!0,allowPercent:!0,multiple:!0},bgRelativeTo:{enums:["inner","include-padding"],multiple:!0},bgRepeat:{enums:["repeat","repeat-x","repeat-y","no-repeat"],multiple:!0},bgFit:{enums:["none","contain","cover"],multiple:!0},bgCrossOrigin:{enums:["anonymous","use-credentials","null"],multiple:!0},bgClip:{enums:["none","node"],multiple:!0},bgContainment:{enums:["inside","over"],multiple:!0},color:{color:!0},colors:{color:!0,multiple:!0},fill:{enums:["solid","linear-gradient","radial-gradient"]},bool:{enums:["yes","no"]},bools:{enums:["yes","no"],multiple:!0},lineStyle:{enums:["solid","dotted","dashed"]},lineCap:{enums:["butt","round","square"]},linePosition:{enums:["center","inside","outside"]},lineJoin:{enums:["round","bevel","miter"]},borderStyle:{enums:["solid","dotted","dashed","double"]},curveStyle:{enums:["bezier","unbundled-bezier","haystack","segments","straight","straight-triangle","taxi","round-segments","round-taxi"]},radiusType:{enums:["arc-radius","influence-radius"],multiple:!0},fontFamily:{regex:'^([\\w- \\"]+(?:\\s*,\\s*[\\w- \\"]+)*)$'},fontStyle:{enums:["italic","normal","oblique"]},fontWeight:{enums:["normal","bold","bolder","lighter","100","200","300","400","500","600","800","900",100,200,300,400,500,600,700,800,900]},textDecoration:{enums:["none","underline","overline","line-through"]},textTransform:{enums:["none","uppercase","lowercase"]},textWrap:{enums:["none","wrap","ellipsis"]},textOverflowWrap:{enums:["whitespace","anywhere"]},textBackgroundShape:{enums:["rectangle","roundrectangle","round-rectangle"]},nodeShape:{enums:["rectangle","roundrectangle","round-rectangle","cutrectangle","cut-rectangle","bottomroundrectangle","bottom-round-rectangle","barrel","ellipse","triangle","round-triangle","square","pentagon","round-pentagon","hexagon","round-hexagon","concavehexagon","concave-hexagon","heptagon","round-heptagon","octagon","round-octagon","tag","round-tag","star","diamond","round-diamond","vee","rhomboid","right-rhomboid","polygon"]},overlayShape:{enums:["roundrectangle","round-rectangle","ellipse"]},cornerRadius:{number:!0,min:0,units:"px|em",implicitUnits:"px",enums:["auto"]},compoundIncludeLabels:{enums:["include","exclude"]},arrowShape:{enums:["tee","triangle","triangle-tee","circle-triangle","triangle-cross","triangle-backcurve","vee","square","circle","diamond","chevron","none"]},arrowFill:{enums:["filled","hollow"]},arrowWidth:{number:!0,units:"%|px|em",implicitUnits:"px",enums:["match-line"]},display:{enums:["element","none"]},visibility:{enums:["hidden","visible"]},zCompoundDepth:{enums:["bottom","orphan","auto","top"]},zIndexCompare:{enums:["auto","manual"]},valign:{enums:["top","center","bottom"]},halign:{enums:["left","center","right"]},justification:{enums:["left","center","right","auto"]},text:{string:!0},data:{mapping:!0,regex:i("data")},layoutData:{mapping:!0,regex:i("layoutData")},scratch:{mapping:!0,regex:i("scratch")},mapData:{mapping:!0,regex:s("mapData")},mapLayoutData:{mapping:!0,regex:s("mapLayoutData")},mapScratch:{mapping:!0,regex:s("mapScratch")},fn:{mapping:!0,fn:!0},url:{regexes:o,singleRegexMatchValue:!0},urls:{regexes:o,singleRegexMatchValue:!0,multiple:!0},propList:{propList:!0},angle:{number:!0,units:"deg|rad",implicitUnits:"rad"},textRotation:{number:!0,units:"deg|rad",implicitUnits:"rad",enums:["none","autorotate"]},polygonPointList:{number:!0,multiple:!0,evenMultiple:!0,min:-1,max:1,unitless:!0},edgeDistances:{enums:["intersection","node-position","endpoints"]},edgeEndpoint:{number:!0,multiple:!0,units:"%|px|em|deg|rad",implicitUnits:"px",enums:["inside-to-node","outside-to-node","outside-to-node-or-label","outside-to-line","outside-to-line-or-label"],singleEnum:!0,validate:function(K,N){switch(K.length){case 2:return N[0]!=="deg"&&N[0]!=="rad"&&N[1]!=="deg"&&N[1]!=="rad";case 1:return fe(K[0])||N[0]==="deg"||N[0]==="rad";default:return!1}}},easing:{regexes:["^(spring)\\s*\\(\\s*("+t+")\\s*,\\s*("+t+")\\s*\\)$","^(cubic-bezier)\\s*\\(\\s*("+t+")\\s*,\\s*("+t+")\\s*,\\s*("+t+")\\s*,\\s*("+t+")\\s*\\)$"],enums:["linear","ease","ease-in","ease-out","ease-in-out","ease-in-sine","ease-out-sine","ease-in-out-sine","ease-in-quad","ease-out-quad","ease-in-out-quad","ease-in-cubic","ease-out-cubic","ease-in-out-cubic","ease-in-quart","ease-out-quart","ease-in-out-quart","ease-in-quint","ease-out-quint","ease-in-out-quint","ease-in-expo","ease-out-expo","ease-in-out-expo","ease-in-circ","ease-out-circ","ease-in-out-circ"]},gradientDirection:{enums:["to-bottom","to-top","to-left","to-right","to-bottom-right","to-bottom-left","to-top-right","to-top-left","to-right-bottom","to-left-bottom","to-right-top","to-left-top"]},boundsExpansion:{number:!0,multiple:!0,min:0,validate:function(K){var N=K.length;return N===1||N===2||N===4}}};var l={zeroNonZero:function(K,N){return(K==null||N==null)&&K!==N||K==0&&N!=0?!0:K!=0&&N==0},any:function(K,N){return K!=N},emptyNonEmpty:function(K,N){var $=tr(K),J=tr(N);return $&&!J||!$&&J}},u=rt.types,v=[{name:"label",type:u.text,triggersBounds:l.any,triggersZOrder:l.emptyNonEmpty},{name:"text-rotation",type:u.textRotation,triggersBounds:l.any},{name:"text-margin-x",type:u.bidirectionalSize,triggersBounds:l.any},{name:"text-margin-y",type:u.bidirectionalSize,triggersBounds:l.any}],f=[{name:"source-label",type:u.text,triggersBounds:l.any},{name:"source-text-rotation",type:u.textRotation,triggersBounds:l.any},{name:"source-text-margin-x",type:u.bidirectionalSize,triggersBounds:l.any},{name:"source-text-margin-y",type:u.bidirectionalSize,triggersBounds:l.any},{name:"source-text-offset",type:u.size,triggersBounds:l.any}],c=[{name:"target-label",type:u.text,triggersBounds:l.any},{name:"target-text-rotation",type:u.textRotation,triggersBounds:l.any},{name:"target-text-margin-x",type:u.bidirectionalSize,triggersBounds:l.any},{name:"target-text-margin-y",type:u.bidirectionalSize,triggersBounds:l.any},{name:"target-text-offset",type:u.size,triggersBounds:l.any}],h=[{name:"font-family",type:u.fontFamily,triggersBounds:l.any},{name:"font-style",type:u.fontStyle,triggersBounds:l.any},{name:"font-weight",type:u.fontWeight,triggersBounds:l.any},{name:"font-size",type:u.size,triggersBounds:l.any},{name:"text-transform",type:u.textTransform,triggersBounds:l.any},{name:"text-wrap",type:u.textWrap,triggersBounds:l.any},{name:"text-overflow-wrap",type:u.textOverflowWrap,triggersBounds:l.any},{name:"text-max-width",type:u.size,triggersBounds:l.any},{name:"text-outline-width",type:u.size,triggersBounds:l.any},{name:"line-height",type:u.positiveNumber,triggersBounds:l.any}],d=[{name:"text-valign",type:u.valign,triggersBounds:l.any},{name:"text-halign",type:u.halign,triggersBounds:l.any},{name:"color",type:u.color},{name:"text-outline-color",type:u.color},{name:"text-outline-opacity",type:u.zeroOneNumber},{name:"text-background-color",type:u.color},{name:"text-background-opacity",type:u.zeroOneNumber},{name:"text-background-padding",type:u.size,triggersBounds:l.any},{name:"text-border-opacity",type:u.zeroOneNumber},{name:"text-border-color",type:u.color},{name:"text-border-width",type:u.size,triggersBounds:l.any},{name:"text-border-style",type:u.borderStyle,triggersBounds:l.any},{name:"text-background-shape",type:u.textBackgroundShape,triggersBounds:l.any},{name:"text-justification",type:u.justification}],y=[{name:"events",type:u.bool,triggersZOrder:l.any},{name:"text-events",type:u.bool,triggersZOrder:l.any}],g=[{name:"display",type:u.display,triggersZOrder:l.any,triggersBounds:l.any,triggersBoundsOfConnectedEdges:l.any,triggersBoundsOfParallelEdges:function(K,N,$){return K===N?!1:$.pstyle("curve-style").value==="bezier"}},{name:"visibility",type:u.visibility,triggersZOrder:l.any},{name:"opacity",type:u.zeroOneNumber,triggersZOrder:l.zeroNonZero},{name:"text-opacity",type:u.zeroOneNumber},{name:"min-zoomed-font-size",type:u.size},{name:"z-compound-depth",type:u.zCompoundDepth,triggersZOrder:l.any},{name:"z-index-compare",type:u.zIndexCompare,triggersZOrder:l.any},{name:"z-index",type:u.number,triggersZOrder:l.any}],p=[{name:"overlay-padding",type:u.size,triggersBounds:l.any},{name:"overlay-color",type:u.color},{name:"overlay-opacity",type:u.zeroOneNumber,triggersBounds:l.zeroNonZero},{name:"overlay-shape",type:u.overlayShape,triggersBounds:l.any},{name:"overlay-corner-radius",type:u.cornerRadius}],m=[{name:"underlay-padding",type:u.size,triggersBounds:l.any},{name:"underlay-color",type:u.color},{name:"underlay-opacity",type:u.zeroOneNumber,triggersBounds:l.zeroNonZero},{name:"underlay-shape",type:u.overlayShape,triggersBounds:l.any},{name:"underlay-corner-radius",type:u.cornerRadius}],b=[{name:"transition-property",type:u.propList},{name:"transition-duration",type:u.time},{name:"transition-delay",type:u.time},{name:"transition-timing-function",type:u.easing}],w=function(K,N){return N.value==="label"?-K.poolIndex():N.pfValue},E=[{name:"height",type:u.nodeSize,triggersBounds:l.any,hashOverride:w},{name:"width",type:u.nodeSize,triggersBounds:l.any,hashOverride:w},{name:"shape",type:u.nodeShape,triggersBounds:l.any},{name:"shape-polygon-points",type:u.polygonPointList,triggersBounds:l.any},{name:"corner-radius",type:u.cornerRadius},{name:"background-color",type:u.color},{name:"background-fill",type:u.fill},{name:"background-opacity",type:u.zeroOneNumber},{name:"background-blacken",type:u.nOneOneNumber},{name:"background-gradient-stop-colors",type:u.colors},{name:"background-gradient-stop-positions",type:u.percentages},{name:"background-gradient-direction",type:u.gradientDirection},{name:"padding",type:u.sizeMaybePercent,triggersBounds:l.any},{name:"padding-relative-to",type:u.paddingRelativeTo,triggersBounds:l.any},{name:"bounds-expansion",type:u.boundsExpansion,triggersBounds:l.any}],C=[{name:"border-color",type:u.color},{name:"border-opacity",type:u.zeroOneNumber},{name:"border-width",type:u.size,triggersBounds:l.any},{name:"border-style",type:u.borderStyle},{name:"border-cap",type:u.lineCap},{name:"border-join",type:u.lineJoin},{name:"border-dash-pattern",type:u.numbers},{name:"border-dash-offset",type:u.number},{name:"border-position",type:u.linePosition}],x=[{name:"outline-color",type:u.color},{name:"outline-opacity",type:u.zeroOneNumber},{name:"outline-width",type:u.size,triggersBounds:l.any},{name:"outline-style",type:u.borderStyle},{name:"outline-offset",type:u.size,triggersBounds:l.any}],S=[{name:"background-image",type:u.urls},{name:"background-image-crossorigin",type:u.bgCrossOrigin},{name:"background-image-opacity",type:u.zeroOneNumbers},{name:"background-image-containment",type:u.bgContainment},{name:"background-image-smoothing",type:u.bools},{name:"background-position-x",type:u.bgPos},{name:"background-position-y",type:u.bgPos},{name:"background-width-relative-to",type:u.bgRelativeTo},{name:"background-height-relative-to",type:u.bgRelativeTo},{name:"background-repeat",type:u.bgRepeat},{name:"background-fit",type:u.bgFit},{name:"background-clip",type:u.bgClip},{name:"background-width",type:u.bgWH},{name:"background-height",type:u.bgWH},{name:"background-offset-x",type:u.bgPos},{name:"background-offset-y",type:u.bgPos}],k=[{name:"position",type:u.position,triggersBounds:l.any},{name:"compound-sizing-wrt-labels",type:u.compoundIncludeLabels,triggersBounds:l.any},{name:"min-width",type:u.size,triggersBounds:l.any},{name:"min-width-bias-left",type:u.sizeMaybePercent,triggersBounds:l.any},{name:"min-width-bias-right",type:u.sizeMaybePercent,triggersBounds:l.any},{name:"min-height",type:u.size,triggersBounds:l.any},{name:"min-height-bias-top",type:u.sizeMaybePercent,triggersBounds:l.any},{name:"min-height-bias-bottom",type:u.sizeMaybePercent,triggersBounds:l.any}],B=[{name:"line-style",type:u.lineStyle},{name:"line-color",type:u.color},{name:"line-fill",type:u.fill},{name:"line-cap",type:u.lineCap},{name:"line-opacity",type:u.zeroOneNumber},{name:"line-dash-pattern",type:u.numbers},{name:"line-dash-offset",type:u.number},{name:"line-outline-width",type:u.size},{name:"line-outline-color",type:u.color},{name:"line-gradient-stop-colors",type:u.colors},{name:"line-gradient-stop-positions",type:u.percentages},{name:"curve-style",type:u.curveStyle,triggersBounds:l.any,triggersBoundsOfParallelEdges:function(K,N){return K===N?!1:K==="bezier"||N==="bezier"}},{name:"haystack-radius",type:u.zeroOneNumber,triggersBounds:l.any},{name:"source-endpoint",type:u.edgeEndpoint,triggersBounds:l.any},{name:"target-endpoint",type:u.edgeEndpoint,triggersBounds:l.any},{name:"control-point-step-size",type:u.size,triggersBounds:l.any},{name:"control-point-distances",type:u.bidirectionalSizes,triggersBounds:l.any},{name:"control-point-weights",type:u.numbers,triggersBounds:l.any},{name:"segment-distances",type:u.bidirectionalSizes,triggersBounds:l.any},{name:"segment-weights",type:u.numbers,triggersBounds:l.any},{name:"segment-radii",type:u.numbers,triggersBounds:l.any},{name:"radius-type",type:u.radiusType,triggersBounds:l.any},{name:"taxi-turn",type:u.bidirectionalSizeMaybePercent,triggersBounds:l.any},{name:"taxi-turn-min-distance",type:u.size,triggersBounds:l.any},{name:"taxi-direction",type:u.axisDirection,triggersBounds:l.any},{name:"taxi-radius",type:u.number,triggersBounds:l.any},{name:"edge-distances",type:u.edgeDistances,triggersBounds:l.any},{name:"arrow-scale",type:u.positiveNumber,triggersBounds:l.any},{name:"loop-direction",type:u.angle,triggersBounds:l.any},{name:"loop-sweep",type:u.angle,triggersBounds:l.any},{name:"source-distance-from-node",type:u.size,triggersBounds:l.any},{name:"target-distance-from-node",type:u.size,triggersBounds:l.any}],D=[{name:"ghost",type:u.bool,triggersBounds:l.any},{name:"ghost-offset-x",type:u.bidirectionalSize,triggersBounds:l.any},{name:"ghost-offset-y",type:u.bidirectionalSize,triggersBounds:l.any},{name:"ghost-opacity",type:u.zeroOneNumber}],A=[{name:"selection-box-color",type:u.color},{name:"selection-box-opacity",type:u.zeroOneNumber},{name:"selection-box-border-color",type:u.color},{name:"selection-box-border-width",type:u.size},{name:"active-bg-color",type:u.color},{name:"active-bg-opacity",type:u.zeroOneNumber},{name:"active-bg-size",type:u.size},{name:"outside-texture-bg-color",type:u.color},{name:"outside-texture-bg-opacity",type:u.zeroOneNumber}],P=[];rt.pieBackgroundN=16,P.push({name:"pie-size",type:u.sizeMaybePercent});for(var R=1;R<=rt.pieBackgroundN;R++)P.push({name:"pie-"+R+"-background-color",type:u.color}),P.push({name:"pie-"+R+"-background-size",type:u.percent}),P.push({name:"pie-"+R+"-background-opacity",type:u.zeroOneNumber});var L=[],I=rt.arrowPrefixes=["source","mid-source","target","mid-target"];[{name:"arrow-shape",type:u.arrowShape,triggersBounds:l.any},{name:"arrow-color",type:u.color},{name:"arrow-fill",type:u.arrowFill},{name:"arrow-width",type:u.arrowWidth}].forEach(function(te){I.forEach(function(K){var N=K+"-"+te.name,$=te.type,J=te.triggersBounds;L.push({name:N,type:$,triggersBounds:J})})},{});var M=rt.properties=[].concat(y,b,g,p,m,D,d,h,v,f,c,E,C,x,S,P,k,B,L,A),O=rt.propertyGroups={behavior:y,transition:b,visibility:g,overlay:p,underlay:m,ghost:D,commonLabel:d,labelDimensions:h,mainLabel:v,sourceLabel:f,targetLabel:c,nodeBody:E,nodeBorder:C,nodeOutline:x,backgroundImage:S,pie:P,compound:k,edgeLine:B,edgeArrow:L,core:A},_=rt.propertyGroupNames={},H=rt.propertyGroupKeys=Object.keys(O);H.forEach(function(te){_[te]=O[te].map(function(K){return K.name}),O[te].forEach(function(K){return K.groupKey=te})});var F=rt.aliases=[{name:"content",pointsTo:"label"},{name:"control-point-distance",pointsTo:"control-point-distances"},{name:"control-point-weight",pointsTo:"control-point-weights"},{name:"segment-distance",pointsTo:"segment-distances"},{name:"segment-weight",pointsTo:"segment-weights"},{name:"segment-radius",pointsTo:"segment-radii"},{name:"edge-text-rotation",pointsTo:"text-rotation"},{name:"padding-left",pointsTo:"padding"},{name:"padding-right",pointsTo:"padding"},{name:"padding-top",pointsTo:"padding"},{name:"padding-bottom",pointsTo:"padding"}];rt.propertyNames=M.map(function(te){return te.name});for(var G=0;G<M.length;G++){var U=M[G];M[U.name]=U}for(var X=0;X<F.length;X++){var Z=F[X],Q=M[Z.pointsTo],ee={name:Z.name,alias:!0,pointsTo:Q};M.push(ee),M[Z.name]=ee}})();rt.getDefaultProperty=function(t){return this.getDefaultProperties()[t]};rt.getDefaultProperties=function(){var t=this._private;if(t.defaultProperties!=null)return t.defaultProperties;for(var e=ge({"selection-box-color":"#ddd","selection-box-opacity":.65,"selection-box-border-color":"#aaa","selection-box-border-width":1,"active-bg-color":"black","active-bg-opacity":.15,"active-bg-size":30,"outside-texture-bg-color":"#000","outside-texture-bg-opacity":.125,events:"yes","text-events":"no","text-valign":"top","text-halign":"center","text-justification":"auto","line-height":1,color:"#000","text-outline-color":"#000","text-outline-width":0,"text-outline-opacity":1,"text-opacity":1,"text-decoration":"none","text-transform":"none","text-wrap":"none","text-overflow-wrap":"whitespace","text-max-width":9999,"text-background-color":"#000","text-background-opacity":0,"text-background-shape":"rectangle","text-background-padding":0,"text-border-opacity":0,"text-border-width":0,"text-border-style":"solid","text-border-color":"#000","font-family":"Helvetica Neue, Helvetica, sans-serif","font-style":"normal","font-weight":"normal","font-size":16,"min-zoomed-font-size":0,"text-rotation":"none","source-text-rotation":"none","target-text-rotation":"none",visibility:"visible",display:"element",opacity:1,"z-compound-depth":"auto","z-index-compare":"auto","z-index":0,label:"","text-margin-x":0,"text-margin-y":0,"source-label":"","source-text-offset":0,"source-text-margin-x":0,"source-text-margin-y":0,"target-label":"","target-text-offset":0,"target-text-margin-x":0,"target-text-margin-y":0,"overlay-opacity":0,"overlay-color":"#000","overlay-padding":10,"overlay-shape":"round-rectangle","overlay-corner-radius":"auto","underlay-opacity":0,"underlay-color":"#000","underlay-padding":10,"underlay-shape":"round-rectangle","underlay-corner-radius":"auto","transition-property":"none","transition-duration":0,"transition-delay":0,"transition-timing-function":"linear","background-blacken":0,"background-color":"#999","background-fill":"solid","background-opacity":1,"background-image":"none","background-image-crossorigin":"anonymous","background-image-opacity":1,"background-image-containment":"inside","background-image-smoothing":"yes","background-position-x":"50%","background-position-y":"50%","background-offset-x":0,"background-offset-y":0,"background-width-relative-to":"include-padding","background-height-relative-to":"include-padding","background-repeat":"no-repeat","background-fit":"none","background-clip":"node","background-width":"auto","background-height":"auto","border-color":"#000","border-opacity":1,"border-width":0,"border-style":"solid","border-dash-pattern":[4,2],"border-dash-offset":0,"border-cap":"butt","border-join":"miter","border-position":"center","outline-color":"#999","outline-opacity":1,"outline-width":0,"outline-offset":0,"outline-style":"solid",height:30,width:30,shape:"ellipse","shape-polygon-points":"-1, -1,   1, -1,   1, 1,   -1, 1","corner-radius":"auto","bounds-expansion":0,"background-gradient-direction":"to-bottom","background-gradient-stop-colors":"#999","background-gradient-stop-positions":"0%",ghost:"no","ghost-offset-y":0,"ghost-offset-x":0,"ghost-opacity":0,padding:0,"padding-relative-to":"width",position:"origin","compound-sizing-wrt-labels":"include","min-width":0,"min-width-bias-left":0,"min-width-bias-right":0,"min-height":0,"min-height-bias-top":0,"min-height-bias-bottom":0},{"pie-size":"100%"},[{name:"pie-{{i}}-background-color",value:"black"},{name:"pie-{{i}}-background-size",value:"0%"},{name:"pie-{{i}}-background-opacity",value:1}].reduce(function(l,u){for(var v=1;v<=rt.pieBackgroundN;v++){var f=u.name.replace("{{i}}",v),c=u.value;l[f]=c}return l},{}),{"line-style":"solid","line-color":"#999","line-fill":"solid","line-cap":"butt","line-opacity":1,"line-outline-width":0,"line-outline-color":"#000","line-gradient-stop-colors":"#999","line-gradient-stop-positions":"0%","control-point-step-size":40,"control-point-weights":.5,"segment-weights":.5,"segment-distances":20,"segment-radii":15,"radius-type":"arc-radius","taxi-turn":"50%","taxi-radius":15,"taxi-turn-min-distance":10,"taxi-direction":"auto","edge-distances":"intersection","curve-style":"haystack","haystack-radius":0,"arrow-scale":1,"loop-direction":"-45deg","loop-sweep":"-90deg","source-distance-from-node":0,"target-distance-from-node":0,"source-endpoint":"outside-to-node","target-endpoint":"outside-to-node","line-dash-pattern":[6,3],"line-dash-offset":0},[{name:"arrow-shape",value:"none"},{name:"arrow-color",value:"#999"},{name:"arrow-fill",value:"filled"},{name:"arrow-width",value:1}].reduce(function(l,u){return rt.arrowPrefixes.forEach(function(v){var f=v+"-"+u.name,c=u.value;l[f]=c}),l},{})),r={},a=0;a<this.properties.length;a++){var n=this.properties[a];if(!n.pointsTo){var i=n.name,s=e[i],o=this.parse(i,s);r[i]=o}}return t.defaultProperties=r,t.defaultProperties};rt.addDefaultStylesheet=function(){this.selector(":parent").css({shape:"rectangle",padding:10,"background-color":"#eee","border-color":"#ccc","border-width":1}).selector("edge").css({width:3}).selector(":loop").css({"curve-style":"bezier"}).selector("edge:compound").css({"curve-style":"bezier","source-endpoint":"outside-to-line","target-endpoint":"outside-to-line"}).selector(":selected").css({"background-color":"#0169D9","line-color":"#0169D9","source-arrow-color":"#0169D9","target-arrow-color":"#0169D9","mid-source-arrow-color":"#0169D9","mid-target-arrow-color":"#0169D9"}).selector(":parent:selected").css({"background-color":"#CCE1F9","border-color":"#aec8e5"}).selector(":active").css({"overlay-color":"black","overlay-padding":10,"overlay-opacity":.25}),this.defaultLength=this.length};var On={};On.parse=function(t,e,r,a){var n=this;if(_e(e))return n.parseImplWarn(t,e,r,a);var i=a==="mapping"||a===!0||a===!1||a==null?"dontcare":a,s=r?"t":"f",o=""+e,l=Yl(t,o,s,i),u=n.propCache=n.propCache||[],v;return(v=u[l])||(v=u[l]=n.parseImplWarn(t,e,r,a)),(r||a==="mapping")&&(v=Nt(v),v&&(v.value=Nt(v.value))),v};On.parseImplWarn=function(t,e,r,a){var n=this.parseImpl(t,e,r,a);return!n&&e!=null&&Re("The style property `".concat(t,": ").concat(e,"` is invalid")),n&&(n.name==="width"||n.name==="height")&&e==="label"&&Re("The style value of `label` is deprecated for `"+n.name+"`"),n};On.parseImpl=function(t,e,r,a){var n=this;t=Ls(t);var i=n.properties[t],s=e,o=n.types;if(!i||e===void 0)return null;i.alias&&(i=i.pointsTo,t=i.name);var l=fe(e);l&&(e=e.trim());var u=i.type;if(!u)return null;if(r&&(e===""||e===null))return{name:t,value:e,bypass:!0,deleteBypass:!0};if(_e(e))return{name:t,value:e,strValue:"fn",mapped:o.fn,bypass:r};var v,f;if(!(!l||a||e.length<7||e[1]!=="a")){if(e.length>=7&&e[0]==="d"&&(v=new RegExp(o.data.regex).exec(e))){if(r)return!1;var c=o.data;return{name:t,value:v,strValue:""+e,mapped:c,field:v[1],bypass:r}}else if(e.length>=10&&e[0]==="m"&&(f=new RegExp(o.mapData.regex).exec(e))){if(r||u.multiple)return!1;var h=o.mapData;if(!(u.color||u.number))return!1;var d=this.parse(t,f[4]);if(!d||d.mapped)return!1;var y=this.parse(t,f[5]);if(!y||y.mapped)return!1;if(d.pfValue===y.pfValue||d.strValue===y.strValue)return Re("`"+t+": "+e+"` is not a valid mapper because the output range is zero; converting to `"+t+": "+d.strValue+"`"),this.parse(t,d.strValue);if(u.color){var g=d.value,p=y.value,m=g[0]===p[0]&&g[1]===p[1]&&g[2]===p[2]&&(g[3]===p[3]||(g[3]==null||g[3]===1)&&(p[3]==null||p[3]===1));if(m)return!1}return{name:t,value:f,strValue:""+e,mapped:h,field:f[1],fieldMin:parseFloat(f[2]),fieldMax:parseFloat(f[3]),valueMin:d.value,valueMax:y.value,bypass:r}}}if(u.multiple&&a!=="multiple"){var b;if(l?b=e.split(/\s+/):Le(e)?b=e:b=[e],u.evenMultiple&&b.length%2!==0)return null;for(var w=[],E=[],C=[],x="",S=!1,k=0;k<b.length;k++){var B=n.parse(t,b[k],r,"multiple");S=S||fe(B.value),w.push(B.value),C.push(B.pfValue!=null?B.pfValue:B.value),E.push(B.units),x+=(k>0?" ":"")+B.strValue}return u.validate&&!u.validate(w,E)?null:u.singleEnum&&S?w.length===1&&fe(w[0])?{name:t,value:w[0],strValue:w[0],bypass:r}:null:{name:t,value:w,pfValue:C,strValue:x,bypass:r,units:E}}var D=function(){for(var K=0;K<u.enums.length;K++){var N=u.enums[K];if(N===e)return{name:t,value:e,strValue:""+e,bypass:r}}return null};if(u.number){var A,P="px";if(u.units&&(A=u.units),u.implicitUnits&&(P=u.implicitUnits),!u.unitless)if(l){var R="px|em"+(u.allowPercent?"|\\%":"");A&&(R=A);var L=e.match("^("+$e+")("+R+")?$");L&&(e=L[1],A=L[2]||P)}else(!A||u.implicitUnits)&&(A=P);if(e=parseFloat(e),isNaN(e)&&u.enums===void 0)return null;if(isNaN(e)&&u.enums!==void 0)return e=s,D();if(u.integer&&!Yf(e)||u.min!==void 0&&(e<u.min||u.strictMin&&e===u.min)||u.max!==void 0&&(e>u.max||u.strictMax&&e===u.max))return null;var I={name:t,value:e,strValue:""+e+(A||""),units:A,bypass:r};return u.unitless||A!=="px"&&A!=="em"?I.pfValue=e:I.pfValue=A==="px"||!A?e:this.getEmSizeInPixels()*e,(A==="ms"||A==="s")&&(I.pfValue=A==="ms"?e:1e3*e),(A==="deg"||A==="rad")&&(I.pfValue=A==="rad"?e:td(e)),A==="%"&&(I.pfValue=e/100),I}else if(u.propList){var M=[],O=""+e;if(O!=="none"){for(var _=O.split(/\s*,\s*|\s+/),H=0;H<_.length;H++){var F=_[H].trim();n.properties[F]?M.push(F):Re("`"+F+"` is not a valid property name")}if(M.length===0)return null}return{name:t,value:M,strValue:M.length===0?"none":M.join(" "),bypass:r}}else if(u.color){var G=_l(e);return G?{name:t,value:G,pfValue:G,strValue:"rgb("+G[0]+","+G[1]+","+G[2]+")",bypass:r}:null}else if(u.regex||u.regexes){if(u.enums){var U=D();if(U)return U}for(var X=u.regexes?u.regexes:[u.regex],Z=0;Z<X.length;Z++){var Q=new RegExp(X[Z]),ee=Q.exec(e);if(ee)return{name:t,value:u.singleRegexMatchValue?ee[1]:ee,strValue:""+e,bypass:r}}return null}else return u.string?{name:t,value:""+e,strValue:""+e,bypass:r}:u.enums?D():null};var et=function(e){if(!(this instanceof et))return new et(e);if(!Ms(e)){Ve("A style must have a core reference");return}this._private={cy:e,coreStyle:{}},this.length=0,this.resetToDefault()},ut=et.prototype;ut.instanceString=function(){return"style"};ut.clear=function(){for(var t=this._private,e=t.cy,r=e.elements(),a=0;a<this.length;a++)this[a]=void 0;return this.length=0,t.contextStyles={},t.propDiffs={},this.cleanElements(r,!0),r.forEach(function(n){var i=n[0]._private;i.styleDirty=!0,i.appliedInitStyle=!1}),this};ut.resetToDefault=function(){return this.clear(),this.addDefaultStylesheet(),this};ut.core=function(t){return this._private.coreStyle[t]||this.getDefaultProperty(t)};ut.selector=function(t){var e=t==="core"?null:new nr(t),r=this.length++;return this[r]={selector:e,properties:[],mappedProperties:[],index:r},this};ut.css=function(){var t=this,e=arguments;if(e.length===1)for(var r=e[0],a=0;a<t.properties.length;a++){var n=t.properties[a],i=r[n.name];i===void 0&&(i=r[xn(n.name)]),i!==void 0&&this.cssRule(n.name,i)}else e.length===2&&this.cssRule(e[0],e[1]);return this};ut.style=ut.css;ut.cssRule=function(t,e){var r=this.parse(t,e);if(r){var a=this.length-1;this[a].properties.push(r),this[a].properties[r.name]=r,r.name.match(/pie-(\d+)-background-size/)&&r.value&&(this._private.hasPie=!0),r.mapped&&this[a].mappedProperties.push(r);var n=!this[a].selector;n&&(this._private.coreStyle[r.name]=r)}return this};ut.append=function(t){return zl(t)?t.appendToStyle(this):Le(t)?this.appendFromJson(t):fe(t)&&this.appendFromString(t),this};et.fromJson=function(t,e){var r=new et(t);return r.fromJson(e),r};et.fromString=function(t,e){return new et(t).fromString(e)};[tt,Ra,Us,Ft,In,Ys,rt,On].forEach(function(t){ge(ut,t)});et.types=ut.types;et.properties=ut.properties;et.propertyGroups=ut.propertyGroups;et.propertyGroupNames=ut.propertyGroupNames;et.propertyGroupKeys=ut.propertyGroupKeys;var ap={style:function(e){if(e){var r=this.setStyle(e);r.update()}return this._private.style},setStyle:function(e){var r=this._private;return zl(e)?r.style=e.generateStyle(this):Le(e)?r.style=et.fromJson(this,e):fe(e)?r.style=et.fromString(this,e):r.style=et(this),r.style},updateStyle:function(){this.mutableElements().updateStyle()}},np="single",wr={autolock:function(e){if(e!==void 0)this._private.autolock=!!e;else return this._private.autolock;return this},autoungrabify:function(e){if(e!==void 0)this._private.autoungrabify=!!e;else return this._private.autoungrabify;return this},autounselectify:function(e){if(e!==void 0)this._private.autounselectify=!!e;else return this._private.autounselectify;return this},selectionType:function(e){var r=this._private;if(r.selectionType==null&&(r.selectionType=np),e!==void 0)(e==="additive"||e==="single")&&(r.selectionType=e);else return r.selectionType;return this},panningEnabled:function(e){if(e!==void 0)this._private.panningEnabled=!!e;else return this._private.panningEnabled;return this},userPanningEnabled:function(e){if(e!==void 0)this._private.userPanningEnabled=!!e;else return this._private.userPanningEnabled;return this},zoomingEnabled:function(e){if(e!==void 0)this._private.zoomingEnabled=!!e;else return this._private.zoomingEnabled;return this},userZoomingEnabled:function(e){if(e!==void 0)this._private.userZoomingEnabled=!!e;else return this._private.userZoomingEnabled;return this},boxSelectionEnabled:function(e){if(e!==void 0)this._private.boxSelectionEnabled=!!e;else return this._private.boxSelectionEnabled;return this},pan:function(){var e=arguments,r=this._private.pan,a,n,i,s,o;switch(e.length){case 0:return r;case 1:if(fe(e[0]))return a=e[0],r[a];if(ke(e[0])){if(!this._private.panningEnabled)return this;i=e[0],s=i.x,o=i.y,ae(s)&&(r.x=s),ae(o)&&(r.y=o),this.emit("pan viewport")}break;case 2:if(!this._private.panningEnabled)return this;a=e[0],n=e[1],(a==="x"||a==="y")&&ae(n)&&(r[a]=n),this.emit("pan viewport");break}return this.notify("viewport"),this},panBy:function(e,r){var a=arguments,n=this._private.pan,i,s,o,l,u;if(!this._private.panningEnabled)return this;switch(a.length){case 1:ke(e)&&(o=a[0],l=o.x,u=o.y,ae(l)&&(n.x+=l),ae(u)&&(n.y+=u),this.emit("pan viewport"));break;case 2:i=e,s=r,(i==="x"||i==="y")&&ae(s)&&(n[i]+=s),this.emit("pan viewport");break}return this.notify("viewport"),this},gc:function(){this.notify("gc")},fit:function(e,r){var a=this.getFitViewport(e,r);if(a){var n=this._private;n.zoom=a.zoom,n.pan=a.pan,this.emit("pan zoom viewport"),this.notify("viewport")}return this},getFitViewport:function(e,r){if(ae(e)&&r===void 0&&(r=e,e=void 0),!(!this._private.panningEnabled||!this._private.zoomingEnabled)){var a;if(fe(e)){var n=e;e=this.$(n)}else if(Qf(e)){var i=e;a={x1:i.x1,y1:i.y1,x2:i.x2,y2:i.y2},a.w=a.x2-a.x1,a.h=a.y2-a.y1}else bt(e)||(e=this.mutableElements());if(!(bt(e)&&e.empty())){a=a||e.boundingBox();var s=this.width(),o=this.height(),l;if(r=ae(r)?r:0,!isNaN(s)&&!isNaN(o)&&s>0&&o>0&&!isNaN(a.w)&&!isNaN(a.h)&&a.w>0&&a.h>0){l=Math.min((s-2*r)/a.w,(o-2*r)/a.h),l=l>this._private.maxZoom?this._private.maxZoom:l,l=l<this._private.minZoom?this._private.minZoom:l;var u={x:(s-l*(a.x1+a.x2))/2,y:(o-l*(a.y1+a.y2))/2};return{zoom:l,pan:u}}}}},zoomRange:function(e,r){var a=this._private;if(r==null){var n=e;e=n.min,r=n.max}return ae(e)&&ae(r)&&e<=r?(a.minZoom=e,a.maxZoom=r):ae(e)&&r===void 0&&e<=a.maxZoom?a.minZoom=e:ae(r)&&e===void 0&&r>=a.minZoom&&(a.maxZoom=r),this},minZoom:function(e){return e===void 0?this._private.minZoom:this.zoomRange({min:e})},maxZoom:function(e){return e===void 0?this._private.maxZoom:this.zoomRange({max:e})},getZoomedViewport:function(e){var r=this._private,a=r.pan,n=r.zoom,i,s,o=!1;if(r.zoomingEnabled||(o=!0),ae(e)?s=e:ke(e)&&(s=e.level,e.position!=null?i=Tn(e.position,n,a):e.renderedPosition!=null&&(i=e.renderedPosition),i!=null&&!r.panningEnabled&&(o=!0)),s=s>r.maxZoom?r.maxZoom:s,s=s<r.minZoom?r.minZoom:s,o||!ae(s)||s===n||i!=null&&(!ae(i.x)||!ae(i.y)))return null;if(i!=null){var l=a,u=n,v=s,f={x:-v/u*(i.x-l.x)+i.x,y:-v/u*(i.y-l.y)+i.y};return{zoomed:!0,panned:!0,zoom:v,pan:f}}else return{zoomed:!0,panned:!1,zoom:s,pan:a}},zoom:function(e){if(e===void 0)return this._private.zoom;var r=this.getZoomedViewport(e),a=this._private;return r==null||!r.zoomed?this:(a.zoom=r.zoom,r.panned&&(a.pan.x=r.pan.x,a.pan.y=r.pan.y),this.emit("zoom"+(r.panned?" pan":"")+" viewport"),this.notify("viewport"),this)},viewport:function(e){var r=this._private,a=!0,n=!0,i=[],s=!1,o=!1;if(!e)return this;if(ae(e.zoom)||(a=!1),ke(e.pan)||(n=!1),!a&&!n)return this;if(a){var l=e.zoom;l<r.minZoom||l>r.maxZoom||!r.zoomingEnabled?s=!0:(r.zoom=l,i.push("zoom"))}if(n&&(!s||!e.cancelOnFailedZoom)&&r.panningEnabled){var u=e.pan;ae(u.x)&&(r.pan.x=u.x,o=!1),ae(u.y)&&(r.pan.y=u.y,o=!1),o||i.push("pan")}return i.length>0&&(i.push("viewport"),this.emit(i.join(" ")),this.notify("viewport")),this},center:function(e){var r=this.getCenterPan(e);return r&&(this._private.pan=r,this.emit("pan viewport"),this.notify("viewport")),this},getCenterPan:function(e,r){if(this._private.panningEnabled){if(fe(e)){var a=e;e=this.mutableElements().filter(a)}else bt(e)||(e=this.mutableElements());if(e.length!==0){var n=e.boundingBox(),i=this.width(),s=this.height();r=r===void 0?this._private.zoom:r;var o={x:(i-r*(n.x1+n.x2))/2,y:(s-r*(n.y1+n.y2))/2};return o}}},reset:function(){return!this._private.panningEnabled||!this._private.zoomingEnabled?this:(this.viewport({pan:{x:0,y:0},zoom:1}),this)},invalidateSize:function(){this._private.sizeCache=null},size:function(){var e=this._private,r=e.container,a=this;return e.sizeCache=e.sizeCache||(r?function(){var n=a.window().getComputedStyle(r),i=function(o){return parseFloat(n.getPropertyValue(o))};return{width:r.clientWidth-i("padding-left")-i("padding-right"),height:r.clientHeight-i("padding-top")-i("padding-bottom")}}():{width:1,height:1})},width:function(){return this.size().width},height:function(){return this.size().height},extent:function(){var e=this._private.pan,r=this._private.zoom,a=this.renderedExtent(),n={x1:(a.x1-e.x)/r,x2:(a.x2-e.x)/r,y1:(a.y1-e.y)/r,y2:(a.y2-e.y)/r};return n.w=n.x2-n.x1,n.h=n.y2-n.y1,n},renderedExtent:function(){var e=this.width(),r=this.height();return{x1:0,y1:0,x2:e,y2:r,w:e,h:r}},multiClickDebounceTime:function(e){if(e)this._private.multiClickDebounceTime=e;else return this._private.multiClickDebounceTime;return this}};wr.centre=wr.center;wr.autolockNodes=wr.autolock;wr.autoungrabifyNodes=wr.autoungrabify;var wa={data:Ae.data({field:"data",bindingEvent:"data",allowBinding:!0,allowSetting:!0,settingEvent:"data",settingTriggersEvent:!0,triggerFnName:"trigger",allowGetting:!0,updateStyle:!0}),removeData:Ae.removeData({field:"data",event:"data",triggerFnName:"trigger",triggerEvent:!0,updateStyle:!0}),scratch:Ae.data({field:"scratch",bindingEvent:"scratch",allowBinding:!0,allowSetting:!0,settingEvent:"scratch",settingTriggersEvent:!0,triggerFnName:"trigger",allowGetting:!0,updateStyle:!0}),removeScratch:Ae.removeData({field:"scratch",event:"scratch",triggerFnName:"trigger",triggerEvent:!0,updateStyle:!0})};wa.attr=wa.data;wa.removeAttr=wa.removeData;var xa=function(e){var r=this;e=ge({},e);var a=e.container;a&&!un(a)&&un(a[0])&&(a=a[0]);var n=a?a._cyreg:null;n=n||{},n&&n.cy&&(n.cy.destroy(),n={});var i=n.readies=n.readies||[];a&&(a._cyreg=n),n.cy=r;var s=Ke!==void 0&&a!==void 0&&!e.headless,o=e;o.layout=ge({name:s?"grid":"null"},o.layout),o.renderer=ge({name:s?"canvas":"null"},o.renderer);var l=function(d,y,g){return y!==void 0?y:g!==void 0?g:d},u=this._private={container:a,ready:!1,options:o,elements:new nt(this),listeners:[],aniEles:new nt(this),data:o.data||{},scratch:{},layout:null,renderer:null,destroyed:!1,notificationsEnabled:!0,minZoom:1e-50,maxZoom:1e50,zoomingEnabled:l(!0,o.zoomingEnabled),userZoomingEnabled:l(!0,o.userZoomingEnabled),panningEnabled:l(!0,o.panningEnabled),userPanningEnabled:l(!0,o.userPanningEnabled),boxSelectionEnabled:l(!0,o.boxSelectionEnabled),autolock:l(!1,o.autolock,o.autolockNodes),autoungrabify:l(!1,o.autoungrabify,o.autoungrabifyNodes),autounselectify:l(!1,o.autounselectify),styleEnabled:o.styleEnabled===void 0?s:o.styleEnabled,zoom:ae(o.zoom)?o.zoom:1,pan:{x:ke(o.pan)&&ae(o.pan.x)?o.pan.x:0,y:ke(o.pan)&&ae(o.pan.y)?o.pan.y:0},animation:{current:[],queue:[]},hasCompoundNodes:!1,multiClickDebounceTime:l(250,o.multiClickDebounceTime)};this.createEmitter(),this.selectionType(o.selectionType),this.zoomRange({min:o.minZoom,max:o.maxZoom});var v=function(d,y){var g=d.some(Jf);if(g)return Wr.all(d).then(y);y(d)};u.styleEnabled&&r.setStyle([]);var f=ge({},o,o.renderer);r.initRenderer(f);var c=function(d,y,g){r.notifications(!1);var p=r.mutableElements();p.length>0&&p.remove(),d!=null&&(ke(d)||Le(d))&&r.add(d),r.one("layoutready",function(b){r.notifications(!0),r.emit(b),r.one("load",y),r.emitAndNotify("load")}).one("layoutstop",function(){r.one("done",g),r.emit("done")});var m=ge({},r._private.options.layout);m.eles=r.elements(),r.layout(m).run()};v([o.style,o.elements],function(h){var d=h[0],y=h[1];u.styleEnabled&&r.style().append(d),c(y,function(){r.startAnimationLoop(),u.ready=!0,_e(o.ready)&&r.on("ready",o.ready);for(var g=0;g<i.length;g++){var p=i[g];r.on("ready",p)}n&&(n.readies=[]),r.emit("ready")},o.done)})},gn=xa.prototype;ge(gn,{instanceString:function(){return"core"},isReady:function(){return this._private.ready},destroyed:function(){return this._private.destroyed},ready:function(e){return this.isReady()?this.emitter().emit("ready",[],e):this.on("ready",e),this},destroy:function(){var e=this;if(!e.destroyed())return e.stopAnimationLoop(),e.destroyRenderer(),this.emit("destroy"),e._private.destroyed=!0,e},hasElementWithId:function(e){return this._private.elements.hasElementWithId(e)},getElementById:function(e){return this._private.elements.getElementById(e)},hasCompoundNodes:function(){return this._private.hasCompoundNodes},headless:function(){return this._private.renderer.isHeadless()},styleEnabled:function(){return this._private.styleEnabled},addToPool:function(e){return this._private.elements.merge(e),this},removeFromPool:function(e){return this._private.elements.unmerge(e),this},container:function(){return this._private.container||null},window:function(){var e=this._private.container;if(e==null)return Ke;var r=this._private.container.ownerDocument;return r===void 0||r==null?Ke:r.defaultView||Ke},mount:function(e){if(e!=null){var r=this,a=r._private,n=a.options;return!un(e)&&un(e[0])&&(e=e[0]),r.stopAnimationLoop(),r.destroyRenderer(),a.container=e,a.styleEnabled=!0,r.invalidateSize(),r.initRenderer(ge({},n,n.renderer,{name:n.renderer.name==="null"?"canvas":n.renderer.name})),r.startAnimationLoop(),r.style(n.style),r.emit("mount"),r}},unmount:function(){var e=this;return e.stopAnimationLoop(),e.destroyRenderer(),e.initRenderer({name:"null"}),e.emit("unmount"),e},options:function(){return Nt(this._private.options)},json:function(e){var r=this,a=r._private,n=r.mutableElements(),i=function(w){return r.getElementById(w.id())};if(ke(e)){if(r.startBatch(),e.elements){var s={},o=function(w,E){for(var C=[],x=[],S=0;S<w.length;S++){var k=w[S];if(!k.data.id){Re("cy.json() cannot handle elements without an ID attribute");continue}var B=""+k.data.id,D=r.getElementById(B);s[B]=!0,D.length!==0?x.push({ele:D,json:k}):(E&&(k.group=E),C.push(k))}r.add(C);for(var A=0;A<x.length;A++){var P=x[A],R=P.ele,L=P.json;R.json(L)}};if(Le(e.elements))o(e.elements);else for(var l=["nodes","edges"],u=0;u<l.length;u++){var v=l[u],f=e.elements[v];Le(f)&&o(f,v)}var c=r.collection();n.filter(function(b){return!s[b.id()]}).forEach(function(b){b.isParent()?c.merge(b):b.remove()}),c.forEach(function(b){return b.children().move({parent:null})}),c.forEach(function(b){return i(b).remove()})}e.style&&r.style(e.style),e.zoom!=null&&e.zoom!==a.zoom&&r.zoom(e.zoom),e.pan&&(e.pan.x!==a.pan.x||e.pan.y!==a.pan.y)&&r.pan(e.pan),e.data&&r.data(e.data);for(var h=["minZoom","maxZoom","zoomingEnabled","userZoomingEnabled","panningEnabled","userPanningEnabled","boxSelectionEnabled","autolock","autoungrabify","autounselectify","multiClickDebounceTime"],d=0;d<h.length;d++){var y=h[d];e[y]!=null&&r[y](e[y])}return r.endBatch(),this}else{var g=!!e,p={};g?p.elements=this.elements().map(function(b){return b.json()}):(p.elements={},n.forEach(function(b){var w=b.group();p.elements[w]||(p.elements[w]=[]),p.elements[w].push(b.json())})),this._private.styleEnabled&&(p.style=r.style().json()),p.data=Nt(r.data());var m=a.options;return p.zoomingEnabled=a.zoomingEnabled,p.userZoomingEnabled=a.userZoomingEnabled,p.zoom=a.zoom,p.minZoom=a.minZoom,p.maxZoom=a.maxZoom,p.panningEnabled=a.panningEnabled,p.userPanningEnabled=a.userPanningEnabled,p.pan=Nt(a.pan),p.boxSelectionEnabled=a.boxSelectionEnabled,p.renderer=Nt(m.renderer),p.hideEdgesOnViewport=m.hideEdgesOnViewport,p.textureOnViewport=m.textureOnViewport,p.wheelSensitivity=m.wheelSensitivity,p.motionBlur=m.motionBlur,p.multiClickDebounceTime=m.multiClickDebounceTime,p}}});gn.$id=gn.getElementById;[Ug,Jg,zv,xs,rn,ep,Es,an,ap,wr,wa].forEach(function(t){ge(gn,t)});var ip={fit:!0,directed:!1,padding:30,circle:!1,grid:!1,spacingFactor:1.75,boundingBox:void 0,avoidOverlap:!0,nodeDimensionsIncludeLabels:!1,roots:void 0,depthSort:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,r){return!0},ready:void 0,stop:void 0,transform:function(e,r){return r}},sp={maximal:!1,acyclic:!1},kr=function(e){return e.scratch("breadthfirst")},il=function(e,r){return e.scratch("breadthfirst",r)};function qv(t){this.options=ge({},ip,sp,t)}qv.prototype.run=function(){var t=this.options,e=t.cy,r=t.eles,a=r.nodes().filter(function(se){return se.isChildless()}),n=r,i=t.directed,s=t.acyclic||t.maximal||t.maximalAdjustments>0,o=!!t.boundingBox,l=e.extent(),u=pt(o?t.boundingBox:{x1:l.x1,y1:l.y1,w:l.w,h:l.h}),v;if(bt(t.roots))v=t.roots;else if(Le(t.roots)){for(var f=[],c=0;c<t.roots.length;c++){var h=t.roots[c],d=e.getElementById(h);f.push(d)}v=e.collection(f)}else if(fe(t.roots))v=e.$(t.roots);else if(i)v=a.roots();else{var y=r.components();v=e.collection();for(var g=function(){var ue=y[p],de=ue.maxDegree(!1),ye=ue.filter(function(he){return he.degree(!1)===de});v=v.add(ye)},p=0;p<y.length;p++)g()}var m=[],b={},w=function(ue,de){m[de]==null&&(m[de]=[]);var ye=m[de].length;m[de].push(ue),il(ue,{index:ye,depth:de})},E=function(ue,de){var ye=kr(ue),he=ye.depth,me=ye.index;m[he][me]=null,ue.isChildless()&&w(ue,de)};n.bfs({roots:v,directed:t.directed,visit:function(ue,de,ye,he,me){var Ce=ue[0],Se=Ce.id();Ce.isChildless()&&w(Ce,me),b[Se]=!0}});for(var C=[],x=0;x<a.length;x++){var S=a[x];b[S.id()]||C.push(S)}var k=function(ue){for(var de=m[ue],ye=0;ye<de.length;ye++){var he=de[ye];if(he==null){de.splice(ye,1),ye--;continue}il(he,{depth:ue,index:ye})}},B=function(ue,de){for(var ye=kr(ue),he=ue.incomers().filter(function(W){return W.isNode()&&r.has(W)}),me=-1,Ce=ue.id(),Se=0;Se<he.length;Se++){var j=he[Se],T=kr(j);me=Math.max(me,T.depth)}if(ye.depth<=me){if(!t.acyclic&&de[Ce])return null;var q=me+1;return E(ue,q),de[Ce]=q,!0}return!1};if(i&&s){var D=[],A={},P=function(ue){return D.push(ue)},R=function(){return D.shift()};for(a.forEach(function(se){return D.push(se)});D.length>0;){var L=R(),I=B(L,A);if(I)L.outgoers().filter(function(se){return se.isNode()&&r.has(se)}).forEach(P);else if(I===null){Re("Detected double maximal shift for node `"+L.id()+"`.  Bailing maximal adjustment due to cycle.  Use `options.maximal: true` only on DAGs.");break}}}var M=0;if(t.avoidOverlap)for(var O=0;O<a.length;O++){var _=a[O],H=_.layoutDimensions(t),F=H.w,G=H.h;M=Math.max(M,F,G)}var U={},X=function(ue){if(U[ue.id()])return U[ue.id()];for(var de=kr(ue).depth,ye=ue.neighborhood(),he=0,me=0,Ce=0;Ce<ye.length;Ce++){var Se=ye[Ce];if(!(Se.isEdge()||Se.isParent()||!a.has(Se))){var j=kr(Se);if(j!=null){var T=j.index,q=j.depth;if(!(T==null||q==null)){var W=m[q].length;q<de&&(he+=T/W,me++)}}}}return me=Math.max(1,me),he=he/me,me===0&&(he=0),U[ue.id()]=he,he},Z=function(ue,de){var ye=X(ue),he=X(de),me=ye-he;return me===0?Vl(ue.id(),de.id()):me};t.depthSort!==void 0&&(Z=t.depthSort);for(var Q=m.length,ee=0;ee<Q;ee++)m[ee].sort(Z),k(ee);for(var te=[],K=0;K<C.length;K++)te.push(C[K]);var N=function(){for(var ue=0;ue<Q;ue++)k(ue)};te.length&&(m.unshift(te),Q=m.length,N());for(var $=0,J=0;J<Q;J++)$=Math.max(m[J].length,$);var re={x:u.x1+u.w/2,y:u.y1+u.h/2},le=a.reduce(function(se,ue){return function(de){return{w:se.w===-1?de.w:(se.w+de.w)/2,h:se.h===-1?de.h:(se.h+de.h)/2}}(ue.boundingBox({includeLabels:t.nodeDimensionsIncludeLabels}))},{w:-1,h:-1}),xe=Math.max(Q===1?0:o?(u.h-t.padding*2-le.h)/(Q-1):(u.h-t.padding*2-le.h)/(Q+1),M),Ie=m.reduce(function(se,ue){return Math.max(se,ue.length)},0),Be=function(ue){var de=kr(ue),ye=de.depth,he=de.index;if(t.circle){var me=Math.min(u.w/2/Q,u.h/2/Q);me=Math.max(me,M);var Ce=me*ye+me-(Q>0&&m[0].length<=3?me/2:0),Se=2*Math.PI/m[ye].length*he;return ye===0&&m[0].length===1&&(Ce=1),{x:re.x+Ce*Math.cos(Se),y:re.y+Ce*Math.sin(Se)}}else{var j=m[ye].length,T=Math.max(j===1?0:o?(u.w-t.padding*2-le.w)/((t.grid?Ie:j)-1):(u.w-t.padding*2-le.w)/((t.grid?Ie:j)+1),M),q={x:re.x+(he+1-(j+1)/2)*T,y:re.y+(ye+1-(Q+1)/2)*xe};return q}};return r.nodes().layoutPositions(this,t,Be),this};var op={fit:!0,padding:30,boundingBox:void 0,avoidOverlap:!0,nodeDimensionsIncludeLabels:!1,spacingFactor:void 0,radius:void 0,startAngle:3/2*Math.PI,sweep:void 0,clockwise:!0,sort:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,r){return!0},ready:void 0,stop:void 0,transform:function(e,r){return r}};function Vv(t){this.options=ge({},op,t)}Vv.prototype.run=function(){var t=this.options,e=t,r=t.cy,a=e.eles,n=e.counterclockwise!==void 0?!e.counterclockwise:e.clockwise,i=a.nodes().not(":parent");e.sort&&(i=i.sort(e.sort));for(var s=pt(e.boundingBox?e.boundingBox:{x1:0,y1:0,w:r.width(),h:r.height()}),o={x:s.x1+s.w/2,y:s.y1+s.h/2},l=e.sweep===void 0?2*Math.PI-2*Math.PI/i.length:e.sweep,u=l/Math.max(1,i.length-1),v,f=0,c=0;c<i.length;c++){var h=i[c],d=h.layoutDimensions(e),y=d.w,g=d.h;f=Math.max(f,y,g)}if(ae(e.radius)?v=e.radius:i.length<=1?v=0:v=Math.min(s.h,s.w)/2-f,i.length>1&&e.avoidOverlap){f*=1.75;var p=Math.cos(u)-Math.cos(0),m=Math.sin(u)-Math.sin(0),b=Math.sqrt(f*f/(p*p+m*m));v=Math.max(b,v)}var w=function(C,x){var S=e.startAngle+x*u*(n?1:-1),k=v*Math.cos(S),B=v*Math.sin(S),D={x:o.x+k,y:o.y+B};return D};return a.nodes().layoutPositions(this,e,w),this};var up={fit:!0,padding:30,startAngle:3/2*Math.PI,sweep:void 0,clockwise:!0,equidistant:!1,minNodeSpacing:10,boundingBox:void 0,avoidOverlap:!0,nodeDimensionsIncludeLabels:!1,height:void 0,width:void 0,spacingFactor:void 0,concentric:function(e){return e.degree()},levelWidth:function(e){return e.maxDegree()/4},animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,r){return!0},ready:void 0,stop:void 0,transform:function(e,r){return r}};function _v(t){this.options=ge({},up,t)}_v.prototype.run=function(){for(var t=this.options,e=t,r=e.counterclockwise!==void 0?!e.counterclockwise:e.clockwise,a=t.cy,n=e.eles,i=n.nodes().not(":parent"),s=pt(e.boundingBox?e.boundingBox:{x1:0,y1:0,w:a.width(),h:a.height()}),o={x:s.x1+s.w/2,y:s.y1+s.h/2},l=[],u=0,v=0;v<i.length;v++){var f=i[v],c=void 0;c=e.concentric(f),l.push({value:c,node:f}),f._private.scratch.concentric=c}i.updateStyle();for(var h=0;h<i.length;h++){var d=i[h],y=d.layoutDimensions(e);u=Math.max(u,y.w,y.h)}l.sort(function(le,xe){return xe.value-le.value});for(var g=e.levelWidth(i),p=[[]],m=p[0],b=0;b<l.length;b++){var w=l[b];if(m.length>0){var E=Math.abs(m[0].value-w.value);E>=g&&(m=[],p.push(m))}m.push(w)}var C=u+e.minNodeSpacing;if(!e.avoidOverlap){var x=p.length>0&&p[0].length>1,S=Math.min(s.w,s.h)/2-C,k=S/(p.length+x?1:0);C=Math.min(C,k)}for(var B=0,D=0;D<p.length;D++){var A=p[D],P=e.sweep===void 0?2*Math.PI-2*Math.PI/A.length:e.sweep,R=A.dTheta=P/Math.max(1,A.length-1);if(A.length>1&&e.avoidOverlap){var L=Math.cos(R)-Math.cos(0),I=Math.sin(R)-Math.sin(0),M=Math.sqrt(C*C/(L*L+I*I));B=Math.max(M,B)}A.r=B,B+=C}if(e.equidistant){for(var O=0,_=0,H=0;H<p.length;H++){var F=p[H],G=F.r-_;O=Math.max(O,G)}_=0;for(var U=0;U<p.length;U++){var X=p[U];U===0&&(_=X.r),X.r=_,_+=O}}for(var Z={},Q=0;Q<p.length;Q++)for(var ee=p[Q],te=ee.dTheta,K=ee.r,N=0;N<ee.length;N++){var $=ee[N],J=e.startAngle+(r?1:-1)*te*N,re={x:o.x+K*Math.cos(J),y:o.y+K*Math.sin(J)};Z[$.node.id()]=re}return n.nodes().layoutPositions(this,e,function(le){var xe=le.id();return Z[xe]}),this};var ss,lp={ready:function(){},stop:function(){},animate:!0,animationEasing:void 0,animationDuration:void 0,animateFilter:function(e,r){return!0},animationThreshold:250,refresh:20,fit:!0,padding:30,boundingBox:void 0,nodeDimensionsIncludeLabels:!1,randomize:!1,componentSpacing:40,nodeRepulsion:function(e){return 2048},nodeOverlap:4,idealEdgeLength:function(e){return 32},edgeElasticity:function(e){return 32},nestingFactor:1.2,gravity:1,numIter:1e3,initialTemp:1e3,coolingFactor:.99,minTemp:1};function Nn(t){this.options=ge({},lp,t),this.options.layout=this;var e=this.options.eles.nodes(),r=this.options.eles.edges(),a=r.filter(function(n){var i=n.source().data("id"),s=n.target().data("id"),o=e.some(function(u){return u.data("id")===i}),l=e.some(function(u){return u.data("id")===s});return!o||!l});this.options.eles=this.options.eles.not(a)}Nn.prototype.run=function(){var t=this.options,e=t.cy,r=this;r.stopped=!1,(t.animate===!0||t.animate===!1)&&r.emit({type:"layoutstart",layout:r}),t.debug===!0?ss=!0:ss=!1;var a=vp(e,r,t);ss&&cp(a),t.randomize&&dp(a);var n=$t(),i=function(){hp(a,e,t),t.fit===!0&&e.fit(t.padding)},s=function(c){return!(r.stopped||c>=t.numIter||(gp(a,t),a.temperature=a.temperature*t.coolingFactor,a.temperature<t.minTemp))},o=function(){if(t.animate===!0||t.animate===!1)i(),r.one("layoutstop",t.stop),r.emit({type:"layoutstop",layout:r});else{var c=t.eles.nodes(),h=Hv(a,t,c);c.layoutPositions(r,t,h)}},l=0,u=!0;if(t.animate===!0){var v=function(){for(var c=0;u&&c<t.refresh;)u=s(l),l++,c++;if(!u)ol(a,t),o();else{var h=$t();h-n>=t.animationThreshold&&i(),ln(v)}};v()}else{for(;u;)u=s(l),l++;ol(a,t),o()}return this};Nn.prototype.stop=function(){return this.stopped=!0,this.thread&&this.thread.stop(),this.emit("layoutstop"),this};Nn.prototype.destroy=function(){return this.thread&&this.thread.stop(),this};var vp=function(e,r,a){for(var n=a.eles.edges(),i=a.eles.nodes(),s=pt(a.boundingBox?a.boundingBox:{x1:0,y1:0,w:e.width(),h:e.height()}),o={isCompound:e.hasCompoundNodes(),layoutNodes:[],idToIndex:{},nodeSize:i.size(),graphSet:[],indexToGraph:[],layoutEdges:[],edgeSize:n.size(),temperature:a.initialTemp,clientWidth:s.w,clientHeight:s.h,boundingBox:s},l=a.eles.components(),u={},v=0;v<l.length;v++)for(var f=l[v],c=0;c<f.length;c++){var h=f[c];u[h.id()]=v}for(var v=0;v<o.nodeSize;v++){var d=i[v],y=d.layoutDimensions(a),g={};g.isLocked=d.locked(),g.id=d.data("id"),g.parentId=d.data("parent"),g.cmptId=u[d.id()],g.children=[],g.positionX=d.position("x"),g.positionY=d.position("y"),g.offsetX=0,g.offsetY=0,g.height=y.w,g.width=y.h,g.maxX=g.positionX+g.width/2,g.minX=g.positionX-g.width/2,g.maxY=g.positionY+g.height/2,g.minY=g.positionY-g.height/2,g.padLeft=parseFloat(d.style("padding")),g.padRight=parseFloat(d.style("padding")),g.padTop=parseFloat(d.style("padding")),g.padBottom=parseFloat(d.style("padding")),g.nodeRepulsion=_e(a.nodeRepulsion)?a.nodeRepulsion(d):a.nodeRepulsion,o.layoutNodes.push(g),o.idToIndex[g.id]=v}for(var p=[],m=0,b=-1,w=[],v=0;v<o.nodeSize;v++){var d=o.layoutNodes[v],E=d.parentId;E!=null?o.layoutNodes[o.idToIndex[E]].children.push(d.id):(p[++b]=d.id,w.push(d.id))}for(o.graphSet.push(w);m<=b;){var C=p[m++],x=o.idToIndex[C],h=o.layoutNodes[x],S=h.children;if(S.length>0){o.graphSet.push(S);for(var v=0;v<S.length;v++)p[++b]=S[v]}}for(var v=0;v<o.graphSet.length;v++)for(var k=o.graphSet[v],c=0;c<k.length;c++){var B=o.idToIndex[k[c]];o.indexToGraph[B]=v}for(var v=0;v<o.edgeSize;v++){var D=n[v],A={};A.id=D.data("id"),A.sourceId=D.data("source"),A.targetId=D.data("target");var P=_e(a.idealEdgeLength)?a.idealEdgeLength(D):a.idealEdgeLength,R=_e(a.edgeElasticity)?a.edgeElasticity(D):a.edgeElasticity,L=o.idToIndex[A.sourceId],I=o.idToIndex[A.targetId],M=o.indexToGraph[L],O=o.indexToGraph[I];if(M!=O){for(var _=fp(A.sourceId,A.targetId,o),H=o.graphSet[_],F=0,g=o.layoutNodes[L];H.indexOf(g.id)===-1;)g=o.layoutNodes[o.idToIndex[g.parentId]],F++;for(g=o.layoutNodes[I];H.indexOf(g.id)===-1;)g=o.layoutNodes[o.idToIndex[g.parentId]],F++;P*=F*a.nestingFactor}A.idealLength=P,A.elasticity=R,o.layoutEdges.push(A)}return o},fp=function(e,r,a){var n=Gv(e,r,0,a);return 2>n.count?0:n.graph},Gv=function(e,r,a,n){var i=n.graphSet[a];if(-1<i.indexOf(e)&&-1<i.indexOf(r))return{count:2,graph:a};for(var s=0,o=0;o<i.length;o++){var l=i[o],u=n.idToIndex[l],v=n.layoutNodes[u].children;if(v.length!==0){var f=n.indexToGraph[n.idToIndex[v[0]]],c=Gv(e,r,f,n);if(c.count!==0)if(c.count===1){if(s++,s===2)break}else return c}}return{count:s,graph:a}},cp,dp=function(e,r){for(var a=e.clientWidth,n=e.clientHeight,i=0;i<e.nodeSize;i++){var s=e.layoutNodes[i];s.children.length===0&&!s.isLocked&&(s.positionX=Math.random()*a,s.positionY=Math.random()*n)}},Hv=function(e,r,a){var n=e.boundingBox,i={x1:1/0,x2:-1/0,y1:1/0,y2:-1/0};return r.boundingBox&&(a.forEach(function(s){var o=e.layoutNodes[e.idToIndex[s.data("id")]];i.x1=Math.min(i.x1,o.positionX),i.x2=Math.max(i.x2,o.positionX),i.y1=Math.min(i.y1,o.positionY),i.y2=Math.max(i.y2,o.positionY)}),i.w=i.x2-i.x1,i.h=i.y2-i.y1),function(s,o){var l=e.layoutNodes[e.idToIndex[s.data("id")]];if(r.boundingBox){var u=(l.positionX-i.x1)/i.w,v=(l.positionY-i.y1)/i.h;return{x:n.x1+u*n.w,y:n.y1+v*n.h}}else return{x:l.positionX,y:l.positionY}}},hp=function(e,r,a){var n=a.layout,i=a.eles.nodes(),s=Hv(e,a,i);i.positions(s),e.ready!==!0&&(e.ready=!0,n.one("layoutready",a.ready),n.emit({type:"layoutready",layout:this}))},gp=function(e,r,a){pp(e,r),bp(e),wp(e,r),xp(e),Ep(e)},pp=function(e,r){for(var a=0;a<e.graphSet.length;a++)for(var n=e.graphSet[a],i=n.length,s=0;s<i;s++)for(var o=e.layoutNodes[e.idToIndex[n[s]]],l=s+1;l<i;l++){var u=e.layoutNodes[e.idToIndex[n[l]]];yp(o,u,e,r)}},sl=function(e){return-1+2*e*Math.random()},yp=function(e,r,a,n){var i=e.cmptId,s=r.cmptId;if(!(i!==s&&!a.isCompound)){var o=r.positionX-e.positionX,l=r.positionY-e.positionY,u=1;o===0&&l===0&&(o=sl(u),l=sl(u));var v=mp(e,r,o,l);if(v>0)var f=n.nodeOverlap*v,c=Math.sqrt(o*o+l*l),h=f*o/c,d=f*l/c;else var y=pn(e,o,l),g=pn(r,-1*o,-1*l),p=g.x-y.x,m=g.y-y.y,b=p*p+m*m,c=Math.sqrt(b),f=(e.nodeRepulsion+r.nodeRepulsion)/b,h=f*p/c,d=f*m/c;e.isLocked||(e.offsetX-=h,e.offsetY-=d),r.isLocked||(r.offsetX+=h,r.offsetY+=d)}},mp=function(e,r,a,n){if(a>0)var i=e.maxX-r.minX;else var i=r.maxX-e.minX;if(n>0)var s=e.maxY-r.minY;else var s=r.maxY-e.minY;return i>=0&&s>=0?Math.sqrt(i*i+s*s):0},pn=function(e,r,a){var n=e.positionX,i=e.positionY,s=e.height||1,o=e.width||1,l=a/r,u=s/o,v={};return r===0&&0<a||r===0&&0>a?(v.x=n,v.y=i+s/2,v):0<r&&-1*u<=l&&l<=u?(v.x=n+o/2,v.y=i+o*a/2/r,v):0>r&&-1*u<=l&&l<=u?(v.x=n-o/2,v.y=i-o*a/2/r,v):0<a&&(l<=-1*u||l>=u)?(v.x=n+s*r/2/a,v.y=i+s/2,v):(0>a&&(l<=-1*u||l>=u)&&(v.x=n-s*r/2/a,v.y=i-s/2),v)},bp=function(e,r){for(var a=0;a<e.edgeSize;a++){var n=e.layoutEdges[a],i=e.idToIndex[n.sourceId],s=e.layoutNodes[i],o=e.idToIndex[n.targetId],l=e.layoutNodes[o],u=l.positionX-s.positionX,v=l.positionY-s.positionY;if(!(u===0&&v===0)){var f=pn(s,u,v),c=pn(l,-1*u,-1*v),h=c.x-f.x,d=c.y-f.y,y=Math.sqrt(h*h+d*d),g=Math.pow(n.idealLength-y,2)/n.elasticity;if(y!==0)var p=g*h/y,m=g*d/y;else var p=0,m=0;s.isLocked||(s.offsetX+=p,s.offsetY+=m),l.isLocked||(l.offsetX-=p,l.offsetY-=m)}}},wp=function(e,r){if(r.gravity!==0)for(var a=1,n=0;n<e.graphSet.length;n++){var i=e.graphSet[n],s=i.length;if(n===0)var o=e.clientHeight/2,l=e.clientWidth/2;else var u=e.layoutNodes[e.idToIndex[i[0]]],v=e.layoutNodes[e.idToIndex[u.parentId]],o=v.positionX,l=v.positionY;for(var f=0;f<s;f++){var c=e.layoutNodes[e.idToIndex[i[f]]];if(!c.isLocked){var h=o-c.positionX,d=l-c.positionY,y=Math.sqrt(h*h+d*d);if(y>a){var g=r.gravity*h/y,p=r.gravity*d/y;c.offsetX+=g,c.offsetY+=p}}}}},xp=function(e,r){var a=[],n=0,i=-1;for(a.push.apply(a,e.graphSet[0]),i+=e.graphSet[0].length;n<=i;){var s=a[n++],o=e.idToIndex[s],l=e.layoutNodes[o],u=l.children;if(0<u.length&&!l.isLocked){for(var v=l.offsetX,f=l.offsetY,c=0;c<u.length;c++){var h=e.layoutNodes[e.idToIndex[u[c]]];h.offsetX+=v,h.offsetY+=f,a[++i]=u[c]}l.offsetX=0,l.offsetY=0}}},Ep=function(e,r){for(var a=0;a<e.nodeSize;a++){var n=e.layoutNodes[a];0<n.children.length&&(n.maxX=void 0,n.minX=void 0,n.maxY=void 0,n.minY=void 0)}for(var a=0;a<e.nodeSize;a++){var n=e.layoutNodes[a];if(!(0<n.children.length||n.isLocked)){var i=Cp(n.offsetX,n.offsetY,e.temperature);n.positionX+=i.x,n.positionY+=i.y,n.offsetX=0,n.offsetY=0,n.minX=n.positionX-n.width,n.maxX=n.positionX+n.width,n.minY=n.positionY-n.height,n.maxY=n.positionY+n.height,Kv(n,e)}}for(var a=0;a<e.nodeSize;a++){var n=e.layoutNodes[a];0<n.children.length&&!n.isLocked&&(n.positionX=(n.maxX+n.minX)/2,n.positionY=(n.maxY+n.minY)/2,n.width=n.maxX-n.minX,n.height=n.maxY-n.minY)}},Cp=function(e,r,a){var n=Math.sqrt(e*e+r*r);if(n>a)var i={x:a*e/n,y:a*r/n};else var i={x:e,y:r};return i},Kv=function(e,r){var a=e.parentId;if(a!=null){var n=r.layoutNodes[r.idToIndex[a]],i=!1;if((n.maxX==null||e.maxX+n.padRight>n.maxX)&&(n.maxX=e.maxX+n.padRight,i=!0),(n.minX==null||e.minX-n.padLeft<n.minX)&&(n.minX=e.minX-n.padLeft,i=!0),(n.maxY==null||e.maxY+n.padBottom>n.maxY)&&(n.maxY=e.maxY+n.padBottom,i=!0),(n.minY==null||e.minY-n.padTop<n.minY)&&(n.minY=e.minY-n.padTop,i=!0),i)return Kv(n,r)}},ol=function(e,r){for(var a=e.layoutNodes,n=[],i=0;i<a.length;i++){var s=a[i],o=s.cmptId,l=n[o]=n[o]||[];l.push(s)}for(var u=0,i=0;i<n.length;i++){var v=n[i];if(v){v.x1=1/0,v.x2=-1/0,v.y1=1/0,v.y2=-1/0;for(var f=0;f<v.length;f++){var c=v[f];v.x1=Math.min(v.x1,c.positionX-c.width/2),v.x2=Math.max(v.x2,c.positionX+c.width/2),v.y1=Math.min(v.y1,c.positionY-c.height/2),v.y2=Math.max(v.y2,c.positionY+c.height/2)}v.w=v.x2-v.x1,v.h=v.y2-v.y1,u+=v.w*v.h}}n.sort(function(m,b){return b.w*b.h-m.w*m.h});for(var h=0,d=0,y=0,g=0,p=Math.sqrt(u)*e.clientWidth/e.clientHeight,i=0;i<n.length;i++){var v=n[i];if(v){for(var f=0;f<v.length;f++){var c=v[f];c.isLocked||(c.positionX+=h-v.x1,c.positionY+=d-v.y1)}h+=v.w+r.componentSpacing,y+=v.w+r.componentSpacing,g=Math.max(g,v.h),y>p&&(d+=g+r.componentSpacing,h=0,y=0,g=0)}}},Tp={fit:!0,padding:30,boundingBox:void 0,avoidOverlap:!0,avoidOverlapPadding:10,nodeDimensionsIncludeLabels:!1,spacingFactor:void 0,condense:!1,rows:void 0,cols:void 0,position:function(e){},sort:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,r){return!0},ready:void 0,stop:void 0,transform:function(e,r){return r}};function $v(t){this.options=ge({},Tp,t)}$v.prototype.run=function(){var t=this.options,e=t,r=t.cy,a=e.eles,n=a.nodes().not(":parent");e.sort&&(n=n.sort(e.sort));var i=pt(e.boundingBox?e.boundingBox:{x1:0,y1:0,w:r.width(),h:r.height()});if(i.h===0||i.w===0)a.nodes().layoutPositions(this,e,function(U){return{x:i.x1,y:i.y1}});else{var s=n.size(),o=Math.sqrt(s*i.h/i.w),l=Math.round(o),u=Math.round(i.w/i.h*o),v=function(X){if(X==null)return Math.min(l,u);var Z=Math.min(l,u);Z==l?l=X:u=X},f=function(X){if(X==null)return Math.max(l,u);var Z=Math.max(l,u);Z==l?l=X:u=X},c=e.rows,h=e.cols!=null?e.cols:e.columns;if(c!=null&&h!=null)l=c,u=h;else if(c!=null&&h==null)l=c,u=Math.ceil(s/l);else if(c==null&&h!=null)u=h,l=Math.ceil(s/u);else if(u*l>s){var d=v(),y=f();(d-1)*y>=s?v(d-1):(y-1)*d>=s&&f(y-1)}else for(;u*l<s;){var g=v(),p=f();(p+1)*g>=s?f(p+1):v(g+1)}var m=i.w/u,b=i.h/l;if(e.condense&&(m=0,b=0),e.avoidOverlap)for(var w=0;w<n.length;w++){var E=n[w],C=E._private.position;(C.x==null||C.y==null)&&(C.x=0,C.y=0);var x=E.layoutDimensions(e),S=e.avoidOverlapPadding,k=x.w+S,B=x.h+S;m=Math.max(m,k),b=Math.max(b,B)}for(var D={},A=function(X,Z){return!!D["c-"+X+"-"+Z]},P=function(X,Z){D["c-"+X+"-"+Z]=!0},R=0,L=0,I=function(){L++,L>=u&&(L=0,R++)},M={},O=0;O<n.length;O++){var _=n[O],H=e.position(_);if(H&&(H.row!==void 0||H.col!==void 0)){var F={row:H.row,col:H.col};if(F.col===void 0)for(F.col=0;A(F.row,F.col);)F.col++;else if(F.row===void 0)for(F.row=0;A(F.row,F.col);)F.row++;M[_.id()]=F,P(F.row,F.col)}}var G=function(X,Z){var Q,ee;if(X.locked()||X.isParent())return!1;var te=M[X.id()];if(te)Q=te.col*m+m/2+i.x1,ee=te.row*b+b/2+i.y1;else{for(;A(R,L);)I();Q=L*m+m/2+i.x1,ee=R*b+b/2+i.y1,P(R,L),I()}return{x:Q,y:ee}};n.layoutPositions(this,e,G)}return this};var Sp={ready:function(){},stop:function(){}};function Xs(t){this.options=ge({},Sp,t)}Xs.prototype.run=function(){var t=this.options,e=t.eles,r=this;return t.cy,r.emit("layoutstart"),e.nodes().positions(function(){return{x:0,y:0}}),r.one("layoutready",t.ready),r.emit("layoutready"),r.one("layoutstop",t.stop),r.emit("layoutstop"),this};Xs.prototype.stop=function(){return this};var Dp={positions:void 0,zoom:void 0,pan:void 0,fit:!0,padding:30,spacingFactor:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,r){return!0},ready:void 0,stop:void 0,transform:function(e,r){return r}};function Wv(t){this.options=ge({},Dp,t)}Wv.prototype.run=function(){var t=this.options,e=t.eles,r=e.nodes(),a=_e(t.positions);function n(i){if(t.positions==null)return Zc(i.position());if(a)return t.positions(i);var s=t.positions[i._private.data.id];return s??null}return r.layoutPositions(this,t,function(i,s){var o=n(i);return i.locked()||o==null?!1:o}),this};var kp={fit:!0,padding:30,boundingBox:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,r){return!0},ready:void 0,stop:void 0,transform:function(e,r){return r}};function Uv(t){this.options=ge({},kp,t)}Uv.prototype.run=function(){var t=this.options,e=t.cy,r=t.eles,a=pt(t.boundingBox?t.boundingBox:{x1:0,y1:0,w:e.width(),h:e.height()}),n=function(s,o){return{x:a.x1+Math.round(Math.random()*a.w),y:a.y1+Math.round(Math.random()*a.h)}};return r.nodes().layoutPositions(this,t,n),this};var Pp=[{name:"breadthfirst",impl:qv},{name:"circle",impl:Vv},{name:"concentric",impl:_v},{name:"cose",impl:Nn},{name:"grid",impl:$v},{name:"null",impl:Xs},{name:"preset",impl:Wv},{name:"random",impl:Uv}];function Yv(t){this.options=t,this.notifications=0}var ul=function(){},ll=function(){throw new Error("A headless instance can not render images")};Yv.prototype={recalculateRenderedStyle:ul,notify:function(){this.notifications++},init:ul,isHeadless:function(){return!0},png:ll,jpg:ll};var Zs={};Zs.arrowShapeWidth=.3;Zs.registerArrowShapes=function(){var t=this.arrowShapes={},e=this,r=function(u,v,f,c,h,d,y){var g=h.x-f/2-y,p=h.x+f/2+y,m=h.y-f/2-y,b=h.y+f/2+y,w=g<=u&&u<=p&&m<=v&&v<=b;return w},a=function(u,v,f,c,h){var d=u*Math.cos(c)-v*Math.sin(c),y=u*Math.sin(c)+v*Math.cos(c),g=d*f,p=y*f,m=g+h.x,b=p+h.y;return{x:m,y:b}},n=function(u,v,f,c){for(var h=[],d=0;d<u.length;d+=2){var y=u[d],g=u[d+1];h.push(a(y,g,v,f,c))}return h},i=function(u){for(var v=[],f=0;f<u.length;f++){var c=u[f];v.push(c.x,c.y)}return v},s=function(u){return u.pstyle("width").pfValue*u.pstyle("arrow-scale").pfValue*2},o=function(u,v){fe(v)&&(v=t[v]),t[u]=ge({name:u,points:[-.15,-.3,.15,-.3,.15,.3,-.15,.3],collide:function(c,h,d,y,g,p){var m=i(n(this.points,d+2*p,y,g)),b=gt(c,h,m);return b},roughCollide:r,draw:function(c,h,d,y){var g=n(this.points,h,d,y);e.arrowShapeImpl("polygon")(c,g)},spacing:function(c){return 0},gap:s},v)};o("none",{collide:vn,roughCollide:vn,draw:Ns,spacing:So,gap:So}),o("triangle",{points:[-.15,-.3,0,0,.15,-.3]}),o("arrow","triangle"),o("triangle-backcurve",{points:t.triangle.points,controlPoint:[0,-.15],roughCollide:r,draw:function(u,v,f,c,h){var d=n(this.points,v,f,c),y=this.controlPoint,g=a(y[0],y[1],v,f,c);e.arrowShapeImpl(this.name)(u,d,g)},gap:function(u){return s(u)*.8}}),o("triangle-tee",{points:[0,0,.15,-.3,-.15,-.3,0,0],pointsTee:[-.15,-.4,-.15,-.5,.15,-.5,.15,-.4],collide:function(u,v,f,c,h,d,y){var g=i(n(this.points,f+2*y,c,h)),p=i(n(this.pointsTee,f+2*y,c,h)),m=gt(u,v,g)||gt(u,v,p);return m},draw:function(u,v,f,c,h){var d=n(this.points,v,f,c),y=n(this.pointsTee,v,f,c);e.arrowShapeImpl(this.name)(u,d,y)}}),o("circle-triangle",{radius:.15,pointsTr:[0,-.15,.15,-.45,-.15,-.45,0,-.15],collide:function(u,v,f,c,h,d,y){var g=h,p=Math.pow(g.x-u,2)+Math.pow(g.y-v,2)<=Math.pow((f+2*y)*this.radius,2),m=i(n(this.points,f+2*y,c,h));return gt(u,v,m)||p},draw:function(u,v,f,c,h){var d=n(this.pointsTr,v,f,c);e.arrowShapeImpl(this.name)(u,d,c.x,c.y,this.radius*v)},spacing:function(u){return e.getArrowWidth(u.pstyle("width").pfValue,u.pstyle("arrow-scale").value)*this.radius}}),o("triangle-cross",{points:[0,0,.15,-.3,-.15,-.3,0,0],baseCrossLinePts:[-.15,-.4,-.15,-.4,.15,-.4,.15,-.4],crossLinePts:function(u,v){var f=this.baseCrossLinePts.slice(),c=v/u,h=3,d=5;return f[h]=f[h]-c,f[d]=f[d]-c,f},collide:function(u,v,f,c,h,d,y){var g=i(n(this.points,f+2*y,c,h)),p=i(n(this.crossLinePts(f,d),f+2*y,c,h)),m=gt(u,v,g)||gt(u,v,p);return m},draw:function(u,v,f,c,h){var d=n(this.points,v,f,c),y=n(this.crossLinePts(v,h),v,f,c);e.arrowShapeImpl(this.name)(u,d,y)}}),o("vee",{points:[-.15,-.3,0,0,.15,-.3,0,-.15],gap:function(u){return s(u)*.525}}),o("circle",{radius:.15,collide:function(u,v,f,c,h,d,y){var g=h,p=Math.pow(g.x-u,2)+Math.pow(g.y-v,2)<=Math.pow((f+2*y)*this.radius,2);return p},draw:function(u,v,f,c,h){e.arrowShapeImpl(this.name)(u,c.x,c.y,this.radius*v)},spacing:function(u){return e.getArrowWidth(u.pstyle("width").pfValue,u.pstyle("arrow-scale").value)*this.radius}}),o("tee",{points:[-.15,0,-.15,-.1,.15,-.1,.15,0],spacing:function(u){return 1},gap:function(u){return 1}}),o("square",{points:[-.15,0,.15,0,.15,-.3,-.15,-.3]}),o("diamond",{points:[-.15,-.15,0,-.3,.15,-.15,0,0],gap:function(u){return u.pstyle("width").pfValue*u.pstyle("arrow-scale").value}}),o("chevron",{points:[0,0,-.15,-.15,-.1,-.2,0,-.1,.1,-.2,.15,-.15],gap:function(u){return .95*u.pstyle("width").pfValue*u.pstyle("arrow-scale").value}})};var Er={};Er.projectIntoViewport=function(t,e){var r=this.cy,a=this.findContainerClientCoords(),n=a[0],i=a[1],s=a[4],o=r.pan(),l=r.zoom(),u=((t-n)/s-o.x)/l,v=((e-i)/s-o.y)/l;return[u,v]};Er.findContainerClientCoords=function(){if(this.containerBB)return this.containerBB;var t=this.container,e=t.getBoundingClientRect(),r=this.cy.window().getComputedStyle(t),a=function(p){return parseFloat(r.getPropertyValue(p))},n={left:a("padding-left"),right:a("padding-right"),top:a("padding-top"),bottom:a("padding-bottom")},i={left:a("border-left-width"),right:a("border-right-width"),top:a("border-top-width"),bottom:a("border-bottom-width")},s=t.clientWidth,o=t.clientHeight,l=n.left+n.right,u=n.top+n.bottom,v=i.left+i.right,f=e.width/(s+v),c=s-l,h=o-u,d=e.left+n.left+i.left,y=e.top+n.top+i.top;return this.containerBB=[d,y,c,h,f]};Er.invalidateContainerClientCoordsCache=function(){this.containerBB=null};Er.findNearestElement=function(t,e,r,a){return this.findNearestElements(t,e,r,a)[0]};Er.findNearestElements=function(t,e,r,a){var n=this,i=this,s=i.getCachedZSortedEles(),o=[],l=i.cy.zoom(),u=i.cy.hasCompoundNodes(),v=(a?24:8)/l,f=(a?8:2)/l,c=(a?8:2)/l,h=1/0,d,y;r&&(s=s.interactive);function g(x,S){if(x.isNode()){if(y)return;y=x,o.push(x)}if(x.isEdge()&&(S==null||S<h))if(d){if(d.pstyle("z-compound-depth").value===x.pstyle("z-compound-depth").value&&d.pstyle("z-compound-depth").value===x.pstyle("z-compound-depth").value){for(var k=0;k<o.length;k++)if(o[k].isEdge()){o[k]=x,d=x,h=S??h;break}}}else o.push(x),d=x,h=S??h}function p(x){var S=x.outerWidth()+2*f,k=x.outerHeight()+2*f,B=S/2,D=k/2,A=x.position(),P=x.pstyle("corner-radius").value==="auto"?"auto":x.pstyle("corner-radius").pfValue,R=x._private.rscratch;if(A.x-B<=t&&t<=A.x+B&&A.y-D<=e&&e<=A.y+D){var L=i.nodeShapes[n.getNodeShape(x)];if(L.checkPoint(t,e,0,S,k,A.x,A.y,P,R))return g(x,0),!0}}function m(x){var S=x._private,k=S.rscratch,B=x.pstyle("width").pfValue,D=x.pstyle("arrow-scale").value,A=B/2+v,P=A*A,R=A*2,O=S.source,_=S.target,L;if(k.edgeType==="segments"||k.edgeType==="straight"||k.edgeType==="haystack"){for(var I=k.allpts,M=0;M+3<I.length;M+=2)if(ld(t,e,I[M],I[M+1],I[M+2],I[M+3],R)&&P>(L=hd(t,e,I[M],I[M+1],I[M+2],I[M+3])))return g(x,L),!0}else if(k.edgeType==="bezier"||k.edgeType==="multibezier"||k.edgeType==="self"||k.edgeType==="compound"){for(var I=k.allpts,M=0;M+5<k.allpts.length;M+=4)if(vd(t,e,I[M],I[M+1],I[M+2],I[M+3],I[M+4],I[M+5],R)&&P>(L=dd(t,e,I[M],I[M+1],I[M+2],I[M+3],I[M+4],I[M+5])))return g(x,L),!0}for(var O=O||S.source,_=_||S.target,H=n.getArrowWidth(B,D),F=[{name:"source",x:k.arrowStartX,y:k.arrowStartY,angle:k.srcArrowAngle},{name:"target",x:k.arrowEndX,y:k.arrowEndY,angle:k.tgtArrowAngle},{name:"mid-source",x:k.midX,y:k.midY,angle:k.midsrcArrowAngle},{name:"mid-target",x:k.midX,y:k.midY,angle:k.midtgtArrowAngle}],M=0;M<F.length;M++){var G=F[M],U=i.arrowShapes[x.pstyle(G.name+"-arrow-shape").value],X=x.pstyle("width").pfValue;if(U.roughCollide(t,e,H,G.angle,{x:G.x,y:G.y},X,v)&&U.collide(t,e,H,G.angle,{x:G.x,y:G.y},X,v))return g(x),!0}u&&o.length>0&&(p(O),p(_))}function b(x,S,k){return Et(x,S,k)}function w(x,S){var k=x._private,B=c,D;S?D=S+"-":D="",x.boundingBox();var A=k.labelBounds[S||"main"],P=x.pstyle(D+"label").value,R=x.pstyle("text-events").strValue==="yes";if(!(!R||!P)){var L=b(k.rscratch,"labelX",S),I=b(k.rscratch,"labelY",S),M=b(k.rscratch,"labelAngle",S),O=x.pstyle(D+"text-margin-x").pfValue,_=x.pstyle(D+"text-margin-y").pfValue,H=A.x1-B-O,F=A.x2+B-O,G=A.y1-B-_,U=A.y2+B-_;if(M){var X=Math.cos(M),Z=Math.sin(M),Q=function(re,le){return re=re-L,le=le-I,{x:re*X-le*Z+L,y:re*Z+le*X+I}},ee=Q(H,G),te=Q(H,U),K=Q(F,G),N=Q(F,U),$=[ee.x+O,ee.y+_,K.x+O,K.y+_,N.x+O,N.y+_,te.x+O,te.y+_];if(gt(t,e,$))return g(x),!0}else if(_r(A,t,e))return g(x),!0}}for(var E=s.length-1;E>=0;E--){var C=s[E];C.isNode()?p(C)||w(C):m(C)||w(C)||w(C,"source")||w(C,"target")}return o};Er.getAllInBox=function(t,e,r,a){var n=this.getCachedZSortedEles().interactive,i=[],s=Math.min(t,r),o=Math.max(t,r),l=Math.min(e,a),u=Math.max(e,a);t=s,r=o,e=l,a=u;for(var v=pt({x1:t,y1:e,x2:r,y2:a}),f=0;f<n.length;f++){var c=n[f];if(c.isNode()){var h=c,d=h.boundingBox({includeNodes:!0,includeEdges:!1,includeLabels:!1});qs(v,d)&&!rv(d,v)&&i.push(h)}else{var y=c,g=y._private,p=g.rscratch;if(p.startX!=null&&p.startY!=null&&!_r(v,p.startX,p.startY)||p.endX!=null&&p.endY!=null&&!_r(v,p.endX,p.endY))continue;if(p.edgeType==="bezier"||p.edgeType==="multibezier"||p.edgeType==="self"||p.edgeType==="compound"||p.edgeType==="segments"||p.edgeType==="haystack"){for(var m=g.rstyle.bezierPts||g.rstyle.linePts||g.rstyle.haystackPts,b=!0,w=0;w<m.length;w++)if(!ud(v,m[w])){b=!1;break}b&&i.push(y)}else(p.edgeType==="haystack"||p.edgeType==="straight")&&i.push(y)}}return i};var yn={};yn.calculateArrowAngles=function(t){var e=t._private.rscratch,r=e.edgeType==="haystack",a=e.edgeType==="bezier",n=e.edgeType==="multibezier",i=e.edgeType==="segments",s=e.edgeType==="compound",o=e.edgeType==="self",l,u,v,f,c,h,p,m;if(r?(v=e.haystackPts[0],f=e.haystackPts[1],c=e.haystackPts[2],h=e.haystackPts[3]):(v=e.arrowStartX,f=e.arrowStartY,c=e.arrowEndX,h=e.arrowEndY),p=e.midX,m=e.midY,i)l=v-e.segpts[0],u=f-e.segpts[1];else if(n||s||o||a){var d=e.allpts,y=Je(d[0],d[2],d[4],.1),g=Je(d[1],d[3],d[5],.1);l=v-y,u=f-g}else l=v-p,u=f-m;e.srcArrowAngle=qa(l,u);var p=e.midX,m=e.midY;if(r&&(p=(v+c)/2,m=(f+h)/2),l=c-v,u=h-f,i){var d=e.allpts;if(d.length/2%2===0){var b=d.length/2,w=b-2;l=d[b]-d[w],u=d[b+1]-d[w+1]}else if(e.isRound)l=e.midVector[1],u=-e.midVector[0];else{var b=d.length/2-1,w=b-2;l=d[b]-d[w],u=d[b+1]-d[w+1]}}else if(n||s||o){var d=e.allpts,E=e.ctrlpts,C,x,S,k;if(E.length/2%2===0){var B=d.length/2-1,D=B+2,A=D+2;C=Je(d[B],d[D],d[A],0),x=Je(d[B+1],d[D+1],d[A+1],0),S=Je(d[B],d[D],d[A],1e-4),k=Je(d[B+1],d[D+1],d[A+1],1e-4)}else{var D=d.length/2-1,B=D-2,A=D+2;C=Je(d[B],d[D],d[A],.4999),x=Je(d[B+1],d[D+1],d[A+1],.4999),S=Je(d[B],d[D],d[A],.5),k=Je(d[B+1],d[D+1],d[A+1],.5)}l=S-C,u=k-x}if(e.midtgtArrowAngle=qa(l,u),e.midDispX=l,e.midDispY=u,l*=-1,u*=-1,i){var d=e.allpts;if(d.length/2%2!==0){if(!e.isRound){var b=d.length/2-1,P=b+2;l=-(d[P]-d[b]),u=-(d[P+1]-d[b+1])}}}if(e.midsrcArrowAngle=qa(l,u),i)l=c-e.segpts[e.segpts.length-2],u=h-e.segpts[e.segpts.length-1];else if(n||s||o||a){var d=e.allpts,R=d.length,y=Je(d[R-6],d[R-4],d[R-2],.9),g=Je(d[R-5],d[R-3],d[R-1],.9);l=c-y,u=h-g}else l=c-p,u=h-m;e.tgtArrowAngle=qa(l,u)};yn.getArrowWidth=yn.getArrowHeight=function(t,e){var r=this.arrowWidthCache=this.arrowWidthCache||{},a=r[t+", "+e];return a||(a=Math.max(Math.pow(t*13.37,.9),29)*e,r[t+", "+e]=a,a)};var Cs,Ts,Ot={},xt={},vl,fl,gr,nn,Gt,fr,hr,It,Pr,Ua,Xv,Zv,Ss,Ds,cl,dl=function(e,r,a){a.x=r.x-e.x,a.y=r.y-e.y,a.len=Math.sqrt(a.x*a.x+a.y*a.y),a.nx=a.x/a.len,a.ny=a.y/a.len,a.ang=Math.atan2(a.ny,a.nx)},Bp=function(e,r){r.x=e.x*-1,r.y=e.y*-1,r.nx=e.nx*-1,r.ny=e.ny*-1,r.ang=e.ang>0?-(Math.PI-e.ang):Math.PI+e.ang},Ap=function(e,r,a,n,i){if(e!==cl?dl(r,e,Ot):Bp(xt,Ot),dl(r,a,xt),vl=Ot.nx*xt.ny-Ot.ny*xt.nx,fl=Ot.nx*xt.nx-Ot.ny*-xt.ny,Gt=Math.asin(Math.max(-1,Math.min(1,vl))),Math.abs(Gt)<1e-6){Cs=r.x,Ts=r.y,hr=Pr=0;return}gr=1,nn=!1,fl<0?Gt<0?Gt=Math.PI+Gt:(Gt=Math.PI-Gt,gr=-1,nn=!0):Gt>0&&(gr=-1,nn=!0),r.radius!==void 0?Pr=r.radius:Pr=n,fr=Gt/2,Ua=Math.min(Ot.len/2,xt.len/2),i?(It=Math.abs(Math.cos(fr)*Pr/Math.sin(fr)),It>Ua?(It=Ua,hr=Math.abs(It*Math.sin(fr)/Math.cos(fr))):hr=Pr):(It=Math.min(Ua,Pr),hr=Math.abs(It*Math.sin(fr)/Math.cos(fr))),Ss=r.x+xt.nx*It,Ds=r.y+xt.ny*It,Cs=Ss-xt.ny*hr*gr,Ts=Ds+xt.nx*hr*gr,Xv=r.x+Ot.nx*It,Zv=r.y+Ot.ny*It,cl=r};function Qv(t,e){e.radius===0?t.lineTo(e.cx,e.cy):t.arc(e.cx,e.cy,e.radius,e.startAngle,e.endAngle,e.counterClockwise)}function Qs(t,e,r,a){var n=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!0;return a===0||e.radius===0?{cx:e.x,cy:e.y,radius:0,startX:e.x,startY:e.y,stopX:e.x,stopY:e.y,startAngle:void 0,endAngle:void 0,counterClockwise:void 0}:(Ap(t,e,r,a,n),{cx:Cs,cy:Ts,radius:hr,startX:Xv,startY:Zv,stopX:Ss,stopY:Ds,startAngle:Ot.ang+Math.PI/2*gr,endAngle:xt.ang-Math.PI/2*gr,counterClockwise:nn})}var Ea=.01,Rp=Math.sqrt(2*Ea),lt={};lt.findMidptPtsEtc=function(t,e){var r=e.posPts,a=e.intersectionPts,n=e.vectorNormInverse,i,s=t.pstyle("source-endpoint"),o=t.pstyle("target-endpoint"),l=s.units!=null&&o.units!=null,u=function(E,C,x,S){var k=S-C,B=x-E,D=Math.sqrt(B*B+k*k);return{x:-k/D,y:B/D}},v=t.pstyle("edge-distances").value;switch(v){case"node-position":i=r;break;case"intersection":i=a;break;case"endpoints":{if(l){var f=this.manualEndptToPx(t.source()[0],s),c=je(f,2),h=c[0],d=c[1],y=this.manualEndptToPx(t.target()[0],o),g=je(y,2),p=g[0],m=g[1],b={x1:h,y1:d,x2:p,y2:m};n=u(h,d,p,m),i=b}else Re("Edge ".concat(t.id()," has edge-distances:endpoints specified without manual endpoints specified via source-endpoint and target-endpoint.  Falling back on edge-distances:intersection (default).")),i=a;break}}return{midptPts:i,vectorNormInverse:n}};lt.findHaystackPoints=function(t){for(var e=0;e<t.length;e++){var r=t[e],a=r._private,n=a.rscratch;if(!n.haystack){var i=Math.random()*2*Math.PI;n.source={x:Math.cos(i),y:Math.sin(i)},i=Math.random()*2*Math.PI,n.target={x:Math.cos(i),y:Math.sin(i)}}var s=a.source,o=a.target,l=s.position(),u=o.position(),v=s.width(),f=o.width(),c=s.height(),h=o.height(),d=r.pstyle("haystack-radius").value,y=d/2;n.haystackPts=n.allpts=[n.source.x*v*y+l.x,n.source.y*c*y+l.y,n.target.x*f*y+u.x,n.target.y*h*y+u.y],n.midX=(n.allpts[0]+n.allpts[2])/2,n.midY=(n.allpts[1]+n.allpts[3])/2,n.edgeType="haystack",n.haystack=!0,this.storeEdgeProjections(r),this.calculateArrowAngles(r),this.recalculateEdgeLabelProjections(r),this.calculateLabelAngles(r)}};lt.findSegmentsPoints=function(t,e){var r=t._private.rscratch,a=t.pstyle("segment-weights"),n=t.pstyle("segment-distances"),i=t.pstyle("segment-radii"),s=t.pstyle("radius-type"),o=Math.min(a.pfValue.length,n.pfValue.length),l=i.pfValue[i.pfValue.length-1],u=s.pfValue[s.pfValue.length-1];r.edgeType="segments",r.segpts=[],r.radii=[],r.isArcRadius=[];for(var v=0;v<o;v++){var f=a.pfValue[v],c=n.pfValue[v],h=1-f,d=f,y=this.findMidptPtsEtc(t,e),g=y.midptPts,p=y.vectorNormInverse,m={x:g.x1*h+g.x2*d,y:g.y1*h+g.y2*d};r.segpts.push(m.x+p.x*c,m.y+p.y*c),r.radii.push(i.pfValue[v]!==void 0?i.pfValue[v]:l),r.isArcRadius.push((s.pfValue[v]!==void 0?s.pfValue[v]:u)==="arc-radius")}};lt.findLoopPoints=function(t,e,r,a){var n=t._private.rscratch,i=e.dirCounts,s=e.srcPos,o=t.pstyle("control-point-distances"),l=o?o.pfValue[0]:void 0,u=t.pstyle("loop-direction").pfValue,v=t.pstyle("loop-sweep").pfValue,f=t.pstyle("control-point-step-size").pfValue;n.edgeType="self";var c=r,h=f;a&&(c=0,h=l);var d=u-Math.PI/2,y=d-v/2,g=d+v/2,p=u+"_"+v;c=i[p]===void 0?i[p]=0:++i[p],n.ctrlpts=[s.x+Math.cos(y)*1.4*h*(c/3+1),s.y+Math.sin(y)*1.4*h*(c/3+1),s.x+Math.cos(g)*1.4*h*(c/3+1),s.y+Math.sin(g)*1.4*h*(c/3+1)]};lt.findCompoundLoopPoints=function(t,e,r,a){var n=t._private.rscratch;n.edgeType="compound";var i=e.srcPos,s=e.tgtPos,o=e.srcW,l=e.srcH,u=e.tgtW,v=e.tgtH,f=t.pstyle("control-point-step-size").pfValue,c=t.pstyle("control-point-distances"),h=c?c.pfValue[0]:void 0,d=r,y=f;a&&(d=0,y=h);var g=50,p={x:i.x-o/2,y:i.y-l/2},m={x:s.x-u/2,y:s.y-v/2},b={x:Math.min(p.x,m.x),y:Math.min(p.y,m.y)},w=.5,E=Math.max(w,Math.log(o*Ea)),C=Math.max(w,Math.log(u*Ea));n.ctrlpts=[b.x,b.y-(1+Math.pow(g,1.12)/100)*y*(d/3+1)*E,b.x-(1+Math.pow(g,1.12)/100)*y*(d/3+1)*C,b.y]};lt.findStraightEdgePoints=function(t){t._private.rscratch.edgeType="straight"};lt.findBezierPoints=function(t,e,r,a,n){var i=t._private.rscratch,s=t.pstyle("control-point-step-size").pfValue,o=t.pstyle("control-point-distances"),l=t.pstyle("control-point-weights"),u=o&&l?Math.min(o.value.length,l.value.length):1,v=o?o.pfValue[0]:void 0,f=l.value[0],c=a;i.edgeType=c?"multibezier":"bezier",i.ctrlpts=[];for(var h=0;h<u;h++){var d=(.5-e.eles.length/2+r)*s*(n?-1:1),y=void 0,g=ev(d);c&&(v=o?o.pfValue[h]:s,f=l.value[h]),a?y=v:y=v!==void 0?g*v:void 0;var p=y!==void 0?y:d,m=1-f,b=f,w=this.findMidptPtsEtc(t,e),E=w.midptPts,C=w.vectorNormInverse,x={x:E.x1*m+E.x2*b,y:E.y1*m+E.y2*b};i.ctrlpts.push(x.x+C.x*p,x.y+C.y*p)}};lt.findTaxiPoints=function(t,e){var r=t._private.rscratch;r.edgeType="segments";var a="vertical",n="horizontal",i="leftward",s="rightward",o="downward",l="upward",u="auto",v=e.posPts,f=e.srcW,c=e.srcH,h=e.tgtW,d=e.tgtH,y=t.pstyle("edge-distances").value,g=y!=="node-position",p=t.pstyle("taxi-direction").value,m=p,b=t.pstyle("taxi-turn"),w=b.units==="%",E=b.pfValue,C=E<0,x=t.pstyle("taxi-turn-min-distance").pfValue,S=g?(f+h)/2:0,k=g?(c+d)/2:0,B=v.x2-v.x1,D=v.y2-v.y1,A=function(Y,ie){return Y>0?Math.max(Y-ie,0):Math.min(Y+ie,0)},P=A(B,S),R=A(D,k),L=!1;m===u?p=Math.abs(P)>Math.abs(R)?n:a:m===l||m===o?(p=a,L=!0):(m===i||m===s)&&(p=n,L=!0);var I=p===a,M=I?R:P,O=I?D:B,_=ev(O),H=!1;!(L&&(w||C))&&(m===o&&O<0||m===l&&O>0||m===i&&O>0||m===s&&O<0)&&(_*=-1,M=_*Math.abs(M),H=!0);var F;if(w){var G=E<0?1+E:E;F=G*M}else{var U=E<0?M:0;F=U+E*_}var X=function(Y){return Math.abs(Y)<x||Math.abs(Y)>=Math.abs(M)},Z=X(F),Q=X(Math.abs(M)-Math.abs(F)),ee=Z||Q;if(ee&&!H)if(I){var te=Math.abs(O)<=c/2,K=Math.abs(B)<=h/2;if(te){var N=(v.x1+v.x2)/2,$=v.y1,J=v.y2;r.segpts=[N,$,N,J]}else if(K){var re=(v.y1+v.y2)/2,le=v.x1,xe=v.x2;r.segpts=[le,re,xe,re]}else r.segpts=[v.x1,v.y2]}else{var Ie=Math.abs(O)<=f/2,Be=Math.abs(D)<=d/2;if(Ie){var se=(v.y1+v.y2)/2,ue=v.x1,de=v.x2;r.segpts=[ue,se,de,se]}else if(Be){var ye=(v.x1+v.x2)/2,he=v.y1,me=v.y2;r.segpts=[ye,he,ye,me]}else r.segpts=[v.x2,v.y1]}else if(I){var Ce=v.y1+F+(g?c/2*_:0),Se=v.x1,j=v.x2;r.segpts=[Se,Ce,j,Ce]}else{var T=v.x1+F+(g?f/2*_:0),q=v.y1,W=v.y2;r.segpts=[T,q,T,W]}if(r.isRound){var z=t.pstyle("taxi-radius").value,V=t.pstyle("radius-type").value[0]==="arc-radius";r.radii=new Array(r.segpts.length/2).fill(z),r.isArcRadius=new Array(r.segpts.length/2).fill(V)}};lt.tryToCorrectInvalidPoints=function(t,e){var r=t._private.rscratch;if(r.edgeType==="bezier"){var a=e.srcPos,n=e.tgtPos,i=e.srcW,s=e.srcH,o=e.tgtW,l=e.tgtH,u=e.srcShape,v=e.tgtShape,f=e.srcCornerRadius,c=e.tgtCornerRadius,h=e.srcRs,d=e.tgtRs,y=!ae(r.startX)||!ae(r.startY),g=!ae(r.arrowStartX)||!ae(r.arrowStartY),p=!ae(r.endX)||!ae(r.endY),m=!ae(r.arrowEndX)||!ae(r.arrowEndY),b=3,w=this.getArrowWidth(t.pstyle("width").pfValue,t.pstyle("arrow-scale").value)*this.arrowShapeWidth,E=b*w,C=yr({x:r.ctrlpts[0],y:r.ctrlpts[1]},{x:r.startX,y:r.startY}),x=C<E,S=yr({x:r.ctrlpts[0],y:r.ctrlpts[1]},{x:r.endX,y:r.endY}),k=S<E,B=!1;if(y||g||x){B=!0;var D={x:r.ctrlpts[0]-a.x,y:r.ctrlpts[1]-a.y},A=Math.sqrt(D.x*D.x+D.y*D.y),P={x:D.x/A,y:D.y/A},R=Math.max(i,s),L={x:r.ctrlpts[0]+P.x*2*R,y:r.ctrlpts[1]+P.y*2*R},I=u.intersectLine(a.x,a.y,i,s,L.x,L.y,0,f,h);x?(r.ctrlpts[0]=r.ctrlpts[0]+P.x*(E-C),r.ctrlpts[1]=r.ctrlpts[1]+P.y*(E-C)):(r.ctrlpts[0]=I[0]+P.x*E,r.ctrlpts[1]=I[1]+P.y*E)}if(p||m||k){B=!0;var M={x:r.ctrlpts[0]-n.x,y:r.ctrlpts[1]-n.y},O=Math.sqrt(M.x*M.x+M.y*M.y),_={x:M.x/O,y:M.y/O},H=Math.max(i,s),F={x:r.ctrlpts[0]+_.x*2*H,y:r.ctrlpts[1]+_.y*2*H},G=v.intersectLine(n.x,n.y,o,l,F.x,F.y,0,c,d);k?(r.ctrlpts[0]=r.ctrlpts[0]+_.x*(E-S),r.ctrlpts[1]=r.ctrlpts[1]+_.y*(E-S)):(r.ctrlpts[0]=G[0]+_.x*E,r.ctrlpts[1]=G[1]+_.y*E)}B&&this.findEndpoints(t)}};lt.storeAllpts=function(t){var e=t._private.rscratch;if(e.edgeType==="multibezier"||e.edgeType==="bezier"||e.edgeType==="self"||e.edgeType==="compound"){e.allpts=[],e.allpts.push(e.startX,e.startY);for(var r=0;r+1<e.ctrlpts.length;r+=2)e.allpts.push(e.ctrlpts[r],e.ctrlpts[r+1]),r+3<e.ctrlpts.length&&e.allpts.push((e.ctrlpts[r]+e.ctrlpts[r+2])/2,(e.ctrlpts[r+1]+e.ctrlpts[r+3])/2);e.allpts.push(e.endX,e.endY);var a,n;e.ctrlpts.length/2%2===0?(a=e.allpts.length/2-1,e.midX=e.allpts[a],e.midY=e.allpts[a+1]):(a=e.allpts.length/2-3,n=.5,e.midX=Je(e.allpts[a],e.allpts[a+2],e.allpts[a+4],n),e.midY=Je(e.allpts[a+1],e.allpts[a+3],e.allpts[a+5],n))}else if(e.edgeType==="straight")e.allpts=[e.startX,e.startY,e.endX,e.endY],e.midX=(e.startX+e.endX+e.arrowStartX+e.arrowEndX)/4,e.midY=(e.startY+e.endY+e.arrowStartY+e.arrowEndY)/4;else if(e.edgeType==="segments"){if(e.allpts=[],e.allpts.push(e.startX,e.startY),e.allpts.push.apply(e.allpts,e.segpts),e.allpts.push(e.endX,e.endY),e.isRound){e.roundCorners=[];for(var i=2;i+3<e.allpts.length;i+=2){var s=e.radii[i/2-1],o=e.isArcRadius[i/2-1];e.roundCorners.push(Qs({x:e.allpts[i-2],y:e.allpts[i-1]},{x:e.allpts[i],y:e.allpts[i+1],radius:s},{x:e.allpts[i+2],y:e.allpts[i+3]},s,o))}}if(e.segpts.length%4===0){var l=e.segpts.length/2,u=l-2;e.midX=(e.segpts[u]+e.segpts[l])/2,e.midY=(e.segpts[u+1]+e.segpts[l+1])/2}else{var v=e.segpts.length/2-1;if(!e.isRound)e.midX=e.segpts[v],e.midY=e.segpts[v+1];else{var f={x:e.segpts[v],y:e.segpts[v+1]},c=e.roundCorners[v/2];if(c.radius===0){var h={x:e.segpts[v+2],y:e.segpts[v+3]};e.midX=f.x,e.midY=f.y,e.midVector=[f.y-h.y,h.x-f.x]}else{var d=[f.x-c.cx,f.y-c.cy],y=c.radius/Math.sqrt(Math.pow(d[0],2)+Math.pow(d[1],2));d=d.map(function(g){return g*y}),e.midX=c.cx+d[0],e.midY=c.cy+d[1],e.midVector=d}}}}};lt.checkForInvalidEdgeWarning=function(t){var e=t[0]._private.rscratch;e.nodesOverlap||ae(e.startX)&&ae(e.startY)&&ae(e.endX)&&ae(e.endY)?e.loggedErr=!1:e.loggedErr||(e.loggedErr=!0,Re("Edge `"+t.id()+"` has invalid endpoints and so it is impossible to draw.  Adjust your edge style (e.g. control points) accordingly or use an alternative edge type.  This is expected behaviour when the source node and the target node overlap."))};lt.findEdgeControlPoints=function(t){var e=this;if(!(!t||t.length===0)){for(var r=this,a=r.cy,n=a.hasCompoundNodes(),i=new Kt,s=function(k,B){return[].concat(Il(k),[B?1:0]).join("-")},o=[],l=[],u=0;u<t.length;u++){var v=t[u],f=v._private,c=v.pstyle("curve-style").value;if(!(v.removed()||!v.takesUpSpace())){if(c==="haystack"){l.push(v);continue}var h=c==="unbundled-bezier"||c.endsWith("segments")||c==="straight"||c==="straight-triangle"||c.endsWith("taxi"),d=c==="unbundled-bezier"||c==="bezier",y=f.source,g=f.target,p=y.poolIndex(),m=g.poolIndex(),b=[p,m].sort(),w=s(b,h),E=i.get(w);E==null&&(E={eles:[]},o.push({pairId:b,edgeIsUnbundled:h}),i.set(w,E)),E.eles.push(v),h&&(E.hasUnbundled=!0),d&&(E.hasBezier=!0)}}for(var C=function(){var k=o[x],B=k.pairId,D=k.edgeIsUnbundled,A=s(B,D),P=i.get(A),R;if(!P.hasUnbundled){var L=P.eles[0].parallelEdges().filter(function(V){return V.isBundledBezier()});Fs(P.eles),L.forEach(function(V){return P.eles.push(V)}),P.eles.sort(function(V,ne){return V.poolIndex()-ne.poolIndex()})}var I=P.eles[0],M=I.source(),O=I.target();if(M.poolIndex()>O.poolIndex()){var _=M;M=O,O=_}var H=P.srcPos=M.position(),F=P.tgtPos=O.position(),G=P.srcW=M.outerWidth(),U=P.srcH=M.outerHeight(),X=P.tgtW=O.outerWidth(),Z=P.tgtH=O.outerHeight(),Q=P.srcShape=r.nodeShapes[e.getNodeShape(M)],ee=P.tgtShape=r.nodeShapes[e.getNodeShape(O)],te=P.srcCornerRadius=M.pstyle("corner-radius").value==="auto"?"auto":M.pstyle("corner-radius").pfValue,K=P.tgtCornerRadius=O.pstyle("corner-radius").value==="auto"?"auto":O.pstyle("corner-radius").pfValue,N=P.tgtRs=O._private.rscratch,$=P.srcRs=M._private.rscratch;P.dirCounts={north:0,west:0,south:0,east:0,northwest:0,southwest:0,northeast:0,southeast:0};for(var J=0;J<P.eles.length;J++){var re=P.eles[J],le=re[0]._private.rscratch,xe=re.pstyle("curve-style").value,Ie=xe==="unbundled-bezier"||xe.endsWith("segments")||xe.endsWith("taxi"),Be=!M.same(re.source());if(!P.calculatedIntersection&&M!==O&&(P.hasBezier||P.hasUnbundled)){P.calculatedIntersection=!0;var se=Q.intersectLine(H.x,H.y,G,U,F.x,F.y,0,te,$),ue=P.srcIntn=se,de=ee.intersectLine(F.x,F.y,X,Z,H.x,H.y,0,K,N),ye=P.tgtIntn=de,he=P.intersectionPts={x1:se[0],x2:de[0],y1:se[1],y2:de[1]},me=P.posPts={x1:H.x,x2:F.x,y1:H.y,y2:F.y},Ce=Math.abs(de[1]-se[1]),Se=Math.abs(de[0]-se[0]),j=Math.sqrt(Se*Se+Ce*Ce);ae(j)&&j>=Rp||(j=Math.sqrt(Math.max(Se*Se,Ea)+Math.max(Ce*Ce,Ea)));var T=P.vector={x:Se,y:Ce},q=P.vectorNorm={x:T.x/j,y:T.y/j},W={x:-q.y,y:q.x};P.nodesOverlap=!ae(j)||ee.checkPoint(se[0],se[1],0,X,Z,F.x,F.y,K,N)||Q.checkPoint(de[0],de[1],0,G,U,H.x,H.y,te,$),P.vectorNormInverse=W,R={nodesOverlap:P.nodesOverlap,dirCounts:P.dirCounts,calculatedIntersection:!0,hasBezier:P.hasBezier,hasUnbundled:P.hasUnbundled,eles:P.eles,srcPos:F,srcRs:N,tgtPos:H,tgtRs:$,srcW:X,srcH:Z,tgtW:G,tgtH:U,srcIntn:ye,tgtIntn:ue,srcShape:ee,tgtShape:Q,posPts:{x1:me.x2,y1:me.y2,x2:me.x1,y2:me.y1},intersectionPts:{x1:he.x2,y1:he.y2,x2:he.x1,y2:he.y1},vector:{x:-T.x,y:-T.y},vectorNorm:{x:-q.x,y:-q.y},vectorNormInverse:{x:-W.x,y:-W.y}}}var z=Be?R:P;le.nodesOverlap=z.nodesOverlap,le.srcIntn=z.srcIntn,le.tgtIntn=z.tgtIntn,le.isRound=xe.startsWith("round"),n&&(M.isParent()||M.isChild()||O.isParent()||O.isChild())&&(M.parents().anySame(O)||O.parents().anySame(M)||M.same(O)&&M.isParent())?e.findCompoundLoopPoints(re,z,J,Ie):M===O?e.findLoopPoints(re,z,J,Ie):xe.endsWith("segments")?e.findSegmentsPoints(re,z):xe.endsWith("taxi")?e.findTaxiPoints(re,z):xe==="straight"||!Ie&&P.eles.length%2===1&&J===Math.floor(P.eles.length/2)?e.findStraightEdgePoints(re):e.findBezierPoints(re,z,J,Ie,Be),e.findEndpoints(re),e.tryToCorrectInvalidPoints(re,z),e.checkForInvalidEdgeWarning(re),e.storeAllpts(re),e.storeEdgeProjections(re),e.calculateArrowAngles(re),e.recalculateEdgeLabelProjections(re),e.calculateLabelAngles(re)}},x=0;x<o.length;x++)C();this.findHaystackPoints(l)}};function Jv(t){var e=[];if(t!=null){for(var r=0;r<t.length;r+=2){var a=t[r],n=t[r+1];e.push({x:a,y:n})}return e}}lt.getSegmentPoints=function(t){var e=t[0]._private.rscratch;this.recalculateRenderedStyle(t);var r=e.edgeType;if(r==="segments")return Jv(e.segpts)};lt.getControlPoints=function(t){var e=t[0]._private.rscratch;this.recalculateRenderedStyle(t);var r=e.edgeType;if(r==="bezier"||r==="multibezier"||r==="self"||r==="compound")return Jv(e.ctrlpts)};lt.getEdgeMidpoint=function(t){var e=t[0]._private.rscratch;return this.recalculateRenderedStyle(t),{x:e.midX,y:e.midY}};var Ma={};Ma.manualEndptToPx=function(t,e){var r=this,a=t.position(),n=t.outerWidth(),i=t.outerHeight(),s=t._private.rscratch;if(e.value.length===2){var o=[e.pfValue[0],e.pfValue[1]];return e.units[0]==="%"&&(o[0]=o[0]*n),e.units[1]==="%"&&(o[1]=o[1]*i),o[0]+=a.x,o[1]+=a.y,o}else{var l=e.pfValue[0];l=-Math.PI/2+l;var u=2*Math.max(n,i),v=[a.x+Math.cos(l)*u,a.y+Math.sin(l)*u];return r.nodeShapes[this.getNodeShape(t)].intersectLine(a.x,a.y,n,i,v[0],v[1],0,t.pstyle("corner-radius").value==="auto"?"auto":t.pstyle("corner-radius").pfValue,s)}};Ma.findEndpoints=function(t){var e=this,r,a=t.source()[0],n=t.target()[0],i=a.position(),s=n.position(),o=t.pstyle("target-arrow-shape").value,l=t.pstyle("source-arrow-shape").value,u=t.pstyle("target-distance-from-node").pfValue,v=t.pstyle("source-distance-from-node").pfValue,f=a._private.rscratch,c=n._private.rscratch,h=t.pstyle("curve-style").value,d=t._private.rscratch,y=d.edgeType,g=h==="taxi",p=y==="self"||y==="compound",m=y==="bezier"||y==="multibezier"||p,b=y!=="bezier",w=y==="straight"||y==="segments",E=y==="segments",C=m||b||w,x=p||g,S=t.pstyle("source-endpoint"),k=x?"outside-to-node":S.value,B=a.pstyle("corner-radius").value==="auto"?"auto":a.pstyle("corner-radius").pfValue,D=t.pstyle("target-endpoint"),A=x?"outside-to-node":D.value,P=n.pstyle("corner-radius").value==="auto"?"auto":n.pstyle("corner-radius").pfValue;d.srcManEndpt=S,d.tgtManEndpt=D;var R,L,I,M;if(m){var O=[d.ctrlpts[0],d.ctrlpts[1]],_=b?[d.ctrlpts[d.ctrlpts.length-2],d.ctrlpts[d.ctrlpts.length-1]]:O;R=_,L=O}else if(w){var H=E?d.segpts.slice(0,2):[s.x,s.y],F=E?d.segpts.slice(d.segpts.length-2):[i.x,i.y];R=F,L=H}if(A==="inside-to-node")r=[s.x,s.y];else if(D.units)r=this.manualEndptToPx(n,D);else if(A==="outside-to-line")r=d.tgtIntn;else if(A==="outside-to-node"||A==="outside-to-node-or-label"?I=R:(A==="outside-to-line"||A==="outside-to-line-or-label")&&(I=[i.x,i.y]),r=e.nodeShapes[this.getNodeShape(n)].intersectLine(s.x,s.y,n.outerWidth(),n.outerHeight(),I[0],I[1],0,P,c),A==="outside-to-node-or-label"||A==="outside-to-line-or-label"){var G=n._private.rscratch,U=G.labelWidth,X=G.labelHeight,Z=G.labelX,Q=G.labelY,ee=U/2,te=X/2,K=n.pstyle("text-valign").value;K==="top"?Q-=te:K==="bottom"&&(Q+=te);var N=n.pstyle("text-halign").value;N==="left"?Z-=ee:N==="right"&&(Z+=ee);var $=ya(I[0],I[1],[Z-ee,Q-te,Z+ee,Q-te,Z+ee,Q+te,Z-ee,Q+te],s.x,s.y);if($.length>0){var J=i,re=cr(J,Lr(r)),le=cr(J,Lr($)),xe=re;if(le<re&&(r=$,xe=le),$.length>2){var Ie=cr(J,{x:$[2],y:$[3]});Ie<xe&&(r=[$[2],$[3]])}}}var Be=Va(r,R,e.arrowShapes[o].spacing(t)+u),se=Va(r,R,e.arrowShapes[o].gap(t)+u);if(d.endX=se[0],d.endY=se[1],d.arrowEndX=Be[0],d.arrowEndY=Be[1],k==="inside-to-node")r=[i.x,i.y];else if(S.units)r=this.manualEndptToPx(a,S);else if(k==="outside-to-line")r=d.srcIntn;else if(k==="outside-to-node"||k==="outside-to-node-or-label"?M=L:(k==="outside-to-line"||k==="outside-to-line-or-label")&&(M=[s.x,s.y]),r=e.nodeShapes[this.getNodeShape(a)].intersectLine(i.x,i.y,a.outerWidth(),a.outerHeight(),M[0],M[1],0,B,f),k==="outside-to-node-or-label"||k==="outside-to-line-or-label"){var ue=a._private.rscratch,de=ue.labelWidth,ye=ue.labelHeight,he=ue.labelX,me=ue.labelY,Ce=de/2,Se=ye/2,j=a.pstyle("text-valign").value;j==="top"?me-=Se:j==="bottom"&&(me+=Se);var T=a.pstyle("text-halign").value;T==="left"?he-=Ce:T==="right"&&(he+=Ce);var q=ya(M[0],M[1],[he-Ce,me-Se,he+Ce,me-Se,he+Ce,me+Se,he-Ce,me+Se],i.x,i.y);if(q.length>0){var W=s,z=cr(W,Lr(r)),V=cr(W,Lr(q)),ne=z;if(V<z&&(r=[q[0],q[1]],ne=V),q.length>2){var Y=cr(W,{x:q[2],y:q[3]});Y<ne&&(r=[q[2],q[3]])}}}var ie=Va(r,L,e.arrowShapes[l].spacing(t)+v),ce=Va(r,L,e.arrowShapes[l].gap(t)+v);d.startX=ce[0],d.startY=ce[1],d.arrowStartX=ie[0],d.arrowStartY=ie[1],C&&(!ae(d.startX)||!ae(d.startY)||!ae(d.endX)||!ae(d.endY)?d.badLine=!0:d.badLine=!1)};Ma.getSourceEndpoint=function(t){var e=t[0]._private.rscratch;switch(this.recalculateRenderedStyle(t),e.edgeType){case"haystack":return{x:e.haystackPts[0],y:e.haystackPts[1]};default:return{x:e.arrowStartX,y:e.arrowStartY}}};Ma.getTargetEndpoint=function(t){var e=t[0]._private.rscratch;switch(this.recalculateRenderedStyle(t),e.edgeType){case"haystack":return{x:e.haystackPts[2],y:e.haystackPts[3]};default:return{x:e.arrowEndX,y:e.arrowEndY}}};var Js={};function Mp(t,e,r){for(var a=function(u,v,f,c){return Je(u,v,f,c)},n=e._private,i=n.rstyle.bezierPts,s=0;s<t.bezierProjPcts.length;s++){var o=t.bezierProjPcts[s];i.push({x:a(r[0],r[2],r[4],o),y:a(r[1],r[3],r[5],o)})}}Js.storeEdgeProjections=function(t){var e=t._private,r=e.rscratch,a=r.edgeType;if(e.rstyle.bezierPts=null,e.rstyle.linePts=null,e.rstyle.haystackPts=null,a==="multibezier"||a==="bezier"||a==="self"||a==="compound"){e.rstyle.bezierPts=[];for(var n=0;n+5<r.allpts.length;n+=4)Mp(this,t,r.allpts.slice(n,n+6))}else if(a==="segments")for(var i=e.rstyle.linePts=[],n=0;n+1<r.allpts.length;n+=2)i.push({x:r.allpts[n],y:r.allpts[n+1]});else if(a==="haystack"){var s=r.haystackPts;e.rstyle.haystackPts=[{x:s[0],y:s[1]},{x:s[2],y:s[3]}]}e.rstyle.arrowWidth=this.getArrowWidth(t.pstyle("width").pfValue,t.pstyle("arrow-scale").value)*this.arrowShapeWidth};Js.recalculateEdgeProjections=function(t){this.findEdgeControlPoints(t)};var zt={};zt.recalculateNodeLabelProjection=function(t){var e=t.pstyle("label").strValue;if(!tr(e)){var r,a,n=t._private,i=t.width(),s=t.height(),o=t.padding(),l=t.position(),u=t.pstyle("text-halign").strValue,v=t.pstyle("text-valign").strValue,f=n.rscratch,c=n.rstyle;switch(u){case"left":r=l.x-i/2-o;break;case"right":r=l.x+i/2+o;break;default:r=l.x}switch(v){case"top":a=l.y-s/2-o;break;case"bottom":a=l.y+s/2+o;break;default:a=l.y}f.labelX=r,f.labelY=a,c.labelX=r,c.labelY=a,this.calculateLabelAngles(t),this.applyLabelDimensions(t)}};var jv=function(e,r){var a=Math.atan(r/e);return e===0&&a<0&&(a=a*-1),a},ef=function(e,r){var a=r.x-e.x,n=r.y-e.y;return jv(a,n)},Lp=function(e,r,a,n){var i=pa(0,n-.001,1),s=pa(0,n+.001,1),o=Nr(e,r,a,i),l=Nr(e,r,a,s);return ef(o,l)};zt.recalculateEdgeLabelProjections=function(t){var e,r=t._private,a=r.rscratch,n=this,i={mid:t.pstyle("label").strValue,source:t.pstyle("source-label").strValue,target:t.pstyle("target-label").strValue};if(i.mid||i.source||i.target){e={x:a.midX,y:a.midY};var s=function(f,c,h){Ht(r.rscratch,f,c,h),Ht(r.rstyle,f,c,h)};s("labelX",null,e.x),s("labelY",null,e.y);var o=jv(a.midDispX,a.midDispY);s("labelAutoAngle",null,o);var l=function(){if(l.cache)return l.cache;for(var f=[],c=0;c+5<a.allpts.length;c+=4){var h={x:a.allpts[c],y:a.allpts[c+1]},d={x:a.allpts[c+2],y:a.allpts[c+3]},y={x:a.allpts[c+4],y:a.allpts[c+5]};f.push({p0:h,p1:d,p2:y,startDist:0,length:0,segments:[]})}var g=r.rstyle.bezierPts,p=n.bezierProjPcts.length;function m(x,S,k,B,D){var A=yr(S,k),P=x.segments[x.segments.length-1],R={p0:S,p1:k,t0:B,t1:D,startDist:P?P.startDist+P.length:0,length:A};x.segments.push(R),x.length+=A}for(var b=0;b<f.length;b++){var w=f[b],E=f[b-1];E&&(w.startDist=E.startDist+E.length),m(w,w.p0,g[b*p],0,n.bezierProjPcts[0]);for(var C=0;C<p-1;C++)m(w,g[b*p+C],g[b*p+C+1],n.bezierProjPcts[C],n.bezierProjPcts[C+1]);m(w,g[b*p+p-1],w.p2,n.bezierProjPcts[p-1],1)}return l.cache=f},u=function(f){var c,h=f==="source";if(i[f]){var d=t.pstyle(f+"-text-offset").pfValue;switch(a.edgeType){case"self":case"compound":case"bezier":case"multibezier":{for(var y=l(),g,p=0,m=0,b=0;b<y.length;b++){for(var w=y[h?b:y.length-1-b],E=0;E<w.segments.length;E++){var C=w.segments[h?E:w.segments.length-1-E],x=b===y.length-1&&E===w.segments.length-1;if(p=m,m+=C.length,m>=d||x){g={cp:w,segment:C};break}}if(g)break}var S=g.cp,k=g.segment,B=(d-p)/k.length,D=k.t1-k.t0,A=h?k.t0+D*B:k.t1-D*B;A=pa(0,A,1),e=Nr(S.p0,S.p1,S.p2,A),c=Lp(S.p0,S.p1,S.p2,A);break}case"straight":case"segments":case"haystack":{for(var P=0,R,L,I,M,O=a.allpts.length,_=0;_+3<O&&(h?(I={x:a.allpts[_],y:a.allpts[_+1]},M={x:a.allpts[_+2],y:a.allpts[_+3]}):(I={x:a.allpts[O-2-_],y:a.allpts[O-1-_]},M={x:a.allpts[O-4-_],y:a.allpts[O-3-_]}),R=yr(I,M),L=P,P+=R,!(P>=d));_+=2);var H=d-L,F=H/R;F=pa(0,F,1),e=ad(I,M,F),c=ef(I,M);break}}s("labelX",f,e.x),s("labelY",f,e.y),s("labelAutoAngle",f,c)}};u("source"),u("target"),this.applyLabelDimensions(t)}};zt.applyLabelDimensions=function(t){this.applyPrefixedLabelDimensions(t),t.isEdge()&&(this.applyPrefixedLabelDimensions(t,"source"),this.applyPrefixedLabelDimensions(t,"target"))};zt.applyPrefixedLabelDimensions=function(t,e){var r=t._private,a=this.getLabelText(t,e),n=rr(a,t._private.labelDimsKey);if(Et(r.rscratch,"prefixedLabelDimsKey",e)!==n){Ht(r.rscratch,"prefixedLabelDimsKey",e,n);var i=this.calculateLabelDimensions(t,a),s=t.pstyle("line-height").pfValue,o=t.pstyle("text-wrap").strValue,l=Et(r.rscratch,"labelWrapCachedLines",e)||[],u=o!=="wrap"?1:Math.max(l.length,1),v=i.height/u,f=v*s,c=i.width,h=i.height+(u-1)*(s-1)*v;Ht(r.rstyle,"labelWidth",e,c),Ht(r.rscratch,"labelWidth",e,c),Ht(r.rstyle,"labelHeight",e,h),Ht(r.rscratch,"labelHeight",e,h),Ht(r.rscratch,"labelLineHeight",e,f)}};zt.getLabelText=function(t,e){var r=t._private,a=e?e+"-":"",n=t.pstyle(a+"label").strValue,i=t.pstyle("text-transform").value,s=function(U,X){return X?(Ht(r.rscratch,U,e,X),X):Et(r.rscratch,U,e)};if(!n)return"";i=="none"||(i=="uppercase"?n=n.toUpperCase():i=="lowercase"&&(n=n.toLowerCase()));var o=t.pstyle("text-wrap").value;if(o==="wrap"){var l=s("labelKey");if(l!=null&&s("labelWrapKey")===l)return s("labelWrapCachedText");for(var u="​",v=n.split(`
`),f=t.pstyle("text-max-width").pfValue,c=t.pstyle("text-overflow-wrap").value,h=c==="anywhere",d=[],y=/[\s\u200b]+|$/g,g=0;g<v.length;g++){var p=v[g],m=this.calculateLabelDimensions(t,p),b=m.width;if(h){var w=p.split("").join(u);p=w}if(b>f){var E=p.matchAll(y),C="",x=0,S=Pt(E),k;try{for(S.s();!(k=S.n()).done;){var B=k.value,D=B[0],A=p.substring(x,B.index);x=B.index+D.length;var P=C.length===0?A:C+A+D,R=this.calculateLabelDimensions(t,P),L=R.width;L<=f?C+=A+D:(C&&d.push(C),C=A+D)}}catch(G){S.e(G)}finally{S.f()}C.match(/^[\s\u200b]+$/)||d.push(C)}else d.push(p)}s("labelWrapCachedLines",d),n=s("labelWrapCachedText",d.join(`
`)),s("labelWrapKey",l)}else if(o==="ellipsis"){var I=t.pstyle("text-max-width").pfValue,M="",O="…",_=!1;if(this.calculateLabelDimensions(t,n).width<I)return n;for(var H=0;H<n.length;H++){var F=this.calculateLabelDimensions(t,M+n[H]+O).width;if(F>I)break;M+=n[H],H===n.length-1&&(_=!0)}return _||(M+=O),M}return n};zt.getLabelJustification=function(t){var e=t.pstyle("text-justification").strValue,r=t.pstyle("text-halign").strValue;if(e==="auto")if(t.isNode())switch(r){case"left":return"right";case"right":return"left";default:return"center"}else return"center";else return e};zt.calculateLabelDimensions=function(t,e){var r=this,a=r.cy.window(),n=a.document,i=0,s=t.pstyle("font-style").strValue,o=t.pstyle("font-size").pfValue,l=t.pstyle("font-family").strValue,u=t.pstyle("font-weight").strValue,v=this.labelCalcCanvas,f=this.labelCalcCanvasContext;if(!v){v=this.labelCalcCanvas=n.createElement("canvas"),f=this.labelCalcCanvasContext=v.getContext("2d");var c=v.style;c.position="absolute",c.left="-9999px",c.top="-9999px",c.zIndex="-1",c.visibility="hidden",c.pointerEvents="none"}f.font="".concat(s," ").concat(u," ").concat(o,"px ").concat(l);for(var h=0,d=0,y=e.split(`
`),g=0;g<y.length;g++){var p=y[g],m=f.measureText(p),b=Math.ceil(m.width),w=o;h=Math.max(b,h),d+=w}return h+=i,d+=i,{width:h,height:d}};zt.calculateLabelAngle=function(t,e){var r=t._private,a=r.rscratch,n=t.isEdge(),i=e?e+"-":"",s=t.pstyle(i+"text-rotation"),o=s.strValue;return o==="none"?0:n&&o==="autorotate"?a.labelAutoAngle:o==="autorotate"?0:s.pfValue};zt.calculateLabelAngles=function(t){var e=this,r=t.isEdge(),a=t._private,n=a.rscratch;n.labelAngle=e.calculateLabelAngle(t),r&&(n.sourceLabelAngle=e.calculateLabelAngle(t,"source"),n.targetLabelAngle=e.calculateLabelAngle(t,"target"))};var tf={},hl=28,gl=!1;tf.getNodeShape=function(t){var e=this,r=t.pstyle("shape").value;if(r==="cutrectangle"&&(t.width()<hl||t.height()<hl))return gl||(Re("The `cutrectangle` node shape can not be used at small sizes so `rectangle` is used instead"),gl=!0),"rectangle";if(t.isParent())return r==="rectangle"||r==="roundrectangle"||r==="round-rectangle"||r==="cutrectangle"||r==="cut-rectangle"||r==="barrel"?r:"rectangle";if(r==="polygon"){var a=t.pstyle("shape-polygon-points").value;return e.nodeShapes.makePolygon(a).name}return r};var Fn={};Fn.registerCalculationListeners=function(){var t=this.cy,e=t.collection(),r=this,a=function(s){var o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;if(e.merge(s),o)for(var l=0;l<s.length;l++){var u=s[l],v=u._private,f=v.rstyle;f.clean=!1,f.cleanConnected=!1}};r.binder(t).on("bounds.* dirty.*",function(s){var o=s.target;a(o)}).on("style.* background.*",function(s){var o=s.target;a(o,!1)});var n=function(s){if(s){var o=r.onUpdateEleCalcsFns;e.cleanStyle();for(var l=0;l<e.length;l++){var u=e[l],v=u._private.rstyle;u.isNode()&&!v.cleanConnected&&(a(u.connectedEdges()),v.cleanConnected=!0)}if(o)for(var f=0;f<o.length;f++){var c=o[f];c(s,e)}r.recalculateRenderedStyle(e),e=t.collection()}};r.flushRenderedStyleQueue=function(){n(!0)},r.beforeRender(n,r.beforeRenderPriorities.eleCalcs)};Fn.onUpdateEleCalcs=function(t){var e=this.onUpdateEleCalcsFns=this.onUpdateEleCalcsFns||[];e.push(t)};Fn.recalculateRenderedStyle=function(t,e){var r=function(w){return w._private.rstyle.cleanConnected};if(t.length!==0){var a=[],n=[];if(!this.destroyed){e===void 0&&(e=!0);for(var i=0;i<t.length;i++){var s=t[i],o=s._private,l=o.rstyle;s.isEdge()&&(!r(s.source())||!r(s.target()))&&(l.clean=!1),s.isEdge()&&s.isBundledBezier()&&s.parallelEdges().some(function(b){return!b._private.rstyle.clean&&b.isBundledBezier()})&&(l.clean=!1),!(e&&l.clean||s.removed())&&s.pstyle("display").value!=="none"&&(o.group==="nodes"?n.push(s):a.push(s),l.clean=!0)}for(var u=0;u<n.length;u++){var v=n[u],f=v._private,c=f.rstyle,h=v.position();this.recalculateNodeLabelProjection(v),c.nodeX=h.x,c.nodeY=h.y,c.nodeW=v.pstyle("width").pfValue,c.nodeH=v.pstyle("height").pfValue}this.recalculateEdgeProjections(a);for(var d=0;d<a.length;d++){var y=a[d],g=y._private,p=g.rstyle,m=g.rscratch;p.srcX=m.arrowStartX,p.srcY=m.arrowStartY,p.tgtX=m.arrowEndX,p.tgtY=m.arrowEndY,p.midX=m.midX,p.midY=m.midY,p.labelAngle=m.labelAngle,p.sourceLabelAngle=m.sourceLabelAngle,p.targetLabelAngle=m.targetLabelAngle}}}};var zn={};zn.updateCachedGrabbedEles=function(){var t=this.cachedZSortedEles;if(t){t.drag=[],t.nondrag=[];for(var e=[],r=0;r<t.length;r++){var a=t[r],n=a._private.rscratch;a.grabbed()&&!a.isParent()?e.push(a):n.inDragLayer?t.drag.push(a):t.nondrag.push(a)}for(var r=0;r<e.length;r++){var a=e[r];t.drag.push(a)}}};zn.invalidateCachedZSortedEles=function(){this.cachedZSortedEles=null};zn.getCachedZSortedEles=function(t){if(t||!this.cachedZSortedEles){var e=this.cy.mutableElements().toArray();e.sort(Nv),e.interactive=e.filter(function(r){return r.interactive()}),this.cachedZSortedEles=e,this.updateCachedGrabbedEles()}else e=this.cachedZSortedEles;return e};var rf={};[Er,yn,lt,Ma,Js,zt,tf,Fn,zn].forEach(function(t){ge(rf,t)});var af={};af.getCachedImage=function(t,e,r){var a=this,n=a.imageCache=a.imageCache||{},i=n[t];if(i)return i.image.complete||i.image.addEventListener("load",r),i.image;i=n[t]=n[t]||{};var s=i.image=new Image;s.addEventListener("load",r),s.addEventListener("error",function(){s.error=!0});var o="data:",l=t.substring(0,o.length).toLowerCase()===o;return l||(e=e==="null"?null:e,s.crossOrigin=e),s.src=t,s};var Xr={};Xr.registerBinding=function(t,e,r,a){var n=Array.prototype.slice.apply(arguments,[1]);if(Array.isArray(t)){for(var i=[],s=0;s<t.length;s++){var o=t[s];if(o!==void 0){var l=this.binder(o);i.push(l.on.apply(l,n))}}return i}var l=this.binder(t);return l.on.apply(l,n)};Xr.binder=function(t){var e=this,r=e.cy.window(),a=t===r||t===r.document||t===r.document.body||Zf(t);if(e.supportsPassiveEvents==null){var n=!1;try{var i=Object.defineProperty({},"passive",{get:function(){return n=!0,!0}});r.addEventListener("test",null,i)}catch{}e.supportsPassiveEvents=n}var s=function(l,u,v){var f=Array.prototype.slice.call(arguments);return a&&e.supportsPassiveEvents&&(f[2]={capture:v??!1,passive:!1,once:!1}),e.bindings.push({target:t,args:f}),(t.addEventListener||t.on).apply(t,f),this};return{on:s,addEventListener:s,addListener:s,bind:s}};Xr.nodeIsDraggable=function(t){return t&&t.isNode()&&!t.locked()&&t.grabbable()};Xr.nodeIsGrabbable=function(t){return this.nodeIsDraggable(t)&&t.interactive()};Xr.load=function(){var t=this,e=t.cy.window(),r=function(T){return T.selected()},a=function(T){var q=T.getRootNode();if(q&&q.nodeType===11&&q.host!==void 0)return q},n=function(T,q,W,z){T==null&&(T=t.cy);for(var V=0;V<q.length;V++){var ne=q[V];T.emit({originalEvent:W,type:ne,position:z})}},i=function(T){return T.shiftKey||T.metaKey||T.ctrlKey},s=function(T,q){var W=!0;if(t.cy.hasCompoundNodes()&&T&&T.pannable())for(var z=0;q&&z<q.length;z++){var T=q[z];if(T.isNode()&&T.isParent()&&!T.pannable()){W=!1;break}}else W=!0;return W},o=function(T){T[0]._private.grabbed=!0},l=function(T){T[0]._private.grabbed=!1},u=function(T){T[0]._private.rscratch.inDragLayer=!0},v=function(T){T[0]._private.rscratch.inDragLayer=!1},f=function(T){T[0]._private.rscratch.isGrabTarget=!0},c=function(T){T[0]._private.rscratch.isGrabTarget=!1},h=function(T,q){var W=q.addToList,z=W.has(T);!z&&T.grabbable()&&!T.locked()&&(W.merge(T),o(T))},d=function(T,q){if(T.cy().hasCompoundNodes()&&!(q.inDragLayer==null&&q.addToList==null)){var W=T.descendants();q.inDragLayer&&(W.forEach(u),W.connectedEdges().forEach(u)),q.addToList&&h(W,q)}},y=function(T,q){q=q||{};var W=T.cy().hasCompoundNodes();q.inDragLayer&&(T.forEach(u),T.neighborhood().stdFilter(function(z){return!W||z.isEdge()}).forEach(u)),q.addToList&&T.forEach(function(z){h(z,q)}),d(T,q),m(T,{inDragLayer:q.inDragLayer}),t.updateCachedGrabbedEles()},g=y,p=function(T){T&&(t.getCachedZSortedEles().forEach(function(q){l(q),v(q),c(q)}),t.updateCachedGrabbedEles())},m=function(T,q){if(!(q.inDragLayer==null&&q.addToList==null)&&T.cy().hasCompoundNodes()){var W=T.ancestors().orphans();if(!W.same(T)){var z=W.descendants().spawnSelf().merge(W).unmerge(T).unmerge(T.descendants()),V=z.connectedEdges();q.inDragLayer&&(V.forEach(u),z.forEach(u)),q.addToList&&z.forEach(function(ne){h(ne,q)})}}},b=function(){document.activeElement!=null&&document.activeElement.blur!=null&&document.activeElement.blur()},w=typeof MutationObserver<"u",E=typeof ResizeObserver<"u";w?(t.removeObserver=new MutationObserver(function(j){for(var T=0;T<j.length;T++){var q=j[T],W=q.removedNodes;if(W)for(var z=0;z<W.length;z++){var V=W[z];if(V===t.container){t.destroy();break}}}}),t.container.parentNode&&t.removeObserver.observe(t.container.parentNode,{childList:!0})):t.registerBinding(t.container,"DOMNodeRemoved",function(j){t.destroy()});var C=Pa(function(){t.cy.resize()},100);w&&(t.styleObserver=new MutationObserver(C),t.styleObserver.observe(t.container,{attributes:!0})),t.registerBinding(e,"resize",C),E&&(t.resizeObserver=new ResizeObserver(C),t.resizeObserver.observe(t.container));var x=function(T,q){for(;T!=null;)q(T),T=T.parentNode},S=function(){t.invalidateContainerClientCoordsCache()};x(t.container,function(j){t.registerBinding(j,"transitionend",S),t.registerBinding(j,"animationend",S),t.registerBinding(j,"scroll",S)}),t.registerBinding(t.container,"contextmenu",function(j){j.preventDefault()});var k=function(){return t.selection[4]!==0},B=function(T){for(var q=t.findContainerClientCoords(),W=q[0],z=q[1],V=q[2],ne=q[3],Y=T.touches?T.touches:[T],ie=!1,ce=0;ce<Y.length;ce++){var Ee=Y[ce];if(W<=Ee.clientX&&Ee.clientX<=W+V&&z<=Ee.clientY&&Ee.clientY<=z+ne){ie=!0;break}}if(!ie)return!1;for(var ve=t.container,be=T.target,we=be.parentNode,pe=!1;we;){if(we===ve){pe=!0;break}we=we.parentNode}return!!pe};t.registerBinding(t.container,"mousedown",function(T){if(B(T)&&!(t.hoverData.which===1&&T.which!==1)){T.preventDefault(),b(),t.hoverData.capture=!0,t.hoverData.which=T.which;var q=t.cy,W=[T.clientX,T.clientY],z=t.projectIntoViewport(W[0],W[1]),V=t.selection,ne=t.findNearestElements(z[0],z[1],!0,!1),Y=ne[0],ie=t.dragData.possibleDragElements;t.hoverData.mdownPos=z,t.hoverData.mdownGPos=W;var ce=function(){t.hoverData.tapholdCancelled=!1,clearTimeout(t.hoverData.tapholdTimeout),t.hoverData.tapholdTimeout=setTimeout(function(){if(!t.hoverData.tapholdCancelled){var Oe=t.hoverData.down;Oe?Oe.emit({originalEvent:T,type:"taphold",position:{x:z[0],y:z[1]}}):q.emit({originalEvent:T,type:"taphold",position:{x:z[0],y:z[1]}})}},t.tapholdDuration)};if(T.which==3){t.hoverData.cxtStarted=!0;var Ee={originalEvent:T,type:"cxttapstart",position:{x:z[0],y:z[1]}};Y?(Y.activate(),Y.emit(Ee),t.hoverData.down=Y):q.emit(Ee),t.hoverData.downTime=new Date().getTime(),t.hoverData.cxtDragged=!1}else if(T.which==1){Y&&Y.activate();{if(Y!=null&&t.nodeIsGrabbable(Y)){var ve=function(Oe){return{originalEvent:T,type:Oe,position:{x:z[0],y:z[1]}}},be=function(Oe){Oe.emit(ve("grab"))};if(f(Y),!Y.selected())ie=t.dragData.possibleDragElements=q.collection(),g(Y,{addToList:ie}),Y.emit(ve("grabon")).emit(ve("grab"));else{ie=t.dragData.possibleDragElements=q.collection();var we=q.$(function(pe){return pe.isNode()&&pe.selected()&&t.nodeIsGrabbable(pe)});y(we,{addToList:ie}),Y.emit(ve("grabon")),we.forEach(be)}t.redrawHint("eles",!0),t.redrawHint("drag",!0)}t.hoverData.down=Y,t.hoverData.downs=ne,t.hoverData.downTime=new Date().getTime()}n(Y,["mousedown","tapstart","vmousedown"],T,{x:z[0],y:z[1]}),Y==null?(V[4]=1,t.data.bgActivePosistion={x:z[0],y:z[1]},t.redrawHint("select",!0),t.redraw()):Y.pannable()&&(V[4]=1),ce()}V[0]=V[2]=z[0],V[1]=V[3]=z[1]}},!1);var D=a(t.container);t.registerBinding([e,D],"mousemove",function(T){var q=t.hoverData.capture;if(!(!q&&!B(T))){var W=!1,z=t.cy,V=z.zoom(),ne=[T.clientX,T.clientY],Y=t.projectIntoViewport(ne[0],ne[1]),ie=t.hoverData.mdownPos,ce=t.hoverData.mdownGPos,Ee=t.selection,ve=null;!t.hoverData.draggingEles&&!t.hoverData.dragging&&!t.hoverData.selecting&&(ve=t.findNearestElement(Y[0],Y[1],!0,!1));var be=t.hoverData.last,we=t.hoverData.down,pe=[Y[0]-Ee[2],Y[1]-Ee[3]],Oe=t.dragData.possibleDragElements,qe;if(ce){var yt=ne[0]-ce[0],mt=yt*yt,He=ne[1]-ce[1],Xe=He*He,Ze=mt+Xe;t.hoverData.isOverThresholdDrag=qe=Ze>=t.desktopTapThreshold2}var vt=i(T);qe&&(t.hoverData.tapholdCancelled=!0);var ft=function(){var Lt=t.hoverData.dragDelta=t.hoverData.dragDelta||[];Lt.length===0?(Lt.push(pe[0]),Lt.push(pe[1])):(Lt[0]+=pe[0],Lt[1]+=pe[1])};W=!0,n(ve,["mousemove","vmousemove","tapdrag"],T,{x:Y[0],y:Y[1]});var Rt=function(){t.data.bgActivePosistion=void 0,t.hoverData.selecting||z.emit({originalEvent:T,type:"boxstart",position:{x:Y[0],y:Y[1]}}),Ee[4]=1,t.hoverData.selecting=!0,t.redrawHint("select",!0),t.redraw()};if(t.hoverData.which===3){if(qe){var wt={originalEvent:T,type:"cxtdrag",position:{x:Y[0],y:Y[1]}};we?we.emit(wt):z.emit(wt),t.hoverData.cxtDragged=!0,(!t.hoverData.cxtOver||ve!==t.hoverData.cxtOver)&&(t.hoverData.cxtOver&&t.hoverData.cxtOver.emit({originalEvent:T,type:"cxtdragout",position:{x:Y[0],y:Y[1]}}),t.hoverData.cxtOver=ve,ve&&ve.emit({originalEvent:T,type:"cxtdragover",position:{x:Y[0],y:Y[1]}}))}}else if(t.hoverData.dragging){if(W=!0,z.panningEnabled()&&z.userPanningEnabled()){var Mt;if(t.hoverData.justStartedPan){var Vt=t.hoverData.mdownPos;Mt={x:(Y[0]-Vt[0])*V,y:(Y[1]-Vt[1])*V},t.hoverData.justStartedPan=!1}else Mt={x:pe[0]*V,y:pe[1]*V};z.panBy(Mt),z.emit("dragpan"),t.hoverData.dragged=!0}Y=t.projectIntoViewport(T.clientX,T.clientY)}else if(Ee[4]==1&&(we==null||we.pannable())){if(qe){if(!t.hoverData.dragging&&z.boxSelectionEnabled()&&(vt||!z.panningEnabled()||!z.userPanningEnabled()))Rt();else if(!t.hoverData.selecting&&z.panningEnabled()&&z.userPanningEnabled()){var _t=s(we,t.hoverData.downs);_t&&(t.hoverData.dragging=!0,t.hoverData.justStartedPan=!0,Ee[4]=0,t.data.bgActivePosistion=Lr(ie),t.redrawHint("select",!0),t.redraw())}we&&we.pannable()&&we.active()&&we.unactivate()}}else{if(we&&we.pannable()&&we.active()&&we.unactivate(),(!we||!we.grabbed())&&ve!=be&&(be&&n(be,["mouseout","tapdragout"],T,{x:Y[0],y:Y[1]}),ve&&n(ve,["mouseover","tapdragover"],T,{x:Y[0],y:Y[1]}),t.hoverData.last=ve),we)if(qe){if(z.boxSelectionEnabled()&&vt)we&&we.grabbed()&&(p(Oe),we.emit("freeon"),Oe.emit("free"),t.dragData.didDrag&&(we.emit("dragfreeon"),Oe.emit("dragfree"))),Rt();else if(we&&we.grabbed()&&t.nodeIsDraggable(we)){var st=!t.dragData.didDrag;st&&t.redrawHint("eles",!0),t.dragData.didDrag=!0,t.hoverData.draggingEles||y(Oe,{inDragLayer:!0});var Qe={x:0,y:0};if(ae(pe[0])&&ae(pe[1])&&(Qe.x+=pe[0],Qe.y+=pe[1],st)){var ht=t.hoverData.dragDelta;ht&&ae(ht[0])&&ae(ht[1])&&(Qe.x+=ht[0],Qe.y+=ht[1])}t.hoverData.draggingEles=!0,Oe.silentShift(Qe).emit("position drag"),t.redrawHint("drag",!0),t.redraw()}}else ft();W=!0}if(Ee[2]=Y[0],Ee[3]=Y[1],W)return T.stopPropagation&&T.stopPropagation(),T.preventDefault&&T.preventDefault(),!1}},!1);var A,P,R;t.registerBinding(e,"mouseup",function(T){if(!(t.hoverData.which===1&&T.which!==1&&t.hoverData.capture)){var q=t.hoverData.capture;if(q){t.hoverData.capture=!1;var W=t.cy,z=t.projectIntoViewport(T.clientX,T.clientY),V=t.selection,ne=t.findNearestElement(z[0],z[1],!0,!1),Y=t.dragData.possibleDragElements,ie=t.hoverData.down,ce=i(T);if(t.data.bgActivePosistion&&(t.redrawHint("select",!0),t.redraw()),t.hoverData.tapholdCancelled=!0,t.data.bgActivePosistion=void 0,ie&&ie.unactivate(),t.hoverData.which===3){var Ee={originalEvent:T,type:"cxttapend",position:{x:z[0],y:z[1]}};if(ie?ie.emit(Ee):W.emit(Ee),!t.hoverData.cxtDragged){var ve={originalEvent:T,type:"cxttap",position:{x:z[0],y:z[1]}};ie?ie.emit(ve):W.emit(ve)}t.hoverData.cxtDragged=!1,t.hoverData.which=null}else if(t.hoverData.which===1){if(n(ne,["mouseup","tapend","vmouseup"],T,{x:z[0],y:z[1]}),!t.dragData.didDrag&&!t.hoverData.dragged&&!t.hoverData.selecting&&!t.hoverData.isOverThresholdDrag&&(n(ie,["click","tap","vclick"],T,{x:z[0],y:z[1]}),P=!1,T.timeStamp-R<=W.multiClickDebounceTime()?(A&&clearTimeout(A),P=!0,R=null,n(ie,["dblclick","dbltap","vdblclick"],T,{x:z[0],y:z[1]})):(A=setTimeout(function(){P||n(ie,["oneclick","onetap","voneclick"],T,{x:z[0],y:z[1]})},W.multiClickDebounceTime()),R=T.timeStamp)),ie==null&&!t.dragData.didDrag&&!t.hoverData.selecting&&!t.hoverData.dragged&&!i(T)&&(W.$(r).unselect(["tapunselect"]),Y.length>0&&t.redrawHint("eles",!0),t.dragData.possibleDragElements=Y=W.collection()),ne==ie&&!t.dragData.didDrag&&!t.hoverData.selecting&&ne!=null&&ne._private.selectable&&(t.hoverData.dragging||(W.selectionType()==="additive"||ce?ne.selected()?ne.unselect(["tapunselect"]):ne.select(["tapselect"]):ce||(W.$(r).unmerge(ne).unselect(["tapunselect"]),ne.select(["tapselect"]))),t.redrawHint("eles",!0)),t.hoverData.selecting){var be=W.collection(t.getAllInBox(V[0],V[1],V[2],V[3]));t.redrawHint("select",!0),be.length>0&&t.redrawHint("eles",!0),W.emit({type:"boxend",originalEvent:T,position:{x:z[0],y:z[1]}});var we=function(qe){return qe.selectable()&&!qe.selected()};W.selectionType()==="additive"||ce||W.$(r).unmerge(be).unselect(),be.emit("box").stdFilter(we).select().emit("boxselect"),t.redraw()}if(t.hoverData.dragging&&(t.hoverData.dragging=!1,t.redrawHint("select",!0),t.redrawHint("eles",!0),t.redraw()),!V[4]){t.redrawHint("drag",!0),t.redrawHint("eles",!0);var pe=ie&&ie.grabbed();p(Y),pe&&(ie.emit("freeon"),Y.emit("free"),t.dragData.didDrag&&(ie.emit("dragfreeon"),Y.emit("dragfree")))}}V[4]=0,t.hoverData.down=null,t.hoverData.cxtStarted=!1,t.hoverData.draggingEles=!1,t.hoverData.selecting=!1,t.hoverData.isOverThresholdDrag=!1,t.dragData.didDrag=!1,t.hoverData.dragged=!1,t.hoverData.dragDelta=[],t.hoverData.mdownPos=null,t.hoverData.mdownGPos=null,t.hoverData.which=null}}},!1);var L=function(T){if(!t.scrollingPage){var q=t.cy,W=q.zoom(),z=q.pan(),V=t.projectIntoViewport(T.clientX,T.clientY),ne=[V[0]*W+z.x,V[1]*W+z.y];if(t.hoverData.draggingEles||t.hoverData.dragging||t.hoverData.cxtStarted||k()){T.preventDefault();return}if(q.panningEnabled()&&q.userPanningEnabled()&&q.zoomingEnabled()&&q.userZoomingEnabled()){T.preventDefault(),t.data.wheelZooming=!0,clearTimeout(t.data.wheelTimeout),t.data.wheelTimeout=setTimeout(function(){t.data.wheelZooming=!1,t.redrawHint("eles",!0),t.redraw()},150);var Y;T.deltaY!=null?Y=T.deltaY/-250:T.wheelDeltaY!=null?Y=T.wheelDeltaY/1e3:Y=T.wheelDelta/1e3,Y=Y*t.wheelSensitivity;var ie=T.deltaMode===1;ie&&(Y*=33);var ce=q.zoom()*Math.pow(10,Y);T.type==="gesturechange"&&(ce=t.gestureStartZoom*T.scale),q.zoom({level:ce,renderedPosition:{x:ne[0],y:ne[1]}}),q.emit(T.type==="gesturechange"?"pinchzoom":"scrollzoom")}}};t.registerBinding(t.container,"wheel",L,!0),t.registerBinding(e,"scroll",function(T){t.scrollingPage=!0,clearTimeout(t.scrollingPageTimeout),t.scrollingPageTimeout=setTimeout(function(){t.scrollingPage=!1},250)},!0),t.registerBinding(t.container,"gesturestart",function(T){t.gestureStartZoom=t.cy.zoom(),t.hasTouchStarted||T.preventDefault()},!0),t.registerBinding(t.container,"gesturechange",function(j){t.hasTouchStarted||L(j)},!0),t.registerBinding(t.container,"mouseout",function(T){var q=t.projectIntoViewport(T.clientX,T.clientY);t.cy.emit({originalEvent:T,type:"mouseout",position:{x:q[0],y:q[1]}})},!1),t.registerBinding(t.container,"mouseover",function(T){var q=t.projectIntoViewport(T.clientX,T.clientY);t.cy.emit({originalEvent:T,type:"mouseover",position:{x:q[0],y:q[1]}})},!1);var I,M,O,_,H,F,G,U,X,Z,Q,ee,te,K=function(T,q,W,z){return Math.sqrt((W-T)*(W-T)+(z-q)*(z-q))},N=function(T,q,W,z){return(W-T)*(W-T)+(z-q)*(z-q)},$;t.registerBinding(t.container,"touchstart",$=function(T){if(t.hasTouchStarted=!0,!!B(T)){b(),t.touchData.capture=!0,t.data.bgActivePosistion=void 0;var q=t.cy,W=t.touchData.now,z=t.touchData.earlier;if(T.touches[0]){var V=t.projectIntoViewport(T.touches[0].clientX,T.touches[0].clientY);W[0]=V[0],W[1]=V[1]}if(T.touches[1]){var V=t.projectIntoViewport(T.touches[1].clientX,T.touches[1].clientY);W[2]=V[0],W[3]=V[1]}if(T.touches[2]){var V=t.projectIntoViewport(T.touches[2].clientX,T.touches[2].clientY);W[4]=V[0],W[5]=V[1]}if(T.touches[1]){t.touchData.singleTouchMoved=!0,p(t.dragData.touchDragEles);var ne=t.findContainerClientCoords();X=ne[0],Z=ne[1],Q=ne[2],ee=ne[3],I=T.touches[0].clientX-X,M=T.touches[0].clientY-Z,O=T.touches[1].clientX-X,_=T.touches[1].clientY-Z,te=0<=I&&I<=Q&&0<=O&&O<=Q&&0<=M&&M<=ee&&0<=_&&_<=ee;var Y=q.pan(),ie=q.zoom();H=K(I,M,O,_),F=N(I,M,O,_),G=[(I+O)/2,(M+_)/2],U=[(G[0]-Y.x)/ie,(G[1]-Y.y)/ie];var ce=200,Ee=ce*ce;if(F<Ee&&!T.touches[2]){var ve=t.findNearestElement(W[0],W[1],!0,!0),be=t.findNearestElement(W[2],W[3],!0,!0);ve&&ve.isNode()?(ve.activate().emit({originalEvent:T,type:"cxttapstart",position:{x:W[0],y:W[1]}}),t.touchData.start=ve):be&&be.isNode()?(be.activate().emit({originalEvent:T,type:"cxttapstart",position:{x:W[0],y:W[1]}}),t.touchData.start=be):q.emit({originalEvent:T,type:"cxttapstart",position:{x:W[0],y:W[1]}}),t.touchData.start&&(t.touchData.start._private.grabbed=!1),t.touchData.cxt=!0,t.touchData.cxtDragged=!1,t.data.bgActivePosistion=void 0,t.redraw();return}}if(T.touches[2])q.boxSelectionEnabled()&&T.preventDefault();else if(!T.touches[1]){if(T.touches[0]){var we=t.findNearestElements(W[0],W[1],!0,!0),pe=we[0];if(pe!=null&&(pe.activate(),t.touchData.start=pe,t.touchData.starts=we,t.nodeIsGrabbable(pe))){var Oe=t.dragData.touchDragEles=q.collection(),qe=null;t.redrawHint("eles",!0),t.redrawHint("drag",!0),pe.selected()?(qe=q.$(function(Ze){return Ze.selected()&&t.nodeIsGrabbable(Ze)}),y(qe,{addToList:Oe})):g(pe,{addToList:Oe}),f(pe);var yt=function(vt){return{originalEvent:T,type:vt,position:{x:W[0],y:W[1]}}};pe.emit(yt("grabon")),qe?qe.forEach(function(Ze){Ze.emit(yt("grab"))}):pe.emit(yt("grab"))}n(pe,["touchstart","tapstart","vmousedown"],T,{x:W[0],y:W[1]}),pe==null&&(t.data.bgActivePosistion={x:V[0],y:V[1]},t.redrawHint("select",!0),t.redraw()),t.touchData.singleTouchMoved=!1,t.touchData.singleTouchStartTime=+new Date,clearTimeout(t.touchData.tapholdTimeout),t.touchData.tapholdTimeout=setTimeout(function(){t.touchData.singleTouchMoved===!1&&!t.pinching&&!t.touchData.selecting&&n(t.touchData.start,["taphold"],T,{x:W[0],y:W[1]})},t.tapholdDuration)}}if(T.touches.length>=1){for(var mt=t.touchData.startPosition=[null,null,null,null,null,null],He=0;He<W.length;He++)mt[He]=z[He]=W[He];var Xe=T.touches[0];t.touchData.startGPosition=[Xe.clientX,Xe.clientY]}}},!1);var J;t.registerBinding(e,"touchmove",J=function(T){var q=t.touchData.capture;if(!(!q&&!B(T))){var W=t.selection,z=t.cy,V=t.touchData.now,ne=t.touchData.earlier,Y=z.zoom();if(T.touches[0]){var ie=t.projectIntoViewport(T.touches[0].clientX,T.touches[0].clientY);V[0]=ie[0],V[1]=ie[1]}if(T.touches[1]){var ie=t.projectIntoViewport(T.touches[1].clientX,T.touches[1].clientY);V[2]=ie[0],V[3]=ie[1]}if(T.touches[2]){var ie=t.projectIntoViewport(T.touches[2].clientX,T.touches[2].clientY);V[4]=ie[0],V[5]=ie[1]}var ce=t.touchData.startGPosition,Ee;if(q&&T.touches[0]&&ce){for(var ve=[],be=0;be<V.length;be++)ve[be]=V[be]-ne[be];var we=T.touches[0].clientX-ce[0],pe=we*we,Oe=T.touches[0].clientY-ce[1],qe=Oe*Oe,yt=pe+qe;Ee=yt>=t.touchTapThreshold2}if(q&&t.touchData.cxt){T.preventDefault();var mt=T.touches[0].clientX-X,He=T.touches[0].clientY-Z,Xe=T.touches[1].clientX-X,Ze=T.touches[1].clientY-Z,vt=N(mt,He,Xe,Ze),ft=vt/F,Rt=150,wt=Rt*Rt,Mt=1.5,Vt=Mt*Mt;if(ft>=Vt||vt>=wt){t.touchData.cxt=!1,t.data.bgActivePosistion=void 0,t.redrawHint("select",!0);var _t={originalEvent:T,type:"cxttapend",position:{x:V[0],y:V[1]}};t.touchData.start?(t.touchData.start.unactivate().emit(_t),t.touchData.start=null):z.emit(_t)}}if(q&&t.touchData.cxt){var _t={originalEvent:T,type:"cxtdrag",position:{x:V[0],y:V[1]}};t.data.bgActivePosistion=void 0,t.redrawHint("select",!0),t.touchData.start?t.touchData.start.emit(_t):z.emit(_t),t.touchData.start&&(t.touchData.start._private.grabbed=!1),t.touchData.cxtDragged=!0;var st=t.findNearestElement(V[0],V[1],!0,!0);(!t.touchData.cxtOver||st!==t.touchData.cxtOver)&&(t.touchData.cxtOver&&t.touchData.cxtOver.emit({originalEvent:T,type:"cxtdragout",position:{x:V[0],y:V[1]}}),t.touchData.cxtOver=st,st&&st.emit({originalEvent:T,type:"cxtdragover",position:{x:V[0],y:V[1]}}))}else if(q&&T.touches[2]&&z.boxSelectionEnabled())T.preventDefault(),t.data.bgActivePosistion=void 0,this.lastThreeTouch=+new Date,t.touchData.selecting||z.emit({originalEvent:T,type:"boxstart",position:{x:V[0],y:V[1]}}),t.touchData.selecting=!0,t.touchData.didSelect=!0,W[4]=1,!W||W.length===0||W[0]===void 0?(W[0]=(V[0]+V[2]+V[4])/3,W[1]=(V[1]+V[3]+V[5])/3,W[2]=(V[0]+V[2]+V[4])/3+1,W[3]=(V[1]+V[3]+V[5])/3+1):(W[2]=(V[0]+V[2]+V[4])/3,W[3]=(V[1]+V[3]+V[5])/3),t.redrawHint("select",!0),t.redraw();else if(q&&T.touches[1]&&!t.touchData.didSelect&&z.zoomingEnabled()&&z.panningEnabled()&&z.userZoomingEnabled()&&z.userPanningEnabled()){T.preventDefault(),t.data.bgActivePosistion=void 0,t.redrawHint("select",!0);var Qe=t.dragData.touchDragEles;if(Qe){t.redrawHint("drag",!0);for(var ht=0;ht<Qe.length;ht++){var Oa=Qe[ht]._private;Oa.grabbed=!1,Oa.rscratch.inDragLayer=!1}}var Lt=t.touchData.start,mt=T.touches[0].clientX-X,He=T.touches[0].clientY-Z,Xe=T.touches[1].clientX-X,Ze=T.touches[1].clientY-Z,ro=K(mt,He,Xe,Ze),kf=ro/H;if(te){var Pf=mt-I,Bf=He-M,Af=Xe-O,Rf=Ze-_,Mf=(Pf+Af)/2,Lf=(Bf+Rf)/2,Jr=z.zoom(),qn=Jr*kf,Na=z.pan(),ao=U[0]*Jr+Na.x,no=U[1]*Jr+Na.y,If={x:-qn/Jr*(ao-Na.x-Mf)+ao,y:-qn/Jr*(no-Na.y-Lf)+no};if(Lt&&Lt.active()){var Qe=t.dragData.touchDragEles;p(Qe),t.redrawHint("drag",!0),t.redrawHint("eles",!0),Lt.unactivate().emit("freeon"),Qe.emit("free"),t.dragData.didDrag&&(Lt.emit("dragfreeon"),Qe.emit("dragfree"))}z.viewport({zoom:qn,pan:If,cancelOnFailedZoom:!0}),z.emit("pinchzoom"),H=ro,I=mt,M=He,O=Xe,_=Ze,t.pinching=!0}if(T.touches[0]){var ie=t.projectIntoViewport(T.touches[0].clientX,T.touches[0].clientY);V[0]=ie[0],V[1]=ie[1]}if(T.touches[1]){var ie=t.projectIntoViewport(T.touches[1].clientX,T.touches[1].clientY);V[2]=ie[0],V[3]=ie[1]}if(T.touches[2]){var ie=t.projectIntoViewport(T.touches[2].clientX,T.touches[2].clientY);V[4]=ie[0],V[5]=ie[1]}}else if(T.touches[0]&&!t.touchData.didSelect){var St=t.touchData.start,Vn=t.touchData.last,st;if(!t.hoverData.draggingEles&&!t.swipePanning&&(st=t.findNearestElement(V[0],V[1],!0,!0)),q&&St!=null&&T.preventDefault(),q&&St!=null&&t.nodeIsDraggable(St))if(Ee){var Qe=t.dragData.touchDragEles,io=!t.dragData.didDrag;io&&y(Qe,{inDragLayer:!0}),t.dragData.didDrag=!0;var jr={x:0,y:0};if(ae(ve[0])&&ae(ve[1])&&(jr.x+=ve[0],jr.y+=ve[1],io)){t.redrawHint("eles",!0);var Dt=t.touchData.dragDelta;Dt&&ae(Dt[0])&&ae(Dt[1])&&(jr.x+=Dt[0],jr.y+=Dt[1])}t.hoverData.draggingEles=!0,Qe.silentShift(jr).emit("position drag"),t.redrawHint("drag",!0),t.touchData.startPosition[0]==ne[0]&&t.touchData.startPosition[1]==ne[1]&&t.redrawHint("eles",!0),t.redraw()}else{var Dt=t.touchData.dragDelta=t.touchData.dragDelta||[];Dt.length===0?(Dt.push(ve[0]),Dt.push(ve[1])):(Dt[0]+=ve[0],Dt[1]+=ve[1])}if(n(St||st,["touchmove","tapdrag","vmousemove"],T,{x:V[0],y:V[1]}),(!St||!St.grabbed())&&st!=Vn&&(Vn&&Vn.emit({originalEvent:T,type:"tapdragout",position:{x:V[0],y:V[1]}}),st&&st.emit({originalEvent:T,type:"tapdragover",position:{x:V[0],y:V[1]}})),t.touchData.last=st,q)for(var ht=0;ht<V.length;ht++)V[ht]&&t.touchData.startPosition[ht]&&Ee&&(t.touchData.singleTouchMoved=!0);if(q&&(St==null||St.pannable())&&z.panningEnabled()&&z.userPanningEnabled()){var Of=s(St,t.touchData.starts);Of&&(T.preventDefault(),t.data.bgActivePosistion||(t.data.bgActivePosistion=Lr(t.touchData.startPosition)),t.swipePanning?(z.panBy({x:ve[0]*Y,y:ve[1]*Y}),z.emit("dragpan")):Ee&&(t.swipePanning=!0,z.panBy({x:we*Y,y:Oe*Y}),z.emit("dragpan"),St&&(St.unactivate(),t.redrawHint("select",!0),t.touchData.start=null)));var ie=t.projectIntoViewport(T.touches[0].clientX,T.touches[0].clientY);V[0]=ie[0],V[1]=ie[1]}}for(var be=0;be<V.length;be++)ne[be]=V[be];q&&T.touches.length>0&&!t.hoverData.draggingEles&&!t.swipePanning&&t.data.bgActivePosistion!=null&&(t.data.bgActivePosistion=void 0,t.redrawHint("select",!0),t.redraw())}},!1);var re;t.registerBinding(e,"touchcancel",re=function(T){var q=t.touchData.start;t.touchData.capture=!1,q&&q.unactivate()});var le,xe,Ie,Be;if(t.registerBinding(e,"touchend",le=function(T){var q=t.touchData.start,W=t.touchData.capture;if(W)T.touches.length===0&&(t.touchData.capture=!1),T.preventDefault();else return;var z=t.selection;t.swipePanning=!1,t.hoverData.draggingEles=!1;var V=t.cy,ne=V.zoom(),Y=t.touchData.now,ie=t.touchData.earlier;if(T.touches[0]){var ce=t.projectIntoViewport(T.touches[0].clientX,T.touches[0].clientY);Y[0]=ce[0],Y[1]=ce[1]}if(T.touches[1]){var ce=t.projectIntoViewport(T.touches[1].clientX,T.touches[1].clientY);Y[2]=ce[0],Y[3]=ce[1]}if(T.touches[2]){var ce=t.projectIntoViewport(T.touches[2].clientX,T.touches[2].clientY);Y[4]=ce[0],Y[5]=ce[1]}q&&q.unactivate();var Ee;if(t.touchData.cxt){if(Ee={originalEvent:T,type:"cxttapend",position:{x:Y[0],y:Y[1]}},q?q.emit(Ee):V.emit(Ee),!t.touchData.cxtDragged){var ve={originalEvent:T,type:"cxttap",position:{x:Y[0],y:Y[1]}};q?q.emit(ve):V.emit(ve)}t.touchData.start&&(t.touchData.start._private.grabbed=!1),t.touchData.cxt=!1,t.touchData.start=null,t.redraw();return}if(!T.touches[2]&&V.boxSelectionEnabled()&&t.touchData.selecting){t.touchData.selecting=!1;var be=V.collection(t.getAllInBox(z[0],z[1],z[2],z[3]));z[0]=void 0,z[1]=void 0,z[2]=void 0,z[3]=void 0,z[4]=0,t.redrawHint("select",!0),V.emit({type:"boxend",originalEvent:T,position:{x:Y[0],y:Y[1]}});var we=function(wt){return wt.selectable()&&!wt.selected()};be.emit("box").stdFilter(we).select().emit("boxselect"),be.nonempty()&&t.redrawHint("eles",!0),t.redraw()}if(q!=null&&q.unactivate(),T.touches[2])t.data.bgActivePosistion=void 0,t.redrawHint("select",!0);else if(!T.touches[1]){if(!T.touches[0]){if(!T.touches[0]){t.data.bgActivePosistion=void 0,t.redrawHint("select",!0);var pe=t.dragData.touchDragEles;if(q!=null){var Oe=q._private.grabbed;p(pe),t.redrawHint("drag",!0),t.redrawHint("eles",!0),Oe&&(q.emit("freeon"),pe.emit("free"),t.dragData.didDrag&&(q.emit("dragfreeon"),pe.emit("dragfree"))),n(q,["touchend","tapend","vmouseup","tapdragout"],T,{x:Y[0],y:Y[1]}),q.unactivate(),t.touchData.start=null}else{var qe=t.findNearestElement(Y[0],Y[1],!0,!0);n(qe,["touchend","tapend","vmouseup","tapdragout"],T,{x:Y[0],y:Y[1]})}var yt=t.touchData.startPosition[0]-Y[0],mt=yt*yt,He=t.touchData.startPosition[1]-Y[1],Xe=He*He,Ze=mt+Xe,vt=Ze*ne*ne;t.touchData.singleTouchMoved||(q||V.$(":selected").unselect(["tapunselect"]),n(q,["tap","vclick"],T,{x:Y[0],y:Y[1]}),xe=!1,T.timeStamp-Be<=V.multiClickDebounceTime()?(Ie&&clearTimeout(Ie),xe=!0,Be=null,n(q,["dbltap","vdblclick"],T,{x:Y[0],y:Y[1]})):(Ie=setTimeout(function(){xe||n(q,["onetap","voneclick"],T,{x:Y[0],y:Y[1]})},V.multiClickDebounceTime()),Be=T.timeStamp)),q!=null&&!t.dragData.didDrag&&q._private.selectable&&vt<t.touchTapThreshold2&&!t.pinching&&(V.selectionType()==="single"?(V.$(r).unmerge(q).unselect(["tapunselect"]),q.select(["tapselect"])):q.selected()?q.unselect(["tapunselect"]):q.select(["tapselect"]),t.redrawHint("eles",!0)),t.touchData.singleTouchMoved=!0}}}for(var ft=0;ft<Y.length;ft++)ie[ft]=Y[ft];t.dragData.didDrag=!1,T.touches.length===0&&(t.touchData.dragDelta=[],t.touchData.startPosition=[null,null,null,null,null,null],t.touchData.startGPosition=null,t.touchData.didSelect=!1),T.touches.length<2&&(T.touches.length===1&&(t.touchData.startGPosition=[T.touches[0].clientX,T.touches[0].clientY]),t.pinching=!1,t.redrawHint("eles",!0),t.redraw())},!1),typeof TouchEvent>"u"){var se=[],ue=function(T){return{clientX:T.clientX,clientY:T.clientY,force:1,identifier:T.pointerId,pageX:T.pageX,pageY:T.pageY,radiusX:T.width/2,radiusY:T.height/2,screenX:T.screenX,screenY:T.screenY,target:T.target}},de=function(T){return{event:T,touch:ue(T)}},ye=function(T){se.push(de(T))},he=function(T){for(var q=0;q<se.length;q++){var W=se[q];if(W.event.pointerId===T.pointerId){se.splice(q,1);return}}},me=function(T){var q=se.filter(function(W){return W.event.pointerId===T.pointerId})[0];q.event=T,q.touch=ue(T)},Ce=function(T){T.touches=se.map(function(q){return q.touch})},Se=function(T){return T.pointerType==="mouse"||T.pointerType===4};t.registerBinding(t.container,"pointerdown",function(j){Se(j)||(j.preventDefault(),ye(j),Ce(j),$(j))}),t.registerBinding(t.container,"pointerup",function(j){Se(j)||(he(j),Ce(j),le(j))}),t.registerBinding(t.container,"pointercancel",function(j){Se(j)||(he(j),Ce(j),re(j))}),t.registerBinding(t.container,"pointermove",function(j){Se(j)||(j.preventDefault(),me(j),Ce(j),J(j))})}};var Ut={};Ut.generatePolygon=function(t,e){return this.nodeShapes[t]={renderer:this,name:t,points:e,draw:function(a,n,i,s,o,l){this.renderer.nodeShapeImpl("polygon",a,n,i,s,o,this.points)},intersectLine:function(a,n,i,s,o,l,u,v){return ya(o,l,this.points,a,n,i/2,s/2,u)},checkPoint:function(a,n,i,s,o,l,u,v){return Wt(a,n,this.points,l,u,s,o,[0,-1],i)}}};Ut.generateEllipse=function(){return this.nodeShapes.ellipse={renderer:this,name:"ellipse",draw:function(e,r,a,n,i,s){this.renderer.nodeShapeImpl(this.name,e,r,a,n,i)},intersectLine:function(e,r,a,n,i,s,o,l){return pd(i,s,e,r,a/2+o,n/2+o)},checkPoint:function(e,r,a,n,i,s,o,l){return pr(e,r,n,i,s,o,a)}}};Ut.generateRoundPolygon=function(t,e){return this.nodeShapes[t]={renderer:this,name:t,points:e,getOrCreateCorners:function(a,n,i,s,o,l,u){if(l[u]!==void 0&&l[u+"-cx"]===a&&l[u+"-cy"]===n)return l[u];l[u]=new Array(e.length/2),l[u+"-cx"]=a,l[u+"-cy"]=n;var v=i/2,f=s/2;o=o==="auto"?iv(i,s):o;for(var c=new Array(e.length/2),h=0;h<e.length/2;h++)c[h]={x:a+v*e[h*2],y:n+f*e[h*2+1]};var d,y,g,p,m=c.length;for(y=c[m-1],d=0;d<m;d++)g=c[d%m],p=c[(d+1)%m],l[u][d]=Qs(y,g,p,o),y=g,g=p;return l[u]},draw:function(a,n,i,s,o,l,u){this.renderer.nodeShapeImpl("round-polygon",a,n,i,s,o,this.points,this.getOrCreateCorners(n,i,s,o,l,u,"drawCorners"))},intersectLine:function(a,n,i,s,o,l,u,v,f){return yd(o,l,this.points,a,n,i,s,u,this.getOrCreateCorners(a,n,i,s,v,f,"corners"))},checkPoint:function(a,n,i,s,o,l,u,v,f){return gd(a,n,this.points,l,u,s,o,this.getOrCreateCorners(l,u,s,o,v,f,"corners"))}}};Ut.generateRoundRectangle=function(){return this.nodeShapes["round-rectangle"]=this.nodeShapes.roundrectangle={renderer:this,name:"round-rectangle",points:ct(4,0),draw:function(e,r,a,n,i,s){this.renderer.nodeShapeImpl(this.name,e,r,a,n,i,this.points,s)},intersectLine:function(e,r,a,n,i,s,o,l){return av(i,s,e,r,a,n,o,l)},checkPoint:function(e,r,a,n,i,s,o,l){var u=n/2,v=i/2;l=l==="auto"?mr(n,i):l,l=Math.min(u,v,l);var f=l*2;return!!(Wt(e,r,this.points,s,o,n,i-f,[0,-1],a)||Wt(e,r,this.points,s,o,n-f,i,[0,-1],a)||pr(e,r,f,f,s-u+l,o-v+l,a)||pr(e,r,f,f,s+u-l,o-v+l,a)||pr(e,r,f,f,s+u-l,o+v-l,a)||pr(e,r,f,f,s-u+l,o+v-l,a))}}};Ut.generateCutRectangle=function(){return this.nodeShapes["cut-rectangle"]=this.nodeShapes.cutrectangle={renderer:this,name:"cut-rectangle",cornerLength:Vs(),points:ct(4,0),draw:function(e,r,a,n,i,s){this.renderer.nodeShapeImpl(this.name,e,r,a,n,i,null,s)},generateCutTrianglePts:function(e,r,a,n,i){var s=i==="auto"?this.cornerLength:i,o=r/2,l=e/2,u=a-l,v=a+l,f=n-o,c=n+o;return{topLeft:[u,f+s,u+s,f,u+s,f+s],topRight:[v-s,f,v,f+s,v-s,f+s],bottomRight:[v,c-s,v-s,c,v-s,c-s],bottomLeft:[u+s,c,u,c-s,u+s,c-s]}},intersectLine:function(e,r,a,n,i,s,o,l){var u=this.generateCutTrianglePts(a+2*o,n+2*o,e,r,l),v=[].concat.apply([],[u.topLeft.splice(0,4),u.topRight.splice(0,4),u.bottomRight.splice(0,4),u.bottomLeft.splice(0,4)]);return ya(i,s,v,e,r)},checkPoint:function(e,r,a,n,i,s,o,l){var u=l==="auto"?this.cornerLength:l;if(Wt(e,r,this.points,s,o,n,i-2*u,[0,-1],a)||Wt(e,r,this.points,s,o,n-2*u,i,[0,-1],a))return!0;var v=this.generateCutTrianglePts(n,i,s,o);return gt(e,r,v.topLeft)||gt(e,r,v.topRight)||gt(e,r,v.bottomRight)||gt(e,r,v.bottomLeft)}}};Ut.generateBarrel=function(){return this.nodeShapes.barrel={renderer:this,name:"barrel",points:ct(4,0),draw:function(e,r,a,n,i,s){this.renderer.nodeShapeImpl(this.name,e,r,a,n,i)},intersectLine:function(e,r,a,n,i,s,o,l){var u=.15,v=.5,f=.85,c=this.generateBarrelBezierPts(a+2*o,n+2*o,e,r),h=function(g){var p=Nr({x:g[0],y:g[1]},{x:g[2],y:g[3]},{x:g[4],y:g[5]},u),m=Nr({x:g[0],y:g[1]},{x:g[2],y:g[3]},{x:g[4],y:g[5]},v),b=Nr({x:g[0],y:g[1]},{x:g[2],y:g[3]},{x:g[4],y:g[5]},f);return[g[0],g[1],p.x,p.y,m.x,m.y,b.x,b.y,g[4],g[5]]},d=[].concat(h(c.topLeft),h(c.topRight),h(c.bottomRight),h(c.bottomLeft));return ya(i,s,d,e,r)},generateBarrelBezierPts:function(e,r,a,n){var i=r/2,s=e/2,o=a-s,l=a+s,u=n-i,v=n+i,f=hs(e,r),c=f.heightOffset,h=f.widthOffset,d=f.ctrlPtOffsetPct*e,y={topLeft:[o,u+c,o+d,u,o+h,u],topRight:[l-h,u,l-d,u,l,u+c],bottomRight:[l,v-c,l-d,v,l-h,v],bottomLeft:[o+h,v,o+d,v,o,v-c]};return y.topLeft.isTop=!0,y.topRight.isTop=!0,y.bottomLeft.isBottom=!0,y.bottomRight.isBottom=!0,y},checkPoint:function(e,r,a,n,i,s,o,l){var u=hs(n,i),v=u.heightOffset,f=u.widthOffset;if(Wt(e,r,this.points,s,o,n,i-2*v,[0,-1],a)||Wt(e,r,this.points,s,o,n-2*f,i,[0,-1],a))return!0;for(var c=this.generateBarrelBezierPts(n,i,s,o),h=function(S,k,B){var D=B[4],A=B[2],P=B[0],R=B[5],L=B[1],I=Math.min(D,P),M=Math.max(D,P),O=Math.min(R,L),_=Math.max(R,L);if(I<=S&&S<=M&&O<=k&&k<=_){var H=md(D,A,P),F=fd(H[0],H[1],H[2],S),G=F.filter(function(U){return 0<=U&&U<=1});if(G.length>0)return G[0]}return null},d=Object.keys(c),y=0;y<d.length;y++){var g=d[y],p=c[g],m=h(e,r,p);if(m!=null){var b=p[5],w=p[3],E=p[1],C=Je(b,w,E,m);if(p.isTop&&C<=r||p.isBottom&&r<=C)return!0}}return!1}}};Ut.generateBottomRoundrectangle=function(){return this.nodeShapes["bottom-round-rectangle"]=this.nodeShapes.bottomroundrectangle={renderer:this,name:"bottom-round-rectangle",points:ct(4,0),draw:function(e,r,a,n,i,s){this.renderer.nodeShapeImpl(this.name,e,r,a,n,i,this.points,s)},intersectLine:function(e,r,a,n,i,s,o,l){var u=e-(a/2+o),v=r-(n/2+o),f=v,c=e+(a/2+o),h=Jt(i,s,e,r,u,v,c,f,!1);return h.length>0?h:av(i,s,e,r,a,n,o,l)},checkPoint:function(e,r,a,n,i,s,o,l){l=l==="auto"?mr(n,i):l;var u=2*l;if(Wt(e,r,this.points,s,o,n,i-u,[0,-1],a)||Wt(e,r,this.points,s,o,n-u,i,[0,-1],a))return!0;var v=n/2+2*a,f=i/2+2*a,c=[s-v,o-f,s-v,o,s+v,o,s+v,o-f];return!!(gt(e,r,c)||pr(e,r,u,u,s+n/2-l,o+i/2-l,a)||pr(e,r,u,u,s-n/2+l,o+i/2-l,a))}}};Ut.registerNodeShapes=function(){var t=this.nodeShapes={},e=this;this.generateEllipse(),this.generatePolygon("triangle",ct(3,0)),this.generateRoundPolygon("round-triangle",ct(3,0)),this.generatePolygon("rectangle",ct(4,0)),t.square=t.rectangle,this.generateRoundRectangle(),this.generateCutRectangle(),this.generateBarrel(),this.generateBottomRoundrectangle();{var r=[0,1,1,0,0,-1,-1,0];this.generatePolygon("diamond",r),this.generateRoundPolygon("round-diamond",r)}this.generatePolygon("pentagon",ct(5,0)),this.generateRoundPolygon("round-pentagon",ct(5,0)),this.generatePolygon("hexagon",ct(6,0)),this.generateRoundPolygon("round-hexagon",ct(6,0)),this.generatePolygon("heptagon",ct(7,0)),this.generateRoundPolygon("round-heptagon",ct(7,0)),this.generatePolygon("octagon",ct(8,0)),this.generateRoundPolygon("round-octagon",ct(8,0));var a=new Array(20);{var n=ds(5,0),i=ds(5,Math.PI/5),s=.5*(3-Math.sqrt(5));s*=1.57;for(var o=0;o<i.length/2;o++)i[o*2]*=s,i[o*2+1]*=s;for(var o=0;o<20/4;o++)a[o*4]=n[o*2],a[o*4+1]=n[o*2+1],a[o*4+2]=i[o*2],a[o*4+3]=i[o*2+1]}a=nv(a),this.generatePolygon("star",a),this.generatePolygon("vee",[-1,-1,0,-.333,1,-1,0,1]),this.generatePolygon("rhomboid",[-1,-1,.333,-1,1,1,-.333,1]),this.generatePolygon("right-rhomboid",[-.333,-1,1,-1,.333,1,-1,1]),this.nodeShapes.concavehexagon=this.generatePolygon("concave-hexagon",[-1,-.95,-.75,0,-1,.95,1,.95,.75,0,1,-.95]);{var l=[-1,-1,.25,-1,1,0,.25,1,-1,1];this.generatePolygon("tag",l),this.generateRoundPolygon("round-tag",l)}t.makePolygon=function(u){var v=u.join("$"),f="polygon-"+v,c;return(c=this[f])?c:e.generatePolygon(f,u)}};var La={};La.timeToRender=function(){return this.redrawTotalTime/this.redrawCount};La.redraw=function(t){t=t||Jl();var e=this;e.averageRedrawTime===void 0&&(e.averageRedrawTime=0),e.lastRedrawTime===void 0&&(e.lastRedrawTime=0),e.lastDrawTime===void 0&&(e.lastDrawTime=0),e.requestedFrame=!0,e.renderOptions=t};La.beforeRender=function(t,e){if(!this.destroyed){e==null&&Ve("Priority is not optional for beforeRender");var r=this.beforeRenderCallbacks;r.push({fn:t,priority:e}),r.sort(function(a,n){return n.priority-a.priority})}};var pl=function(e,r,a){for(var n=e.beforeRenderCallbacks,i=0;i<n.length;i++)n[i].fn(r,a)};La.startRenderLoop=function(){var t=this,e=t.cy;if(!t.renderLoopStarted){t.renderLoopStarted=!0;var r=function(n){if(!t.destroyed){if(!e.batching())if(t.requestedFrame&&!t.skipFrame){pl(t,!0,n);var i=$t();t.render(t.renderOptions);var s=t.lastDrawTime=$t();t.averageRedrawTime===void 0&&(t.averageRedrawTime=s-i),t.redrawCount===void 0&&(t.redrawCount=0),t.redrawCount++,t.redrawTotalTime===void 0&&(t.redrawTotalTime=0);var o=s-i;t.redrawTotalTime+=o,t.lastRedrawTime=o,t.averageRedrawTime=t.averageRedrawTime/2+o/2,t.requestedFrame=!1}else pl(t,!1,n);t.skipFrame=!1,ln(r)}};ln(r)}};var Ip=function(e){this.init(e)},nf=Ip,Zr=nf.prototype;Zr.clientFunctions=["redrawHint","render","renderTo","matchCanvasSize","nodeShapeImpl","arrowShapeImpl"];Zr.init=function(t){var e=this;e.options=t,e.cy=t.cy;var r=e.container=t.cy.container(),a=e.cy.window();if(a){var n=a.document,i=n.head,s="__________cytoscape_stylesheet",o="__________cytoscape_container",l=n.getElementById(s)!=null;if(r.className.indexOf(o)<0&&(r.className=(r.className||"")+" "+o),!l){var u=n.createElement("style");u.id=s,u.textContent="."+o+" { position: relative; }",i.insertBefore(u,i.children[0])}var v=a.getComputedStyle(r),f=v.getPropertyValue("position");f==="static"&&Re("A Cytoscape container has style position:static and so can not use UI extensions properly")}e.selection=[void 0,void 0,void 0,void 0,0],e.bezierProjPcts=[.05,.225,.4,.5,.6,.775,.95],e.hoverData={down:null,last:null,downTime:null,triggerMode:null,dragging:!1,initialPan:[null,null],capture:!1},e.dragData={possibleDragElements:[]},e.touchData={start:null,capture:!1,startPosition:[null,null,null,null,null,null],singleTouchStartTime:null,singleTouchMoved:!0,now:[null,null,null,null,null,null],earlier:[null,null,null,null,null,null]},e.redraws=0,e.showFps=t.showFps,e.debug=t.debug,e.webgl=t.webgl,e.hideEdgesOnViewport=t.hideEdgesOnViewport,e.textureOnViewport=t.textureOnViewport,e.wheelSensitivity=t.wheelSensitivity,e.motionBlurEnabled=t.motionBlur,e.forcedPixelRatio=ae(t.pixelRatio)?t.pixelRatio:null,e.motionBlur=t.motionBlur,e.motionBlurOpacity=t.motionBlurOpacity,e.motionBlurTransparency=1-e.motionBlurOpacity,e.motionBlurPxRatio=1,e.mbPxRBlurry=1,e.minMbLowQualFrames=4,e.fullQualityMb=!1,e.clearedForMotionBlur=[],e.desktopTapThreshold=t.desktopTapThreshold,e.desktopTapThreshold2=t.desktopTapThreshold*t.desktopTapThreshold,e.touchTapThreshold=t.touchTapThreshold,e.touchTapThreshold2=t.touchTapThreshold*t.touchTapThreshold,e.tapholdDuration=500,e.bindings=[],e.beforeRenderCallbacks=[],e.beforeRenderPriorities={animations:400,eleCalcs:300,eleTxrDeq:200,lyrTxrDeq:150,lyrTxrSkip:100},e.registerNodeShapes(),e.registerArrowShapes(),e.registerCalculationListeners()};Zr.notify=function(t,e){var r=this,a=r.cy;if(!this.destroyed){if(t==="init"){r.load();return}if(t==="destroy"){r.destroy();return}(t==="add"||t==="remove"||t==="move"&&a.hasCompoundNodes()||t==="load"||t==="zorder"||t==="mount")&&r.invalidateCachedZSortedEles(),t==="viewport"&&r.redrawHint("select",!0),t==="gc"&&r.redrawHint("gc",!0),(t==="load"||t==="resize"||t==="mount")&&(r.invalidateContainerClientCoordsCache(),r.matchCanvasSize(r.container)),r.redrawHint("eles",!0),r.redrawHint("drag",!0),this.startRenderLoop(),this.redraw()}};Zr.destroy=function(){var t=this;t.destroyed=!0,t.cy.stopAnimationLoop();for(var e=0;e<t.bindings.length;e++){var r=t.bindings[e],a=r,n=a.target;(n.off||n.removeEventListener).apply(n,a.args)}if(t.bindings=[],t.beforeRenderCallbacks=[],t.onUpdateEleCalcsFns=[],t.removeObserver&&t.removeObserver.disconnect(),t.styleObserver&&t.styleObserver.disconnect(),t.resizeObserver&&t.resizeObserver.disconnect(),t.labelCalcDiv)try{document.body.removeChild(t.labelCalcDiv)}catch{}};Zr.isHeadless=function(){return!1};[Zs,rf,af,Xr,Ut,La].forEach(function(t){ge(Zr,t)});var os=1e3/60,sf={setupDequeueing:function(e){return function(){var a=this,n=this.renderer;if(!a.dequeueingSetup){a.dequeueingSetup=!0;var i=Pa(function(){n.redrawHint("eles",!0),n.redrawHint("drag",!0),n.redraw()},e.deqRedrawThreshold),s=function(u,v){var f=$t(),c=n.averageRedrawTime,h=n.lastRedrawTime,d=[],y=n.cy.extent(),g=n.getPixelRatio();for(u||n.flushRenderedStyleQueue();;){var p=$t(),m=p-f,b=p-v;if(h<os){var w=os-(u?c:0);if(b>=e.deqFastCost*w)break}else if(u){if(m>=e.deqCost*h||m>=e.deqAvgCost*c)break}else if(b>=e.deqNoDrawCost*os)break;var E=e.deq(a,g,y);if(E.length>0)for(var C=0;C<E.length;C++)d.push(E[C]);else break}d.length>0&&(e.onDeqd(a,d),!u&&e.shouldRedraw(a,d,g,y)&&i())},o=e.priority||Ns;n.beforeRender(s,o(a))}}}},Op=function(){function t(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:vn;or(this,t),this.idsByKey=new Kt,this.keyForId=new Kt,this.cachesByLvl=new Kt,this.lvls=[],this.getKey=e,this.doesEleInvalidateKey=r}return ur(t,[{key:"getIdsFor",value:function(r){r==null&&Ve("Can not get id list for null key");var a=this.idsByKey,n=this.idsByKey.get(r);return n||(n=new $r,a.set(r,n)),n}},{key:"addIdForKey",value:function(r,a){r!=null&&this.getIdsFor(r).add(a)}},{key:"deleteIdForKey",value:function(r,a){r!=null&&this.getIdsFor(r).delete(a)}},{key:"getNumberOfIdsForKey",value:function(r){return r==null?0:this.getIdsFor(r).size}},{key:"updateKeyMappingFor",value:function(r){var a=r.id(),n=this.keyForId.get(a),i=this.getKey(r);this.deleteIdForKey(n,a),this.addIdForKey(i,a),this.keyForId.set(a,i)}},{key:"deleteKeyMappingFor",value:function(r){var a=r.id(),n=this.keyForId.get(a);this.deleteIdForKey(n,a),this.keyForId.delete(a)}},{key:"keyHasChangedFor",value:function(r){var a=r.id(),n=this.keyForId.get(a),i=this.getKey(r);return n!==i}},{key:"isInvalid",value:function(r){return this.keyHasChangedFor(r)||this.doesEleInvalidateKey(r)}},{key:"getCachesAt",value:function(r){var a=this.cachesByLvl,n=this.lvls,i=a.get(r);return i||(i=new Kt,a.set(r,i),n.push(r)),i}},{key:"getCache",value:function(r,a){return this.getCachesAt(a).get(r)}},{key:"get",value:function(r,a){var n=this.getKey(r),i=this.getCache(n,a);return i!=null&&this.updateKeyMappingFor(r),i}},{key:"getForCachedKey",value:function(r,a){var n=this.keyForId.get(r.id()),i=this.getCache(n,a);return i}},{key:"hasCache",value:function(r,a){return this.getCachesAt(a).has(r)}},{key:"has",value:function(r,a){var n=this.getKey(r);return this.hasCache(n,a)}},{key:"setCache",value:function(r,a,n){n.key=r,this.getCachesAt(a).set(r,n)}},{key:"set",value:function(r,a,n){var i=this.getKey(r);this.setCache(i,a,n),this.updateKeyMappingFor(r)}},{key:"deleteCache",value:function(r,a){this.getCachesAt(a).delete(r)}},{key:"delete",value:function(r,a){var n=this.getKey(r);this.deleteCache(n,a)}},{key:"invalidateKey",value:function(r){var a=this;this.lvls.forEach(function(n){return a.deleteCache(r,n)})}},{key:"invalidate",value:function(r){var a=r.id(),n=this.keyForId.get(a);this.deleteKeyMappingFor(r);var i=this.doesEleInvalidateKey(r);return i&&this.invalidateKey(n),i||this.getNumberOfIdsForKey(n)===0}}])}(),yl=25,Ya=50,sn=-4,ks=3,of=7.99,Np=8,Fp=1024,zp=1024,qp=1024,Vp=.2,_p=.8,Gp=10,Hp=.15,Kp=.1,$p=.9,Wp=.9,Up=100,Yp=1,Or={dequeue:"dequeue",downscale:"downscale",highQuality:"highQuality"},Xp=Ue({getKey:null,doesEleInvalidateKey:vn,drawElement:null,getBoundingBox:null,getRotationPoint:null,getRotationOffset:null,isVisible:Xl,allowEdgeTxrCaching:!0,allowParentTxrCaching:!0}),la=function(e,r){var a=this;a.renderer=e,a.onDequeues=[];var n=Xp(r);ge(a,n),a.lookup=new Op(n.getKey,n.doesEleInvalidateKey),a.setupDequeueing()},Ye=la.prototype;Ye.reasons=Or;Ye.getTextureQueue=function(t){var e=this;return e.eleImgCaches=e.eleImgCaches||{},e.eleImgCaches[t]=e.eleImgCaches[t]||[]};Ye.getRetiredTextureQueue=function(t){var e=this,r=e.eleImgCaches.retired=e.eleImgCaches.retired||{},a=r[t]=r[t]||[];return a};Ye.getElementQueue=function(){var t=this,e=t.eleCacheQueue=t.eleCacheQueue||new Ba(function(r,a){return a.reqs-r.reqs});return e};Ye.getElementKeyToQueue=function(){var t=this,e=t.eleKeyToCacheQueue=t.eleKeyToCacheQueue||{};return e};Ye.getElement=function(t,e,r,a,n){var i=this,s=this.renderer,o=s.cy.zoom(),l=this.lookup;if(!e||e.w===0||e.h===0||isNaN(e.w)||isNaN(e.h)||!t.visible()||t.removed()||!i.allowEdgeTxrCaching&&t.isEdge()||!i.allowParentTxrCaching&&t.isParent())return null;if(a==null&&(a=Math.ceil(zs(o*r))),a<sn)a=sn;else if(o>=of||a>ks)return null;var u=Math.pow(2,a),v=e.h*u,f=e.w*u,c=s.eleTextBiggerThanMin(t,u);if(!this.isVisible(t,c))return null;var h=l.get(t,a);if(h&&h.invalidated&&(h.invalidated=!1,h.texture.invalidatedWidth-=h.width),h)return h;var d;if(v<=yl?d=yl:v<=Ya?d=Ya:d=Math.ceil(v/Ya)*Ya,v>qp||f>zp)return null;var y=i.getTextureQueue(d),g=y[y.length-2],p=function(){return i.recycleTexture(d,f)||i.addTexture(d,f)};g||(g=y[y.length-1]),g||(g=p()),g.width-g.usedWidth<f&&(g=p());for(var m=function(I){return I&&I.scaledLabelShown===c},b=n&&n===Or.dequeue,w=n&&n===Or.highQuality,E=n&&n===Or.downscale,C,x=a+1;x<=ks;x++){var S=l.get(t,x);if(S){C=S;break}}var k=C&&C.level===a+1?C:null,B=function(){g.context.drawImage(k.texture.canvas,k.x,0,k.width,k.height,g.usedWidth,0,f,v)};if(g.context.setTransform(1,0,0,1,0,0),g.context.clearRect(g.usedWidth,0,f,d),m(k))B();else if(m(C))if(w){for(var D=C.level;D>a;D--)k=i.getElement(t,e,r,D,Or.downscale);B()}else return i.queueElement(t,C.level-1),C;else{var A;if(!b&&!w&&!E)for(var P=a-1;P>=sn;P--){var R=l.get(t,P);if(R){A=R;break}}if(m(A))return i.queueElement(t,a),A;g.context.translate(g.usedWidth,0),g.context.scale(u,u),this.drawElement(g.context,t,e,c,!1),g.context.scale(1/u,1/u),g.context.translate(-g.usedWidth,0)}return h={x:g.usedWidth,texture:g,level:a,scale:u,width:f,height:v,scaledLabelShown:c},g.usedWidth+=Math.ceil(f+Np),g.eleCaches.push(h),l.set(t,a,h),i.checkTextureFullness(g),h};Ye.invalidateElements=function(t){for(var e=0;e<t.length;e++)this.invalidateElement(t[e])};Ye.invalidateElement=function(t){var e=this,r=e.lookup,a=[],n=r.isInvalid(t);if(n){for(var i=sn;i<=ks;i++){var s=r.getForCachedKey(t,i);s&&a.push(s)}var o=r.invalidate(t);if(o)for(var l=0;l<a.length;l++){var u=a[l],v=u.texture;v.invalidatedWidth+=u.width,u.invalidated=!0,e.checkTextureUtility(v)}e.removeFromQueue(t)}};Ye.checkTextureUtility=function(t){t.invalidatedWidth>=Vp*t.width&&this.retireTexture(t)};Ye.checkTextureFullness=function(t){var e=this,r=e.getTextureQueue(t.height);t.usedWidth/t.width>_p&&t.fullnessChecks>=Gp?ar(r,t):t.fullnessChecks++};Ye.retireTexture=function(t){var e=this,r=t.height,a=e.getTextureQueue(r),n=this.lookup;ar(a,t),t.retired=!0;for(var i=t.eleCaches,s=0;s<i.length;s++){var o=i[s];n.deleteCache(o.key,o.level)}Fs(i);var l=e.getRetiredTextureQueue(r);l.push(t)};Ye.addTexture=function(t,e){var r=this,a=r.getTextureQueue(t),n={};return a.push(n),n.eleCaches=[],n.height=t,n.width=Math.max(Fp,e),n.usedWidth=0,n.invalidatedWidth=0,n.fullnessChecks=0,n.canvas=r.renderer.makeOffscreenCanvas(n.width,n.height),n.context=n.canvas.getContext("2d"),n};Ye.recycleTexture=function(t,e){for(var r=this,a=r.getTextureQueue(t),n=r.getRetiredTextureQueue(t),i=0;i<n.length;i++){var s=n[i];if(s.width>=e)return s.retired=!1,s.usedWidth=0,s.invalidatedWidth=0,s.fullnessChecks=0,Fs(s.eleCaches),s.context.setTransform(1,0,0,1,0,0),s.context.clearRect(0,0,s.width,s.height),ar(n,s),a.push(s),s}};Ye.queueElement=function(t,e){var r=this,a=r.getElementQueue(),n=r.getElementKeyToQueue(),i=this.getKey(t),s=n[i];if(s)s.level=Math.max(s.level,e),s.eles.merge(t),s.reqs++,a.updateItem(s);else{var o={eles:t.spawn().merge(t),level:e,reqs:1,key:i};a.push(o),n[i]=o}};Ye.dequeue=function(t){for(var e=this,r=e.getElementQueue(),a=e.getElementKeyToQueue(),n=[],i=e.lookup,s=0;s<Yp&&r.size()>0;s++){var o=r.pop(),l=o.key,u=o.eles[0],v=i.hasCache(u,o.level);if(a[l]=null,v)continue;n.push(o);var f=e.getBoundingBox(u);e.getElement(u,f,t,o.level,Or.dequeue)}return n};Ye.removeFromQueue=function(t){var e=this,r=e.getElementQueue(),a=e.getElementKeyToQueue(),n=this.getKey(t),i=a[n];i!=null&&(i.eles.length===1?(i.reqs=Os,r.updateItem(i),r.pop(),a[n]=null):i.eles.unmerge(t))};Ye.onDequeue=function(t){this.onDequeues.push(t)};Ye.offDequeue=function(t){ar(this.onDequeues,t)};Ye.setupDequeueing=sf.setupDequeueing({deqRedrawThreshold:Up,deqCost:Hp,deqAvgCost:Kp,deqNoDrawCost:$p,deqFastCost:Wp,deq:function(e,r,a){return e.dequeue(r,a)},onDeqd:function(e,r){for(var a=0;a<e.onDequeues.length;a++){var n=e.onDequeues[a];n(r)}},shouldRedraw:function(e,r,a,n){for(var i=0;i<r.length;i++)for(var s=r[i].eles,o=0;o<s.length;o++){var l=s[o].boundingBox();if(qs(l,n))return!0}return!1},priority:function(e){return e.renderer.beforeRenderPriorities.eleTxrDeq}});var Zp=1,fa=-4,mn=2,Qp=3.99,Jp=50,jp=50,ey=.15,ty=.1,ry=.9,ay=.9,ny=1,ml=250,iy=4e3*4e3,bl=32767,sy=!0,uf=function(e){var r=this,a=r.renderer=e,n=a.cy;r.layersByLevel={},r.firstGet=!0,r.lastInvalidationTime=$t()-2*ml,r.skipping=!1,r.eleTxrDeqs=n.collection(),r.scheduleElementRefinement=Pa(function(){r.refineElementTextures(r.eleTxrDeqs),r.eleTxrDeqs.unmerge(r.eleTxrDeqs)},jp),a.beforeRender(function(s,o){o-r.lastInvalidationTime<=ml?r.skipping=!0:r.skipping=!1},a.beforeRenderPriorities.lyrTxrSkip);var i=function(o,l){return l.reqs-o.reqs};r.layersQueue=new Ba(i),r.setupDequeueing()},it=uf.prototype,wl=0,oy=Math.pow(2,53)-1;it.makeLayer=function(t,e){var r=Math.pow(2,e),a=Math.ceil(t.w*r),n=Math.ceil(t.h*r),i=this.renderer.makeOffscreenCanvas(a,n),s={id:wl=++wl%oy,bb:t,level:e,width:a,height:n,canvas:i,context:i.getContext("2d"),eles:[],elesQueue:[],reqs:0},o=s.context,l=-s.bb.x1,u=-s.bb.y1;return o.scale(r,r),o.translate(l,u),s};it.getLayers=function(t,e,r){var a=this,n=a.renderer,i=n.cy,s=i.zoom(),o=a.firstGet;if(a.firstGet=!1,r==null){if(r=Math.ceil(zs(s*e)),r<fa)r=fa;else if(s>=Qp||r>mn)return null}a.validateLayersElesOrdering(r,t);var l=a.layersByLevel,u=Math.pow(2,r),v=l[r]=l[r]||[],f,c=a.levelIsComplete(r,t),h,d=function(){var B=function(L){if(a.validateLayersElesOrdering(L,t),a.levelIsComplete(L,t))return h=l[L],!0},D=function(L){if(!h)for(var I=r+L;fa<=I&&I<=mn&&!B(I);I+=L);};D(1),D(-1);for(var A=v.length-1;A>=0;A--){var P=v[A];P.invalid&&ar(v,P)}};if(!c)d();else return v;var y=function(){if(!f){f=pt();for(var B=0;B<t.length;B++)tv(f,t[B].boundingBox())}return f},g=function(B){B=B||{};var D=B.after;y();var A=Math.ceil(f.w*u),P=Math.ceil(f.h*u);if(A>bl||P>bl)return null;var R=A*P;if(R>iy)return null;var L=a.makeLayer(f,r);if(D!=null){var I=v.indexOf(D)+1;v.splice(I,0,L)}else(B.insert===void 0||B.insert)&&v.unshift(L);return L};if(a.skipping&&!o)return null;for(var p=null,m=t.length/Zp,b=!o,w=0;w<t.length;w++){var E=t[w],C=E._private.rscratch,x=C.imgLayerCaches=C.imgLayerCaches||{},S=x[r];if(S){p=S;continue}if((!p||p.eles.length>=m||!rv(p.bb,E.boundingBox()))&&(p=g({insert:!0,after:p}),!p))return null;h||b?a.queueLayer(p,E):a.drawEleInLayer(p,E,r,e),p.eles.push(E),x[r]=p}return h||(b?null:v)};it.getEleLevelForLayerLevel=function(t,e){return t};it.drawEleInLayer=function(t,e,r,a){var n=this,i=this.renderer,s=t.context,o=e.boundingBox();o.w===0||o.h===0||!e.visible()||(r=n.getEleLevelForLayerLevel(r,a),i.setImgSmoothing(s,!1),i.drawCachedElement(s,e,null,null,r,sy),i.setImgSmoothing(s,!0))};it.levelIsComplete=function(t,e){var r=this,a=r.layersByLevel[t];if(!a||a.length===0)return!1;for(var n=0,i=0;i<a.length;i++){var s=a[i];if(s.reqs>0||s.invalid)return!1;n+=s.eles.length}return n===e.length};it.validateLayersElesOrdering=function(t,e){var r=this.layersByLevel[t];if(r)for(var a=0;a<r.length;a++){for(var n=r[a],i=-1,s=0;s<e.length;s++)if(n.eles[0]===e[s]){i=s;break}if(i<0){this.invalidateLayer(n);continue}for(var o=i,s=0;s<n.eles.length;s++)if(n.eles[s]!==e[o+s]){this.invalidateLayer(n);break}}};it.updateElementsInLayers=function(t,e){for(var r=this,a=Ta(t[0]),n=0;n<t.length;n++)for(var i=a?null:t[n],s=a?t[n]:t[n].ele,o=s._private.rscratch,l=o.imgLayerCaches=o.imgLayerCaches||{},u=fa;u<=mn;u++){var v=l[u];v&&(i&&r.getEleLevelForLayerLevel(v.level)!==i.level||e(v,s,i))}};it.haveLayers=function(){for(var t=this,e=!1,r=fa;r<=mn;r++){var a=t.layersByLevel[r];if(a&&a.length>0){e=!0;break}}return e};it.invalidateElements=function(t){var e=this;t.length!==0&&(e.lastInvalidationTime=$t(),!(t.length===0||!e.haveLayers())&&e.updateElementsInLayers(t,function(a,n,i){e.invalidateLayer(a)}))};it.invalidateLayer=function(t){if(this.lastInvalidationTime=$t(),!t.invalid){var e=t.level,r=t.eles,a=this.layersByLevel[e];ar(a,t),t.elesQueue=[],t.invalid=!0,t.replacement&&(t.replacement.invalid=!0);for(var n=0;n<r.length;n++){var i=r[n]._private.rscratch.imgLayerCaches;i&&(i[e]=null)}}};it.refineElementTextures=function(t){var e=this;e.updateElementsInLayers(t,function(a,n,i){var s=a.replacement;if(s||(s=a.replacement=e.makeLayer(a.bb,a.level),s.replaces=a,s.eles=a.eles),!s.reqs)for(var o=0;o<s.eles.length;o++)e.queueLayer(s,s.eles[o])})};it.enqueueElementRefinement=function(t){this.eleTxrDeqs.merge(t),this.scheduleElementRefinement()};it.queueLayer=function(t,e){var r=this,a=r.layersQueue,n=t.elesQueue,i=n.hasId=n.hasId||{};if(!t.replacement){if(e){if(i[e.id()])return;n.push(e),i[e.id()]=!0}t.reqs?(t.reqs++,a.updateItem(t)):(t.reqs=1,a.push(t))}};it.dequeue=function(t){for(var e=this,r=e.layersQueue,a=[],n=0;n<ny&&r.size()!==0;){var i=r.peek();if(i.replacement){r.pop();continue}if(i.replaces&&i!==i.replaces.replacement){r.pop();continue}if(i.invalid){r.pop();continue}var s=i.elesQueue.shift();s&&(e.drawEleInLayer(i,s,i.level,t),n++),a.length===0&&a.push(!0),i.elesQueue.length===0&&(r.pop(),i.reqs=0,i.replaces&&e.applyLayerReplacement(i),e.requestRedraw())}return a};it.applyLayerReplacement=function(t){var e=this,r=e.layersByLevel[t.level],a=t.replaces,n=r.indexOf(a);if(!(n<0||a.invalid)){r[n]=t;for(var i=0;i<t.eles.length;i++){var s=t.eles[i]._private,o=s.imgLayerCaches=s.imgLayerCaches||{};o&&(o[t.level]=t)}e.requestRedraw()}};it.requestRedraw=Pa(function(){var t=this.renderer;t.redrawHint("eles",!0),t.redrawHint("drag",!0),t.redraw()},100);it.setupDequeueing=sf.setupDequeueing({deqRedrawThreshold:Jp,deqCost:ey,deqAvgCost:ty,deqNoDrawCost:ry,deqFastCost:ay,deq:function(e,r){return e.dequeue(r)},onDeqd:Ns,shouldRedraw:Xl,priority:function(e){return e.renderer.beforeRenderPriorities.lyrTxrDeq}});var lf={},xl;function uy(t,e){for(var r=0;r<e.length;r++){var a=e[r];t.lineTo(a.x,a.y)}}function ly(t,e,r){for(var a,n=0;n<e.length;n++){var i=e[n];n===0&&(a=i),t.lineTo(i.x,i.y)}t.quadraticCurveTo(r.x,r.y,a.x,a.y)}function El(t,e,r){t.beginPath&&t.beginPath();for(var a=e,n=0;n<a.length;n++){var i=a[n];t.lineTo(i.x,i.y)}var s=r,o=r[0];t.moveTo(o.x,o.y);for(var n=1;n<s.length;n++){var i=s[n];t.lineTo(i.x,i.y)}t.closePath&&t.closePath()}function vy(t,e,r,a,n){t.beginPath&&t.beginPath(),t.arc(r,a,n,0,Math.PI*2,!1);var i=e,s=i[0];t.moveTo(s.x,s.y);for(var o=0;o<i.length;o++){var l=i[o];t.lineTo(l.x,l.y)}t.closePath&&t.closePath()}function fy(t,e,r,a){t.arc(e,r,a,0,Math.PI*2,!1)}lf.arrowShapeImpl=function(t){return(xl||(xl={polygon:uy,"triangle-backcurve":ly,"triangle-tee":El,"circle-triangle":vy,"triangle-cross":El,circle:fy}))[t]};var qt={};qt.drawElement=function(t,e,r,a,n,i){var s=this;e.isNode()?s.drawNode(t,e,r,a,n,i):s.drawEdge(t,e,r,a,n,i)};qt.drawElementOverlay=function(t,e){var r=this;e.isNode()?r.drawNodeOverlay(t,e):r.drawEdgeOverlay(t,e)};qt.drawElementUnderlay=function(t,e){var r=this;e.isNode()?r.drawNodeUnderlay(t,e):r.drawEdgeUnderlay(t,e)};qt.drawCachedElementPortion=function(t,e,r,a,n,i,s,o){var l=this,u=r.getBoundingBox(e);if(!(u.w===0||u.h===0)){var v=r.getElement(e,u,a,n,i);if(v!=null){var f=o(l,e);if(f===0)return;var c=s(l,e),h=u.x1,d=u.y1,y=u.w,g=u.h,p,m,b,w,E;if(c!==0){var C=r.getRotationPoint(e);b=C.x,w=C.y,t.translate(b,w),t.rotate(c),E=l.getImgSmoothing(t),E||l.setImgSmoothing(t,!0);var x=r.getRotationOffset(e);p=x.x,m=x.y}else p=h,m=d;var S;f!==1&&(S=t.globalAlpha,t.globalAlpha=S*f),t.drawImage(v.texture.canvas,v.x,0,v.width,v.height,p,m,y,g),f!==1&&(t.globalAlpha=S),c!==0&&(t.rotate(-c),t.translate(-b,-w),E||l.setImgSmoothing(t,!1))}else r.drawElement(t,e)}};var cy=function(){return 0},dy=function(e,r){return e.getTextAngle(r,null)},hy=function(e,r){return e.getTextAngle(r,"source")},gy=function(e,r){return e.getTextAngle(r,"target")},py=function(e,r){return r.effectiveOpacity()},us=function(e,r){return r.pstyle("text-opacity").pfValue*r.effectiveOpacity()};qt.drawCachedElement=function(t,e,r,a,n,i){var s=this,o=s.data,l=o.eleTxrCache,u=o.lblTxrCache,v=o.slbTxrCache,f=o.tlbTxrCache,c=e.boundingBox(),h=i===!0?l.reasons.highQuality:null;if(!(c.w===0||c.h===0||!e.visible())&&(!a||qs(c,a))){var d=e.isEdge(),y=e.element()._private.rscratch.badLine;s.drawElementUnderlay(t,e),s.drawCachedElementPortion(t,e,l,r,n,h,cy,py),(!d||!y)&&s.drawCachedElementPortion(t,e,u,r,n,h,dy,us),d&&!y&&(s.drawCachedElementPortion(t,e,v,r,n,h,hy,us),s.drawCachedElementPortion(t,e,f,r,n,h,gy,us)),s.drawElementOverlay(t,e)}};qt.drawElements=function(t,e){for(var r=this,a=0;a<e.length;a++){var n=e[a];r.drawElement(t,n)}};qt.drawCachedElements=function(t,e,r,a){for(var n=this,i=0;i<e.length;i++){var s=e[i];n.drawCachedElement(t,s,r,a)}};qt.drawCachedNodes=function(t,e,r,a){for(var n=this,i=0;i<e.length;i++){var s=e[i];s.isNode()&&n.drawCachedElement(t,s,r,a)}};qt.drawLayeredElements=function(t,e,r,a){var n=this,i=n.data.lyrTxrCache.getLayers(e,r);if(i)for(var s=0;s<i.length;s++){var o=i[s],l=o.bb;l.w===0||l.h===0||t.drawImage(o.canvas,l.x1,l.y1,l.w,l.h)}else n.drawCachedElements(t,e,r,a)};var Yt={};Yt.drawEdge=function(t,e,r){var a=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!0,n=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!0,i=arguments.length>5&&arguments[5]!==void 0?arguments[5]:!0,s=this,o=e._private.rscratch;if(!(i&&!e.visible())&&!(o.badLine||o.allpts==null||isNaN(o.allpts[0]))){var l;r&&(l=r,t.translate(-l.x1,-l.y1));var u=i?e.pstyle("opacity").value:1,v=i?e.pstyle("line-opacity").value:1,f=e.pstyle("curve-style").value,c=e.pstyle("line-style").value,h=e.pstyle("width").pfValue,d=e.pstyle("line-cap").value,y=e.pstyle("line-outline-width").value,g=e.pstyle("line-outline-color").value,p=u*v,m=u*v,b=function(){var L=arguments.length>0&&arguments[0]!==void 0?arguments[0]:p;f==="straight-triangle"?(s.eleStrokeStyle(t,e,L),s.drawEdgeTrianglePath(e,t,o.allpts)):(t.lineWidth=h,t.lineCap=d,s.eleStrokeStyle(t,e,L),s.drawEdgePath(e,t,o.allpts,c),t.lineCap="butt")},w=function(){var L=arguments.length>0&&arguments[0]!==void 0?arguments[0]:p;if(t.lineWidth=h+y,t.lineCap=d,y>0)s.colorStrokeStyle(t,g[0],g[1],g[2],L);else{t.lineCap="butt";return}f==="straight-triangle"?s.drawEdgeTrianglePath(e,t,o.allpts):(s.drawEdgePath(e,t,o.allpts,c),t.lineCap="butt")},E=function(){n&&s.drawEdgeOverlay(t,e)},C=function(){n&&s.drawEdgeUnderlay(t,e)},x=function(){var L=arguments.length>0&&arguments[0]!==void 0?arguments[0]:m;s.drawArrowheads(t,e,L)},S=function(){s.drawElementText(t,e,null,a)};t.lineJoin="round";var k=e.pstyle("ghost").value==="yes";if(k){var B=e.pstyle("ghost-offset-x").pfValue,D=e.pstyle("ghost-offset-y").pfValue,A=e.pstyle("ghost-opacity").value,P=p*A;t.translate(B,D),b(P),x(P),t.translate(-B,-D)}else w();C(),b(),x(),E(),S(),r&&t.translate(l.x1,l.y1)}};var vf=function(e){if(!["overlay","underlay"].includes(e))throw new Error("Invalid state");return function(r,a){if(a.visible()){var n=a.pstyle("".concat(e,"-opacity")).value;if(n!==0){var i=this,s=i.usePaths(),o=a._private.rscratch,l=a.pstyle("".concat(e,"-padding")).pfValue,u=2*l,v=a.pstyle("".concat(e,"-color")).value;r.lineWidth=u,o.edgeType==="self"&&!s?r.lineCap="butt":r.lineCap="round",i.colorStrokeStyle(r,v[0],v[1],v[2],n),i.drawEdgePath(a,r,o.allpts,"solid")}}}};Yt.drawEdgeOverlay=vf("overlay");Yt.drawEdgeUnderlay=vf("underlay");Yt.drawEdgePath=function(t,e,r,a){var n=t._private.rscratch,i=e,s,o=!1,l=this.usePaths(),u=t.pstyle("line-dash-pattern").pfValue,v=t.pstyle("line-dash-offset").pfValue;if(l){var f=r.join("$"),c=n.pathCacheKey&&n.pathCacheKey===f;c?(s=e=n.pathCache,o=!0):(s=e=new Path2D,n.pathCacheKey=f,n.pathCache=s)}if(i.setLineDash)switch(a){case"dotted":i.setLineDash([1,1]);break;case"dashed":i.setLineDash(u),i.lineDashOffset=v;break;case"solid":i.setLineDash([]);break}if(!o&&!n.badLine)switch(e.beginPath&&e.beginPath(),e.moveTo(r[0],r[1]),n.edgeType){case"bezier":case"self":case"compound":case"multibezier":for(var h=2;h+3<r.length;h+=4)e.quadraticCurveTo(r[h],r[h+1],r[h+2],r[h+3]);break;case"straight":case"haystack":for(var d=2;d+1<r.length;d+=2)e.lineTo(r[d],r[d+1]);break;case"segments":if(n.isRound){var y=Pt(n.roundCorners),g;try{for(y.s();!(g=y.n()).done;){var p=g.value;Qv(e,p)}}catch(b){y.e(b)}finally{y.f()}e.lineTo(r[r.length-2],r[r.length-1])}else for(var m=2;m+1<r.length;m+=2)e.lineTo(r[m],r[m+1]);break}e=i,l?e.stroke(s):e.stroke(),e.setLineDash&&e.setLineDash([])};Yt.drawEdgeTrianglePath=function(t,e,r){e.fillStyle=e.strokeStyle;for(var a=t.pstyle("width").pfValue,n=0;n+1<r.length;n+=2){var i=[r[n+2]-r[n],r[n+3]-r[n+1]],s=Math.sqrt(i[0]*i[0]+i[1]*i[1]),o=[i[1]/s,-i[0]/s],l=[o[0]*a/2,o[1]*a/2];e.beginPath(),e.moveTo(r[n]-l[0],r[n+1]-l[1]),e.lineTo(r[n]+l[0],r[n+1]+l[1]),e.lineTo(r[n+2],r[n+3]),e.closePath(),e.fill()}};Yt.drawArrowheads=function(t,e,r){var a=e._private.rscratch,n=a.edgeType==="haystack";n||this.drawArrowhead(t,e,"source",a.arrowStartX,a.arrowStartY,a.srcArrowAngle,r),this.drawArrowhead(t,e,"mid-target",a.midX,a.midY,a.midtgtArrowAngle,r),this.drawArrowhead(t,e,"mid-source",a.midX,a.midY,a.midsrcArrowAngle,r),n||this.drawArrowhead(t,e,"target",a.arrowEndX,a.arrowEndY,a.tgtArrowAngle,r)};Yt.drawArrowhead=function(t,e,r,a,n,i,s){if(!(isNaN(a)||a==null||isNaN(n)||n==null||isNaN(i)||i==null)){var o=this,l=e.pstyle(r+"-arrow-shape").value;if(l!=="none"){var u=e.pstyle(r+"-arrow-fill").value==="hollow"?"both":"filled",v=e.pstyle(r+"-arrow-fill").value,f=e.pstyle("width").pfValue,c=e.pstyle(r+"-arrow-width"),h=c.value==="match-line"?f:c.pfValue;c.units==="%"&&(h*=f);var d=e.pstyle("opacity").value;s===void 0&&(s=d);var y=t.globalCompositeOperation;(s!==1||v==="hollow")&&(t.globalCompositeOperation="destination-out",o.colorFillStyle(t,255,255,255,1),o.colorStrokeStyle(t,255,255,255,1),o.drawArrowShape(e,t,u,f,l,h,a,n,i),t.globalCompositeOperation=y);var g=e.pstyle(r+"-arrow-color").value;o.colorFillStyle(t,g[0],g[1],g[2],s),o.colorStrokeStyle(t,g[0],g[1],g[2],s),o.drawArrowShape(e,t,v,f,l,h,a,n,i)}}};Yt.drawArrowShape=function(t,e,r,a,n,i,s,o,l){var u=this,v=this.usePaths()&&n!=="triangle-cross",f=!1,c,h=e,d={x:s,y:o},y=t.pstyle("arrow-scale").value,g=this.getArrowWidth(a,y),p=u.arrowShapes[n];if(v){var m=u.arrowPathCache=u.arrowPathCache||[],b=rr(n),w=m[b];w!=null?(c=e=w,f=!0):(c=e=new Path2D,m[b]=c)}f||(e.beginPath&&e.beginPath(),v?p.draw(e,1,0,{x:0,y:0},1):p.draw(e,g,l,d,a),e.closePath&&e.closePath()),e=h,v&&(e.translate(s,o),e.rotate(l),e.scale(g,g)),(r==="filled"||r==="both")&&(v?e.fill(c):e.fill()),(r==="hollow"||r==="both")&&(e.lineWidth=i/(v?g:1),e.lineJoin="miter",v?e.stroke(c):e.stroke()),v&&(e.scale(1/g,1/g),e.rotate(-l),e.translate(-s,-o))};var js={};js.safeDrawImage=function(t,e,r,a,n,i,s,o,l,u){if(!(n<=0||i<=0||l<=0||u<=0))try{t.drawImage(e,r,a,n,i,s,o,l,u)}catch(v){Re(v)}};js.drawInscribedImage=function(t,e,r,a,n){var i=this,s=r.position(),o=s.x,l=s.y,u=r.cy().style(),v=u.getIndexedStyle.bind(u),f=v(r,"background-fit","value",a),c=v(r,"background-repeat","value",a),h=r.width(),d=r.height(),y=r.padding()*2,g=h+(v(r,"background-width-relative-to","value",a)==="inner"?0:y),p=d+(v(r,"background-height-relative-to","value",a)==="inner"?0:y),m=r._private.rscratch,b=v(r,"background-clip","value",a),w=b==="node",E=v(r,"background-image-opacity","value",a)*n,C=v(r,"background-image-smoothing","value",a),x=r.pstyle("corner-radius").value;x!=="auto"&&(x=r.pstyle("corner-radius").pfValue);var S=e.width||e.cachedW,k=e.height||e.cachedH;(S==null||k==null)&&(document.body.appendChild(e),S=e.cachedW=e.width||e.offsetWidth,k=e.cachedH=e.height||e.offsetHeight,document.body.removeChild(e));var B=S,D=k;if(v(r,"background-width","value",a)!=="auto"&&(v(r,"background-width","units",a)==="%"?B=v(r,"background-width","pfValue",a)*g:B=v(r,"background-width","pfValue",a)),v(r,"background-height","value",a)!=="auto"&&(v(r,"background-height","units",a)==="%"?D=v(r,"background-height","pfValue",a)*p:D=v(r,"background-height","pfValue",a)),!(B===0||D===0)){if(f==="contain"){var A=Math.min(g/B,p/D);B*=A,D*=A}else if(f==="cover"){var A=Math.max(g/B,p/D);B*=A,D*=A}var P=o-g/2,R=v(r,"background-position-x","units",a),L=v(r,"background-position-x","pfValue",a);R==="%"?P+=(g-B)*L:P+=L;var I=v(r,"background-offset-x","units",a),M=v(r,"background-offset-x","pfValue",a);I==="%"?P+=(g-B)*M:P+=M;var O=l-p/2,_=v(r,"background-position-y","units",a),H=v(r,"background-position-y","pfValue",a);_==="%"?O+=(p-D)*H:O+=H;var F=v(r,"background-offset-y","units",a),G=v(r,"background-offset-y","pfValue",a);F==="%"?O+=(p-D)*G:O+=G,m.pathCache&&(P-=o,O-=l,o=0,l=0);var U=t.globalAlpha;t.globalAlpha=E;var X=i.getImgSmoothing(t),Z=!1;if(C==="no"&&X?(i.setImgSmoothing(t,!1),Z=!0):C==="yes"&&!X&&(i.setImgSmoothing(t,!0),Z=!0),c==="no-repeat")w&&(t.save(),m.pathCache?t.clip(m.pathCache):(i.nodeShapes[i.getNodeShape(r)].draw(t,o,l,g,p,x,m),t.clip())),i.safeDrawImage(t,e,0,0,S,k,P,O,B,D),w&&t.restore();else{var Q=t.createPattern(e,c);t.fillStyle=Q,i.nodeShapes[i.getNodeShape(r)].draw(t,o,l,g,p,x,m),t.translate(P,O),t.fill(),t.translate(-P,-O)}t.globalAlpha=U,Z&&i.setImgSmoothing(t,X)}};var Cr={};Cr.eleTextBiggerThanMin=function(t,e){if(!e){var r=t.cy().zoom(),a=this.getPixelRatio(),n=Math.ceil(zs(r*a));e=Math.pow(2,n)}var i=t.pstyle("font-size").pfValue*e,s=t.pstyle("min-zoomed-font-size").pfValue;return!(i<s)};Cr.drawElementText=function(t,e,r,a,n){var i=arguments.length>5&&arguments[5]!==void 0?arguments[5]:!0,s=this;if(a==null){if(i&&!s.eleTextBiggerThanMin(e))return}else if(a===!1)return;if(e.isNode()){var o=e.pstyle("label");if(!o||!o.value)return;var l=s.getLabelJustification(e);t.textAlign=l,t.textBaseline="bottom"}else{var u=e.element()._private.rscratch.badLine,v=e.pstyle("label"),f=e.pstyle("source-label"),c=e.pstyle("target-label");if(u||(!v||!v.value)&&(!f||!f.value)&&(!c||!c.value))return;t.textAlign="center",t.textBaseline="bottom"}var h=!r,d;r&&(d=r,t.translate(-d.x1,-d.y1)),n==null?(s.drawText(t,e,null,h,i),e.isEdge()&&(s.drawText(t,e,"source",h,i),s.drawText(t,e,"target",h,i))):s.drawText(t,e,n,h,i),r&&t.translate(d.x1,d.y1)};Cr.getFontCache=function(t){var e;this.fontCaches=this.fontCaches||[];for(var r=0;r<this.fontCaches.length;r++)if(e=this.fontCaches[r],e.context===t)return e;return e={context:t},this.fontCaches.push(e),e};Cr.setupTextStyle=function(t,e){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,a=e.pstyle("font-style").strValue,n=e.pstyle("font-size").pfValue+"px",i=e.pstyle("font-family").strValue,s=e.pstyle("font-weight").strValue,o=r?e.effectiveOpacity()*e.pstyle("text-opacity").value:1,l=e.pstyle("text-outline-opacity").value*o,u=e.pstyle("color").value,v=e.pstyle("text-outline-color").value;t.font=a+" "+s+" "+n+" "+i,t.lineJoin="round",this.colorFillStyle(t,u[0],u[1],u[2],o),this.colorStrokeStyle(t,v[0],v[1],v[2],l)};function ls(t,e,r,a,n){var i=arguments.length>5&&arguments[5]!==void 0?arguments[5]:5,s=arguments.length>6?arguments[6]:void 0;t.beginPath(),t.moveTo(e+i,r),t.lineTo(e+a-i,r),t.quadraticCurveTo(e+a,r,e+a,r+i),t.lineTo(e+a,r+n-i),t.quadraticCurveTo(e+a,r+n,e+a-i,r+n),t.lineTo(e+i,r+n),t.quadraticCurveTo(e,r+n,e,r+n-i),t.lineTo(e,r+i),t.quadraticCurveTo(e,r,e+i,r),t.closePath(),s?t.stroke():t.fill()}Cr.getTextAngle=function(t,e){var r,a=t._private,n=a.rscratch,i=e?e+"-":"",s=t.pstyle(i+"text-rotation");if(s.strValue==="autorotate"){var o=Et(n,"labelAngle",e);r=t.isEdge()?o:0}else s.strValue==="none"?r=0:r=s.pfValue;return r};Cr.drawText=function(t,e,r){var a=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!0,n=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!0,i=e._private,s=i.rscratch,o=n?e.effectiveOpacity():1;if(!(n&&(o===0||e.pstyle("text-opacity").value===0))){r==="main"&&(r=null);var l=Et(s,"labelX",r),u=Et(s,"labelY",r),v,f,c=this.getLabelText(e,r);if(c!=null&&c!==""&&!isNaN(l)&&!isNaN(u)){this.setupTextStyle(t,e,n);var h=r?r+"-":"",d=Et(s,"labelWidth",r),y=Et(s,"labelHeight",r),g=e.pstyle(h+"text-margin-x").pfValue,p=e.pstyle(h+"text-margin-y").pfValue,m=e.isEdge(),b=e.pstyle("text-halign").value,w=e.pstyle("text-valign").value;m&&(b="center",w="center"),l+=g,u+=p;var E;switch(a?E=this.getTextAngle(e,r):E=0,E!==0&&(v=l,f=u,t.translate(v,f),t.rotate(E),l=0,u=0),w){case"top":break;case"center":u+=y/2;break;case"bottom":u+=y;break}var C=e.pstyle("text-background-opacity").value,x=e.pstyle("text-border-opacity").value,S=e.pstyle("text-border-width").pfValue,k=e.pstyle("text-background-padding").pfValue,B=e.pstyle("text-background-shape").strValue,D=B.indexOf("round")===0,A=2;if(C>0||S>0&&x>0){var P=l-k;switch(b){case"left":P-=d;break;case"center":P-=d/2;break}var R=u-y-k,L=d+2*k,I=y+2*k;if(C>0){var M=t.fillStyle,O=e.pstyle("text-background-color").value;t.fillStyle="rgba("+O[0]+","+O[1]+","+O[2]+","+C*o+")",D?ls(t,P,R,L,I,A):t.fillRect(P,R,L,I),t.fillStyle=M}if(S>0&&x>0){var _=t.strokeStyle,H=t.lineWidth,F=e.pstyle("text-border-color").value,G=e.pstyle("text-border-style").value;if(t.strokeStyle="rgba("+F[0]+","+F[1]+","+F[2]+","+x*o+")",t.lineWidth=S,t.setLineDash)switch(G){case"dotted":t.setLineDash([1,1]);break;case"dashed":t.setLineDash([4,2]);break;case"double":t.lineWidth=S/4,t.setLineDash([]);break;case"solid":t.setLineDash([]);break}if(D?ls(t,P,R,L,I,A,"stroke"):t.strokeRect(P,R,L,I),G==="double"){var U=S/2;D?ls(t,P+U,R+U,L-U*2,I-U*2,A,"stroke"):t.strokeRect(P+U,R+U,L-U*2,I-U*2)}t.setLineDash&&t.setLineDash([]),t.lineWidth=H,t.strokeStyle=_}}var X=2*e.pstyle("text-outline-width").pfValue;if(X>0&&(t.lineWidth=X),e.pstyle("text-wrap").value==="wrap"){var Z=Et(s,"labelWrapCachedLines",r),Q=Et(s,"labelLineHeight",r),ee=d/2,te=this.getLabelJustification(e);switch(te==="auto"||(b==="left"?te==="left"?l+=-d:te==="center"&&(l+=-ee):b==="center"?te==="left"?l+=-ee:te==="right"&&(l+=ee):b==="right"&&(te==="center"?l+=ee:te==="right"&&(l+=d))),w){case"top":u-=(Z.length-1)*Q;break;case"center":case"bottom":u-=(Z.length-1)*Q;break}for(var K=0;K<Z.length;K++)X>0&&t.strokeText(Z[K],l,u),t.fillText(Z[K],l,u),u+=Q}else X>0&&t.strokeText(c,l,u),t.fillText(c,l,u);E!==0&&(t.rotate(-E),t.translate(-v,-f))}}};var Qr={};Qr.drawNode=function(t,e,r){var a=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!0,n=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!0,i=arguments.length>5&&arguments[5]!==void 0?arguments[5]:!0,s=this,o,l,u=e._private,v=u.rscratch,f=e.position();if(!(!ae(f.x)||!ae(f.y))&&!(i&&!e.visible())){var c=i?e.effectiveOpacity():1,h=s.usePaths(),d,y=!1,g=e.padding();o=e.width()+2*g,l=e.height()+2*g;var p;r&&(p=r,t.translate(-p.x1,-p.y1));for(var m=e.pstyle("background-image"),b=m.value,w=new Array(b.length),E=new Array(b.length),C=0,x=0;x<b.length;x++){var S=b[x],k=w[x]=S!=null&&S!=="none";if(k){var B=e.cy().style().getIndexedStyle(e,"background-image-crossorigin","value",x);C++,E[x]=s.getCachedImage(S,B,function(){u.backgroundTimestamp=Date.now(),e.emitAndNotify("background")})}}var D=e.pstyle("background-blacken").value,A=e.pstyle("border-width").pfValue,P=e.pstyle("background-opacity").value*c,R=e.pstyle("border-color").value,L=e.pstyle("border-style").value,I=e.pstyle("border-join").value,M=e.pstyle("border-cap").value,O=e.pstyle("border-position").value,_=e.pstyle("border-dash-pattern").pfValue,H=e.pstyle("border-dash-offset").pfValue,F=e.pstyle("border-opacity").value*c,G=e.pstyle("outline-width").pfValue,U=e.pstyle("outline-color").value,X=e.pstyle("outline-style").value,Z=e.pstyle("outline-opacity").value*c,Q=e.pstyle("outline-offset").value,ee=e.pstyle("corner-radius").value;ee!=="auto"&&(ee=e.pstyle("corner-radius").pfValue);var te=function(){var z=arguments.length>0&&arguments[0]!==void 0?arguments[0]:P;s.eleFillStyle(t,e,z)},K=function(){var z=arguments.length>0&&arguments[0]!==void 0?arguments[0]:F;s.colorStrokeStyle(t,R[0],R[1],R[2],z)},N=function(){var z=arguments.length>0&&arguments[0]!==void 0?arguments[0]:Z;s.colorStrokeStyle(t,U[0],U[1],U[2],z)},$=function(z,V,ne,Y){var ie=s.nodePathCache=s.nodePathCache||[],ce=Yl(ne==="polygon"?ne+","+Y.join(","):ne,""+V,""+z,""+ee),Ee=ie[ce],ve,be=!1;return Ee!=null?(ve=Ee,be=!0,v.pathCache=ve):(ve=new Path2D,ie[ce]=v.pathCache=ve),{path:ve,cacheHit:be}},J=e.pstyle("shape").strValue,re=e.pstyle("shape-polygon-points").pfValue;if(h){t.translate(f.x,f.y);var le=$(o,l,J,re);d=le.path,y=le.cacheHit}var xe=function(){if(!y){var z=f;h&&(z={x:0,y:0}),s.nodeShapes[s.getNodeShape(e)].draw(d||t,z.x,z.y,o,l,ee,v)}h?t.fill(d):t.fill()},Ie=function(){for(var z=arguments.length>0&&arguments[0]!==void 0?arguments[0]:c,V=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,ne=u.backgrounding,Y=0,ie=0;ie<E.length;ie++){var ce=e.cy().style().getIndexedStyle(e,"background-image-containment","value",ie);if(V&&ce==="over"||!V&&ce==="inside"){Y++;continue}w[ie]&&E[ie].complete&&!E[ie].error&&(Y++,s.drawInscribedImage(t,E[ie],e,ie,z))}u.backgrounding=Y!==C,ne!==u.backgrounding&&e.updateStyle(!1)},Be=function(){var z=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,V=arguments.length>1&&arguments[1]!==void 0?arguments[1]:c;s.hasPie(e)&&(s.drawPie(t,e,V),z&&(h||s.nodeShapes[s.getNodeShape(e)].draw(t,f.x,f.y,o,l,ee,v)))},se=function(){var z=arguments.length>0&&arguments[0]!==void 0?arguments[0]:c,V=(D>0?D:-D)*z,ne=D>0?0:255;D!==0&&(s.colorFillStyle(t,ne,ne,ne,V),h?t.fill(d):t.fill())},ue=function(){if(A>0){if(t.lineWidth=A,t.lineCap=M,t.lineJoin=I,t.setLineDash)switch(L){case"dotted":t.setLineDash([1,1]);break;case"dashed":t.setLineDash(_),t.lineDashOffset=H;break;case"solid":case"double":t.setLineDash([]);break}if(O!=="center"){if(t.save(),t.lineWidth*=2,O==="inside")h?t.clip(d):t.clip();else{var z=new Path2D;z.rect(-o/2-A,-l/2-A,o+2*A,l+2*A),z.addPath(d),t.clip(z,"evenodd")}h?t.stroke(d):t.stroke(),t.restore()}else h?t.stroke(d):t.stroke();if(L==="double"){t.lineWidth=A/3;var V=t.globalCompositeOperation;t.globalCompositeOperation="destination-out",h?t.stroke(d):t.stroke(),t.globalCompositeOperation=V}t.setLineDash&&t.setLineDash([])}},de=function(){if(G>0){if(t.lineWidth=G,t.lineCap="butt",t.setLineDash)switch(X){case"dotted":t.setLineDash([1,1]);break;case"dashed":t.setLineDash([4,2]);break;case"solid":case"double":t.setLineDash([]);break}var z=f;h&&(z={x:0,y:0});var V=s.getNodeShape(e),ne=A;O==="inside"&&(ne=0),O==="outside"&&(ne*=2);var Y=(o+ne+(G+Q))/o,ie=(l+ne+(G+Q))/l,ce=o*Y,Ee=l*ie,ve=s.nodeShapes[V].points,be;if(h){var we=$(ce,Ee,V,ve);be=we.path}if(V==="ellipse")s.drawEllipsePath(be||t,z.x,z.y,ce,Ee);else if(["round-diamond","round-heptagon","round-hexagon","round-octagon","round-pentagon","round-polygon","round-triangle","round-tag"].includes(V)){var pe=0,Oe=0,qe=0;V==="round-diamond"?pe=(ne+Q+G)*1.4:V==="round-heptagon"?(pe=(ne+Q+G)*1.075,qe=-(ne/2+Q+G)/35):V==="round-hexagon"?pe=(ne+Q+G)*1.12:V==="round-pentagon"?(pe=(ne+Q+G)*1.13,qe=-(ne/2+Q+G)/15):V==="round-tag"?(pe=(ne+Q+G)*1.12,Oe=(ne/2+G+Q)*.07):V==="round-triangle"&&(pe=(ne+Q+G)*(Math.PI/2),qe=-(ne+Q/2+G)/Math.PI),pe!==0&&(Y=(o+pe)/o,ce=o*Y,["round-hexagon","round-tag"].includes(V)||(ie=(l+pe)/l,Ee=l*ie)),ee=ee==="auto"?iv(ce,Ee):ee;for(var yt=ce/2,mt=Ee/2,He=ee+(ne+G+Q)/2,Xe=new Array(ve.length/2),Ze=new Array(ve.length/2),vt=0;vt<ve.length/2;vt++)Xe[vt]={x:z.x+Oe+yt*ve[vt*2],y:z.y+qe+mt*ve[vt*2+1]};var ft,Rt,wt,Mt,Vt=Xe.length;for(Rt=Xe[Vt-1],ft=0;ft<Vt;ft++)wt=Xe[ft%Vt],Mt=Xe[(ft+1)%Vt],Ze[ft]=Qs(Rt,wt,Mt,He),Rt=wt,wt=Mt;s.drawRoundPolygonPath(be||t,z.x+Oe,z.y+qe,o*Y,l*ie,ve,Ze)}else if(["roundrectangle","round-rectangle"].includes(V))ee=ee==="auto"?mr(ce,Ee):ee,s.drawRoundRectanglePath(be||t,z.x,z.y,ce,Ee,ee+(ne+G+Q)/2);else if(["cutrectangle","cut-rectangle"].includes(V))ee=ee==="auto"?Vs():ee,s.drawCutRectanglePath(be||t,z.x,z.y,ce,Ee,null,ee+(ne+G+Q)/4);else if(["bottomroundrectangle","bottom-round-rectangle"].includes(V))ee=ee==="auto"?mr(ce,Ee):ee,s.drawBottomRoundRectanglePath(be||t,z.x,z.y,ce,Ee,ee+(ne+G+Q)/2);else if(V==="barrel")s.drawBarrelPath(be||t,z.x,z.y,ce,Ee);else if(V.startsWith("polygon")||["rhomboid","right-rhomboid","round-tag","tag","vee"].includes(V)){var _t=(ne+G+Q)/o;ve=fn(cn(ve,_t)),s.drawPolygonPath(be||t,z.x,z.y,o,l,ve)}else{var st=(ne+G+Q)/o;ve=fn(cn(ve,-st)),s.drawPolygonPath(be||t,z.x,z.y,o,l,ve)}if(h?t.stroke(be):t.stroke(),X==="double"){t.lineWidth=ne/3;var Qe=t.globalCompositeOperation;t.globalCompositeOperation="destination-out",h?t.stroke(be):t.stroke(),t.globalCompositeOperation=Qe}t.setLineDash&&t.setLineDash([])}},ye=function(){n&&s.drawNodeOverlay(t,e,f,o,l)},he=function(){n&&s.drawNodeUnderlay(t,e,f,o,l)},me=function(){s.drawElementText(t,e,null,a)},Ce=e.pstyle("ghost").value==="yes";if(Ce){var Se=e.pstyle("ghost-offset-x").pfValue,j=e.pstyle("ghost-offset-y").pfValue,T=e.pstyle("ghost-opacity").value,q=T*c;t.translate(Se,j),N(),de(),te(T*P),xe(),Ie(q,!0),K(T*F),ue(),Be(D!==0||A!==0),Ie(q,!1),se(q),t.translate(-Se,-j)}h&&t.translate(-f.x,-f.y),he(),h&&t.translate(f.x,f.y),N(),de(),te(),xe(),Ie(c,!0),K(),ue(),Be(D!==0||A!==0),Ie(c,!1),se(),h&&t.translate(-f.x,-f.y),me(),ye(),r&&t.translate(p.x1,p.y1)}};var ff=function(e){if(!["overlay","underlay"].includes(e))throw new Error("Invalid state");return function(r,a,n,i,s){var o=this;if(a.visible()){var l=a.pstyle("".concat(e,"-padding")).pfValue,u=a.pstyle("".concat(e,"-opacity")).value,v=a.pstyle("".concat(e,"-color")).value,f=a.pstyle("".concat(e,"-shape")).value,c=a.pstyle("".concat(e,"-corner-radius")).value;if(u>0){if(n=n||a.position(),i==null||s==null){var h=a.padding();i=a.width()+2*h,s=a.height()+2*h}o.colorFillStyle(r,v[0],v[1],v[2],u),o.nodeShapes[f].draw(r,n.x,n.y,i+l*2,s+l*2,c),r.fill()}}}};Qr.drawNodeOverlay=ff("overlay");Qr.drawNodeUnderlay=ff("underlay");Qr.hasPie=function(t){return t=t[0],t._private.hasPie};Qr.drawPie=function(t,e,r,a){e=e[0],a=a||e.position();var n=e.cy().style(),i=e.pstyle("pie-size"),s=a.x,o=a.y,l=e.width(),u=e.height(),v=Math.min(l,u)/2,f=0,c=this.usePaths();c&&(s=0,o=0),i.units==="%"?v=v*i.pfValue:i.pfValue!==void 0&&(v=i.pfValue/2);for(var h=1;h<=n.pieBackgroundN;h++){var d=e.pstyle("pie-"+h+"-background-size").value,y=e.pstyle("pie-"+h+"-background-color").value,g=e.pstyle("pie-"+h+"-background-opacity").value*r,p=d/100;p+f>1&&(p=1-f);var m=1.5*Math.PI+2*Math.PI*f,b=2*Math.PI*p,w=m+b;d===0||f>=1||f+p>1||(t.beginPath(),t.moveTo(s,o),t.arc(s,o,v,m,w),t.closePath(),this.colorFillStyle(t,y[0],y[1],y[2],g),t.fill(),f+=p)}};var dt={},yy=100;dt.getPixelRatio=function(){var t=this.data.contexts[0];if(this.forcedPixelRatio!=null)return this.forcedPixelRatio;var e=this.cy.window(),r=t.backingStorePixelRatio||t.webkitBackingStorePixelRatio||t.mozBackingStorePixelRatio||t.msBackingStorePixelRatio||t.oBackingStorePixelRatio||t.backingStorePixelRatio||1;return(e.devicePixelRatio||1)/r};dt.paintCache=function(t){for(var e=this.paintCaches=this.paintCaches||[],r=!0,a,n=0;n<e.length;n++)if(a=e[n],a.context===t){r=!1;break}return r&&(a={context:t},e.push(a)),a};dt.createGradientStyleFor=function(t,e,r,a,n){var i,s=this.usePaths(),o=r.pstyle(e+"-gradient-stop-colors").value,l=r.pstyle(e+"-gradient-stop-positions").pfValue;if(a==="radial-gradient")if(r.isEdge()){var u=r.sourceEndpoint(),v=r.targetEndpoint(),f=r.midpoint(),c=yr(u,f),h=yr(v,f);i=t.createRadialGradient(f.x,f.y,0,f.x,f.y,Math.max(c,h))}else{var d=s?{x:0,y:0}:r.position(),y=r.paddedWidth(),g=r.paddedHeight();i=t.createRadialGradient(d.x,d.y,0,d.x,d.y,Math.max(y,g))}else if(r.isEdge()){var p=r.sourceEndpoint(),m=r.targetEndpoint();i=t.createLinearGradient(p.x,p.y,m.x,m.y)}else{var b=s?{x:0,y:0}:r.position(),w=r.paddedWidth(),E=r.paddedHeight(),C=w/2,x=E/2,S=r.pstyle("background-gradient-direction").value;switch(S){case"to-bottom":i=t.createLinearGradient(b.x,b.y-x,b.x,b.y+x);break;case"to-top":i=t.createLinearGradient(b.x,b.y+x,b.x,b.y-x);break;case"to-left":i=t.createLinearGradient(b.x+C,b.y,b.x-C,b.y);break;case"to-right":i=t.createLinearGradient(b.x-C,b.y,b.x+C,b.y);break;case"to-bottom-right":case"to-right-bottom":i=t.createLinearGradient(b.x-C,b.y-x,b.x+C,b.y+x);break;case"to-top-right":case"to-right-top":i=t.createLinearGradient(b.x-C,b.y+x,b.x+C,b.y-x);break;case"to-bottom-left":case"to-left-bottom":i=t.createLinearGradient(b.x+C,b.y-x,b.x-C,b.y+x);break;case"to-top-left":case"to-left-top":i=t.createLinearGradient(b.x+C,b.y+x,b.x-C,b.y-x);break}}if(!i)return null;for(var k=l.length===o.length,B=o.length,D=0;D<B;D++)i.addColorStop(k?l[D]:D/(B-1),"rgba("+o[D][0]+","+o[D][1]+","+o[D][2]+","+n+")");return i};dt.gradientFillStyle=function(t,e,r,a){var n=this.createGradientStyleFor(t,"background",e,r,a);if(!n)return null;t.fillStyle=n};dt.colorFillStyle=function(t,e,r,a,n){t.fillStyle="rgba("+e+","+r+","+a+","+n+")"};dt.eleFillStyle=function(t,e,r){var a=e.pstyle("background-fill").value;if(a==="linear-gradient"||a==="radial-gradient")this.gradientFillStyle(t,e,a,r);else{var n=e.pstyle("background-color").value;this.colorFillStyle(t,n[0],n[1],n[2],r)}};dt.gradientStrokeStyle=function(t,e,r,a){var n=this.createGradientStyleFor(t,"line",e,r,a);if(!n)return null;t.strokeStyle=n};dt.colorStrokeStyle=function(t,e,r,a,n){t.strokeStyle="rgba("+e+","+r+","+a+","+n+")"};dt.eleStrokeStyle=function(t,e,r){var a=e.pstyle("line-fill").value;if(a==="linear-gradient"||a==="radial-gradient")this.gradientStrokeStyle(t,e,a,r);else{var n=e.pstyle("line-color").value;this.colorStrokeStyle(t,n[0],n[1],n[2],r)}};dt.matchCanvasSize=function(t){var e=this,r=e.data,a=e.findContainerClientCoords(),n=a[2],i=a[3],s=e.getPixelRatio(),o=e.motionBlurPxRatio;(t===e.data.bufferCanvases[e.MOTIONBLUR_BUFFER_NODE]||t===e.data.bufferCanvases[e.MOTIONBLUR_BUFFER_DRAG])&&(s=o);var l=n*s,u=i*s,v;if(!(l===e.canvasWidth&&u===e.canvasHeight)){e.fontCaches=null;var f=r.canvasContainer;f.style.width=n+"px",f.style.height=i+"px";for(var c=0;c<e.CANVAS_LAYERS;c++)v=r.canvases[c],v.width=l,v.height=u,v.style.width=n+"px",v.style.height=i+"px";for(var c=0;c<e.BUFFER_COUNT;c++)v=r.bufferCanvases[c],v.width=l,v.height=u,v.style.width=n+"px",v.style.height=i+"px";e.textureMult=1,s<=1&&(v=r.bufferCanvases[e.TEXTURE_BUFFER],e.textureMult=2,v.width=l*e.textureMult,v.height=u*e.textureMult),e.canvasWidth=l,e.canvasHeight=u,e.pixelRatio=s}};dt.renderTo=function(t,e,r,a){this.render({forcedContext:t,forcedZoom:e,forcedPan:r,drawAllLayers:!0,forcedPxRatio:a})};dt.clearCanvas=function(){var t=this,e=t.data;function r(a){a.clearRect(0,0,t.canvasWidth,t.canvasHeight)}r(e.contexts[t.NODE]),r(e.contexts[t.DRAG])};dt.render=function(t){var e=this;t=t||Jl();var r=e.cy,a=t.forcedContext,n=t.drawAllLayers,i=t.drawOnlyNodeLayer,s=t.forcedZoom,o=t.forcedPan,l=t.forcedPxRatio===void 0?this.getPixelRatio():t.forcedPxRatio,u=e.data,v=u.canvasNeedsRedraw,f=e.textureOnViewport&&!a&&(e.pinching||e.hoverData.dragging||e.swipePanning||e.data.wheelZooming),c=t.motionBlur!==void 0?t.motionBlur:e.motionBlur,h=e.motionBlurPxRatio,d=r.hasCompoundNodes(),y=e.hoverData.draggingEles,g=!!(e.hoverData.selecting||e.touchData.selecting);c=c&&!a&&e.motionBlurEnabled&&!g;var p=c;a||(e.prevPxRatio!==l&&(e.invalidateContainerClientCoordsCache(),e.matchCanvasSize(e.container),e.redrawHint("eles",!0),e.redrawHint("drag",!0)),e.prevPxRatio=l),!a&&e.motionBlurTimeout&&clearTimeout(e.motionBlurTimeout),c&&(e.mbFrames==null&&(e.mbFrames=0),e.mbFrames++,e.mbFrames<3&&(p=!1),e.mbFrames>e.minMbLowQualFrames&&(e.motionBlurPxRatio=e.mbPxRBlurry)),e.clearingMotionBlur&&(e.motionBlurPxRatio=1),e.textureDrawLastFrame&&!f&&(v[e.NODE]=!0,v[e.SELECT_BOX]=!0);var m=r.style(),b=r.zoom(),w=s!==void 0?s:b,E=r.pan(),C={x:E.x,y:E.y},x={zoom:b,pan:{x:E.x,y:E.y}},S=e.prevViewport,k=S===void 0||x.zoom!==S.zoom||x.pan.x!==S.pan.x||x.pan.y!==S.pan.y;!k&&!(y&&!d)&&(e.motionBlurPxRatio=1),o&&(C=o),w*=l,C.x*=l,C.y*=l;var B=e.getCachedZSortedEles();function D(K,N,$,J,re){var le=K.globalCompositeOperation;K.globalCompositeOperation="destination-out",e.colorFillStyle(K,255,255,255,e.motionBlurTransparency),K.fillRect(N,$,J,re),K.globalCompositeOperation=le}function A(K,N){var $,J,re,le;!e.clearingMotionBlur&&(K===u.bufferContexts[e.MOTIONBLUR_BUFFER_NODE]||K===u.bufferContexts[e.MOTIONBLUR_BUFFER_DRAG])?($={x:E.x*h,y:E.y*h},J=b*h,re=e.canvasWidth*h,le=e.canvasHeight*h):($=C,J=w,re=e.canvasWidth,le=e.canvasHeight),K.setTransform(1,0,0,1,0,0),N==="motionBlur"?D(K,0,0,re,le):!a&&(N===void 0||N)&&K.clearRect(0,0,re,le),n||(K.translate($.x,$.y),K.scale(J,J)),o&&K.translate(o.x,o.y),s&&K.scale(s,s)}if(f||(e.textureDrawLastFrame=!1),f){if(e.textureDrawLastFrame=!0,!e.textureCache){e.textureCache={},e.textureCache.bb=r.mutableElements().boundingBox(),e.textureCache.texture=e.data.bufferCanvases[e.TEXTURE_BUFFER];var P=e.data.bufferContexts[e.TEXTURE_BUFFER];P.setTransform(1,0,0,1,0,0),P.clearRect(0,0,e.canvasWidth*e.textureMult,e.canvasHeight*e.textureMult),e.render({forcedContext:P,drawOnlyNodeLayer:!0,forcedPxRatio:l*e.textureMult});var x=e.textureCache.viewport={zoom:r.zoom(),pan:r.pan(),width:e.canvasWidth,height:e.canvasHeight};x.mpan={x:(0-x.pan.x)/x.zoom,y:(0-x.pan.y)/x.zoom}}v[e.DRAG]=!1,v[e.NODE]=!1;var R=u.contexts[e.NODE],L=e.textureCache.texture,x=e.textureCache.viewport;R.setTransform(1,0,0,1,0,0),c?D(R,0,0,x.width,x.height):R.clearRect(0,0,x.width,x.height);var I=m.core("outside-texture-bg-color").value,M=m.core("outside-texture-bg-opacity").value;e.colorFillStyle(R,I[0],I[1],I[2],M),R.fillRect(0,0,x.width,x.height);var b=r.zoom();A(R,!1),R.clearRect(x.mpan.x,x.mpan.y,x.width/x.zoom/l,x.height/x.zoom/l),R.drawImage(L,x.mpan.x,x.mpan.y,x.width/x.zoom/l,x.height/x.zoom/l)}else e.textureOnViewport&&!a&&(e.textureCache=null);var O=r.extent(),_=e.pinching||e.hoverData.dragging||e.swipePanning||e.data.wheelZooming||e.hoverData.draggingEles||e.cy.animated(),H=e.hideEdgesOnViewport&&_,F=[];if(F[e.NODE]=!v[e.NODE]&&c&&!e.clearedForMotionBlur[e.NODE]||e.clearingMotionBlur,F[e.NODE]&&(e.clearedForMotionBlur[e.NODE]=!0),F[e.DRAG]=!v[e.DRAG]&&c&&!e.clearedForMotionBlur[e.DRAG]||e.clearingMotionBlur,F[e.DRAG]&&(e.clearedForMotionBlur[e.DRAG]=!0),v[e.NODE]||n||i||F[e.NODE]){var G=c&&!F[e.NODE]&&h!==1,R=a||(G?e.data.bufferContexts[e.MOTIONBLUR_BUFFER_NODE]:u.contexts[e.NODE]),U=c&&!G?"motionBlur":void 0;A(R,U),H?e.drawCachedNodes(R,B.nondrag,l,O):e.drawLayeredElements(R,B.nondrag,l,O),e.debug&&e.drawDebugPoints(R,B.nondrag),!n&&!c&&(v[e.NODE]=!1)}if(!i&&(v[e.DRAG]||n||F[e.DRAG])){var G=c&&!F[e.DRAG]&&h!==1,R=a||(G?e.data.bufferContexts[e.MOTIONBLUR_BUFFER_DRAG]:u.contexts[e.DRAG]);A(R,c&&!G?"motionBlur":void 0),H?e.drawCachedNodes(R,B.drag,l,O):e.drawCachedElements(R,B.drag,l,O),e.debug&&e.drawDebugPoints(R,B.drag),!n&&!c&&(v[e.DRAG]=!1)}if(this.drawSelectionRectangle(t,A),c&&h!==1){var X=u.contexts[e.NODE],Z=e.data.bufferCanvases[e.MOTIONBLUR_BUFFER_NODE],Q=u.contexts[e.DRAG],ee=e.data.bufferCanvases[e.MOTIONBLUR_BUFFER_DRAG],te=function(N,$,J){N.setTransform(1,0,0,1,0,0),J||!p?N.clearRect(0,0,e.canvasWidth,e.canvasHeight):D(N,0,0,e.canvasWidth,e.canvasHeight);var re=h;N.drawImage($,0,0,e.canvasWidth*re,e.canvasHeight*re,0,0,e.canvasWidth,e.canvasHeight)};(v[e.NODE]||F[e.NODE])&&(te(X,Z,F[e.NODE]),v[e.NODE]=!1),(v[e.DRAG]||F[e.DRAG])&&(te(Q,ee,F[e.DRAG]),v[e.DRAG]=!1)}e.prevViewport=x,e.clearingMotionBlur&&(e.clearingMotionBlur=!1,e.motionBlurCleared=!0,e.motionBlur=!0),c&&(e.motionBlurTimeout=setTimeout(function(){e.motionBlurTimeout=null,e.clearedForMotionBlur[e.NODE]=!1,e.clearedForMotionBlur[e.DRAG]=!1,e.motionBlur=!1,e.clearingMotionBlur=!f,e.mbFrames=0,v[e.NODE]=!0,v[e.DRAG]=!0,e.redraw()},yy)),a||r.emit("render")};var na;dt.drawSelectionRectangle=function(t,e){var r=this,a=r.cy,n=r.data,i=a.style(),s=t.drawOnlyNodeLayer,o=t.drawAllLayers,l=n.canvasNeedsRedraw,u=t.forcedContext;if(r.showFps||!s&&l[r.SELECT_BOX]&&!o){var v=u||n.contexts[r.SELECT_BOX];if(e(v),r.selection[4]==1&&(r.hoverData.selecting||r.touchData.selecting)){var f=r.cy.zoom(),c=i.core("selection-box-border-width").value/f;v.lineWidth=c,v.fillStyle="rgba("+i.core("selection-box-color").value[0]+","+i.core("selection-box-color").value[1]+","+i.core("selection-box-color").value[2]+","+i.core("selection-box-opacity").value+")",v.fillRect(r.selection[0],r.selection[1],r.selection[2]-r.selection[0],r.selection[3]-r.selection[1]),c>0&&(v.strokeStyle="rgba("+i.core("selection-box-border-color").value[0]+","+i.core("selection-box-border-color").value[1]+","+i.core("selection-box-border-color").value[2]+","+i.core("selection-box-opacity").value+")",v.strokeRect(r.selection[0],r.selection[1],r.selection[2]-r.selection[0],r.selection[3]-r.selection[1]))}if(n.bgActivePosistion&&!r.hoverData.selecting){var f=r.cy.zoom(),h=n.bgActivePosistion;v.fillStyle="rgba("+i.core("active-bg-color").value[0]+","+i.core("active-bg-color").value[1]+","+i.core("active-bg-color").value[2]+","+i.core("active-bg-opacity").value+")",v.beginPath(),v.arc(h.x,h.y,i.core("active-bg-size").pfValue/f,0,2*Math.PI),v.fill()}var d=r.lastRedrawTime;if(r.showFps&&d){d=Math.round(d);var y=Math.round(1e3/d),g="1 frame = "+d+" ms = "+y+" fps";if(v.setTransform(1,0,0,1,0,0),v.fillStyle="rgba(255, 0, 0, 0.75)",v.strokeStyle="rgba(255, 0, 0, 0.75)",v.font="30px Arial",!na){var p=v.measureText(g);na=p.actualBoundingBoxAscent}v.fillText(g,0,na);var m=60;v.strokeRect(0,na+10,250,20),v.fillRect(0,na+10,250*Math.min(y/m,1),20)}o||(l[r.SELECT_BOX]=!1)}};function Cl(t,e,r){var a=t.createShader(e);if(t.shaderSource(a,r),t.compileShader(a),!t.getShaderParameter(a,t.COMPILE_STATUS))throw new Error(t.getShaderInfoLog(a));return a}function my(t,e,r){var a=Cl(t,t.VERTEX_SHADER,e),n=Cl(t,t.FRAGMENT_SHADER,r),i=t.createProgram();if(t.attachShader(i,a),t.attachShader(i,n),t.linkProgram(i),!t.getProgramParameter(i,t.LINK_STATUS))throw new Error("Could not initialize shaders");return i}function by(t,e,r){r===void 0&&(r=e);var a=t.makeOffscreenCanvas(e,r),n=a.context=a.getContext("2d");return a.clear=function(){return n.clearRect(0,0,a.width,a.height)},a.clear(),a}function eo(t){var e=t.pixelRatio,r=t.cy.zoom(),a=t.cy.pan();return{zoom:r*e,pan:{x:a.x*e,y:a.y*e}}}function wy(t,e,r,a,n){var i=a*r+e.x,s=n*r+e.y;return s=Math.round(t.canvasHeight-s),[i,s]}function ia(t,e,r){var a=t[0]/255,n=t[1]/255,i=t[2]/255,s=e,o=r||new Array(4);return o[0]=a*s,o[1]=n*s,o[2]=i*s,o[3]=s,o}function Br(t,e){var r=e||new Array(4);return r[0]=(t>>0&255)/255,r[1]=(t>>8&255)/255,r[2]=(t>>16&255)/255,r[3]=(t>>24&255)/255,r}function xy(t){return t[0]+(t[1]<<8)+(t[2]<<16)+(t[3]<<24)}function Ey(t,e){var r=t.createTexture();return r.buffer=function(a){t.bindTexture(t.TEXTURE_2D,r),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_WRAP_S,t.CLAMP_TO_EDGE),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_WRAP_T,t.CLAMP_TO_EDGE),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_MAG_FILTER,t.LINEAR),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_MIN_FILTER,t.LINEAR_MIPMAP_NEAREST),t.pixelStorei(t.UNPACK_PREMULTIPLY_ALPHA_WEBGL,!0),t.texImage2D(t.TEXTURE_2D,0,t.RGBA,t.RGBA,t.UNSIGNED_BYTE,a),t.generateMipmap(t.TEXTURE_2D),t.bindTexture(t.TEXTURE_2D,null)},r.deleteTexture=function(){t.deleteTexture(r)},r}function cf(t,e){switch(e){case"float":return[1,t.FLOAT,4];case"vec2":return[2,t.FLOAT,4];case"vec3":return[3,t.FLOAT,4];case"vec4":return[4,t.FLOAT,4];case"int":return[1,t.INT,4];case"ivec2":return[2,t.INT,4]}}function df(t,e,r){switch(e){case t.FLOAT:return new Float32Array(r);case t.INT:return new Int32Array(r)}}function Cy(t,e,r,a,n,i){switch(e){case t.FLOAT:return new Float32Array(r.buffer,i*a,n);case t.INT:return new Int32Array(r.buffer,i*a,n)}}function Ty(t,e,r,a){var n=cf(t,e),i=je(n,2),s=i[0],o=i[1],l=df(t,o,a),u=t.createBuffer();return t.bindBuffer(t.ARRAY_BUFFER,u),t.bufferData(t.ARRAY_BUFFER,l,t.STATIC_DRAW),o===t.FLOAT?t.vertexAttribPointer(r,s,o,!1,0,0):o===t.INT&&t.vertexAttribIPointer(r,s,o,0,0),t.enableVertexAttribArray(r),t.bindBuffer(t.ARRAY_BUFFER,null),u}function Qt(t,e,r,a){var n=cf(t,r),i=je(n,3),s=i[0],o=i[1],l=i[2],u=df(t,o,e*s),v=s*l,f=t.createBuffer();t.bindBuffer(t.ARRAY_BUFFER,f),t.bufferData(t.ARRAY_BUFFER,e*v,t.DYNAMIC_DRAW),t.enableVertexAttribArray(a),o===t.FLOAT?t.vertexAttribPointer(a,s,o,!1,v,0):o===t.INT&&t.vertexAttribIPointer(a,s,o,v,0),t.vertexAttribDivisor(a,1),t.bindBuffer(t.ARRAY_BUFFER,null);for(var c=new Array(e),h=0;h<e;h++)c[h]=Cy(t,o,u,v,s,h);return f.dataArray=u,f.stride=v,f.size=s,f.getView=function(d){return c[d]},f.setPoint=function(d,y,g){var p=c[d];p[0]=y,p[1]=g},f.bufferSubData=function(d){t.bindBuffer(t.ARRAY_BUFFER,f),d?t.bufferSubData(t.ARRAY_BUFFER,0,u,0,d*s):t.bufferSubData(t.ARRAY_BUFFER,0,u)},f}function Sy(t,e,r){for(var a=9,n=new Float32Array(e*a),i=new Array(e),s=0;s<e;s++){var o=s*a*4;i[s]=new Float32Array(n.buffer,o,a)}var l=t.createBuffer();t.bindBuffer(t.ARRAY_BUFFER,l),t.bufferData(t.ARRAY_BUFFER,n.byteLength,t.DYNAMIC_DRAW);for(var u=0;u<3;u++){var v=r+u;t.enableVertexAttribArray(v),t.vertexAttribPointer(v,3,t.FLOAT,!1,3*12,u*12),t.vertexAttribDivisor(v,1)}return t.bindBuffer(t.ARRAY_BUFFER,null),l.getMatrixView=function(f){return i[f]},l.setData=function(f,c){i[c].set(f,0)},l.bufferSubData=function(){t.bindBuffer(t.ARRAY_BUFFER,l),t.bufferSubData(t.ARRAY_BUFFER,0,n)},l}function Dy(t){var e=t.createFramebuffer();t.bindFramebuffer(t.FRAMEBUFFER,e);var r=t.createTexture();return t.bindTexture(t.TEXTURE_2D,r),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_MIN_FILTER,t.LINEAR),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_WRAP_S,t.CLAMP_TO_EDGE),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_WRAP_T,t.CLAMP_TO_EDGE),t.framebufferTexture2D(t.FRAMEBUFFER,t.COLOR_ATTACHMENT0,t.TEXTURE_2D,r,0),t.bindFramebuffer(t.FRAMEBUFFER,null),e.setFramebufferAttachmentSizes=function(a,n){t.bindTexture(t.TEXTURE_2D,r),t.texImage2D(t.TEXTURE_2D,0,t.RGBA,a,n,0,t.RGBA,t.UNSIGNED_BYTE,null)},e}var Tl=typeof Float32Array<"u"?Float32Array:Array;Math.hypot||(Math.hypot=function(){for(var t=0,e=arguments.length;e--;)t+=arguments[e]*arguments[e];return Math.sqrt(t)});function on(){var t=new Tl(9);return Tl!=Float32Array&&(t[1]=0,t[2]=0,t[3]=0,t[5]=0,t[6]=0,t[7]=0),t[0]=1,t[4]=1,t[8]=1,t}function hf(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=0,t[4]=1,t[5]=0,t[6]=0,t[7]=0,t[8]=1,t}function ky(t,e,r){var a=e[0],n=e[1],i=e[2],s=e[3],o=e[4],l=e[5],u=e[6],v=e[7],f=e[8],c=r[0],h=r[1],d=r[2],y=r[3],g=r[4],p=r[5],m=r[6],b=r[7],w=r[8];return t[0]=c*a+h*s+d*u,t[1]=c*n+h*o+d*v,t[2]=c*i+h*l+d*f,t[3]=y*a+g*s+p*u,t[4]=y*n+g*o+p*v,t[5]=y*i+g*l+p*f,t[6]=m*a+b*s+w*u,t[7]=m*n+b*o+w*v,t[8]=m*i+b*l+w*f,t}function bn(t,e,r){var a=e[0],n=e[1],i=e[2],s=e[3],o=e[4],l=e[5],u=e[6],v=e[7],f=e[8],c=r[0],h=r[1];return t[0]=a,t[1]=n,t[2]=i,t[3]=s,t[4]=o,t[5]=l,t[6]=c*a+h*s+u,t[7]=c*n+h*o+v,t[8]=c*i+h*l+f,t}function gf(t,e,r){var a=e[0],n=e[1],i=e[2],s=e[3],o=e[4],l=e[5],u=e[6],v=e[7],f=e[8],c=Math.sin(r),h=Math.cos(r);return t[0]=h*a+c*s,t[1]=h*n+c*o,t[2]=h*i+c*l,t[3]=h*s-c*a,t[4]=h*o-c*n,t[5]=h*l-c*i,t[6]=u,t[7]=v,t[8]=f,t}function to(t,e,r){var a=r[0],n=r[1];return t[0]=a*e[0],t[1]=a*e[1],t[2]=a*e[2],t[3]=n*e[3],t[4]=n*e[4],t[5]=n*e[5],t[6]=e[6],t[7]=e[7],t[8]=e[8],t}function Py(t,e,r){return t[0]=2/e,t[1]=0,t[2]=0,t[3]=0,t[4]=-2/r,t[5]=0,t[6]=-1,t[7]=1,t[8]=1,t}var ca={SCREEN:{name:"screen",screen:!0},PICKING:{name:"picking",picking:!0}},Sl=Ue({texRows:24}),Ar=Ue({collection:"default",getKey:null,drawElement:null,getBoundingBox:null,getRotation:null,getRotationPoint:null,getRotationOffset:null,isVisible:function(){return!0},getPadding:0}),By=function(){function t(e,r,a,n){or(this,t),this.debugID=Math.floor(Math.random()*1e4),this.r=e,this.texSize=r,this.texRows=a,this.texHeight=Math.floor(r/a),this.enableWrapping=!0,this.locked=!1,this.texture=null,this.needsBuffer=!0,this.freePointer={x:0,row:0},this.keyToLocation=new Map,this.canvas=n(e,r,r),this.scratch=n(e,r,this.texHeight,"scratch")}return ur(t,[{key:"lock",value:function(){this.locked=!0}},{key:"getKeys",value:function(){return new Set(this.keyToLocation.keys())}},{key:"getScale",value:function(r){var a=r.w,n=r.h,i=this.texHeight,s=this.texSize,o=i/n,l=a*o,u=n*o;return l>s&&(o=s/a,l=a*o,u=n*o),{scale:o,texW:l,texH:u}}},{key:"draw",value:function(r,a,n){var i=this;if(this.locked)throw new Error("can't draw, atlas is locked");var s=this.texSize,o=this.texRows,l=this.texHeight,u=this.getScale(a),v=u.scale,f=u.texW,c=u.texH,h=[null,null],d=function(b,w){if(n&&w){var E=w.context,C=b.x,x=b.row,S=C,k=l*x;E.save(),E.translate(S,k),E.scale(v,v),n(E,a),E.restore()}},y=function(){d(i.freePointer,i.canvas),h[0]={x:i.freePointer.x,y:i.freePointer.row*l,w:f,h:c},h[1]={x:i.freePointer.x+f,y:i.freePointer.row*l,w:0,h:c},i.freePointer.x+=f,i.freePointer.x==s&&(i.freePointer.x=0,i.freePointer.row++)},g=function(){var b=i.scratch,w=i.canvas;b.clear(),d({x:0,row:0},b);var E=s-i.freePointer.x,C=f-E,x=l;{var S=i.freePointer.x,k=i.freePointer.row*l,B=E;w.context.drawImage(b,0,0,B,x,S,k,B,x),h[0]={x:S,y:k,w:B,h:c}}{var D=E,A=(i.freePointer.row+1)*l,P=C;w&&w.context.drawImage(b,D,0,P,x,0,A,P,x),h[1]={x:0,y:A,w:P,h:c}}i.freePointer.x=C,i.freePointer.row++},p=function(){i.freePointer.x=0,i.freePointer.row++};if(this.freePointer.x+f<=s)y();else{if(this.freePointer.row>=o-1)return!1;this.freePointer.x===s?(p(),y()):this.enableWrapping?g():(p(),y())}return this.keyToLocation.set(r,h),this.needsBuffer=!0,h}},{key:"getOffsets",value:function(r){return this.keyToLocation.get(r)}},{key:"isEmpty",value:function(){return this.freePointer.x===0&&this.freePointer.row===0}},{key:"canFit",value:function(r){if(this.locked)return!1;var a=this.texSize,n=this.texRows,i=this.getScale(r),s=i.texW;return this.freePointer.x+s>a?this.freePointer.row<n-1:!0}},{key:"bufferIfNeeded",value:function(r){this.texture||(this.texture=Ey(r,this.debugID)),this.needsBuffer&&(this.texture.buffer(this.canvas),this.needsBuffer=!1,this.locked&&(this.canvas=null,this.scratch=null))}},{key:"dispose",value:function(){this.texture&&(this.texture.deleteTexture(),this.texture=null),this.canvas=null,this.scratch=null,this.locked=!0}}])}(),Ay=function(){function t(e,r,a,n){or(this,t),this.r=e,this.texSize=r,this.texRows=a,this.createTextureCanvas=n,this.atlases=[],this.styleKeyToAtlas=new Map,this.markedKeys=new Set}return ur(t,[{key:"getKeys",value:function(){return new Set(this.styleKeyToAtlas.keys())}},{key:"_createAtlas",value:function(){var r=this.r,a=this.texSize,n=this.texRows,i=this.createTextureCanvas;return new By(r,a,n,i)}},{key:"_getScratchCanvas",value:function(){if(!this.scratch){var r=this.r,a=this.texSize,n=this.texRows,i=this.createTextureCanvas,s=Math.floor(a/n);this.scratch=i(r,a,s,"scratch")}return this.scratch}},{key:"draw",value:function(r,a,n){var i=this.styleKeyToAtlas.get(r);return i||(i=this.atlases[this.atlases.length-1],(!i||!i.canFit(a))&&(i&&i.lock(),i=this._createAtlas(),this.atlases.push(i)),i.draw(r,a,n),this.styleKeyToAtlas.set(r,i)),i}},{key:"getAtlas",value:function(r){return this.styleKeyToAtlas.get(r)}},{key:"hasAtlas",value:function(r){return this.styleKeyToAtlas.has(r)}},{key:"markKeyForGC",value:function(r){this.markedKeys.add(r)}},{key:"gc",value:function(){var r=this,a=this.markedKeys;if(a.size===0){console.log("nothing to garbage collect");return}var n=[],i=new Map,s=null,o=Pt(this.atlases),l;try{var u=function(){var f=l.value,c=f.getKeys(),h=Ry(a,c);if(h.size===0)return n.push(f),c.forEach(function(E){return i.set(E,f)}),1;s||(s=r._createAtlas(),n.push(s));var d=Pt(c),y;try{for(d.s();!(y=d.n()).done;){var g=y.value;if(!h.has(g)){var p=f.getOffsets(g),m=je(p,2),b=m[0],w=m[1];s.canFit({w:b.w+w.w,h:b.h})||(s.lock(),s=r._createAtlas(),n.push(s)),r._copyTextureToNewAtlas(g,f,s),i.set(g,s)}}}catch(E){d.e(E)}finally{d.f()}f.dispose()};for(o.s();!(l=o.n()).done;)u()}catch(v){o.e(v)}finally{o.f()}this.atlases=n,this.styleKeyToAtlas=i,this.markedKeys=new Set}},{key:"_copyTextureToNewAtlas",value:function(r,a,n){var i=a.getOffsets(r),s=je(i,2),o=s[0],l=s[1];if(l.w===0)n.draw(r,o,function(c){c.drawImage(a.canvas,o.x,o.y,o.w,o.h,0,0,o.w,o.h)});else{var u=this._getScratchCanvas();u.clear(),u.context.drawImage(a.canvas,o.x,o.y,o.w,o.h,0,0,o.w,o.h),u.context.drawImage(a.canvas,l.x,l.y,l.w,l.h,o.w,0,l.w,l.h);var v=o.w+l.w,f=o.h;n.draw(r,{w:v,h:f},function(c){c.drawImage(u,0,0,v,f,0,0,v,f)})}}},{key:"getCounts",value:function(){return{keyCount:this.styleKeyToAtlas.size,atlasCount:new Set(this.styleKeyToAtlas.values()).size}}}])}();function Ry(t,e){return t.intersection?t.intersection(e):new Set(Il(t).filter(function(r){return e.has(r)}))}var My=function(){function t(e,r){or(this,t),this.r=e,this.globalOptions=r,this.atlasSize=r.webglTexSize,this.maxAtlasesPerBatch=r.webglTexPerBatch,this.renderTypes=new Map,this.collections=new Map,this.typeAndIdToKey=new Map,this.batchAtlases=[]}return ur(t,[{key:"getAtlasSize",value:function(){return this.atlasSize}},{key:"getMaxAtlasesPerBatch",value:function(){return this.maxAtlasesPerBatch}},{key:"addAtlasCollection",value:function(r,a){var n=this.globalOptions,i=n.webglTexSize,s=n.createTextureCanvas,o=a.texRows,l=this._cacheScratchCanvas(s),u=new Ay(this.r,i,o,l);this.collections.set(r,u)}},{key:"addRenderType",value:function(r,a){var n=a.collection;if(!this.collections.has(n))throw new Error("invalid atlas collection name '".concat(n,"'"));var i=this.collections.get(n),s=ge({type:r,atlasCollection:i},a);this.renderTypes.set(r,s)}},{key:"getRenderTypeOpts",value:function(r){return this.renderTypes.get(r)}},{key:"getAtlasCollection",value:function(r){return this.collections.get(r)}},{key:"_cacheScratchCanvas",value:function(r){var a=-1,n=-1,i=null;return function(s,o,l,u){return u?((!i||o!=a||l!=n)&&(a=o,n=l,i=r(s,o,l)),i):r(s,o,l)}}},{key:"_key",value:function(r,a){return"".concat(r,"-").concat(a)}},{key:"invalidate",value:function(r){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=a.forceRedraw,i=n===void 0?!1:n,s=a.filterEle,o=s===void 0?function(){return!0}:s,l=a.filterType,u=l===void 0?function(){return!0}:l,v=!1,f=!1,c=Pt(r),h;try{for(c.s();!(h=c.n()).done;){var d=h.value;if(o(d)){var y=Pt(this.renderTypes.values()),g;try{for(y.s();!(g=y.n()).done;){var p=g.value,m=p.type;if(u(m)){var b=p.getKey(d),w=this.collections.get(p.collection);if(i)w.markKeyForGC(b),f=!0;else{var E=p.getID?p.getID(d):d.id(),C=this._key(m,E),x=this.typeAndIdToKey.get(C);x!==void 0&&x!==b&&(this.typeAndIdToKey.delete(C),w.markKeyForGC(x),v=!0)}}}}catch(S){y.e(S)}finally{y.f()}}}}catch(S){c.e(S)}finally{c.f()}return f&&(this.gc(),v=!1),v}},{key:"gc",value:function(){var r=Pt(this.collections.values()),a;try{for(r.s();!(a=r.n()).done;){var n=a.value;n.gc()}}catch(i){r.e(i)}finally{r.f()}}},{key:"getOrCreateAtlas",value:function(r,a,n){var i=this.renderTypes.get(a),s=i.getKey(r);n||(n=i.getBoundingBox(r));var o=this.collections.get(i.collection),l=!1,u=o.draw(s,n,function(c){i.drawElement(c,r,n,!0,!0),l=!0});if(l){var v=i.getID?i.getID(r):r.id(),f=this._key(a,v);this.typeAndIdToKey.set(f,s)}return u}},{key:"startBatch",value:function(){this.batchAtlases=[]}},{key:"getAtlasCount",value:function(){return this.batchAtlases.length}},{key:"getAtlases",value:function(){return this.batchAtlases}},{key:"canAddToCurrentBatch",value:function(r,a){if(this.batchAtlases.length===this.maxAtlasesPerBatch){var n=this.renderTypes.get(a),i=n.getKey(r),s=this.collections.get(n.collection),o=s.getAtlas(i);return!!o&&this.batchAtlases.includes(o)}return!0}},{key:"getAtlasIndexForBatch",value:function(r){var a=this.batchAtlases.indexOf(r);if(a<0){if(this.batchAtlases.length===this.maxAtlasesPerBatch)return;this.batchAtlases.push(r),a=this.batchAtlases.length-1}return a}},{key:"getIndexArray",value:function(){return Array.from({length:this.maxAtlasesPerBatch},function(r,a){return a})}},{key:"getAtlasInfo",value:function(r,a){var n=this.renderTypes.get(a),i=n.getBoundingBox(r),s=this.getOrCreateAtlas(r,a,i),o=this.getAtlasIndexForBatch(s);if(o!==void 0){var l=n.getKey(r),u=s.getOffsets(l),v=je(u,2),f=v[0],c=v[1];return{index:o,tex1:f,tex2:c,bb:i}}}},{key:"setTransformMatrix",value:function(r,a,n,i){var s=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!0,o=this.getRenderTypeOpts(n),l=o.getPadding?o.getPadding(r):0;if(i){var u=i.bb,v=i.tex1,f=i.tex2,c=v.w/(v.w+f.w);s||(c=1-c);var h=this.getAdjustedBB(u,l,s,c);this._applyTransformMatrix(a,h,o,r)}else{var d=o.getBoundingBox(r),y=this.getAdjustedBB(d,l,!0,1);this._applyTransformMatrix(a,y,o,r)}}},{key:"_applyTransformMatrix",value:function(r,a,n,i){var s,o;hf(r);var l=n.getRotation?n.getRotation(i):0;if(l!==0){var u=n.getRotationPoint(i),v=u.x,f=u.y;bn(r,r,[v,f]),gf(r,r,l);var c=n.getRotationOffset(i);s=c.x+a.xOffset,o=c.y}else s=a.x1,o=a.y1;bn(r,r,[s,o]),to(r,r,[a.w,a.h])}},{key:"getAdjustedBB",value:function(r,a,n,i){var s=r.x1,o=r.y1,l=r.w,u=r.h;a&&(s-=a,o-=a,l+=2*a,u+=2*a);var v=0,f=l*i;return n&&i<1?l=f:!n&&i<1&&(v=l-f,s+=v,l=f),{x1:s,y1:o,w:l,h:u,xOffset:v}}},{key:"getDebugInfo",value:function(){var r=[],a=Pt(this.collections),n;try{for(a.s();!(n=a.n()).done;){var i=je(n.value,2),s=i[0],o=i[1],l=o.getCounts(),u=l.keyCount,v=l.atlasCount;r.push({type:s,keyCount:u,atlasCount:v})}}catch(f){a.e(f)}finally{a.f()}return r}}])}(),Xa=0,Dl=1,kl=2,vs=3,Pl=4,Ly=function(){function t(e,r,a){or(this,t),this.r=e,this.gl=r,this.maxInstances=a.webglBatchSize,this.atlasSize=a.webglTexSize,this.bgColor=a.bgColor,this.debug=a.webglDebug,this.batchDebugInfo=[],a.enableWrapping=!0,a.createTextureCanvas=by,this.atlasManager=new My(e,a),this.program=this.createShaderProgram(ca.SCREEN),this.pickingProgram=this.createShaderProgram(ca.PICKING),this.vao=this.createVAO()}return ur(t,[{key:"addAtlasCollection",value:function(r,a){this.atlasManager.addAtlasCollection(r,a)}},{key:"addAtlasRenderType",value:function(r,a){this.atlasManager.addRenderType(r,a)}},{key:"invalidate",value:function(r){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=a.type,i=this.atlasManager;return n?i.invalidate(r,{filterType:function(o){return o===n},forceRedraw:!0}):i.invalidate(r)}},{key:"gc",value:function(){this.atlasManager.gc()}},{key:"createShaderProgram",value:function(r){var a=this.gl,n=`#version 300 es
      precision highp float;

      uniform mat3 uPanZoomMatrix;
      uniform int  uAtlasSize;
      
      // instanced
      in vec2 aPosition; 

      in mat3 aTransform;

      // what are we rendering?
      in int aVertType;

      // for picking
      in vec4 aIndex;
      
      // For textures
      in int aAtlasId; // which shader unit/atlas to use
      in vec4 aTex; // x/y/w/h of texture in atlas

      // for edges
      in vec4 aPointAPointB;
      in vec4 aPointCPointD;
      in float aLineWidth;
      in vec4 aColor;

      out vec2 vTexCoord;
      out vec4 vColor;
      flat out int vAtlasId;
      flat out vec4 vIndex;
      flat out int vVertType;

      void main(void) {
        int vid = gl_VertexID;
        vec2 position = aPosition;

        if(aVertType == `.concat(Xa,`) {
          float texX = aTex.x;
          float texY = aTex.y;
          float texW = aTex.z;
          float texH = aTex.w;

          int vid = gl_VertexID;

          if(vid == 1 || vid == 2 || vid == 4) {
            texX += texW;
          }
          if(vid == 2 || vid == 4 || vid == 5) {
            texY += texH;
          }

          float d = float(uAtlasSize);
          vTexCoord = vec2(texX / d, texY / d); // tex coords must be between 0 and 1

          gl_Position = vec4(uPanZoomMatrix * aTransform * vec3(position, 1.0), 1.0);
        }
        else if(aVertType == `).concat(Pl,`) {
          gl_Position = vec4(uPanZoomMatrix * aTransform * vec3(position, 1.0), 1.0);
          vColor = aColor;
        }
        else if(aVertType == `).concat(Dl,`) {
          vec2 source = aPointAPointB.xy;
          vec2 target = aPointAPointB.zw;

          // adjust the geometry so that the line is centered on the edge
          position.y = position.y - 0.5;

          vec2 xBasis = target - source;
          vec2 yBasis = normalize(vec2(-xBasis.y, xBasis.x));
          vec2 point = source + xBasis * position.x + yBasis * aLineWidth * position.y;

          gl_Position = vec4(uPanZoomMatrix * vec3(point, 1.0), 1.0);
          vColor = aColor;
        } 
        else if(aVertType == `).concat(kl,`) {
          vec2 pointA = aPointAPointB.xy;
          vec2 pointB = aPointAPointB.zw;
          vec2 pointC = aPointCPointD.xy;
          vec2 pointD = aPointCPointD.zw;

          // adjust the geometry so that the line is centered on the edge
          position.y = position.y - 0.5;

          vec2 p0 = pointA;
          vec2 p1 = pointB;
          vec2 p2 = pointC;
          vec2 pos = position;
          if(position.x == 1.0) {
            p0 = pointD;
            p1 = pointC;
            p2 = pointB;
            pos = vec2(0.0, -position.y);
          }

          vec2 p01 = p1 - p0;
          vec2 p12 = p2 - p1;
          vec2 p21 = p1 - p2;

          // Find the normal vector.
          vec2 tangent = normalize(normalize(p12) + normalize(p01));
          vec2 normal = vec2(-tangent.y, tangent.x);

          // Find the vector perpendicular to p0 -> p1.
          vec2 p01Norm = normalize(vec2(-p01.y, p01.x));

          // Determine the bend direction.
          float sigma = sign(dot(p01 + p21, normal));
          float width = aLineWidth;

          if(sign(pos.y) == -sigma) {
            // This is an intersecting vertex. Adjust the position so that there's no overlap.
            vec2 point = 0.5 * width * normal * -sigma / dot(normal, p01Norm);
            gl_Position = vec4(uPanZoomMatrix * vec3(p1 + point, 1.0), 1.0);
          } else {
            // This is a non-intersecting vertex. Treat it like a mitre join.
            vec2 point = 0.5 * width * normal * sigma * dot(normal, p01Norm);
            gl_Position = vec4(uPanZoomMatrix * vec3(p1 + point, 1.0), 1.0);
          }

          vColor = aColor;
        } 
        else if(aVertType == `).concat(vs,` && vid < 3) {
          // massage the first triangle into an edge arrow
          if(vid == 0)
            position = vec2(-0.15, -0.3);
          if(vid == 1)
            position = vec2( 0.0,   0.0);
          if(vid == 2)
            position = vec2( 0.15, -0.3);

          gl_Position = vec4(uPanZoomMatrix * aTransform * vec3(position, 1.0), 1.0);
          vColor = aColor;
        }
        else {
          gl_Position = vec4(2.0, 0.0, 0.0, 1.0); // discard vertex by putting it outside webgl clip space
        }

        vAtlasId = aAtlasId;
        vIndex = aIndex;
        vVertType = aVertType;
      }
    `),i=this.atlasManager.getIndexArray(),s=`#version 300 es
      precision highp float;

      // define texture unit for each node in the batch
      `.concat(i.map(function(u){return"uniform sampler2D uTexture".concat(u,";")}).join(`
	`),`

      uniform vec4 uBGColor;

      in vec2 vTexCoord;
      in vec4 vColor;
      flat in int vAtlasId;
      flat in vec4 vIndex;
      flat in int vVertType;

      out vec4 outColor;

      void main(void) {
        if(vVertType == `).concat(Xa,`) {
          `).concat(i.map(function(u){return"if(vAtlasId == ".concat(u,") outColor = texture(uTexture").concat(u,", vTexCoord);")}).join(`
	else `),`
        } else if(vVertType == `).concat(vs,`) {
          // blend arrow color with background (using premultiplied alpha)
          outColor.rgb = vColor.rgb + (uBGColor.rgb * (1.0 - vColor.a)); 
          outColor.a = 1.0; // make opaque, masks out line under arrow
        } else {
          outColor = vColor;
        }

        `).concat(r.picking?`if(outColor.a == 0.0) discard;
             else outColor = vIndex;`:"",`
      }
    `),o=my(a,n,s);o.aPosition=a.getAttribLocation(o,"aPosition"),o.aIndex=a.getAttribLocation(o,"aIndex"),o.aVertType=a.getAttribLocation(o,"aVertType"),o.aTransform=a.getAttribLocation(o,"aTransform"),o.aAtlasId=a.getAttribLocation(o,"aAtlasId"),o.aTex=a.getAttribLocation(o,"aTex"),o.aPointAPointB=a.getAttribLocation(o,"aPointAPointB"),o.aPointCPointD=a.getAttribLocation(o,"aPointCPointD"),o.aLineWidth=a.getAttribLocation(o,"aLineWidth"),o.aColor=a.getAttribLocation(o,"aColor"),o.uPanZoomMatrix=a.getUniformLocation(o,"uPanZoomMatrix"),o.uAtlasSize=a.getUniformLocation(o,"uAtlasSize"),o.uBGColor=a.getUniformLocation(o,"uBGColor"),o.uTextures=[];for(var l=0;l<this.atlasManager.getMaxAtlasesPerBatch();l++)o.uTextures.push(a.getUniformLocation(o,"uTexture".concat(l)));return o}},{key:"createVAO",value:function(){var r=[0,0,1,0,1,1,0,0,1,1,0,1];this.vertexCount=r.length/2;var a=this.maxInstances,n=this.gl,i=this.program,s=n.createVertexArray();return n.bindVertexArray(s),Ty(n,"vec2",i.aPosition,r),this.transformBuffer=Sy(n,a,i.aTransform),this.indexBuffer=Qt(n,a,"vec4",i.aIndex),this.vertTypeBuffer=Qt(n,a,"int",i.aVertType),this.atlasIdBuffer=Qt(n,a,"int",i.aAtlasId),this.texBuffer=Qt(n,a,"vec4",i.aTex),this.pointAPointBBuffer=Qt(n,a,"vec4",i.aPointAPointB),this.pointCPointDBuffer=Qt(n,a,"vec4",i.aPointCPointD),this.lineWidthBuffer=Qt(n,a,"float",i.aLineWidth),this.colorBuffer=Qt(n,a,"vec4",i.aColor),n.bindVertexArray(null),s}},{key:"buffers",get:function(){var r=this;return this._buffers||(this._buffers=Object.keys(this).filter(function(a){return a.endsWith("Buffer")}).map(function(a){return r[a]})),this._buffers}},{key:"startFrame",value:function(r){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:ca.SCREEN;this.panZoomMatrix=r,this.renderTarget=a,this.batchDebugInfo=[],this.wrappedCount=0,this.rectangleCount=0,this.startBatch()}},{key:"startBatch",value:function(){this.instanceCount=0,this.atlasManager.startBatch()}},{key:"endFrame",value:function(){this.endBatch()}},{key:"getTempMatrix",value:function(){return this.tempMatrix=this.tempMatrix||on()}},{key:"drawTexture",value:function(r,a,n){var i=this.atlasManager;if(r.visible()&&i.getRenderTypeOpts(n).isVisible(r)){i.canAddToCurrentBatch(r,n)||this.endBatch(),this.instanceCount+1>=this.maxInstances&&this.endBatch();var s=this.instanceCount;this.vertTypeBuffer.getView(s)[0]=Xa;var o=this.indexBuffer.getView(s);Br(a,o);var l=i.getAtlasInfo(r,n),u=l.index,v=l.tex1,f=l.tex2;f.w>0&&this.wrappedCount++;for(var c=!0,h=0,d=[v,f];h<d.length;h++){var y=d[h];if(y.w!=0){var g=this.instanceCount;this.vertTypeBuffer.getView(g)[0]=Xa;var p=this.indexBuffer.getView(g);Br(a,p);var m=this.atlasIdBuffer.getView(g);m[0]=u;var b=this.texBuffer.getView(g);b[0]=y.x,b[1]=y.y,b[2]=y.w,b[3]=y.h;var w=this.transformBuffer.getMatrixView(g);i.setTransformMatrix(r,w,n,l,c),this.instanceCount++}c=!1}this.instanceCount>=this.maxInstances&&this.endBatch()}}},{key:"drawSimpleRectangle",value:function(r,a,n){if(r.visible()){var i=this.atlasManager,s=this.instanceCount;this.vertTypeBuffer.getView(s)[0]=Pl;var o=this.indexBuffer.getView(s);Br(a,o);var l=r.pstyle("background-color").value,u=r.pstyle("background-opacity").value,v=this.colorBuffer.getView(s);ia(l,u,v);var f=this.transformBuffer.getMatrixView(s);i.setTransformMatrix(r,f,n),this.rectangleCount++,this.instanceCount++,this.instanceCount>=this.maxInstances&&this.endBatch()}}},{key:"drawEdgeArrow",value:function(r,a,n){if(r.visible()){var i=r._private.rscratch,s,o,l;if(n==="source"?(s=i.arrowStartX,o=i.arrowStartY,l=i.srcArrowAngle):(s=i.arrowEndX,o=i.arrowEndY,l=i.tgtArrowAngle),!(isNaN(s)||s==null||isNaN(o)||o==null||isNaN(l)||l==null)){var u=r.pstyle(n+"-arrow-shape").value;if(u!=="none"){var v=r.pstyle(n+"-arrow-color").value,f=r.pstyle("opacity").value,c=r.pstyle("line-opacity").value,h=f*c,d=r.pstyle("width").pfValue,y=r.pstyle("arrow-scale").value,g=this.r.getArrowWidth(d,y),p=this.instanceCount,m=this.transformBuffer.getMatrixView(p);hf(m),bn(m,m,[s,o]),to(m,m,[g,g]),gf(m,m,l),this.vertTypeBuffer.getView(p)[0]=vs;var b=this.indexBuffer.getView(p);Br(a,b);var w=this.colorBuffer.getView(p);ia(v,h,w),this.instanceCount++,this.instanceCount>=this.maxInstances&&this.endBatch()}}}}},{key:"drawEdgeLine",value:function(r,a){if(r.visible()){var n=this.getEdgePoints(r);if(n){var i=r.pstyle("opacity").value,s=r.pstyle("line-opacity").value,o=r.pstyle("width").pfValue,l=r.pstyle("line-color").value,u=i*s;if(n.length/2+this.instanceCount>this.maxInstances&&this.endBatch(),n.length==4){var v=this.instanceCount;this.vertTypeBuffer.getView(v)[0]=Dl;var f=this.indexBuffer.getView(v);Br(a,f);var c=this.colorBuffer.getView(v);ia(l,u,c);var h=this.lineWidthBuffer.getView(v);h[0]=o;var d=this.pointAPointBBuffer.getView(v);d[0]=n[0],d[1]=n[1],d[2]=n[2],d[3]=n[3],this.instanceCount++,this.instanceCount>=this.maxInstances&&this.endBatch()}else for(var y=0;y<n.length-2;y+=2){var g=this.instanceCount;this.vertTypeBuffer.getView(g)[0]=kl;var p=this.indexBuffer.getView(g);Br(a,p);var m=this.colorBuffer.getView(g);ia(l,u,m);var b=this.lineWidthBuffer.getView(g);b[0]=o;var w=n[y-2],E=n[y-1],C=n[y],x=n[y+1],S=n[y+2],k=n[y+3],B=n[y+4],D=n[y+5];y==0&&(w=2*C-S+.001,E=2*x-k+.001),y==n.length-4&&(B=2*S-C+.001,D=2*k-x+.001);var A=this.pointAPointBBuffer.getView(g);A[0]=w,A[1]=E,A[2]=C,A[3]=x;var P=this.pointCPointDBuffer.getView(g);P[0]=S,P[1]=k,P[2]=B,P[3]=D,this.instanceCount++,this.instanceCount>=this.maxInstances&&this.endBatch()}}}}},{key:"getEdgePoints",value:function(r){var a=r._private.rscratch;if(!(a.badLine||a.allpts==null||isNaN(a.allpts[0]))){var n=a.allpts;if(n.length==4)return n;var i=this.getNumSegments(r);return this.getCurveSegmentPoints(n,i)}}},{key:"getNumSegments",value:function(r){var a=15;return Math.min(Math.max(a,5),this.maxInstances)}},{key:"getCurveSegmentPoints",value:function(r,a){if(r.length==4)return r;for(var n=Array((a+1)*2),i=0;i<=a;i++)if(i==0)n[0]=r[0],n[1]=r[1];else if(i==a)n[i*2]=r[r.length-2],n[i*2+1]=r[r.length-1];else{var s=i/a;this.setCurvePoint(r,s,n,i*2)}return n}},{key:"setCurvePoint",value:function(r,a,n,i){if(r.length<=2)n[i]=r[0],n[i+1]=r[1];else{for(var s=Array(r.length-2),o=0;o<s.length;o+=2){var l=(1-a)*r[o]+a*r[o+2],u=(1-a)*r[o+1]+a*r[o+3];s[o]=l,s[o+1]=u}return this.setCurvePoint(s,a,n,i)}}},{key:"endBatch",value:function(){var r=this.gl,a=this.vao,n=this.vertexCount,i=this.instanceCount;if(i!==0){var s=this.renderTarget.picking?this.pickingProgram:this.program;r.useProgram(s),r.bindVertexArray(a);var o=Pt(this.buffers),l;try{for(o.s();!(l=o.n()).done;){var u=l.value;u.bufferSubData(i)}}catch(d){o.e(d)}finally{o.f()}for(var v=this.atlasManager.getAtlases(),f=0;f<v.length;f++)v[f].bufferIfNeeded(r);for(var c=0;c<v.length;c++)r.activeTexture(r.TEXTURE0+c),r.bindTexture(r.TEXTURE_2D,v[c].texture),r.uniform1i(s.uTextures[c],c);r.uniformMatrix3fv(s.uPanZoomMatrix,!1,this.panZoomMatrix),r.uniform1i(s.uAtlasSize,this.atlasManager.getAtlasSize());var h=ia(this.bgColor,1);r.uniform4fv(s.uBGColor,h),r.drawArraysInstanced(r.TRIANGLES,0,n,i),r.bindVertexArray(null),r.bindTexture(r.TEXTURE_2D,null),this.debug&&this.batchDebugInfo.push({count:i,atlasCount:v.length}),this.startBatch()}}},{key:"getDebugInfo",value:function(){var r=this.atlasManager.getDebugInfo(),a=r.reduce(function(s,o){return s+o.atlasCount},0),n=this.batchDebugInfo,i=n.reduce(function(s,o){return s+o.count},0);return{atlasInfo:r,totalAtlases:a,wrappedCount:this.wrappedCount,rectangleCount:this.rectangleCount,batchCount:n.length,batchInfo:n,totalInstances:i}}}])}();function Bl(t,e){return"rgba(".concat(t[0],", ").concat(t[1],", ").concat(t[2],", ").concat(e,")")}var Iy=function(){function t(e){or(this,t),this.r=e}return ur(t,[{key:"getStyleKey",value:function(r,a){var n=this.getStyle(r,a),i=n.shape,s=n.opacity,o=n.color;if(!i)return null;var l=a.width(),u=a.height(),v=Bl(o,s);return rr("".concat(i,"-").concat(l,"-").concat(u,"-").concat(v))}},{key:"isVisible",value:function(r,a){var n=a.pstyle("".concat(r,"-opacity")).value;return n>0}},{key:"getStyle",value:function(r,a){var n=a.pstyle("".concat(r,"-opacity")).value,i=a.pstyle("".concat(r,"-color")).value,s=a.pstyle("".concat(r,"-shape")).value;return{opacity:n,color:i,shape:s}}},{key:"getPadding",value:function(r,a){return a.pstyle("".concat(r,"-padding")).pfValue}},{key:"draw",value:function(r,a,n,i){if(this.isVisible(r,n)){var s=this.r,o=i.w,l=i.h,u=o/2,v=l/2,f=this.getStyle(r,n),c=f.shape,h=f.color,d=f.opacity;a.save(),a.fillStyle=Bl(h,d),c==="round-rectangle"||c==="roundrectangle"?s.drawRoundRectanglePath(a,u,v,o,l,"auto"):c==="ellipse"&&s.drawEllipsePath(a,u,v,o,l),a.fill(),a.restore()}}}])}(),pf={};pf.initWebgl=function(t,e){var r=this,a=r.data.contexts[r.WEBGL];t.bgColor=Oy(r),t.webglTexSize=Math.min(t.webglTexSize,a.getParameter(a.MAX_TEXTURE_SIZE)),t.webglTexRows=Math.min(t.webglTexRows,54),t.webglTexRowsNodes=Math.min(t.webglTexRowsNodes,54),t.webglBatchSize=Math.min(t.webglBatchSize,16384),t.webglTexPerBatch=Math.min(t.webglTexPerBatch,a.getParameter(a.MAX_TEXTURE_IMAGE_UNITS)),r.webglDebug=t.webglDebug,r.webglDebugShowAtlases=t.webglDebugShowAtlases,r.pickingFrameBuffer=Dy(a),r.pickingFrameBuffer.needsDraw=!0;var n=function(u){return function(v){return r.getTextAngle(v,u)}},i=function(u){return function(v){var f=v.pstyle(u);return f&&f.value}};r.drawing=new Ly(r,a,t);var s=new Iy(r);r.drawing.addAtlasCollection("node",Sl({texRows:t.webglTexRowsNodes})),r.drawing.addAtlasCollection("label",Sl({texRows:t.webglTexRows})),r.drawing.addAtlasRenderType("node-body",Ar({collection:"node",getKey:e.getStyleKey,getBoundingBox:e.getElementBox,drawElement:e.drawElement})),r.drawing.addAtlasRenderType("label",Ar({collection:"label",getKey:e.getLabelKey,getBoundingBox:e.getLabelBox,drawElement:e.drawLabel,getRotation:n(null),getRotationPoint:e.getLabelRotationPoint,getRotationOffset:e.getLabelRotationOffset,isVisible:i("label")})),r.drawing.addAtlasRenderType("node-overlay",Ar({collection:"node",getBoundingBox:e.getElementBox,getKey:function(u){return s.getStyleKey("overlay",u)},drawElement:function(u,v,f){return s.draw("overlay",u,v,f)},isVisible:function(u){return s.isVisible("overlay",u)},getPadding:function(u){return s.getPadding("overlay",u)}})),r.drawing.addAtlasRenderType("node-underlay",Ar({collection:"node",getBoundingBox:e.getElementBox,getKey:function(u){return s.getStyleKey("underlay",u)},drawElement:function(u,v,f){return s.draw("underlay",u,v,f)},isVisible:function(u){return s.isVisible("underlay",u)},getPadding:function(u){return s.getPadding("underlay",u)}})),r.drawing.addAtlasRenderType("edge-source-label",Ar({collection:"label",getKey:e.getSourceLabelKey,getBoundingBox:e.getSourceLabelBox,drawElement:e.drawSourceLabel,getRotation:n("source"),getRotationPoint:e.getSourceLabelRotationPoint,getRotationOffset:e.getSourceLabelRotationOffset,isVisible:i("source-label")})),r.drawing.addAtlasRenderType("edge-target-label",Ar({collection:"label",getKey:e.getTargetLabelKey,getBoundingBox:e.getTargetLabelBox,drawElement:e.drawTargetLabel,getRotation:n("target"),getRotationPoint:e.getTargetLabelRotationPoint,getRotationOffset:e.getTargetLabelRotationOffset,isVisible:i("target-label")}));var o=Pa(function(){console.log("garbage collect flag set"),r.data.gc=!0},1e4);r.onUpdateEleCalcs(function(l,u){var v=!1;u&&u.length>0&&(v|=r.drawing.invalidate(u)),v&&o()}),Ny(r)};function Oy(t){var e=t.cy.container(),r=e&&e.style&&e.style.backgroundColor||"white";return _l(r)}function Ny(t){{var e=t.render;t.render=function(i){i=i||{};var s=t.cy;t.webgl&&(s.zoom()>of?(Fy(t),e.call(t,i)):(zy(t),mf(t,i,ca.SCREEN)))}}{var r=t.matchCanvasSize;t.matchCanvasSize=function(i){r.call(t,i),t.pickingFrameBuffer.setFramebufferAttachmentSizes(t.canvasWidth,t.canvasHeight),t.pickingFrameBuffer.needsDraw=!0}}t.findNearestElements=function(i,s,o,l){return Ky(t,i,s)};{var a=t.invalidateCachedZSortedEles;t.invalidateCachedZSortedEles=function(){a.call(t),t.pickingFrameBuffer.needsDraw=!0}}{var n=t.notify;t.notify=function(i,s){n.call(t,i,s),i==="viewport"||i==="bounds"?t.pickingFrameBuffer.needsDraw=!0:i==="background"&&t.drawing.invalidate(s,{type:"node-body"})}}}function Fy(t){var e=t.data.contexts[t.WEBGL];e.clear(e.COLOR_BUFFER_BIT|e.DEPTH_BUFFER_BIT)}function zy(t){var e=function(a){a.save(),a.setTransform(1,0,0,1,0,0),a.clearRect(0,0,t.canvasWidth,t.canvasHeight),a.restore()};e(t.data.contexts[t.NODE]),e(t.data.contexts[t.DRAG])}function qy(t){var e=t.canvasWidth,r=t.canvasHeight,a=eo(t),n=a.pan,i=a.zoom,s=on();bn(s,s,[n.x,n.y]),to(s,s,[i,i]);var o=on();Py(o,e,r);var l=on();return ky(l,o,s),l}function yf(t,e){var r=t.canvasWidth,a=t.canvasHeight,n=eo(t),i=n.pan,s=n.zoom;e.setTransform(1,0,0,1,0,0),e.clearRect(0,0,r,a),e.translate(i.x,i.y),e.scale(s,s)}function Vy(t,e){t.drawSelectionRectangle(e,function(r){return yf(t,r)})}function _y(t){var e=t.data.contexts[t.NODE];e.save(),yf(t,e),e.strokeStyle="rgba(0, 0, 0, 0.3)",e.beginPath(),e.moveTo(-1e3,0),e.lineTo(1e3,0),e.stroke(),e.beginPath(),e.moveTo(0,-1e3),e.lineTo(0,1e3),e.stroke(),e.restore()}function Gy(t){var e=function(n,i,s){for(var o=n.atlasManager.getAtlasCollection(i),l=t.data.contexts[t.NODE],u=.125,v=o.atlases,f=0;f<v.length;f++){var c=v[f],h=c.canvas;if(h){var d=h.width,y=h.height,g=d*f,p=h.height*s;l.save(),l.scale(u,u),l.drawImage(h,g,p),l.strokeStyle="black",l.rect(g,p,d,y),l.stroke(),l.restore()}}},r=0;e(t.drawing,"node",r++),e(t.drawing,"label",r++)}function Hy(t,e,r,a,n){var i,s,o,l,u=eo(t),v=u.pan,f=u.zoom;{var c=wy(t,v,f,e,r),h=je(c,2),d=h[0],y=h[1],g=6;i=d-g/2,s=y-g/2,o=g,l=g}if(o===0||l===0)return[];var p=t.data.contexts[t.WEBGL];p.bindFramebuffer(p.FRAMEBUFFER,t.pickingFrameBuffer),t.pickingFrameBuffer.needsDraw&&(p.viewport(0,0,p.canvas.width,p.canvas.height),mf(t,null,ca.PICKING),t.pickingFrameBuffer.needsDraw=!1);var m=o*l,b=new Uint8Array(m*4);p.readPixels(i,s,o,l,p.RGBA,p.UNSIGNED_BYTE,b),p.bindFramebuffer(p.FRAMEBUFFER,null);for(var w=new Set,E=0;E<m;E++){var C=b.slice(E*4,E*4+4),x=xy(C)-1;x>=0&&w.add(x)}return w}function Ky(t,e,r){var a=Hy(t,e,r),n=t.getCachedZSortedEles(),i,s,o=Pt(a),l;try{for(o.s();!(l=o.n()).done;){var u=l.value,v=n[u];if(!i&&v.isNode()&&(i=v),!s&&v.isEdge()&&(s=v),i&&s)break}}catch(f){o.e(f)}finally{o.f()}return[i,s].filter(Boolean)}function $y(t){return t.pstyle("shape").value==="rectangle"&&t.pstyle("background-fill").value==="solid"&&t.pstyle("border-width").pfValue===0&&t.pstyle("background-image").strValue==="none"}function fs(t,e,r){var a=t.drawing;e+=1,r.isNode()?(a.drawTexture(r,e,"node-underlay"),$y(r)?a.drawSimpleRectangle(r,e,"node-body"):a.drawTexture(r,e,"node-body"),a.drawTexture(r,e,"label"),a.drawTexture(r,e,"node-overlay")):(a.drawEdgeLine(r,e),a.drawEdgeArrow(r,e,"source"),a.drawEdgeArrow(r,e,"target"),a.drawTexture(r,e,"label"),a.drawTexture(r,e,"edge-source-label"),a.drawTexture(r,e,"edge-target-label"))}function mf(t,e,r){var a;t.webglDebug&&(a=performance.now());var n=t.drawing,i=0;if(r.screen&&t.data.canvasNeedsRedraw[t.SELECT_BOX]&&Vy(t,e),t.data.canvasNeedsRedraw[t.NODE]||r.picking){var s=t.data.contexts[t.WEBGL];r.screen?(s.clearColor(0,0,0,0),s.enable(s.BLEND),s.blendFunc(s.ONE,s.ONE_MINUS_SRC_ALPHA)):s.disable(s.BLEND),s.clear(s.COLOR_BUFFER_BIT|s.DEPTH_BUFFER_BIT),s.viewport(0,0,s.canvas.width,s.canvas.height);var o=qy(t),l=t.getCachedZSortedEles();if(i=l.length,n.startFrame(o,r),r.screen){for(var u=0;u<l.nondrag.length;u++)fs(t,u,l.nondrag[u]);for(var v=0;v<l.drag.length;v++)fs(t,v,l.drag[v])}else if(r.picking)for(var f=0;f<l.length;f++)fs(t,f,l[f]);n.endFrame(),r.screen&&t.webglDebugShowAtlases&&(_y(t),Gy(t)),t.data.canvasNeedsRedraw[t.NODE]=!1,t.data.canvasNeedsRedraw[t.DRAG]=!1}if(t.webglDebug){var c=performance.now(),h=!1,d=Math.ceil(c-a),y=n.getDebugInfo(),g=["".concat(i," elements"),"".concat(y.totalInstances," instances"),"".concat(y.batchCount," batches"),"".concat(y.totalAtlases," atlases"),"".concat(y.wrappedCount," wrapped textures"),"".concat(y.rectangleCount," simple rectangles")].join(", ");if(h)console.log("WebGL (".concat(r.name,") - time ").concat(d,"ms, ").concat(g));else{console.log("WebGL (".concat(r.name,") - frame time ").concat(d,"ms")),console.log("Totals:"),console.log("  ".concat(g)),console.log("Texture Atlases Used:");var p=y.atlasInfo,m=Pt(p),b;try{for(m.s();!(b=m.n()).done;){var w=b.value;console.log("  ".concat(w.type,": ").concat(w.keyCount," keys, ").concat(w.atlasCount," atlases"))}}catch(E){m.e(E)}finally{m.f()}console.log("")}}t.data.gc&&(console.log("Garbage Collect!"),t.data.gc=!1,n.gc())}var vr={};vr.drawPolygonPath=function(t,e,r,a,n,i){var s=a/2,o=n/2;t.beginPath&&t.beginPath(),t.moveTo(e+s*i[0],r+o*i[1]);for(var l=1;l<i.length/2;l++)t.lineTo(e+s*i[l*2],r+o*i[l*2+1]);t.closePath()};vr.drawRoundPolygonPath=function(t,e,r,a,n,i,s){s.forEach(function(o){return Qv(t,o)}),t.closePath()};vr.drawRoundRectanglePath=function(t,e,r,a,n,i){var s=a/2,o=n/2,l=i==="auto"?mr(a,n):Math.min(i,o,s);t.beginPath&&t.beginPath(),t.moveTo(e,r-o),t.arcTo(e+s,r-o,e+s,r,l),t.arcTo(e+s,r+o,e,r+o,l),t.arcTo(e-s,r+o,e-s,r,l),t.arcTo(e-s,r-o,e,r-o,l),t.lineTo(e,r-o),t.closePath()};vr.drawBottomRoundRectanglePath=function(t,e,r,a,n,i){var s=a/2,o=n/2,l=i==="auto"?mr(a,n):i;t.beginPath&&t.beginPath(),t.moveTo(e,r-o),t.lineTo(e+s,r-o),t.lineTo(e+s,r),t.arcTo(e+s,r+o,e,r+o,l),t.arcTo(e-s,r+o,e-s,r,l),t.lineTo(e-s,r-o),t.lineTo(e,r-o),t.closePath()};vr.drawCutRectanglePath=function(t,e,r,a,n,i,s){var o=a/2,l=n/2,u=s==="auto"?Vs():s;t.beginPath&&t.beginPath(),t.moveTo(e-o+u,r-l),t.lineTo(e+o-u,r-l),t.lineTo(e+o,r-l+u),t.lineTo(e+o,r+l-u),t.lineTo(e+o-u,r+l),t.lineTo(e-o+u,r+l),t.lineTo(e-o,r+l-u),t.lineTo(e-o,r-l+u),t.closePath()};vr.drawBarrelPath=function(t,e,r,a,n){var i=a/2,s=n/2,o=e-i,l=e+i,u=r-s,v=r+s,f=hs(a,n),c=f.widthOffset,h=f.heightOffset,d=f.ctrlPtOffsetPct*c;t.beginPath&&t.beginPath(),t.moveTo(o,u+h),t.lineTo(o,v-h),t.quadraticCurveTo(o+d,v,o+c,v),t.lineTo(l-c,v),t.quadraticCurveTo(l-d,v,l,v-h),t.lineTo(l,u+h),t.quadraticCurveTo(l-d,u,l-c,u),t.lineTo(o+c,u),t.quadraticCurveTo(o+d,u,o,u+h),t.closePath()};var Al=Math.sin(0),Rl=Math.cos(0),Ps={},Bs={},bf=Math.PI/40;for(var Rr=0*Math.PI;Rr<2*Math.PI;Rr+=bf)Ps[Rr]=Math.sin(Rr),Bs[Rr]=Math.cos(Rr);vr.drawEllipsePath=function(t,e,r,a,n){if(t.beginPath&&t.beginPath(),t.ellipse)t.ellipse(e,r,a/2,n/2,0,0,2*Math.PI);else for(var i,s,o=a/2,l=n/2,u=0*Math.PI;u<2*Math.PI;u+=bf)i=e-o*Ps[u]*Al+o*Bs[u]*Rl,s=r+l*Bs[u]*Al+l*Ps[u]*Rl,u===0?t.moveTo(i,s):t.lineTo(i,s);t.closePath()};var Ia={};Ia.createBuffer=function(t,e){var r=document.createElement("canvas");return r.width=t,r.height=e,[r,r.getContext("2d")]};Ia.bufferCanvasImage=function(t){var e=this.cy,r=e.mutableElements(),a=r.boundingBox(),n=this.findContainerClientCoords(),i=t.full?Math.ceil(a.w):n[2],s=t.full?Math.ceil(a.h):n[3],o=ae(t.maxWidth)||ae(t.maxHeight),l=this.getPixelRatio(),u=1;if(t.scale!==void 0)i*=t.scale,s*=t.scale,u=t.scale;else if(o){var v=1/0,f=1/0;ae(t.maxWidth)&&(v=u*t.maxWidth/i),ae(t.maxHeight)&&(f=u*t.maxHeight/s),u=Math.min(v,f),i*=u,s*=u}o||(i*=l,s*=l,u*=l);var c=document.createElement("canvas");c.width=i,c.height=s,c.style.width=i+"px",c.style.height=s+"px";var h=c.getContext("2d");if(i>0&&s>0){h.clearRect(0,0,i,s),h.globalCompositeOperation="source-over";var d=this.getCachedZSortedEles();if(t.full)h.translate(-a.x1*u,-a.y1*u),h.scale(u,u),this.drawElements(h,d),h.scale(1/u,1/u),h.translate(a.x1*u,a.y1*u);else{var y=e.pan(),g={x:y.x*u,y:y.y*u};u*=e.zoom(),h.translate(g.x,g.y),h.scale(u,u),this.drawElements(h,d),h.scale(1/u,1/u),h.translate(-g.x,-g.y)}t.bg&&(h.globalCompositeOperation="destination-over",h.fillStyle=t.bg,h.rect(0,0,i,s),h.fill())}return c};function Wy(t,e){for(var r=atob(t),a=new ArrayBuffer(r.length),n=new Uint8Array(a),i=0;i<r.length;i++)n[i]=r.charCodeAt(i);return new Blob([a],{type:e})}function Ml(t){var e=t.indexOf(",");return t.substr(e+1)}function wf(t,e,r){var a=function(){return e.toDataURL(r,t.quality)};switch(t.output){case"blob-promise":return new Wr(function(n,i){try{e.toBlob(function(s){s!=null?n(s):i(new Error("`canvas.toBlob()` sent a null value in its callback"))},r,t.quality)}catch(s){i(s)}});case"blob":return Wy(Ml(a()),r);case"base64":return Ml(a());case"base64uri":default:return a()}}Ia.png=function(t){return wf(t,this.bufferCanvasImage(t),"image/png")};Ia.jpg=function(t){return wf(t,this.bufferCanvasImage(t),"image/jpeg")};var xf={};xf.nodeShapeImpl=function(t,e,r,a,n,i,s,o){switch(t){case"ellipse":return this.drawEllipsePath(e,r,a,n,i);case"polygon":return this.drawPolygonPath(e,r,a,n,i,s);case"round-polygon":return this.drawRoundPolygonPath(e,r,a,n,i,s,o);case"roundrectangle":case"round-rectangle":return this.drawRoundRectanglePath(e,r,a,n,i,o);case"cutrectangle":case"cut-rectangle":return this.drawCutRectanglePath(e,r,a,n,i,s,o);case"bottomroundrectangle":case"bottom-round-rectangle":return this.drawBottomRoundRectanglePath(e,r,a,n,i,o);case"barrel":return this.drawBarrelPath(e,r,a,n,i)}};var Uy=Ef,Te=Ef.prototype;Te.CANVAS_LAYERS=3;Te.SELECT_BOX=0;Te.DRAG=1;Te.NODE=2;Te.WEBGL=3;Te.CANVAS_TYPES=["2d","2d","2d","webgl2"];Te.BUFFER_COUNT=3;Te.TEXTURE_BUFFER=0;Te.MOTIONBLUR_BUFFER_NODE=1;Te.MOTIONBLUR_BUFFER_DRAG=2;function Ef(t){var e=this,r=e.cy.window(),a=r.document;t.webgl&&(Te.CANVAS_LAYERS=e.CANVAS_LAYERS=4,console.log("webgl rendering enabled")),e.data={canvases:new Array(Te.CANVAS_LAYERS),contexts:new Array(Te.CANVAS_LAYERS),canvasNeedsRedraw:new Array(Te.CANVAS_LAYERS),bufferCanvases:new Array(Te.BUFFER_COUNT),bufferContexts:new Array(Te.CANVAS_LAYERS)};var n="-webkit-tap-highlight-color",i="rgba(0,0,0,0)";e.data.canvasContainer=a.createElement("div");var s=e.data.canvasContainer.style;e.data.canvasContainer.style[n]=i,s.position="relative",s.zIndex="0",s.overflow="hidden";var o=t.cy.container();o.appendChild(e.data.canvasContainer),o.style[n]=i;var l={"-webkit-user-select":"none","-moz-user-select":"-moz-none","user-select":"none","-webkit-tap-highlight-color":"rgba(0,0,0,0)","outline-style":"none"};jf()&&(l["-ms-touch-action"]="none",l["touch-action"]="none");for(var u=0;u<Te.CANVAS_LAYERS;u++){var v=e.data.canvases[u]=a.createElement("canvas"),f=Te.CANVAS_TYPES[u];e.data.contexts[u]=v.getContext(f),e.data.contexts[u]||Ve("Could not create canvas of type "+f),Object.keys(l).forEach(function(K){v.style[K]=l[K]}),v.style.position="absolute",v.setAttribute("data-id","layer"+u),v.style.zIndex=String(Te.CANVAS_LAYERS-u),e.data.canvasContainer.appendChild(v),e.data.canvasNeedsRedraw[u]=!1}e.data.topCanvas=e.data.canvases[0],e.data.canvases[Te.NODE].setAttribute("data-id","layer"+Te.NODE+"-node"),e.data.canvases[Te.SELECT_BOX].setAttribute("data-id","layer"+Te.SELECT_BOX+"-selectbox"),e.data.canvases[Te.DRAG].setAttribute("data-id","layer"+Te.DRAG+"-drag"),e.data.canvases[Te.WEBGL]&&e.data.canvases[Te.WEBGL].setAttribute("data-id","layer"+Te.WEBGL+"-webgl");for(var u=0;u<Te.BUFFER_COUNT;u++)e.data.bufferCanvases[u]=a.createElement("canvas"),e.data.bufferContexts[u]=e.data.bufferCanvases[u].getContext("2d"),e.data.bufferCanvases[u].style.position="absolute",e.data.bufferCanvases[u].setAttribute("data-id","buffer"+u),e.data.bufferCanvases[u].style.zIndex=String(-u-1),e.data.bufferCanvases[u].style.visibility="hidden";e.pathsEnabled=!0;var c=pt(),h=function(N){return{x:(N.x1+N.x2)/2,y:(N.y1+N.y2)/2}},d=function(N){return{x:-N.w/2,y:-N.h/2}},y=function(N){var $=N[0]._private,J=$.oldBackgroundTimestamp===$.backgroundTimestamp;return!J},g=function(N){return N[0]._private.nodeKey},p=function(N){return N[0]._private.labelStyleKey},m=function(N){return N[0]._private.sourceLabelStyleKey},b=function(N){return N[0]._private.targetLabelStyleKey},w=function(N,$,J,re,le){return e.drawElement(N,$,J,!1,!1,le)},E=function(N,$,J,re,le){return e.drawElementText(N,$,J,re,"main",le)},C=function(N,$,J,re,le){return e.drawElementText(N,$,J,re,"source",le)},x=function(N,$,J,re,le){return e.drawElementText(N,$,J,re,"target",le)},S=function(N){return N.boundingBox(),N[0]._private.bodyBounds},k=function(N){return N.boundingBox(),N[0]._private.labelBounds.main||c},B=function(N){return N.boundingBox(),N[0]._private.labelBounds.source||c},D=function(N){return N.boundingBox(),N[0]._private.labelBounds.target||c},A=function(N,$){return $},P=function(N){return h(S(N))},R=function(N,$,J){var re=N?N+"-":"";return{x:$.x+J.pstyle(re+"text-margin-x").pfValue,y:$.y+J.pstyle(re+"text-margin-y").pfValue}},L=function(N,$,J){var re=N[0]._private.rscratch;return{x:re[$],y:re[J]}},I=function(N){return R("",L(N,"labelX","labelY"),N)},M=function(N){return R("source",L(N,"sourceLabelX","sourceLabelY"),N)},O=function(N){return R("target",L(N,"targetLabelX","targetLabelY"),N)},_=function(N){return d(S(N))},H=function(N){return d(B(N))},F=function(N){return d(D(N))},G=function(N){var $=k(N),J=d(k(N));if(N.isNode()){switch(N.pstyle("text-halign").value){case"left":J.x=-$.w-($.leftPad||0);break;case"right":J.x=-($.rightPad||0);break}switch(N.pstyle("text-valign").value){case"top":J.y=-$.h-($.topPad||0);break;case"bottom":J.y=-($.botPad||0);break}}return J},U=e.data.eleTxrCache=new la(e,{getKey:g,doesEleInvalidateKey:y,drawElement:w,getBoundingBox:S,getRotationPoint:P,getRotationOffset:_,allowEdgeTxrCaching:!1,allowParentTxrCaching:!1}),X=e.data.lblTxrCache=new la(e,{getKey:p,drawElement:E,getBoundingBox:k,getRotationPoint:I,getRotationOffset:G,isVisible:A}),Z=e.data.slbTxrCache=new la(e,{getKey:m,drawElement:C,getBoundingBox:B,getRotationPoint:M,getRotationOffset:H,isVisible:A}),Q=e.data.tlbTxrCache=new la(e,{getKey:b,drawElement:x,getBoundingBox:D,getRotationPoint:O,getRotationOffset:F,isVisible:A}),ee=e.data.lyrTxrCache=new uf(e);e.onUpdateEleCalcs(function(N,$){U.invalidateElements($),X.invalidateElements($),Z.invalidateElements($),Q.invalidateElements($),ee.invalidateElements($);for(var J=0;J<$.length;J++){var re=$[J]._private;re.oldBackgroundTimestamp=re.backgroundTimestamp}});var te=function(N){for(var $=0;$<N.length;$++)ee.enqueueElementRefinement(N[$].ele)};U.onDequeue(te),X.onDequeue(te),Z.onDequeue(te),Q.onDequeue(te),t.webgl&&e.initWebgl(t,{getStyleKey:g,getLabelKey:p,getSourceLabelKey:m,getTargetLabelKey:b,drawElement:w,drawLabel:E,drawSourceLabel:C,drawTargetLabel:x,getElementBox:S,getLabelBox:k,getSourceLabelBox:B,getTargetLabelBox:D,getElementRotationPoint:P,getElementRotationOffset:_,getLabelRotationPoint:I,getSourceLabelRotationPoint:M,getTargetLabelRotationPoint:O,getLabelRotationOffset:G,getSourceLabelRotationOffset:H,getTargetLabelRotationOffset:F})}Te.redrawHint=function(t,e){var r=this;switch(t){case"eles":r.data.canvasNeedsRedraw[Te.NODE]=e;break;case"drag":r.data.canvasNeedsRedraw[Te.DRAG]=e;break;case"select":r.data.canvasNeedsRedraw[Te.SELECT_BOX]=e;break;case"gc":r.data.gc=!0;break}};var Yy=typeof Path2D<"u";Te.path2dEnabled=function(t){if(t===void 0)return this.pathsEnabled;this.pathsEnabled=!!t};Te.usePaths=function(){return Yy&&this.pathsEnabled};Te.setImgSmoothing=function(t,e){t.imageSmoothingEnabled!=null?t.imageSmoothingEnabled=e:(t.webkitImageSmoothingEnabled=e,t.mozImageSmoothingEnabled=e,t.msImageSmoothingEnabled=e)};Te.getImgSmoothing=function(t){return t.imageSmoothingEnabled!=null?t.imageSmoothingEnabled:t.webkitImageSmoothingEnabled||t.mozImageSmoothingEnabled||t.msImageSmoothingEnabled};Te.makeOffscreenCanvas=function(t,e){var r;if((typeof OffscreenCanvas>"u"?"undefined":We(OffscreenCanvas))!=="undefined")r=new OffscreenCanvas(t,e);else{var a=this.cy.window(),n=a.document;r=n.createElement("canvas"),r.width=t,r.height=e}return r};[lf,qt,Yt,js,Cr,Qr,dt,pf,vr,Ia,xf].forEach(function(t){ge(Te,t)});var Xy=[{name:"null",impl:Yv},{name:"base",impl:nf},{name:"canvas",impl:Uy}],Zy=[{type:"layout",extensions:Pp},{type:"renderer",extensions:Xy}],Cf={},Tf={};function Sf(t,e,r){var a=r,n=function(S){Re("Can not register `"+e+"` for `"+t+"` since `"+S+"` already exists in the prototype and can not be overridden")};if(t==="core"){if(xa.prototype[e])return n(e);xa.prototype[e]=r}else if(t==="collection"){if(nt.prototype[e])return n(e);nt.prototype[e]=r}else if(t==="layout"){for(var i=function(S){this.options=S,r.call(this,S),ke(this._private)||(this._private={}),this._private.cy=S.cy,this._private.listeners=[],this.createEmitter()},s=i.prototype=Object.create(r.prototype),o=[],l=0;l<o.length;l++){var u=o[l];s[u]=s[u]||function(){return this}}s.start&&!s.run?s.run=function(){return this.start(),this}:!s.start&&s.run&&(s.start=function(){return this.run(),this});var v=r.prototype.stop;s.stop=function(){var x=this.options;if(x&&x.animate){var S=this.animations;if(S)for(var k=0;k<S.length;k++)S[k].stop()}return v?v.call(this):this.emit("layoutstop"),this},s.destroy||(s.destroy=function(){return this}),s.cy=function(){return this._private.cy};var f=function(S){return S._private.cy},c={addEventFields:function(S,k){k.layout=S,k.cy=f(S),k.target=S},bubble:function(){return!0},parent:function(S){return f(S)}};ge(s,{createEmitter:function(){return this._private.emitter=new Rn(c,this),this},emitter:function(){return this._private.emitter},on:function(S,k){return this.emitter().on(S,k),this},one:function(S,k){return this.emitter().one(S,k),this},once:function(S,k){return this.emitter().one(S,k),this},removeListener:function(S,k){return this.emitter().removeListener(S,k),this},removeAllListeners:function(){return this.emitter().removeAllListeners(),this},emit:function(S,k){return this.emitter().emit(S,k),this}}),Ae.eventAliasesOn(s),a=i}else if(t==="renderer"&&e!=="null"&&e!=="base"){var h=Df("renderer","base"),d=h.prototype,y=r,g=r.prototype,p=function(){h.apply(this,arguments),y.apply(this,arguments)},m=p.prototype;for(var b in d){var w=d[b],E=g[b]!=null;if(E)return n(b);m[b]=w}for(var C in g)m[C]=g[C];d.clientFunctions.forEach(function(x){m[x]=m[x]||function(){Ve("Renderer does not implement `renderer."+x+"()` on its prototype")}}),a=p}else if(t==="__proto__"||t==="constructor"||t==="prototype")return Ve(t+" is an illegal type to be registered, possibly lead to prototype pollutions");return Gl({map:Cf,keys:[t,e],value:a})}function Df(t,e){return Hl({map:Cf,keys:[t,e]})}function Qy(t,e,r,a,n){return Gl({map:Tf,keys:[t,e,r,a],value:n})}function Jy(t,e,r,a){return Hl({map:Tf,keys:[t,e,r,a]})}var As=function(){if(arguments.length===2)return Df.apply(null,arguments);if(arguments.length===3)return Sf.apply(null,arguments);if(arguments.length===4)return Jy.apply(null,arguments);if(arguments.length===5)return Qy.apply(null,arguments);Ve("Invalid extension access syntax")};xa.prototype.extension=As;Zy.forEach(function(t){t.extensions.forEach(function(e){Sf(t.type,e.name,e.impl)})});var wn=function(){if(!(this instanceof wn))return new wn;this.length=0},xr=wn.prototype;xr.instanceString=function(){return"stylesheet"};xr.selector=function(t){var e=this.length++;return this[e]={selector:t,properties:[]},this};xr.css=function(t,e){var r=this.length-1;if(fe(t))this[r].properties.push({name:t,value:e});else if(ke(t))for(var a=t,n=Object.keys(a),i=0;i<n.length;i++){var s=n[i],o=a[s];if(o!=null){var l=et.properties[s]||et.properties[xn(s)];if(l!=null){var u=l.name,v=o;this[r].properties.push({name:u,value:v})}}}return this};xr.style=xr.css;xr.generateStyle=function(t){var e=new et(t);return this.appendToStyle(e)};xr.appendToStyle=function(t){for(var e=0;e<this.length;e++){var r=this[e],a=r.selector,n=r.properties;t.selector(a);for(var i=0;i<n.length;i++){var s=n[i];t.css(s.name,s.value)}}return t};var jy="3.31.2",Kr=function(e){if(e===void 0&&(e={}),ke(e))return new xa(e);if(fe(e))return As.apply(As,arguments)};Kr.use=function(t){var e=Array.prototype.slice.call(arguments,1);return e.unshift(Kr),t.apply(null,e),this};Kr.warnings=function(t){return Zl(t)};Kr.version=jy;Kr.stylesheet=Kr.Stylesheet=wn;export{Kr as c};
