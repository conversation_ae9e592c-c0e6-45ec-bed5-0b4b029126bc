import{j as t,E as Ga,I as Dt,F as Va,G as Ya,H as Nt,J as Ja,V as zt,L as Qa,K as Xa,M as Ct,N as Pt,Q as Za,U as St,W as _t,X as Et,_ as ye,d as Ft}from"./ui-vendor-CeCm8EER.js";import{r as s,g as et,R as Tt}from"./react-vendor-DEwriMA6.js";import{c as E,C as at,a as Ot,b as At,d as sa,F as Rt,e as la,f as tt,u as me,s as Mt,g as M,U as ra,S as It,h as nt,B as R,X as it,i as qt,j as ee,D as Be,k as ba,l as Ue,m as $e,n as He,o as Ke,p as Lt,q as Bt,E as Ut,T as ya,I as Me,r as ot,t as st,L as $t,v as Ht,w as Kt,x as ja,y as wa,z as Wt,A as Gt,G as Vt,H as Yt,J as Jt,K as Qt,M as _e,N as Ee,O as ka,P as Qe,Q as Xt,R as Da,V as Na,W as Zt,Y as en,Z as an,_ as Xe,$ as Ze,a0 as tn}from"./feature-graph-BWr9U7tw.js";const za=St,wi=Et,Ca=_t,ca=s.forwardRef(({className:e,children:a,...n},i)=>t.jsxs(Ga,{ref:i,className:E("border-input bg-background ring-offset-background placeholder:text-muted-foreground focus:ring-ring flex h-10 w-full items-center justify-between rounded-md border px-3 py-2 text-sm focus:ring-2 focus:ring-offset-2 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...n,children:[a,t.jsx(Dt,{asChild:!0,children:t.jsx(at,{className:"h-4 w-4 opacity-50"})})]}));ca.displayName=Ga.displayName;const lt=s.forwardRef(({className:e,...a},n)=>t.jsx(Va,{ref:n,className:E("flex cursor-default items-center justify-center py-1",e),...a,children:t.jsx(Ot,{className:"h-4 w-4"})}));lt.displayName=Va.displayName;const rt=s.forwardRef(({className:e,...a},n)=>t.jsx(Ya,{ref:n,className:E("flex cursor-default items-center justify-center py-1",e),...a,children:t.jsx(at,{className:"h-4 w-4"})}));rt.displayName=Ya.displayName;const pa=s.forwardRef(({className:e,children:a,position:n="popper",...i},l)=>t.jsx(Nt,{children:t.jsxs(Ja,{ref:l,className:E("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border shadow-md",n==="popper"&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:n,...i,children:[t.jsx(lt,{}),t.jsx(zt,{className:E("p-1",n==="popper"&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:a}),t.jsx(rt,{})]})}));pa.displayName=Ja.displayName;const nn=s.forwardRef(({className:e,...a},n)=>t.jsx(Qa,{ref:n,className:E("py-1.5 pr-2 pl-8 text-sm font-semibold",e),...a}));nn.displayName=Qa.displayName;const da=s.forwardRef(({className:e,children:a,...n},i)=>t.jsxs(Xa,{ref:i,className:E("focus:bg-accent focus:text-accent-foreground relative flex w-full cursor-default items-center rounded-sm py-1.5 pr-2 pl-8 text-sm outline-none select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...n,children:[t.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:t.jsx(Ct,{children:t.jsx(At,{className:"h-4 w-4"})})}),t.jsx(Pt,{children:a})]}));da.displayName=Xa.displayName;const on=s.forwardRef(({className:e,...a},n)=>t.jsx(Za,{ref:n,className:E("bg-muted -mx-1 my-1 h-px",e),...a}));on.displayName=Za.displayName;const ct=s.forwardRef(({className:e,...a},n)=>t.jsx("div",{className:"relative w-full overflow-auto",children:t.jsx("table",{ref:n,className:E("w-full caption-bottom text-sm",e),...a})}));ct.displayName="Table";const pt=s.forwardRef(({className:e,...a},n)=>t.jsx("thead",{ref:n,className:E("[&_tr]:border-b",e),...a}));pt.displayName="TableHeader";const dt=s.forwardRef(({className:e,...a},n)=>t.jsx("tbody",{ref:n,className:E("[&_tr:last-child]:border-0",e),...a}));dt.displayName="TableBody";const sn=s.forwardRef(({className:e,...a},n)=>t.jsx("tfoot",{ref:n,className:E("bg-muted/50 border-t font-medium [&>tr]:last:border-b-0",e),...a}));sn.displayName="TableFooter";const ma=s.forwardRef(({className:e,...a},n)=>t.jsx("tr",{ref:n,className:E("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...a}));ma.displayName="TableRow";const ie=s.forwardRef(({className:e,...a},n)=>t.jsx("th",{ref:n,className:E("text-muted-foreground h-10 px-2 text-left align-middle font-medium [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...a}));ie.displayName="TableHead";const oe=s.forwardRef(({className:e,...a},n)=>t.jsx("td",{ref:n,className:E("p-2 align-middle [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...a}));oe.displayName="TableCell";const ln=s.forwardRef(({className:e,...a},n)=>t.jsx("caption",{ref:n,className:E("text-muted-foreground mt-4 text-sm",e),...a}));ln.displayName="TableCaption";function rn({title:e,description:a,icon:n=Rt,action:i,className:l,...r}){return t.jsxs(sa,{className:E("flex w-full flex-col items-center justify-center space-y-6 bg-transparent p-16",l),...r,children:[t.jsx("div",{className:"mr-4 shrink-0 rounded-full border border-dashed p-4",children:t.jsx(n,{className:"text-muted-foreground size-8","aria-hidden":"true"})}),t.jsxs("div",{className:"flex flex-col items-center gap-1.5 text-center",children:[t.jsx(la,{children:e}),a?t.jsx(tt,{children:a}):null]}),i||null]})}var ea={exports:{}},aa,Pa;function cn(){if(Pa)return aa;Pa=1;var e="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED";return aa=e,aa}var ta,Sa;function pn(){if(Sa)return ta;Sa=1;var e=cn();function a(){}function n(){}return n.resetWarningCache=a,ta=function(){function i(c,p,k,x,g,F){if(F!==e){var y=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw y.name="Invariant Violation",y}}i.isRequired=i;function l(){return i}var r={array:i,bigint:i,bool:i,func:i,number:i,object:i,string:i,symbol:i,any:i,arrayOf:l,element:i,elementType:i,instanceOf:l,node:i,objectOf:l,oneOf:l,oneOfType:l,shape:l,exact:l,checkPropTypes:n,resetWarningCache:a};return r.PropTypes=r,r},ta}var _a;function dn(){return _a||(_a=1,ea.exports=pn()()),ea.exports}var mn=dn();const A=et(mn),un=new Map([["1km","application/vnd.1000minds.decision-model+xml"],["3dml","text/vnd.in3d.3dml"],["3ds","image/x-3ds"],["3g2","video/3gpp2"],["3gp","video/3gp"],["3gpp","video/3gpp"],["3mf","model/3mf"],["7z","application/x-7z-compressed"],["7zip","application/x-7z-compressed"],["123","application/vnd.lotus-1-2-3"],["aab","application/x-authorware-bin"],["aac","audio/x-acc"],["aam","application/x-authorware-map"],["aas","application/x-authorware-seg"],["abw","application/x-abiword"],["ac","application/vnd.nokia.n-gage.ac+xml"],["ac3","audio/ac3"],["acc","application/vnd.americandynamics.acc"],["ace","application/x-ace-compressed"],["acu","application/vnd.acucobol"],["acutc","application/vnd.acucorp"],["adp","audio/adpcm"],["aep","application/vnd.audiograph"],["afm","application/x-font-type1"],["afp","application/vnd.ibm.modcap"],["ahead","application/vnd.ahead.space"],["ai","application/pdf"],["aif","audio/x-aiff"],["aifc","audio/x-aiff"],["aiff","audio/x-aiff"],["air","application/vnd.adobe.air-application-installer-package+zip"],["ait","application/vnd.dvb.ait"],["ami","application/vnd.amiga.ami"],["amr","audio/amr"],["apk","application/vnd.android.package-archive"],["apng","image/apng"],["appcache","text/cache-manifest"],["application","application/x-ms-application"],["apr","application/vnd.lotus-approach"],["arc","application/x-freearc"],["arj","application/x-arj"],["asc","application/pgp-signature"],["asf","video/x-ms-asf"],["asm","text/x-asm"],["aso","application/vnd.accpac.simply.aso"],["asx","video/x-ms-asf"],["atc","application/vnd.acucorp"],["atom","application/atom+xml"],["atomcat","application/atomcat+xml"],["atomdeleted","application/atomdeleted+xml"],["atomsvc","application/atomsvc+xml"],["atx","application/vnd.antix.game-component"],["au","audio/x-au"],["avi","video/x-msvideo"],["avif","image/avif"],["aw","application/applixware"],["azf","application/vnd.airzip.filesecure.azf"],["azs","application/vnd.airzip.filesecure.azs"],["azv","image/vnd.airzip.accelerator.azv"],["azw","application/vnd.amazon.ebook"],["b16","image/vnd.pco.b16"],["bat","application/x-msdownload"],["bcpio","application/x-bcpio"],["bdf","application/x-font-bdf"],["bdm","application/vnd.syncml.dm+wbxml"],["bdoc","application/x-bdoc"],["bed","application/vnd.realvnc.bed"],["bh2","application/vnd.fujitsu.oasysprs"],["bin","application/octet-stream"],["blb","application/x-blorb"],["blorb","application/x-blorb"],["bmi","application/vnd.bmi"],["bmml","application/vnd.balsamiq.bmml+xml"],["bmp","image/bmp"],["book","application/vnd.framemaker"],["box","application/vnd.previewsystems.box"],["boz","application/x-bzip2"],["bpk","application/octet-stream"],["bpmn","application/octet-stream"],["bsp","model/vnd.valve.source.compiled-map"],["btif","image/prs.btif"],["buffer","application/octet-stream"],["bz","application/x-bzip"],["bz2","application/x-bzip2"],["c","text/x-c"],["c4d","application/vnd.clonk.c4group"],["c4f","application/vnd.clonk.c4group"],["c4g","application/vnd.clonk.c4group"],["c4p","application/vnd.clonk.c4group"],["c4u","application/vnd.clonk.c4group"],["c11amc","application/vnd.cluetrust.cartomobile-config"],["c11amz","application/vnd.cluetrust.cartomobile-config-pkg"],["cab","application/vnd.ms-cab-compressed"],["caf","audio/x-caf"],["cap","application/vnd.tcpdump.pcap"],["car","application/vnd.curl.car"],["cat","application/vnd.ms-pki.seccat"],["cb7","application/x-cbr"],["cba","application/x-cbr"],["cbr","application/x-cbr"],["cbt","application/x-cbr"],["cbz","application/x-cbr"],["cc","text/x-c"],["cco","application/x-cocoa"],["cct","application/x-director"],["ccxml","application/ccxml+xml"],["cdbcmsg","application/vnd.contact.cmsg"],["cda","application/x-cdf"],["cdf","application/x-netcdf"],["cdfx","application/cdfx+xml"],["cdkey","application/vnd.mediastation.cdkey"],["cdmia","application/cdmi-capability"],["cdmic","application/cdmi-container"],["cdmid","application/cdmi-domain"],["cdmio","application/cdmi-object"],["cdmiq","application/cdmi-queue"],["cdr","application/cdr"],["cdx","chemical/x-cdx"],["cdxml","application/vnd.chemdraw+xml"],["cdy","application/vnd.cinderella"],["cer","application/pkix-cert"],["cfs","application/x-cfs-compressed"],["cgm","image/cgm"],["chat","application/x-chat"],["chm","application/vnd.ms-htmlhelp"],["chrt","application/vnd.kde.kchart"],["cif","chemical/x-cif"],["cii","application/vnd.anser-web-certificate-issue-initiation"],["cil","application/vnd.ms-artgalry"],["cjs","application/node"],["cla","application/vnd.claymore"],["class","application/octet-stream"],["clkk","application/vnd.crick.clicker.keyboard"],["clkp","application/vnd.crick.clicker.palette"],["clkt","application/vnd.crick.clicker.template"],["clkw","application/vnd.crick.clicker.wordbank"],["clkx","application/vnd.crick.clicker"],["clp","application/x-msclip"],["cmc","application/vnd.cosmocaller"],["cmdf","chemical/x-cmdf"],["cml","chemical/x-cml"],["cmp","application/vnd.yellowriver-custom-menu"],["cmx","image/x-cmx"],["cod","application/vnd.rim.cod"],["coffee","text/coffeescript"],["com","application/x-msdownload"],["conf","text/plain"],["cpio","application/x-cpio"],["cpp","text/x-c"],["cpt","application/mac-compactpro"],["crd","application/x-mscardfile"],["crl","application/pkix-crl"],["crt","application/x-x509-ca-cert"],["crx","application/x-chrome-extension"],["cryptonote","application/vnd.rig.cryptonote"],["csh","application/x-csh"],["csl","application/vnd.citationstyles.style+xml"],["csml","chemical/x-csml"],["csp","application/vnd.commonspace"],["csr","application/octet-stream"],["css","text/css"],["cst","application/x-director"],["csv","text/csv"],["cu","application/cu-seeme"],["curl","text/vnd.curl"],["cww","application/prs.cww"],["cxt","application/x-director"],["cxx","text/x-c"],["dae","model/vnd.collada+xml"],["daf","application/vnd.mobius.daf"],["dart","application/vnd.dart"],["dataless","application/vnd.fdsn.seed"],["davmount","application/davmount+xml"],["dbf","application/vnd.dbf"],["dbk","application/docbook+xml"],["dcr","application/x-director"],["dcurl","text/vnd.curl.dcurl"],["dd2","application/vnd.oma.dd2+xml"],["ddd","application/vnd.fujixerox.ddd"],["ddf","application/vnd.syncml.dmddf+xml"],["dds","image/vnd.ms-dds"],["deb","application/x-debian-package"],["def","text/plain"],["deploy","application/octet-stream"],["der","application/x-x509-ca-cert"],["dfac","application/vnd.dreamfactory"],["dgc","application/x-dgc-compressed"],["dic","text/x-c"],["dir","application/x-director"],["dis","application/vnd.mobius.dis"],["disposition-notification","message/disposition-notification"],["dist","application/octet-stream"],["distz","application/octet-stream"],["djv","image/vnd.djvu"],["djvu","image/vnd.djvu"],["dll","application/octet-stream"],["dmg","application/x-apple-diskimage"],["dmn","application/octet-stream"],["dmp","application/vnd.tcpdump.pcap"],["dms","application/octet-stream"],["dna","application/vnd.dna"],["doc","application/msword"],["docm","application/vnd.ms-word.template.macroEnabled.12"],["docx","application/vnd.openxmlformats-officedocument.wordprocessingml.document"],["dot","application/msword"],["dotm","application/vnd.ms-word.template.macroEnabled.12"],["dotx","application/vnd.openxmlformats-officedocument.wordprocessingml.template"],["dp","application/vnd.osgi.dp"],["dpg","application/vnd.dpgraph"],["dra","audio/vnd.dra"],["drle","image/dicom-rle"],["dsc","text/prs.lines.tag"],["dssc","application/dssc+der"],["dtb","application/x-dtbook+xml"],["dtd","application/xml-dtd"],["dts","audio/vnd.dts"],["dtshd","audio/vnd.dts.hd"],["dump","application/octet-stream"],["dvb","video/vnd.dvb.file"],["dvi","application/x-dvi"],["dwd","application/atsc-dwd+xml"],["dwf","model/vnd.dwf"],["dwg","image/vnd.dwg"],["dxf","image/vnd.dxf"],["dxp","application/vnd.spotfire.dxp"],["dxr","application/x-director"],["ear","application/java-archive"],["ecelp4800","audio/vnd.nuera.ecelp4800"],["ecelp7470","audio/vnd.nuera.ecelp7470"],["ecelp9600","audio/vnd.nuera.ecelp9600"],["ecma","application/ecmascript"],["edm","application/vnd.novadigm.edm"],["edx","application/vnd.novadigm.edx"],["efif","application/vnd.picsel"],["ei6","application/vnd.pg.osasli"],["elc","application/octet-stream"],["emf","image/emf"],["eml","message/rfc822"],["emma","application/emma+xml"],["emotionml","application/emotionml+xml"],["emz","application/x-msmetafile"],["eol","audio/vnd.digital-winds"],["eot","application/vnd.ms-fontobject"],["eps","application/postscript"],["epub","application/epub+zip"],["es","application/ecmascript"],["es3","application/vnd.eszigno3+xml"],["esa","application/vnd.osgi.subsystem"],["esf","application/vnd.epson.esf"],["et3","application/vnd.eszigno3+xml"],["etx","text/x-setext"],["eva","application/x-eva"],["evy","application/x-envoy"],["exe","application/octet-stream"],["exi","application/exi"],["exp","application/express"],["exr","image/aces"],["ext","application/vnd.novadigm.ext"],["ez","application/andrew-inset"],["ez2","application/vnd.ezpix-album"],["ez3","application/vnd.ezpix-package"],["f","text/x-fortran"],["f4v","video/mp4"],["f77","text/x-fortran"],["f90","text/x-fortran"],["fbs","image/vnd.fastbidsheet"],["fcdt","application/vnd.adobe.formscentral.fcdt"],["fcs","application/vnd.isac.fcs"],["fdf","application/vnd.fdf"],["fdt","application/fdt+xml"],["fe_launch","application/vnd.denovo.fcselayout-link"],["fg5","application/vnd.fujitsu.oasysgp"],["fgd","application/x-director"],["fh","image/x-freehand"],["fh4","image/x-freehand"],["fh5","image/x-freehand"],["fh7","image/x-freehand"],["fhc","image/x-freehand"],["fig","application/x-xfig"],["fits","image/fits"],["flac","audio/x-flac"],["fli","video/x-fli"],["flo","application/vnd.micrografx.flo"],["flv","video/x-flv"],["flw","application/vnd.kde.kivio"],["flx","text/vnd.fmi.flexstor"],["fly","text/vnd.fly"],["fm","application/vnd.framemaker"],["fnc","application/vnd.frogans.fnc"],["fo","application/vnd.software602.filler.form+xml"],["for","text/x-fortran"],["fpx","image/vnd.fpx"],["frame","application/vnd.framemaker"],["fsc","application/vnd.fsc.weblaunch"],["fst","image/vnd.fst"],["ftc","application/vnd.fluxtime.clip"],["fti","application/vnd.anser-web-funds-transfer-initiation"],["fvt","video/vnd.fvt"],["fxp","application/vnd.adobe.fxp"],["fxpl","application/vnd.adobe.fxp"],["fzs","application/vnd.fuzzysheet"],["g2w","application/vnd.geoplan"],["g3","image/g3fax"],["g3w","application/vnd.geospace"],["gac","application/vnd.groove-account"],["gam","application/x-tads"],["gbr","application/rpki-ghostbusters"],["gca","application/x-gca-compressed"],["gdl","model/vnd.gdl"],["gdoc","application/vnd.google-apps.document"],["geo","application/vnd.dynageo"],["geojson","application/geo+json"],["gex","application/vnd.geometry-explorer"],["ggb","application/vnd.geogebra.file"],["ggt","application/vnd.geogebra.tool"],["ghf","application/vnd.groove-help"],["gif","image/gif"],["gim","application/vnd.groove-identity-message"],["glb","model/gltf-binary"],["gltf","model/gltf+json"],["gml","application/gml+xml"],["gmx","application/vnd.gmx"],["gnumeric","application/x-gnumeric"],["gpg","application/gpg-keys"],["gph","application/vnd.flographit"],["gpx","application/gpx+xml"],["gqf","application/vnd.grafeq"],["gqs","application/vnd.grafeq"],["gram","application/srgs"],["gramps","application/x-gramps-xml"],["gre","application/vnd.geometry-explorer"],["grv","application/vnd.groove-injector"],["grxml","application/srgs+xml"],["gsf","application/x-font-ghostscript"],["gsheet","application/vnd.google-apps.spreadsheet"],["gslides","application/vnd.google-apps.presentation"],["gtar","application/x-gtar"],["gtm","application/vnd.groove-tool-message"],["gtw","model/vnd.gtw"],["gv","text/vnd.graphviz"],["gxf","application/gxf"],["gxt","application/vnd.geonext"],["gz","application/gzip"],["gzip","application/gzip"],["h","text/x-c"],["h261","video/h261"],["h263","video/h263"],["h264","video/h264"],["hal","application/vnd.hal+xml"],["hbci","application/vnd.hbci"],["hbs","text/x-handlebars-template"],["hdd","application/x-virtualbox-hdd"],["hdf","application/x-hdf"],["heic","image/heic"],["heics","image/heic-sequence"],["heif","image/heif"],["heifs","image/heif-sequence"],["hej2","image/hej2k"],["held","application/atsc-held+xml"],["hh","text/x-c"],["hjson","application/hjson"],["hlp","application/winhlp"],["hpgl","application/vnd.hp-hpgl"],["hpid","application/vnd.hp-hpid"],["hps","application/vnd.hp-hps"],["hqx","application/mac-binhex40"],["hsj2","image/hsj2"],["htc","text/x-component"],["htke","application/vnd.kenameaapp"],["htm","text/html"],["html","text/html"],["hvd","application/vnd.yamaha.hv-dic"],["hvp","application/vnd.yamaha.hv-voice"],["hvs","application/vnd.yamaha.hv-script"],["i2g","application/vnd.intergeo"],["icc","application/vnd.iccprofile"],["ice","x-conference/x-cooltalk"],["icm","application/vnd.iccprofile"],["ico","image/x-icon"],["ics","text/calendar"],["ief","image/ief"],["ifb","text/calendar"],["ifm","application/vnd.shana.informed.formdata"],["iges","model/iges"],["igl","application/vnd.igloader"],["igm","application/vnd.insors.igm"],["igs","model/iges"],["igx","application/vnd.micrografx.igx"],["iif","application/vnd.shana.informed.interchange"],["img","application/octet-stream"],["imp","application/vnd.accpac.simply.imp"],["ims","application/vnd.ms-ims"],["in","text/plain"],["ini","text/plain"],["ink","application/inkml+xml"],["inkml","application/inkml+xml"],["install","application/x-install-instructions"],["iota","application/vnd.astraea-software.iota"],["ipfix","application/ipfix"],["ipk","application/vnd.shana.informed.package"],["irm","application/vnd.ibm.rights-management"],["irp","application/vnd.irepository.package+xml"],["iso","application/x-iso9660-image"],["itp","application/vnd.shana.informed.formtemplate"],["its","application/its+xml"],["ivp","application/vnd.immervision-ivp"],["ivu","application/vnd.immervision-ivu"],["jad","text/vnd.sun.j2me.app-descriptor"],["jade","text/jade"],["jam","application/vnd.jam"],["jar","application/java-archive"],["jardiff","application/x-java-archive-diff"],["java","text/x-java-source"],["jhc","image/jphc"],["jisp","application/vnd.jisp"],["jls","image/jls"],["jlt","application/vnd.hp-jlyt"],["jng","image/x-jng"],["jnlp","application/x-java-jnlp-file"],["joda","application/vnd.joost.joda-archive"],["jp2","image/jp2"],["jpe","image/jpeg"],["jpeg","image/jpeg"],["jpf","image/jpx"],["jpg","image/jpeg"],["jpg2","image/jp2"],["jpgm","video/jpm"],["jpgv","video/jpeg"],["jph","image/jph"],["jpm","video/jpm"],["jpx","image/jpx"],["js","application/javascript"],["json","application/json"],["json5","application/json5"],["jsonld","application/ld+json"],["jsonl","application/jsonl"],["jsonml","application/jsonml+json"],["jsx","text/jsx"],["jxr","image/jxr"],["jxra","image/jxra"],["jxrs","image/jxrs"],["jxs","image/jxs"],["jxsc","image/jxsc"],["jxsi","image/jxsi"],["jxss","image/jxss"],["kar","audio/midi"],["karbon","application/vnd.kde.karbon"],["kdb","application/octet-stream"],["kdbx","application/x-keepass2"],["key","application/x-iwork-keynote-sffkey"],["kfo","application/vnd.kde.kformula"],["kia","application/vnd.kidspiration"],["kml","application/vnd.google-earth.kml+xml"],["kmz","application/vnd.google-earth.kmz"],["kne","application/vnd.kinar"],["knp","application/vnd.kinar"],["kon","application/vnd.kde.kontour"],["kpr","application/vnd.kde.kpresenter"],["kpt","application/vnd.kde.kpresenter"],["kpxx","application/vnd.ds-keypoint"],["ksp","application/vnd.kde.kspread"],["ktr","application/vnd.kahootz"],["ktx","image/ktx"],["ktx2","image/ktx2"],["ktz","application/vnd.kahootz"],["kwd","application/vnd.kde.kword"],["kwt","application/vnd.kde.kword"],["lasxml","application/vnd.las.las+xml"],["latex","application/x-latex"],["lbd","application/vnd.llamagraphics.life-balance.desktop"],["lbe","application/vnd.llamagraphics.life-balance.exchange+xml"],["les","application/vnd.hhe.lesson-player"],["less","text/less"],["lgr","application/lgr+xml"],["lha","application/octet-stream"],["link66","application/vnd.route66.link66+xml"],["list","text/plain"],["list3820","application/vnd.ibm.modcap"],["listafp","application/vnd.ibm.modcap"],["litcoffee","text/coffeescript"],["lnk","application/x-ms-shortcut"],["log","text/plain"],["lostxml","application/lost+xml"],["lrf","application/octet-stream"],["lrm","application/vnd.ms-lrm"],["ltf","application/vnd.frogans.ltf"],["lua","text/x-lua"],["luac","application/x-lua-bytecode"],["lvp","audio/vnd.lucent.voice"],["lwp","application/vnd.lotus-wordpro"],["lzh","application/octet-stream"],["m1v","video/mpeg"],["m2a","audio/mpeg"],["m2v","video/mpeg"],["m3a","audio/mpeg"],["m3u","text/plain"],["m3u8","application/vnd.apple.mpegurl"],["m4a","audio/x-m4a"],["m4p","application/mp4"],["m4s","video/iso.segment"],["m4u","application/vnd.mpegurl"],["m4v","video/x-m4v"],["m13","application/x-msmediaview"],["m14","application/x-msmediaview"],["m21","application/mp21"],["ma","application/mathematica"],["mads","application/mads+xml"],["maei","application/mmt-aei+xml"],["mag","application/vnd.ecowin.chart"],["maker","application/vnd.framemaker"],["man","text/troff"],["manifest","text/cache-manifest"],["map","application/json"],["mar","application/octet-stream"],["markdown","text/markdown"],["mathml","application/mathml+xml"],["mb","application/mathematica"],["mbk","application/vnd.mobius.mbk"],["mbox","application/mbox"],["mc1","application/vnd.medcalcdata"],["mcd","application/vnd.mcd"],["mcurl","text/vnd.curl.mcurl"],["md","text/markdown"],["mdb","application/x-msaccess"],["mdi","image/vnd.ms-modi"],["mdx","text/mdx"],["me","text/troff"],["mesh","model/mesh"],["meta4","application/metalink4+xml"],["metalink","application/metalink+xml"],["mets","application/mets+xml"],["mfm","application/vnd.mfmp"],["mft","application/rpki-manifest"],["mgp","application/vnd.osgeo.mapguide.package"],["mgz","application/vnd.proteus.magazine"],["mid","audio/midi"],["midi","audio/midi"],["mie","application/x-mie"],["mif","application/vnd.mif"],["mime","message/rfc822"],["mj2","video/mj2"],["mjp2","video/mj2"],["mjs","application/javascript"],["mk3d","video/x-matroska"],["mka","audio/x-matroska"],["mkd","text/x-markdown"],["mks","video/x-matroska"],["mkv","video/x-matroska"],["mlp","application/vnd.dolby.mlp"],["mmd","application/vnd.chipnuts.karaoke-mmd"],["mmf","application/vnd.smaf"],["mml","text/mathml"],["mmr","image/vnd.fujixerox.edmics-mmr"],["mng","video/x-mng"],["mny","application/x-msmoney"],["mobi","application/x-mobipocket-ebook"],["mods","application/mods+xml"],["mov","video/quicktime"],["movie","video/x-sgi-movie"],["mp2","audio/mpeg"],["mp2a","audio/mpeg"],["mp3","audio/mpeg"],["mp4","video/mp4"],["mp4a","audio/mp4"],["mp4s","application/mp4"],["mp4v","video/mp4"],["mp21","application/mp21"],["mpc","application/vnd.mophun.certificate"],["mpd","application/dash+xml"],["mpe","video/mpeg"],["mpeg","video/mpeg"],["mpg","video/mpeg"],["mpg4","video/mp4"],["mpga","audio/mpeg"],["mpkg","application/vnd.apple.installer+xml"],["mpm","application/vnd.blueice.multipass"],["mpn","application/vnd.mophun.application"],["mpp","application/vnd.ms-project"],["mpt","application/vnd.ms-project"],["mpy","application/vnd.ibm.minipay"],["mqy","application/vnd.mobius.mqy"],["mrc","application/marc"],["mrcx","application/marcxml+xml"],["ms","text/troff"],["mscml","application/mediaservercontrol+xml"],["mseed","application/vnd.fdsn.mseed"],["mseq","application/vnd.mseq"],["msf","application/vnd.epson.msf"],["msg","application/vnd.ms-outlook"],["msh","model/mesh"],["msi","application/x-msdownload"],["msl","application/vnd.mobius.msl"],["msm","application/octet-stream"],["msp","application/octet-stream"],["msty","application/vnd.muvee.style"],["mtl","model/mtl"],["mts","model/vnd.mts"],["mus","application/vnd.musician"],["musd","application/mmt-usd+xml"],["musicxml","application/vnd.recordare.musicxml+xml"],["mvb","application/x-msmediaview"],["mvt","application/vnd.mapbox-vector-tile"],["mwf","application/vnd.mfer"],["mxf","application/mxf"],["mxl","application/vnd.recordare.musicxml"],["mxmf","audio/mobile-xmf"],["mxml","application/xv+xml"],["mxs","application/vnd.triscape.mxs"],["mxu","video/vnd.mpegurl"],["n-gage","application/vnd.nokia.n-gage.symbian.install"],["n3","text/n3"],["nb","application/mathematica"],["nbp","application/vnd.wolfram.player"],["nc","application/x-netcdf"],["ncx","application/x-dtbncx+xml"],["nfo","text/x-nfo"],["ngdat","application/vnd.nokia.n-gage.data"],["nitf","application/vnd.nitf"],["nlu","application/vnd.neurolanguage.nlu"],["nml","application/vnd.enliven"],["nnd","application/vnd.noblenet-directory"],["nns","application/vnd.noblenet-sealer"],["nnw","application/vnd.noblenet-web"],["npx","image/vnd.net-fpx"],["nq","application/n-quads"],["nsc","application/x-conference"],["nsf","application/vnd.lotus-notes"],["nt","application/n-triples"],["ntf","application/vnd.nitf"],["numbers","application/x-iwork-numbers-sffnumbers"],["nzb","application/x-nzb"],["oa2","application/vnd.fujitsu.oasys2"],["oa3","application/vnd.fujitsu.oasys3"],["oas","application/vnd.fujitsu.oasys"],["obd","application/x-msbinder"],["obgx","application/vnd.openblox.game+xml"],["obj","model/obj"],["oda","application/oda"],["odb","application/vnd.oasis.opendocument.database"],["odc","application/vnd.oasis.opendocument.chart"],["odf","application/vnd.oasis.opendocument.formula"],["odft","application/vnd.oasis.opendocument.formula-template"],["odg","application/vnd.oasis.opendocument.graphics"],["odi","application/vnd.oasis.opendocument.image"],["odm","application/vnd.oasis.opendocument.text-master"],["odp","application/vnd.oasis.opendocument.presentation"],["ods","application/vnd.oasis.opendocument.spreadsheet"],["odt","application/vnd.oasis.opendocument.text"],["oga","audio/ogg"],["ogex","model/vnd.opengex"],["ogg","audio/ogg"],["ogv","video/ogg"],["ogx","application/ogg"],["omdoc","application/omdoc+xml"],["onepkg","application/onenote"],["onetmp","application/onenote"],["onetoc","application/onenote"],["onetoc2","application/onenote"],["opf","application/oebps-package+xml"],["opml","text/x-opml"],["oprc","application/vnd.palm"],["opus","audio/ogg"],["org","text/x-org"],["osf","application/vnd.yamaha.openscoreformat"],["osfpvg","application/vnd.yamaha.openscoreformat.osfpvg+xml"],["osm","application/vnd.openstreetmap.data+xml"],["otc","application/vnd.oasis.opendocument.chart-template"],["otf","font/otf"],["otg","application/vnd.oasis.opendocument.graphics-template"],["oth","application/vnd.oasis.opendocument.text-web"],["oti","application/vnd.oasis.opendocument.image-template"],["otp","application/vnd.oasis.opendocument.presentation-template"],["ots","application/vnd.oasis.opendocument.spreadsheet-template"],["ott","application/vnd.oasis.opendocument.text-template"],["ova","application/x-virtualbox-ova"],["ovf","application/x-virtualbox-ovf"],["owl","application/rdf+xml"],["oxps","application/oxps"],["oxt","application/vnd.openofficeorg.extension"],["p","text/x-pascal"],["p7a","application/x-pkcs7-signature"],["p7b","application/x-pkcs7-certificates"],["p7c","application/pkcs7-mime"],["p7m","application/pkcs7-mime"],["p7r","application/x-pkcs7-certreqresp"],["p7s","application/pkcs7-signature"],["p8","application/pkcs8"],["p10","application/x-pkcs10"],["p12","application/x-pkcs12"],["pac","application/x-ns-proxy-autoconfig"],["pages","application/x-iwork-pages-sffpages"],["pas","text/x-pascal"],["paw","application/vnd.pawaafile"],["pbd","application/vnd.powerbuilder6"],["pbm","image/x-portable-bitmap"],["pcap","application/vnd.tcpdump.pcap"],["pcf","application/x-font-pcf"],["pcl","application/vnd.hp-pcl"],["pclxl","application/vnd.hp-pclxl"],["pct","image/x-pict"],["pcurl","application/vnd.curl.pcurl"],["pcx","image/x-pcx"],["pdb","application/x-pilot"],["pde","text/x-processing"],["pdf","application/pdf"],["pem","application/x-x509-user-cert"],["pfa","application/x-font-type1"],["pfb","application/x-font-type1"],["pfm","application/x-font-type1"],["pfr","application/font-tdpfr"],["pfx","application/x-pkcs12"],["pgm","image/x-portable-graymap"],["pgn","application/x-chess-pgn"],["pgp","application/pgp"],["php","application/x-httpd-php"],["php3","application/x-httpd-php"],["php4","application/x-httpd-php"],["phps","application/x-httpd-php-source"],["phtml","application/x-httpd-php"],["pic","image/x-pict"],["pkg","application/octet-stream"],["pki","application/pkixcmp"],["pkipath","application/pkix-pkipath"],["pkpass","application/vnd.apple.pkpass"],["pl","application/x-perl"],["plb","application/vnd.3gpp.pic-bw-large"],["plc","application/vnd.mobius.plc"],["plf","application/vnd.pocketlearn"],["pls","application/pls+xml"],["pm","application/x-perl"],["pml","application/vnd.ctc-posml"],["png","image/png"],["pnm","image/x-portable-anymap"],["portpkg","application/vnd.macports.portpkg"],["pot","application/vnd.ms-powerpoint"],["potm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["potx","application/vnd.openxmlformats-officedocument.presentationml.template"],["ppa","application/vnd.ms-powerpoint"],["ppam","application/vnd.ms-powerpoint.addin.macroEnabled.12"],["ppd","application/vnd.cups-ppd"],["ppm","image/x-portable-pixmap"],["pps","application/vnd.ms-powerpoint"],["ppsm","application/vnd.ms-powerpoint.slideshow.macroEnabled.12"],["ppsx","application/vnd.openxmlformats-officedocument.presentationml.slideshow"],["ppt","application/powerpoint"],["pptm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["pptx","application/vnd.openxmlformats-officedocument.presentationml.presentation"],["pqa","application/vnd.palm"],["prc","application/x-pilot"],["pre","application/vnd.lotus-freelance"],["prf","application/pics-rules"],["provx","application/provenance+xml"],["ps","application/postscript"],["psb","application/vnd.3gpp.pic-bw-small"],["psd","application/x-photoshop"],["psf","application/x-font-linux-psf"],["pskcxml","application/pskc+xml"],["pti","image/prs.pti"],["ptid","application/vnd.pvi.ptid1"],["pub","application/x-mspublisher"],["pvb","application/vnd.3gpp.pic-bw-var"],["pwn","application/vnd.3m.post-it-notes"],["pya","audio/vnd.ms-playready.media.pya"],["pyv","video/vnd.ms-playready.media.pyv"],["qam","application/vnd.epson.quickanime"],["qbo","application/vnd.intu.qbo"],["qfx","application/vnd.intu.qfx"],["qps","application/vnd.publishare-delta-tree"],["qt","video/quicktime"],["qwd","application/vnd.quark.quarkxpress"],["qwt","application/vnd.quark.quarkxpress"],["qxb","application/vnd.quark.quarkxpress"],["qxd","application/vnd.quark.quarkxpress"],["qxl","application/vnd.quark.quarkxpress"],["qxt","application/vnd.quark.quarkxpress"],["ra","audio/x-realaudio"],["ram","audio/x-pn-realaudio"],["raml","application/raml+yaml"],["rapd","application/route-apd+xml"],["rar","application/x-rar"],["ras","image/x-cmu-raster"],["rcprofile","application/vnd.ipunplugged.rcprofile"],["rdf","application/rdf+xml"],["rdz","application/vnd.data-vision.rdz"],["relo","application/p2p-overlay+xml"],["rep","application/vnd.businessobjects"],["res","application/x-dtbresource+xml"],["rgb","image/x-rgb"],["rif","application/reginfo+xml"],["rip","audio/vnd.rip"],["ris","application/x-research-info-systems"],["rl","application/resource-lists+xml"],["rlc","image/vnd.fujixerox.edmics-rlc"],["rld","application/resource-lists-diff+xml"],["rm","audio/x-pn-realaudio"],["rmi","audio/midi"],["rmp","audio/x-pn-realaudio-plugin"],["rms","application/vnd.jcp.javame.midlet-rms"],["rmvb","application/vnd.rn-realmedia-vbr"],["rnc","application/relax-ng-compact-syntax"],["rng","application/xml"],["roa","application/rpki-roa"],["roff","text/troff"],["rp9","application/vnd.cloanto.rp9"],["rpm","audio/x-pn-realaudio-plugin"],["rpss","application/vnd.nokia.radio-presets"],["rpst","application/vnd.nokia.radio-preset"],["rq","application/sparql-query"],["rs","application/rls-services+xml"],["rsa","application/x-pkcs7"],["rsat","application/atsc-rsat+xml"],["rsd","application/rsd+xml"],["rsheet","application/urc-ressheet+xml"],["rss","application/rss+xml"],["rtf","text/rtf"],["rtx","text/richtext"],["run","application/x-makeself"],["rusd","application/route-usd+xml"],["rv","video/vnd.rn-realvideo"],["s","text/x-asm"],["s3m","audio/s3m"],["saf","application/vnd.yamaha.smaf-audio"],["sass","text/x-sass"],["sbml","application/sbml+xml"],["sc","application/vnd.ibm.secure-container"],["scd","application/x-msschedule"],["scm","application/vnd.lotus-screencam"],["scq","application/scvp-cv-request"],["scs","application/scvp-cv-response"],["scss","text/x-scss"],["scurl","text/vnd.curl.scurl"],["sda","application/vnd.stardivision.draw"],["sdc","application/vnd.stardivision.calc"],["sdd","application/vnd.stardivision.impress"],["sdkd","application/vnd.solent.sdkm+xml"],["sdkm","application/vnd.solent.sdkm+xml"],["sdp","application/sdp"],["sdw","application/vnd.stardivision.writer"],["sea","application/octet-stream"],["see","application/vnd.seemail"],["seed","application/vnd.fdsn.seed"],["sema","application/vnd.sema"],["semd","application/vnd.semd"],["semf","application/vnd.semf"],["senmlx","application/senml+xml"],["sensmlx","application/sensml+xml"],["ser","application/java-serialized-object"],["setpay","application/set-payment-initiation"],["setreg","application/set-registration-initiation"],["sfd-hdstx","application/vnd.hydrostatix.sof-data"],["sfs","application/vnd.spotfire.sfs"],["sfv","text/x-sfv"],["sgi","image/sgi"],["sgl","application/vnd.stardivision.writer-global"],["sgm","text/sgml"],["sgml","text/sgml"],["sh","application/x-sh"],["shar","application/x-shar"],["shex","text/shex"],["shf","application/shf+xml"],["shtml","text/html"],["sid","image/x-mrsid-image"],["sieve","application/sieve"],["sig","application/pgp-signature"],["sil","audio/silk"],["silo","model/mesh"],["sis","application/vnd.symbian.install"],["sisx","application/vnd.symbian.install"],["sit","application/x-stuffit"],["sitx","application/x-stuffitx"],["siv","application/sieve"],["skd","application/vnd.koan"],["skm","application/vnd.koan"],["skp","application/vnd.koan"],["skt","application/vnd.koan"],["sldm","application/vnd.ms-powerpoint.slide.macroenabled.12"],["sldx","application/vnd.openxmlformats-officedocument.presentationml.slide"],["slim","text/slim"],["slm","text/slim"],["sls","application/route-s-tsid+xml"],["slt","application/vnd.epson.salt"],["sm","application/vnd.stepmania.stepchart"],["smf","application/vnd.stardivision.math"],["smi","application/smil"],["smil","application/smil"],["smv","video/x-smv"],["smzip","application/vnd.stepmania.package"],["snd","audio/basic"],["snf","application/x-font-snf"],["so","application/octet-stream"],["spc","application/x-pkcs7-certificates"],["spdx","text/spdx"],["spf","application/vnd.yamaha.smaf-phrase"],["spl","application/x-futuresplash"],["spot","text/vnd.in3d.spot"],["spp","application/scvp-vp-response"],["spq","application/scvp-vp-request"],["spx","audio/ogg"],["sql","application/x-sql"],["src","application/x-wais-source"],["srt","application/x-subrip"],["sru","application/sru+xml"],["srx","application/sparql-results+xml"],["ssdl","application/ssdl+xml"],["sse","application/vnd.kodak-descriptor"],["ssf","application/vnd.epson.ssf"],["ssml","application/ssml+xml"],["sst","application/octet-stream"],["st","application/vnd.sailingtracker.track"],["stc","application/vnd.sun.xml.calc.template"],["std","application/vnd.sun.xml.draw.template"],["stf","application/vnd.wt.stf"],["sti","application/vnd.sun.xml.impress.template"],["stk","application/hyperstudio"],["stl","model/stl"],["stpx","model/step+xml"],["stpxz","model/step-xml+zip"],["stpz","model/step+zip"],["str","application/vnd.pg.format"],["stw","application/vnd.sun.xml.writer.template"],["styl","text/stylus"],["stylus","text/stylus"],["sub","text/vnd.dvb.subtitle"],["sus","application/vnd.sus-calendar"],["susp","application/vnd.sus-calendar"],["sv4cpio","application/x-sv4cpio"],["sv4crc","application/x-sv4crc"],["svc","application/vnd.dvb.service"],["svd","application/vnd.svd"],["svg","image/svg+xml"],["svgz","image/svg+xml"],["swa","application/x-director"],["swf","application/x-shockwave-flash"],["swi","application/vnd.aristanetworks.swi"],["swidtag","application/swid+xml"],["sxc","application/vnd.sun.xml.calc"],["sxd","application/vnd.sun.xml.draw"],["sxg","application/vnd.sun.xml.writer.global"],["sxi","application/vnd.sun.xml.impress"],["sxm","application/vnd.sun.xml.math"],["sxw","application/vnd.sun.xml.writer"],["t","text/troff"],["t3","application/x-t3vm-image"],["t38","image/t38"],["taglet","application/vnd.mynfc"],["tao","application/vnd.tao.intent-module-archive"],["tap","image/vnd.tencent.tap"],["tar","application/x-tar"],["tcap","application/vnd.3gpp2.tcap"],["tcl","application/x-tcl"],["td","application/urc-targetdesc+xml"],["teacher","application/vnd.smart.teacher"],["tei","application/tei+xml"],["teicorpus","application/tei+xml"],["tex","application/x-tex"],["texi","application/x-texinfo"],["texinfo","application/x-texinfo"],["text","text/plain"],["tfi","application/thraud+xml"],["tfm","application/x-tex-tfm"],["tfx","image/tiff-fx"],["tga","image/x-tga"],["tgz","application/x-tar"],["thmx","application/vnd.ms-officetheme"],["tif","image/tiff"],["tiff","image/tiff"],["tk","application/x-tcl"],["tmo","application/vnd.tmobile-livetv"],["toml","application/toml"],["torrent","application/x-bittorrent"],["tpl","application/vnd.groove-tool-template"],["tpt","application/vnd.trid.tpt"],["tr","text/troff"],["tra","application/vnd.trueapp"],["trig","application/trig"],["trm","application/x-msterminal"],["ts","video/mp2t"],["tsd","application/timestamped-data"],["tsv","text/tab-separated-values"],["ttc","font/collection"],["ttf","font/ttf"],["ttl","text/turtle"],["ttml","application/ttml+xml"],["twd","application/vnd.simtech-mindmapper"],["twds","application/vnd.simtech-mindmapper"],["txd","application/vnd.genomatix.tuxedo"],["txf","application/vnd.mobius.txf"],["txt","text/plain"],["u8dsn","message/global-delivery-status"],["u8hdr","message/global-headers"],["u8mdn","message/global-disposition-notification"],["u8msg","message/global"],["u32","application/x-authorware-bin"],["ubj","application/ubjson"],["udeb","application/x-debian-package"],["ufd","application/vnd.ufdl"],["ufdl","application/vnd.ufdl"],["ulx","application/x-glulx"],["umj","application/vnd.umajin"],["unityweb","application/vnd.unity"],["uoml","application/vnd.uoml+xml"],["uri","text/uri-list"],["uris","text/uri-list"],["urls","text/uri-list"],["usdz","model/vnd.usdz+zip"],["ustar","application/x-ustar"],["utz","application/vnd.uiq.theme"],["uu","text/x-uuencode"],["uva","audio/vnd.dece.audio"],["uvd","application/vnd.dece.data"],["uvf","application/vnd.dece.data"],["uvg","image/vnd.dece.graphic"],["uvh","video/vnd.dece.hd"],["uvi","image/vnd.dece.graphic"],["uvm","video/vnd.dece.mobile"],["uvp","video/vnd.dece.pd"],["uvs","video/vnd.dece.sd"],["uvt","application/vnd.dece.ttml+xml"],["uvu","video/vnd.uvvu.mp4"],["uvv","video/vnd.dece.video"],["uvva","audio/vnd.dece.audio"],["uvvd","application/vnd.dece.data"],["uvvf","application/vnd.dece.data"],["uvvg","image/vnd.dece.graphic"],["uvvh","video/vnd.dece.hd"],["uvvi","image/vnd.dece.graphic"],["uvvm","video/vnd.dece.mobile"],["uvvp","video/vnd.dece.pd"],["uvvs","video/vnd.dece.sd"],["uvvt","application/vnd.dece.ttml+xml"],["uvvu","video/vnd.uvvu.mp4"],["uvvv","video/vnd.dece.video"],["uvvx","application/vnd.dece.unspecified"],["uvvz","application/vnd.dece.zip"],["uvx","application/vnd.dece.unspecified"],["uvz","application/vnd.dece.zip"],["vbox","application/x-virtualbox-vbox"],["vbox-extpack","application/x-virtualbox-vbox-extpack"],["vcard","text/vcard"],["vcd","application/x-cdlink"],["vcf","text/x-vcard"],["vcg","application/vnd.groove-vcard"],["vcs","text/x-vcalendar"],["vcx","application/vnd.vcx"],["vdi","application/x-virtualbox-vdi"],["vds","model/vnd.sap.vds"],["vhd","application/x-virtualbox-vhd"],["vis","application/vnd.visionary"],["viv","video/vnd.vivo"],["vlc","application/videolan"],["vmdk","application/x-virtualbox-vmdk"],["vob","video/x-ms-vob"],["vor","application/vnd.stardivision.writer"],["vox","application/x-authorware-bin"],["vrml","model/vrml"],["vsd","application/vnd.visio"],["vsf","application/vnd.vsf"],["vss","application/vnd.visio"],["vst","application/vnd.visio"],["vsw","application/vnd.visio"],["vtf","image/vnd.valve.source.texture"],["vtt","text/vtt"],["vtu","model/vnd.vtu"],["vxml","application/voicexml+xml"],["w3d","application/x-director"],["wad","application/x-doom"],["wadl","application/vnd.sun.wadl+xml"],["war","application/java-archive"],["wasm","application/wasm"],["wav","audio/x-wav"],["wax","audio/x-ms-wax"],["wbmp","image/vnd.wap.wbmp"],["wbs","application/vnd.criticaltools.wbs+xml"],["wbxml","application/wbxml"],["wcm","application/vnd.ms-works"],["wdb","application/vnd.ms-works"],["wdp","image/vnd.ms-photo"],["weba","audio/webm"],["webapp","application/x-web-app-manifest+json"],["webm","video/webm"],["webmanifest","application/manifest+json"],["webp","image/webp"],["wg","application/vnd.pmi.widget"],["wgt","application/widget"],["wks","application/vnd.ms-works"],["wm","video/x-ms-wm"],["wma","audio/x-ms-wma"],["wmd","application/x-ms-wmd"],["wmf","image/wmf"],["wml","text/vnd.wap.wml"],["wmlc","application/wmlc"],["wmls","text/vnd.wap.wmlscript"],["wmlsc","application/vnd.wap.wmlscriptc"],["wmv","video/x-ms-wmv"],["wmx","video/x-ms-wmx"],["wmz","application/x-msmetafile"],["woff","font/woff"],["woff2","font/woff2"],["word","application/msword"],["wpd","application/vnd.wordperfect"],["wpl","application/vnd.ms-wpl"],["wps","application/vnd.ms-works"],["wqd","application/vnd.wqd"],["wri","application/x-mswrite"],["wrl","model/vrml"],["wsc","message/vnd.wfa.wsc"],["wsdl","application/wsdl+xml"],["wspolicy","application/wspolicy+xml"],["wtb","application/vnd.webturbo"],["wvx","video/x-ms-wvx"],["x3d","model/x3d+xml"],["x3db","model/x3d+fastinfoset"],["x3dbz","model/x3d+binary"],["x3dv","model/x3d-vrml"],["x3dvz","model/x3d+vrml"],["x3dz","model/x3d+xml"],["x32","application/x-authorware-bin"],["x_b","model/vnd.parasolid.transmit.binary"],["x_t","model/vnd.parasolid.transmit.text"],["xaml","application/xaml+xml"],["xap","application/x-silverlight-app"],["xar","application/vnd.xara"],["xav","application/xcap-att+xml"],["xbap","application/x-ms-xbap"],["xbd","application/vnd.fujixerox.docuworks.binder"],["xbm","image/x-xbitmap"],["xca","application/xcap-caps+xml"],["xcs","application/calendar+xml"],["xdf","application/xcap-diff+xml"],["xdm","application/vnd.syncml.dm+xml"],["xdp","application/vnd.adobe.xdp+xml"],["xdssc","application/dssc+xml"],["xdw","application/vnd.fujixerox.docuworks"],["xel","application/xcap-el+xml"],["xenc","application/xenc+xml"],["xer","application/patch-ops-error+xml"],["xfdf","application/vnd.adobe.xfdf"],["xfdl","application/vnd.xfdl"],["xht","application/xhtml+xml"],["xhtml","application/xhtml+xml"],["xhvml","application/xv+xml"],["xif","image/vnd.xiff"],["xl","application/excel"],["xla","application/vnd.ms-excel"],["xlam","application/vnd.ms-excel.addin.macroEnabled.12"],["xlc","application/vnd.ms-excel"],["xlf","application/xliff+xml"],["xlm","application/vnd.ms-excel"],["xls","application/vnd.ms-excel"],["xlsb","application/vnd.ms-excel.sheet.binary.macroEnabled.12"],["xlsm","application/vnd.ms-excel.sheet.macroEnabled.12"],["xlsx","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"],["xlt","application/vnd.ms-excel"],["xltm","application/vnd.ms-excel.template.macroEnabled.12"],["xltx","application/vnd.openxmlformats-officedocument.spreadsheetml.template"],["xlw","application/vnd.ms-excel"],["xm","audio/xm"],["xml","application/xml"],["xns","application/xcap-ns+xml"],["xo","application/vnd.olpc-sugar"],["xop","application/xop+xml"],["xpi","application/x-xpinstall"],["xpl","application/xproc+xml"],["xpm","image/x-xpixmap"],["xpr","application/vnd.is-xpr"],["xps","application/vnd.ms-xpsdocument"],["xpw","application/vnd.intercon.formnet"],["xpx","application/vnd.intercon.formnet"],["xsd","application/xml"],["xsl","application/xml"],["xslt","application/xslt+xml"],["xsm","application/vnd.syncml+xml"],["xspf","application/xspf+xml"],["xul","application/vnd.mozilla.xul+xml"],["xvm","application/xv+xml"],["xvml","application/xv+xml"],["xwd","image/x-xwindowdump"],["xyz","chemical/x-xyz"],["xz","application/x-xz"],["yaml","text/yaml"],["yang","application/yang"],["yin","application/yin+xml"],["yml","text/yaml"],["ymp","text/x-suse-ymp"],["z","application/x-compress"],["z1","application/x-zmachine"],["z2","application/x-zmachine"],["z3","application/x-zmachine"],["z4","application/x-zmachine"],["z5","application/x-zmachine"],["z6","application/x-zmachine"],["z7","application/x-zmachine"],["z8","application/x-zmachine"],["zaz","application/vnd.zzazz.deck+xml"],["zip","application/zip"],["zir","application/vnd.zul"],["zirz","application/vnd.zul"],["zmm","application/vnd.handheld-entertainment+xml"],["zsh","text/x-scriptzsh"]]);function Pe(e,a,n){const i=fn(e),{webkitRelativePath:l}=e,r=typeof a=="string"?a:typeof l=="string"&&l.length>0?l:`./${e.name}`;return typeof i.path!="string"&&Ea(i,"path",r),Ea(i,"relativePath",r),i}function fn(e){const{name:a}=e;if(a&&a.lastIndexOf(".")!==-1&&!e.type){const i=a.split(".").pop().toLowerCase(),l=un.get(i);l&&Object.defineProperty(e,"type",{value:l,writable:!1,configurable:!1,enumerable:!0})}return e}function Ea(e,a,n){Object.defineProperty(e,a,{value:n,writable:!1,configurable:!1,enumerable:!0})}const xn=[".DS_Store","Thumbs.db"];function vn(e){return ye(this,void 0,void 0,function*(){return Ie(e)&&gn(e.dataTransfer)?jn(e.dataTransfer,e.type):hn(e)?bn(e):Array.isArray(e)&&e.every(a=>"getFile"in a&&typeof a.getFile=="function")?yn(e):[]})}function gn(e){return Ie(e)}function hn(e){return Ie(e)&&Ie(e.target)}function Ie(e){return typeof e=="object"&&e!==null}function bn(e){return ua(e.target.files).map(a=>Pe(a))}function yn(e){return ye(this,void 0,void 0,function*(){return(yield Promise.all(e.map(n=>n.getFile()))).map(n=>Pe(n))})}function jn(e,a){return ye(this,void 0,void 0,function*(){if(e.items){const n=ua(e.items).filter(l=>l.kind==="file");if(a!=="drop")return n;const i=yield Promise.all(n.map(wn));return Fa(mt(i))}return Fa(ua(e.files).map(n=>Pe(n)))})}function Fa(e){return e.filter(a=>xn.indexOf(a.name)===-1)}function ua(e){if(e===null)return[];const a=[];for(let n=0;n<e.length;n++){const i=e[n];a.push(i)}return a}function wn(e){if(typeof e.webkitGetAsEntry!="function")return Ta(e);const a=e.webkitGetAsEntry();return a&&a.isDirectory?ut(a):Ta(e,a)}function mt(e){return e.reduce((a,n)=>[...a,...Array.isArray(n)?mt(n):[n]],[])}function Ta(e,a){return ye(this,void 0,void 0,function*(){var n;if(globalThis.isSecureContext&&typeof e.getAsFileSystemHandle=="function"){const r=yield e.getAsFileSystemHandle();if(r===null)throw new Error(`${e} is not a File`);if(r!==void 0){const c=yield r.getFile();return c.handle=r,Pe(c)}}const i=e.getAsFile();if(!i)throw new Error(`${e} is not a File`);return Pe(i,(n=a==null?void 0:a.fullPath)!==null&&n!==void 0?n:void 0)})}function kn(e){return ye(this,void 0,void 0,function*(){return e.isDirectory?ut(e):Dn(e)})}function ut(e){const a=e.createReader();return new Promise((n,i)=>{const l=[];function r(){a.readEntries(c=>ye(this,void 0,void 0,function*(){if(c.length){const p=Promise.all(c.map(kn));l.push(p),r()}else try{const p=yield Promise.all(l);n(p)}catch(p){i(p)}}),c=>{i(c)})}r()})}function Dn(e){return ye(this,void 0,void 0,function*(){return new Promise((a,n)=>{e.file(i=>{const l=Pe(i,e.fullPath);a(l)},i=>{n(i)})})})}var Ae={},Oa;function Nn(){return Oa||(Oa=1,Ae.__esModule=!0,Ae.default=function(e,a){if(e&&a){var n=Array.isArray(a)?a:a.split(",");if(n.length===0)return!0;var i=e.name||"",l=(e.type||"").toLowerCase(),r=l.replace(/\/.*$/,"");return n.some(function(c){var p=c.trim().toLowerCase();return p.charAt(0)==="."?i.toLowerCase().endsWith(p):p.endsWith("/*")?r===p.replace(/\/.*$/,""):l===p})}return!0}),Ae}var zn=Nn();const na=et(zn);function Aa(e){return Sn(e)||Pn(e)||xt(e)||Cn()}function Cn(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Pn(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function Sn(e){if(Array.isArray(e))return fa(e)}function Ra(e,a){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);a&&(i=i.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),n.push.apply(n,i)}return n}function Ma(e){for(var a=1;a<arguments.length;a++){var n=arguments[a]!=null?arguments[a]:{};a%2?Ra(Object(n),!0).forEach(function(i){ft(e,i,n[i])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ra(Object(n)).forEach(function(i){Object.defineProperty(e,i,Object.getOwnPropertyDescriptor(n,i))})}return e}function ft(e,a,n){return a in e?Object.defineProperty(e,a,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[a]=n,e}function Fe(e,a){return Fn(e)||En(e,a)||xt(e,a)||_n()}function _n(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function xt(e,a){if(e){if(typeof e=="string")return fa(e,a);var n=Object.prototype.toString.call(e).slice(8,-1);if(n==="Object"&&e.constructor&&(n=e.constructor.name),n==="Map"||n==="Set")return Array.from(e);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return fa(e,a)}}function fa(e,a){(a==null||a>e.length)&&(a=e.length);for(var n=0,i=new Array(a);n<a;n++)i[n]=e[n];return i}function En(e,a){var n=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(n!=null){var i=[],l=!0,r=!1,c,p;try{for(n=n.call(e);!(l=(c=n.next()).done)&&(i.push(c.value),!(a&&i.length===a));l=!0);}catch(k){r=!0,p=k}finally{try{!l&&n.return!=null&&n.return()}finally{if(r)throw p}}return i}}function Fn(e){if(Array.isArray(e))return e}var Tn=typeof na=="function"?na:na.default,On="file-invalid-type",An="file-too-large",Rn="file-too-small",Mn="too-many-files",In=function(){var a=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",n=a.split(","),i=n.length>1?"one of ".concat(n.join(", ")):n[0];return{code:On,message:"File type must be ".concat(i)}},Ia=function(a){return{code:An,message:"File is larger than ".concat(a," ").concat(a===1?"byte":"bytes")}},qa=function(a){return{code:Rn,message:"File is smaller than ".concat(a," ").concat(a===1?"byte":"bytes")}},qn={code:Mn,message:"Too many files"};function vt(e,a){var n=e.type==="application/x-moz-file"||Tn(e,a);return[n,n?null:In(a)]}function gt(e,a,n){if(be(e.size))if(be(a)&&be(n)){if(e.size>n)return[!1,Ia(n)];if(e.size<a)return[!1,qa(a)]}else{if(be(a)&&e.size<a)return[!1,qa(a)];if(be(n)&&e.size>n)return[!1,Ia(n)]}return[!0,null]}function be(e){return e!=null}function Ln(e){var a=e.files,n=e.accept,i=e.minSize,l=e.maxSize,r=e.multiple,c=e.maxFiles,p=e.validator;return!r&&a.length>1||r&&c>=1&&a.length>c?!1:a.every(function(k){var x=vt(k,n),g=Fe(x,1),F=g[0],y=gt(k,i,l),j=Fe(y,1),C=j[0],L=p?p(k):null;return F&&C&&!L})}function qe(e){return typeof e.isPropagationStopped=="function"?e.isPropagationStopped():typeof e.cancelBubble<"u"?e.cancelBubble:!1}function Re(e){return e.dataTransfer?Array.prototype.some.call(e.dataTransfer.types,function(a){return a==="Files"||a==="application/x-moz-file"}):!!e.target&&!!e.target.files}function La(e){e.preventDefault()}function Bn(e){return e.indexOf("MSIE")!==-1||e.indexOf("Trident/")!==-1}function Un(e){return e.indexOf("Edge/")!==-1}function $n(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:window.navigator.userAgent;return Bn(e)||Un(e)}function Z(){for(var e=arguments.length,a=new Array(e),n=0;n<e;n++)a[n]=arguments[n];return function(i){for(var l=arguments.length,r=new Array(l>1?l-1:0),c=1;c<l;c++)r[c-1]=arguments[c];return a.some(function(p){return!qe(i)&&p&&p.apply(void 0,[i].concat(r)),qe(i)})}}function Hn(){return"showOpenFilePicker"in window}function Kn(e){if(be(e)){var a=Object.entries(e).filter(function(n){var i=Fe(n,2),l=i[0],r=i[1],c=!0;return ht(l)||(console.warn('Skipped "'.concat(l,'" because it is not a valid MIME type. Check https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types/Common_types for a list of valid MIME types.')),c=!1),(!Array.isArray(r)||!r.every(bt))&&(console.warn('Skipped "'.concat(l,'" because an invalid file extension was provided.')),c=!1),c}).reduce(function(n,i){var l=Fe(i,2),r=l[0],c=l[1];return Ma(Ma({},n),{},ft({},r,c))},{});return[{description:"Files",accept:a}]}return e}function Wn(e){if(be(e))return Object.entries(e).reduce(function(a,n){var i=Fe(n,2),l=i[0],r=i[1];return[].concat(Aa(a),[l],Aa(r))},[]).filter(function(a){return ht(a)||bt(a)}).join(",")}function Gn(e){return e instanceof DOMException&&(e.name==="AbortError"||e.code===e.ABORT_ERR)}function Vn(e){return e instanceof DOMException&&(e.name==="SecurityError"||e.code===e.SECURITY_ERR)}function ht(e){return e==="audio/*"||e==="video/*"||e==="image/*"||e==="text/*"||e==="application/*"||/\w+\/[-+.\w]+/g.test(e)}function bt(e){return/^.*\.[\w]+$/.test(e)}var Yn=["children"],Jn=["open"],Qn=["refKey","role","onKeyDown","onFocus","onBlur","onClick","onDragEnter","onDragOver","onDragLeave","onDrop"],Xn=["refKey","onChange","onClick"];function Zn(e){return ti(e)||ai(e)||yt(e)||ei()}function ei(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function ai(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function ti(e){if(Array.isArray(e))return xa(e)}function ia(e,a){return oi(e)||ii(e,a)||yt(e,a)||ni()}function ni(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function yt(e,a){if(e){if(typeof e=="string")return xa(e,a);var n=Object.prototype.toString.call(e).slice(8,-1);if(n==="Object"&&e.constructor&&(n=e.constructor.name),n==="Map"||n==="Set")return Array.from(e);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return xa(e,a)}}function xa(e,a){(a==null||a>e.length)&&(a=e.length);for(var n=0,i=new Array(a);n<a;n++)i[n]=e[n];return i}function ii(e,a){var n=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(n!=null){var i=[],l=!0,r=!1,c,p;try{for(n=n.call(e);!(l=(c=n.next()).done)&&(i.push(c.value),!(a&&i.length===a));l=!0);}catch(k){r=!0,p=k}finally{try{!l&&n.return!=null&&n.return()}finally{if(r)throw p}}return i}}function oi(e){if(Array.isArray(e))return e}function Ba(e,a){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);a&&(i=i.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),n.push.apply(n,i)}return n}function q(e){for(var a=1;a<arguments.length;a++){var n=arguments[a]!=null?arguments[a]:{};a%2?Ba(Object(n),!0).forEach(function(i){va(e,i,n[i])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ba(Object(n)).forEach(function(i){Object.defineProperty(e,i,Object.getOwnPropertyDescriptor(n,i))})}return e}function va(e,a,n){return a in e?Object.defineProperty(e,a,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[a]=n,e}function Le(e,a){if(e==null)return{};var n=si(e,a),i,l;if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);for(l=0;l<r.length;l++)i=r[l],!(a.indexOf(i)>=0)&&Object.prototype.propertyIsEnumerable.call(e,i)&&(n[i]=e[i])}return n}function si(e,a){if(e==null)return{};var n={},i=Object.keys(e),l,r;for(r=0;r<i.length;r++)l=i[r],!(a.indexOf(l)>=0)&&(n[l]=e[l]);return n}var We=s.forwardRef(function(e,a){var n=e.children,i=Le(e,Yn),l=li(i),r=l.open,c=Le(l,Jn);return s.useImperativeHandle(a,function(){return{open:r}},[r]),Tt.createElement(s.Fragment,null,n(q(q({},c),{},{open:r})))});We.displayName="Dropzone";var jt={disabled:!1,getFilesFromEvent:vn,maxSize:1/0,minSize:0,multiple:!0,maxFiles:0,preventDropOnDocument:!0,noClick:!1,noKeyboard:!1,noDrag:!1,noDragEventsBubbling:!1,validator:null,useFsAccessApi:!1,autoFocus:!1};We.defaultProps=jt;We.propTypes={children:A.func,accept:A.objectOf(A.arrayOf(A.string)),multiple:A.bool,preventDropOnDocument:A.bool,noClick:A.bool,noKeyboard:A.bool,noDrag:A.bool,noDragEventsBubbling:A.bool,minSize:A.number,maxSize:A.number,maxFiles:A.number,disabled:A.bool,getFilesFromEvent:A.func,onFileDialogCancel:A.func,onFileDialogOpen:A.func,useFsAccessApi:A.bool,autoFocus:A.bool,onDragEnter:A.func,onDragLeave:A.func,onDragOver:A.func,onDrop:A.func,onDropAccepted:A.func,onDropRejected:A.func,onError:A.func,validator:A.func};var ga={isFocused:!1,isFileDialogActive:!1,isDragActive:!1,isDragAccept:!1,isDragReject:!1,acceptedFiles:[],fileRejections:[]};function li(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},a=q(q({},jt),e),n=a.accept,i=a.disabled,l=a.getFilesFromEvent,r=a.maxSize,c=a.minSize,p=a.multiple,k=a.maxFiles,x=a.onDragEnter,g=a.onDragLeave,F=a.onDragOver,y=a.onDrop,j=a.onDropAccepted,C=a.onDropRejected,L=a.onFileDialogCancel,u=a.onFileDialogOpen,S=a.useFsAccessApi,N=a.autoFocus,K=a.preventDropOnDocument,z=a.noClick,h=a.noKeyboard,v=a.noDrag,_=a.noDragEventsBubbling,P=a.onError,J=a.validator,w=s.useMemo(function(){return Wn(n)},[n]),je=s.useMemo(function(){return Kn(n)},[n]),$=s.useMemo(function(){return typeof u=="function"?u:Ua},[u]),W=s.useMemo(function(){return typeof L=="function"?L:Ua},[L]),I=s.useRef(null),B=s.useRef(null),ue=s.useReducer(ri,ga),fe=ia(ue,2),se=fe[0],U=fe[1],Te=se.isFocused,ae=se.isFileDialogActive,G=s.useRef(typeof window<"u"&&window.isSecureContext&&S&&Hn()),xe=function(){!G.current&&ae&&setTimeout(function(){if(B.current){var b=B.current.files;b.length||(U({type:"closeDialog"}),W())}},300)};s.useEffect(function(){return window.addEventListener("focus",xe,!1),function(){window.removeEventListener("focus",xe,!1)}},[B,ae,W,G]);var te=s.useRef([]),we=function(b){I.current&&I.current.contains(b.target)||(b.preventDefault(),te.current=[])};s.useEffect(function(){return K&&(document.addEventListener("dragover",La,!1),document.addEventListener("drop",we,!1)),function(){K&&(document.removeEventListener("dragover",La),document.removeEventListener("drop",we))}},[I,K]),s.useEffect(function(){return!i&&N&&I.current&&I.current.focus(),function(){}},[I,N,i]);var Q=s.useCallback(function(m){P?P(m):console.error(m)},[P]),Oe=s.useCallback(function(m){m.preventDefault(),m.persist(),pe(m),te.current=[].concat(Zn(te.current),[m.target]),Re(m)&&Promise.resolve(l(m)).then(function(b){if(!(qe(m)&&!_)){var o=b.length,d=o>0&&Ln({files:b,accept:w,minSize:c,maxSize:r,multiple:p,maxFiles:k,validator:J}),f=o>0&&!d;U({isDragAccept:d,isDragReject:f,isDragActive:!0,type:"setDraggedFiles"}),x&&x(m)}}).catch(function(b){return Q(b)})},[l,x,Q,_,w,c,r,p,k,J]),le=s.useCallback(function(m){m.preventDefault(),m.persist(),pe(m);var b=Re(m);if(b&&m.dataTransfer)try{m.dataTransfer.dropEffect="copy"}catch{}return b&&F&&F(m),!1},[F,_]),Se=s.useCallback(function(m){m.preventDefault(),m.persist(),pe(m);var b=te.current.filter(function(d){return I.current&&I.current.contains(d)}),o=b.indexOf(m.target);o!==-1&&b.splice(o,1),te.current=b,!(b.length>0)&&(U({type:"setDraggedFiles",isDragActive:!1,isDragAccept:!1,isDragReject:!1}),Re(m)&&g&&g(m))},[I,g,_]),ke=s.useCallback(function(m,b){var o=[],d=[];m.forEach(function(f){var D=vt(f,w),T=ia(D,2),O=T[0],H=T[1],de=gt(f,c,r),ne=ia(de,2),ze=ne[0],Ce=ne[1],Ye=J?J(f):null;if(O&&ze&&!Ye)o.push(f);else{var Je=[H,Ce];Ye&&(Je=Je.concat(Ye)),d.push({file:f,errors:Je.filter(function(kt){return kt})})}}),(!p&&o.length>1||p&&k>=1&&o.length>k)&&(o.forEach(function(f){d.push({file:f,errors:[qn]})}),o.splice(0)),U({acceptedFiles:o,fileRejections:d,isDragReject:d.length>0,type:"setFiles"}),y&&y(o,d,b),d.length>0&&C&&C(d,b),o.length>0&&j&&j(o,b)},[U,p,w,c,r,k,y,j,C,J]),re=s.useCallback(function(m){m.preventDefault(),m.persist(),pe(m),te.current=[],Re(m)&&Promise.resolve(l(m)).then(function(b){qe(m)&&!_||ke(b,m)}).catch(function(b){return Q(b)}),U({type:"reset"})},[l,ke,Q,_]),X=s.useCallback(function(){if(G.current){U({type:"openDialog"}),$();var m={multiple:p,types:je};window.showOpenFilePicker(m).then(function(b){return l(b)}).then(function(b){ke(b,null),U({type:"closeDialog"})}).catch(function(b){Gn(b)?(W(b),U({type:"closeDialog"})):Vn(b)?(G.current=!1,B.current?(B.current.value=null,B.current.click()):Q(new Error("Cannot open the file picker because the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API is not supported and no <input> was provided."))):Q(b)});return}B.current&&(U({type:"openDialog"}),$(),B.current.value=null,B.current.click())},[U,$,W,S,ke,Q,je,p]),ve=s.useCallback(function(m){!I.current||!I.current.isEqualNode(m.target)||(m.key===" "||m.key==="Enter"||m.keyCode===32||m.keyCode===13)&&(m.preventDefault(),X())},[I,X]),ce=s.useCallback(function(){U({type:"focus"})},[]),De=s.useCallback(function(){U({type:"blur"})},[]),ge=s.useCallback(function(){z||($n()?setTimeout(X,0):X())},[z,X]),Y=function(b){return i?null:b},V=function(b){return h?null:Y(b)},Ne=function(b){return v?null:Y(b)},pe=function(b){_&&b.stopPropagation()},Ge=s.useMemo(function(){return function(){var m=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},b=m.refKey,o=b===void 0?"ref":b,d=m.role,f=m.onKeyDown,D=m.onFocus,T=m.onBlur,O=m.onClick,H=m.onDragEnter,de=m.onDragOver,ne=m.onDragLeave,ze=m.onDrop,Ce=Le(m,Qn);return q(q(va({onKeyDown:V(Z(f,ve)),onFocus:V(Z(D,ce)),onBlur:V(Z(T,De)),onClick:Y(Z(O,ge)),onDragEnter:Ne(Z(H,Oe)),onDragOver:Ne(Z(de,le)),onDragLeave:Ne(Z(ne,Se)),onDrop:Ne(Z(ze,re)),role:typeof d=="string"&&d!==""?d:"presentation"},o,I),!i&&!h?{tabIndex:0}:{}),Ce)}},[I,ve,ce,De,ge,Oe,le,Se,re,h,v,i]),Ve=s.useCallback(function(m){m.stopPropagation()},[]),he=s.useMemo(function(){return function(){var m=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},b=m.refKey,o=b===void 0?"ref":b,d=m.onChange,f=m.onClick,D=Le(m,Xn),T=va({accept:w,multiple:p,type:"file",style:{border:0,clip:"rect(0, 0, 0, 0)",clipPath:"inset(50%)",height:"1px",margin:"0 -1px -1px 0",overflow:"hidden",padding:0,position:"absolute",width:"1px",whiteSpace:"nowrap"},onChange:Y(Z(d,re)),onClick:Y(Z(f,Ve)),tabIndex:-1},o,B);return q(q({},T),D)}},[B,n,p,re,i]);return q(q({},se),{},{isFocused:Te&&!i,getRootProps:Ge,getInputProps:he,rootRef:I,inputRef:B,open:Y(X)})}function ri(e,a){switch(a.type){case"focus":return q(q({},e),{},{isFocused:!0});case"blur":return q(q({},e),{},{isFocused:!1});case"openDialog":return q(q({},ga),{},{isFileDialogActive:!0});case"closeDialog":return q(q({},e),{},{isFileDialogActive:!1});case"setDraggedFiles":return q(q({},e),{},{isDragActive:a.isDragActive,isDragAccept:a.isDragAccept,isDragReject:a.isDragReject});case"setFiles":return q(q({},e),{},{acceptedFiles:a.acceptedFiles,fileRejections:a.fileRejections,isDragReject:a.isDragReject});case"reset":return q({},ga);default:return e}}function Ua(){}function ha(e,a={}){const{decimals:n=0,sizeType:i="normal"}=a,l=["Bytes","KB","MB","GB","TB"],r=["Bytes","KiB","MiB","GiB","TiB"];if(e===0)return"0 Byte";const c=Math.floor(Math.log(e)/Math.log(1024));return`${(e/Math.pow(1024,c)).toFixed(n)} ${i==="accurate"?r[c]??"Bytes":l[c]??"Bytes"}`}function ci(e){const{t:a}=me(),{value:n,onValueChange:i,onUpload:l,onReject:r,progresses:c,fileErrors:p,accept:k=Mt,maxSize:x=1024*1024*200,maxFileCount:g=1,multiple:F=!1,disabled:y=!1,description:j,className:C,...L}=e,[u,S]=Ft({prop:n,onChange:i}),N=s.useCallback((h,v)=>{const _=((u==null?void 0:u.length)??0)+h.length+v.length;if(!F&&g===1&&h.length+v.length>1){M.error(a("documentPanel.uploadDocuments.fileUploader.singleFileLimit"));return}if(_>g){M.error(a("documentPanel.uploadDocuments.fileUploader.maxFilesLimit",{count:g}));return}v.length>0&&(r?r(v):v.forEach(({file:$})=>{M.error(a("documentPanel.uploadDocuments.fileUploader.fileRejected",{name:$.name}))}));const P=h.map($=>Object.assign($,{preview:URL.createObjectURL($)})),J=v.map(({file:$})=>Object.assign($,{preview:URL.createObjectURL($),rejected:!0})),w=[...P,...J],je=u?[...u,...w]:w;if(S(je),l&&h.length>0){const $=h.filter(W=>{var fe;if(!W.name)return!1;const I=`.${((fe=W.name.split(".").pop())==null?void 0:fe.toLowerCase())||""}`,B=Object.entries(k||{}).some(([se,U])=>W.type===se||Array.isArray(U)&&U.includes(I)),ue=W.size<=x;return B&&ue});$.length>0&&l($)}},[u,g,F,l,r,S,a,k,x]);function K(h){if(!u)return;const v=u.filter((_,P)=>P!==h);S(v),i==null||i(v)}s.useEffect(()=>()=>{u&&u.forEach(h=>{wt(h)&&URL.revokeObjectURL(h.preview)})},[]);const z=y||((u==null?void 0:u.length)??0)>=g;return t.jsxs("div",{className:"relative flex flex-col gap-6 overflow-hidden",children:[t.jsx(We,{onDrop:N,noClick:!1,noKeyboard:!1,maxSize:x,maxFiles:g,multiple:g>1||F,disabled:z,validator:h=>{var P;if(!h.name)return{code:"invalid-file-name",message:a("documentPanel.uploadDocuments.fileUploader.invalidFileName",{fallback:"Invalid file name"})};const v=`.${((P=h.name.split(".").pop())==null?void 0:P.toLowerCase())||""}`;return Object.entries(k||{}).some(([J,w])=>h.type===J||Array.isArray(w)&&w.includes(v))?h.size>x?{code:"file-too-large",message:a("documentPanel.uploadDocuments.fileUploader.fileTooLarge",{maxSize:ha(x)})}:null:{code:"file-invalid-type",message:a("documentPanel.uploadDocuments.fileUploader.unsupportedType")}},children:({getRootProps:h,getInputProps:v,isDragActive:_})=>t.jsxs("div",{...h(),className:E("group border-muted-foreground/25 hover:bg-muted/25 relative grid h-52 w-full cursor-pointer place-items-center rounded-lg border-2 border-dashed px-5 py-2.5 text-center transition","ring-offset-background focus-visible:ring-ring focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none",_&&"border-muted-foreground/50",z&&"pointer-events-none opacity-60",C),...L,children:[t.jsx("input",{...v()}),_?t.jsxs("div",{className:"flex flex-col items-center justify-center gap-4 sm:px-5",children:[t.jsx("div",{className:"rounded-full border border-dashed p-3",children:t.jsx(ra,{className:"text-muted-foreground size-7","aria-hidden":"true"})}),t.jsx("p",{className:"text-muted-foreground font-medium",children:a("documentPanel.uploadDocuments.fileUploader.dropHere")})]}):t.jsxs("div",{className:"flex flex-col items-center justify-center gap-4 sm:px-5",children:[t.jsx("div",{className:"rounded-full border border-dashed p-3",children:t.jsx(ra,{className:"text-muted-foreground size-7","aria-hidden":"true"})}),t.jsxs("div",{className:"flex flex-col gap-px",children:[t.jsx("p",{className:"text-muted-foreground font-medium",children:a("documentPanel.uploadDocuments.fileUploader.dragAndDrop")}),j?t.jsx("p",{className:"text-muted-foreground/70 text-sm",children:j}):t.jsxs("p",{className:"text-muted-foreground/70 text-sm",children:[a("documentPanel.uploadDocuments.fileUploader.uploadDescription",{count:g,isMultiple:g===1/0,maxSize:ha(x)}),a("documentPanel.uploadDocuments.fileTypes")]})]})]})]})}),u!=null&&u.length?t.jsx(It,{className:"h-fit w-full px-3",children:t.jsx("div",{className:"flex max-h-48 flex-col gap-4",children:u==null?void 0:u.map((h,v)=>t.jsx(pi,{file:h,onRemove:()=>K(v),progress:c==null?void 0:c[h.name],error:p==null?void 0:p[h.name]},v))})}):null]})}function $a({value:e,error:a}){return t.jsx("div",{className:"relative h-2 w-full",children:t.jsx("div",{className:"h-full w-full overflow-hidden rounded-full bg-secondary",children:t.jsx("div",{className:E("h-full transition-all",a?"bg-red-400":"bg-primary"),style:{width:`${e}%`}})})})}function pi({file:e,progress:a,error:n,onRemove:i}){const{t:l}=me();return t.jsxs("div",{className:"relative flex items-center gap-2.5",children:[t.jsxs("div",{className:"flex flex-1 gap-2.5",children:[n?t.jsx(nt,{className:"text-red-400 size-10","aria-hidden":"true"}):wt(e)?t.jsx(di,{file:e}):null,t.jsxs("div",{className:"flex w-full flex-col gap-2",children:[t.jsxs("div",{className:"flex flex-col gap-px",children:[t.jsx("p",{className:"text-foreground/80 line-clamp-1 text-sm font-medium",children:e.name}),t.jsx("p",{className:"text-muted-foreground text-xs",children:ha(e.size)})]}),n?t.jsxs("div",{className:"text-red-400 text-sm",children:[t.jsx("div",{className:"relative mb-2",children:t.jsx($a,{value:100,error:!0})}),t.jsx("p",{children:n})]}):a?t.jsx($a,{value:a}):null]})]}),t.jsx("div",{className:"flex items-center gap-2",children:t.jsxs(R,{type:"button",variant:"outline",size:"icon",className:"size-7",onClick:i,children:[t.jsx(it,{className:"size-4","aria-hidden":"true"}),t.jsx("span",{className:"sr-only",children:l("documentPanel.uploadDocuments.fileUploader.removeFile")})]})})]})}function wt(e){return"preview"in e&&typeof e.preview=="string"}function di({file:e}){return e.type.startsWith("image/")?t.jsx("div",{className:"aspect-square shrink-0 rounded-md object-cover"}):t.jsx(nt,{className:"text-muted-foreground size-10","aria-hidden":"true"})}function mi({onDocumentsUploaded:e}){const{t:a}=me(),[n,i]=s.useState(!1),[l,r]=s.useState(!1),[c,p]=s.useState({}),[k,x]=s.useState({}),g=s.useCallback(y=>{y.forEach(({file:j,errors:C})=>{var u;let L=((u=C[0])==null?void 0:u.message)||a("documentPanel.uploadDocuments.fileUploader.fileRejected",{name:j.name});L.includes("file-invalid-type")&&(L=a("documentPanel.uploadDocuments.fileUploader.unsupportedType")),p(S=>({...S,[j.name]:100})),x(S=>({...S,[j.name]:L}))})},[p,x,a]),F=s.useCallback(async y=>{var L,u;r(!0);let j=!1;x(S=>{const N={...S};return y.forEach(K=>{delete N[K.name]}),N});const C=M.loading(a("documentPanel.uploadDocuments.batch.uploading"));try{const S={},N=new Intl.Collator(["zh-CN","en"],{sensitivity:"accent",numeric:!0}),K=[...y].sort((h,v)=>N.compare(h.name,v.name));for(const h of K)try{p(_=>({..._,[h.name]:0}));const v=await qt(h,_=>{console.debug(a("documentPanel.uploadDocuments.single.uploading",{name:h.name,percent:_})),p(P=>({...P,[h.name]:_}))});v.status==="duplicated"?(S[h.name]=a("documentPanel.uploadDocuments.fileUploader.duplicateFile"),x(_=>({..._,[h.name]:a("documentPanel.uploadDocuments.fileUploader.duplicateFile")}))):v.status!=="success"?(S[h.name]=v.message,x(_=>({..._,[h.name]:v.message}))):j=!0}catch(v){console.error(`Upload failed for ${h.name}:`,v);let _=ee(v);if(v&&typeof v=="object"&&"response"in v){const P=v;((L=P.response)==null?void 0:L.status)===400&&(_=((u=P.response.data)==null?void 0:u.detail)||_),p(J=>({...J,[h.name]:100}))}S[h.name]=_,x(P=>({...P,[h.name]:_}))}Object.keys(S).length>0?M.error(a("documentPanel.uploadDocuments.batch.error"),{id:C}):M.success(a("documentPanel.uploadDocuments.batch.success"),{id:C}),j&&e&&e().catch(h=>{console.error("Error refreshing documents:",h)})}catch(S){console.error("Unexpected error during upload:",S),M.error(a("documentPanel.uploadDocuments.generalError",{error:ee(S)}),{id:C})}finally{r(!1)}},[r,p,x,a,e]);return t.jsxs(Be,{open:n,onOpenChange:y=>{l||(y||(p({}),x({})),i(y))},children:[t.jsx(ba,{asChild:!0,children:t.jsxs(R,{variant:"default",side:"bottom",tooltip:a("documentPanel.uploadDocuments.tooltip"),size:"sm",children:[t.jsx(ra,{})," ",a("documentPanel.uploadDocuments.button")]})}),t.jsxs(Ue,{className:"sm:max-w-xl",onCloseAutoFocus:y=>y.preventDefault(),children:[t.jsxs($e,{children:[t.jsx(He,{children:a("documentPanel.uploadDocuments.title")}),t.jsx(Ke,{children:a("documentPanel.uploadDocuments.description")})]}),t.jsx(ci,{maxFileCount:1/0,maxSize:200*1024*1024,description:a("documentPanel.uploadDocuments.fileTypes"),onUpload:F,onReject:g,progresses:c,fileErrors:k,disabled:l})]})]})}const Ha=({htmlFor:e,className:a,children:n,...i})=>t.jsx("label",{htmlFor:e,className:a,...i,children:n});function ui({onDocumentsCleared:e}){const{t:a}=me(),[n,i]=s.useState(!1),[l,r]=s.useState(""),[c,p]=s.useState(!1),[k,x]=s.useState(!1),g=s.useRef(null),F=l.toLowerCase()==="yes",y=3e4;s.useEffect(()=>{n||(r(""),p(!1),x(!1),g.current&&(clearTimeout(g.current),g.current=null))},[n]),s.useEffect(()=>()=>{g.current&&clearTimeout(g.current)},[]);const j=s.useCallback(async()=>{if(!(!F||k)){x(!0),g.current=setTimeout(()=>{k&&(M.error(a("documentPanel.clearDocuments.timeout")),x(!1),r(""))},y);try{const C=await Lt();if(C.status!=="success"){M.error(a("documentPanel.clearDocuments.failed",{message:C.message})),r("");return}if(M.success(a("documentPanel.clearDocuments.success")),c)try{await Bt(),M.success(a("documentPanel.clearDocuments.cacheCleared"))}catch(L){M.error(a("documentPanel.clearDocuments.cacheClearFailed",{error:ee(L)}))}e&&e().catch(console.error),i(!1)}catch(C){M.error(a("documentPanel.clearDocuments.error",{error:ee(C)})),r("")}finally{g.current&&(clearTimeout(g.current),g.current=null),x(!1)}}},[F,k,c,i,a,e,y]);return t.jsxs(Be,{open:n,onOpenChange:i,children:[t.jsx(ba,{asChild:!0,children:t.jsxs(R,{variant:"outline",side:"bottom",tooltip:a("documentPanel.clearDocuments.tooltip"),size:"sm",children:[t.jsx(Ut,{})," ",a("documentPanel.clearDocuments.button")]})}),t.jsxs(Ue,{className:"sm:max-w-xl",onCloseAutoFocus:C=>C.preventDefault(),children:[t.jsxs($e,{children:[t.jsxs(He,{className:"flex items-center gap-2 text-red-500 dark:text-red-400 font-bold",children:[t.jsx(ya,{className:"h-5 w-5"}),a("documentPanel.clearDocuments.title")]}),t.jsx(Ke,{className:"pt-2",children:a("documentPanel.clearDocuments.description")})]}),t.jsx("div",{className:"text-red-500 dark:text-red-400 font-semibold mb-4",children:a("documentPanel.clearDocuments.warning")}),t.jsx("div",{className:"mb-4",children:a("documentPanel.clearDocuments.confirm")}),t.jsxs("div",{className:"space-y-4",children:[t.jsxs("div",{className:"space-y-2",children:[t.jsx(Ha,{htmlFor:"confirm-text",className:"text-sm font-medium",children:a("documentPanel.clearDocuments.confirmPrompt")}),t.jsx(Me,{id:"confirm-text",value:l,onChange:C=>r(C.target.value),placeholder:a("documentPanel.clearDocuments.confirmPlaceholder"),className:"w-full",disabled:k})]}),t.jsxs("div",{className:"flex items-center space-x-2",children:[t.jsx(ot,{id:"clear-cache",checked:c,onCheckedChange:C=>p(C===!0),disabled:k}),t.jsx(Ha,{htmlFor:"clear-cache",className:"text-sm font-medium cursor-pointer",children:a("documentPanel.clearDocuments.clearCache")})]})]}),t.jsxs(st,{children:[t.jsx(R,{variant:"outline",onClick:()=>i(!1),disabled:k,children:a("common.cancel")}),t.jsx(R,{variant:"destructive",onClick:j,disabled:!F||k,children:k?t.jsxs(t.Fragment,{children:[t.jsx($t,{className:"mr-2 h-4 w-4 animate-spin"}),a("documentPanel.clearDocuments.clearing")]}):a("documentPanel.clearDocuments.confirmButton")})]})]})]})}const Ka=({htmlFor:e,className:a,children:n,...i})=>t.jsx("label",{htmlFor:e,className:a,...i,children:n});function fi({selectedDocIds:e,onDocumentsDeleted:a}){const{t:n}=me(),[i,l]=s.useState(!1),[r,c]=s.useState(""),[p,k]=s.useState(!1),[x,g]=s.useState(!1),F=r.toLowerCase()==="yes"&&!x;s.useEffect(()=>{i||(c(""),k(!1),g(!1))},[i]);const y=s.useCallback(async()=>{if(!(!F||e.length===0)){g(!0);try{const j=await Ht(e,p);if(j.status==="deletion_started")M.success(n("documentPanel.deleteDocuments.success",{count:e.length}));else if(j.status==="busy"){M.error(n("documentPanel.deleteDocuments.busy")),c(""),g(!1);return}else if(j.status==="not_allowed"){M.error(n("documentPanel.deleteDocuments.notAllowed")),c(""),g(!1);return}else{M.error(n("documentPanel.deleteDocuments.failed",{message:j.message})),c(""),g(!1);return}a&&a().catch(console.error),l(!1)}catch(j){M.error(n("documentPanel.deleteDocuments.error",{error:ee(j)})),c("")}finally{g(!1)}}},[F,e,p,l,n,a]);return t.jsxs(Be,{open:i,onOpenChange:l,children:[t.jsx(ba,{asChild:!0,children:t.jsxs(R,{variant:"destructive",side:"bottom",tooltip:n("documentPanel.deleteDocuments.tooltip",{count:e.length}),size:"sm",children:[t.jsx(Kt,{})," ",n("documentPanel.deleteDocuments.button")]})}),t.jsxs(Ue,{className:"sm:max-w-xl",onCloseAutoFocus:j=>j.preventDefault(),children:[t.jsxs($e,{children:[t.jsxs(He,{className:"flex items-center gap-2 text-red-500 dark:text-red-400 font-bold",children:[t.jsx(ya,{className:"h-5 w-5"}),n("documentPanel.deleteDocuments.title")]}),t.jsx(Ke,{className:"pt-2",children:n("documentPanel.deleteDocuments.description",{count:e.length})})]}),t.jsx("div",{className:"text-red-500 dark:text-red-400 font-semibold mb-4",children:n("documentPanel.deleteDocuments.warning")}),t.jsx("div",{className:"mb-4",children:n("documentPanel.deleteDocuments.confirm",{count:e.length})}),t.jsxs("div",{className:"space-y-4",children:[t.jsxs("div",{className:"space-y-2",children:[t.jsx(Ka,{htmlFor:"confirm-text",className:"text-sm font-medium",children:n("documentPanel.deleteDocuments.confirmPrompt")}),t.jsx(Me,{id:"confirm-text",value:r,onChange:j=>c(j.target.value),placeholder:n("documentPanel.deleteDocuments.confirmPlaceholder"),className:"w-full",disabled:x})]}),t.jsxs("div",{className:"flex items-center space-x-2",children:[t.jsx("input",{type:"checkbox",id:"delete-file",checked:p,onChange:j=>k(j.target.checked),disabled:x,className:"h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"}),t.jsx(Ka,{htmlFor:"delete-file",className:"text-sm font-medium cursor-pointer",children:n("documentPanel.deleteDocuments.deleteFileOption")})]})]}),t.jsxs(st,{children:[t.jsx(R,{variant:"outline",onClick:()=>l(!1),disabled:x,children:n("common.cancel")}),t.jsx(R,{variant:"destructive",onClick:y,disabled:!F,children:n(x?"documentPanel.deleteDocuments.deleting":"documentPanel.deleteDocuments.confirmButton")})]})]})]})}const Wa=[{value:10,label:"10"},{value:20,label:"20"},{value:50,label:"50"},{value:100,label:"100"},{value:200,label:"200"}];function xi({currentPage:e,totalPages:a,pageSize:n,totalCount:i,onPageChange:l,onPageSizeChange:r,isLoading:c=!1,compact:p=!1,className:k}){const{t:x}=me(),[g,F]=s.useState(e.toString());s.useEffect(()=>{F(e.toString())},[e]);const y=s.useCallback(z=>{F(z)},[]),j=s.useCallback(()=>{const z=parseInt(g,10);!isNaN(z)&&z>=1&&z<=a?l(z):F(e.toString())},[g,a,l,e]),C=s.useCallback(z=>{z.key==="Enter"&&j()},[j]),L=s.useCallback(z=>{const h=parseInt(z,10);isNaN(h)||r(h)},[r]),u=s.useCallback(()=>{e>1&&!c&&l(1)},[e,l,c]),S=s.useCallback(()=>{e>1&&!c&&l(e-1)},[e,l,c]),N=s.useCallback(()=>{e<a&&!c&&l(e+1)},[e,a,l,c]),K=s.useCallback(()=>{e<a&&!c&&l(a)},[e,a,l,c]);return a<=1?null:p?t.jsxs("div",{className:E("flex items-center gap-2",k),children:[t.jsxs("div",{className:"flex items-center gap-1",children:[t.jsx(R,{variant:"outline",size:"sm",onClick:S,disabled:e<=1||c,className:"h-8 w-8 p-0",children:t.jsx(ja,{className:"h-4 w-4"})}),t.jsxs("div",{className:"flex items-center gap-1",children:[t.jsx(Me,{type:"text",value:g,onChange:z=>y(z.target.value),onBlur:j,onKeyPress:C,disabled:c,className:"h-8 w-12 text-center text-sm"}),t.jsxs("span",{className:"text-sm text-gray-500",children:["/ ",a]})]}),t.jsx(R,{variant:"outline",size:"sm",onClick:N,disabled:e>=a||c,className:"h-8 w-8 p-0",children:t.jsx(wa,{className:"h-4 w-4"})})]}),t.jsxs(za,{value:n.toString(),onValueChange:L,disabled:c,children:[t.jsx(ca,{className:"h-8 w-16",children:t.jsx(Ca,{})}),t.jsx(pa,{children:Wa.map(z=>t.jsx(da,{value:z.value.toString(),children:z.label},z.value))})]})]}):t.jsxs("div",{className:E("flex items-center justify-between gap-4",k),children:[t.jsx("div",{className:"text-sm text-gray-500",children:x("pagination.showing",{start:Math.min((e-1)*n+1,i),end:Math.min(e*n,i),total:i})}),t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsxs("div",{className:"flex items-center gap-1",children:[t.jsx(R,{variant:"outline",size:"sm",onClick:u,disabled:e<=1||c,className:"h-8 w-8 p-0",tooltip:x("pagination.firstPage"),children:t.jsx(Wt,{className:"h-4 w-4"})}),t.jsx(R,{variant:"outline",size:"sm",onClick:S,disabled:e<=1||c,className:"h-8 w-8 p-0",tooltip:x("pagination.prevPage"),children:t.jsx(ja,{className:"h-4 w-4"})}),t.jsxs("div",{className:"flex items-center gap-1",children:[t.jsx("span",{className:"text-sm",children:x("pagination.page")}),t.jsx(Me,{type:"text",value:g,onChange:z=>y(z.target.value),onBlur:j,onKeyPress:C,disabled:c,className:"h-8 w-16 text-center text-sm"}),t.jsxs("span",{className:"text-sm",children:["/ ",a]})]}),t.jsx(R,{variant:"outline",size:"sm",onClick:N,disabled:e>=a||c,className:"h-8 w-8 p-0",tooltip:x("pagination.nextPage"),children:t.jsx(wa,{className:"h-4 w-4"})}),t.jsx(R,{variant:"outline",size:"sm",onClick:K,disabled:e>=a||c,className:"h-8 w-8 p-0",tooltip:x("pagination.lastPage"),children:t.jsx(Gt,{className:"h-4 w-4"})})]}),t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsx("span",{className:"text-sm",children:x("pagination.pageSize")}),t.jsxs(za,{value:n.toString(),onValueChange:L,disabled:c,children:[t.jsx(ca,{className:"h-8 w-16",children:t.jsx(Ca,{})}),t.jsx(pa,{children:Wa.map(z=>t.jsx(da,{value:z.value.toString(),children:z.label},z.value))})]})]})]})]})}function vi({open:e,onOpenChange:a}){var F;const{t:n}=me(),[i,l]=s.useState(null),[r,c]=s.useState("center"),[p,k]=s.useState(!1),x=s.useRef(null);s.useEffect(()=>{e&&(c("center"),k(!1))},[e]),s.useEffect(()=>{const y=x.current;!y||p||(y.scrollTop=y.scrollHeight)},[i==null?void 0:i.history_messages,p]);const g=()=>{const y=x.current;if(!y)return;const j=Math.abs(y.scrollHeight-y.scrollTop-y.clientHeight)<1;k(!j)};return s.useEffect(()=>{if(!e)return;const y=async()=>{try{const C=await Qt();l(C)}catch(C){M.error(n("documentPanel.pipelineStatus.errors.fetchFailed",{error:ee(C)}))}};y();const j=setInterval(y,2e3);return()=>clearInterval(j)},[e,n]),t.jsx(Be,{open:e,onOpenChange:a,children:t.jsxs(Ue,{className:E("sm:max-w-[800px] transition-all duration-200 fixed",r==="left"&&"!left-[25%] !translate-x-[-50%] !mx-4",r==="center"&&"!left-1/2 !-translate-x-1/2",r==="right"&&"!left-[75%] !translate-x-[-50%] !mx-4"),children:[t.jsx(Ke,{className:"sr-only",children:i!=null&&i.job_name?`${n("documentPanel.pipelineStatus.jobName")}: ${i.job_name}, ${n("documentPanel.pipelineStatus.progress")}: ${i.cur_batch}/${i.batchs}`:n("documentPanel.pipelineStatus.noActiveJob")}),t.jsxs($e,{className:"flex flex-row items-center",children:[t.jsx(He,{className:"flex-1",children:n("documentPanel.pipelineStatus.title")}),t.jsxs("div",{className:"flex items-center gap-2 mr-8",children:[t.jsx(R,{variant:"ghost",size:"icon",className:E("h-6 w-6",r==="left"&&"bg-zinc-200 text-zinc-800 hover:bg-zinc-300 dark:bg-zinc-700 dark:text-zinc-200 dark:hover:bg-zinc-600"),onClick:()=>c("left"),children:t.jsx(Vt,{className:"h-4 w-4"})}),t.jsx(R,{variant:"ghost",size:"icon",className:E("h-6 w-6",r==="center"&&"bg-zinc-200 text-zinc-800 hover:bg-zinc-300 dark:bg-zinc-700 dark:text-zinc-200 dark:hover:bg-zinc-600"),onClick:()=>c("center"),children:t.jsx(Yt,{className:"h-4 w-4"})}),t.jsx(R,{variant:"ghost",size:"icon",className:E("h-6 w-6",r==="right"&&"bg-zinc-200 text-zinc-800 hover:bg-zinc-300 dark:bg-zinc-700 dark:text-zinc-200 dark:hover:bg-zinc-600"),onClick:()=>c("right"),children:t.jsx(Jt,{className:"h-4 w-4"})})]})]}),t.jsxs("div",{className:"space-y-4 pt-4",children:[t.jsxs("div",{className:"flex items-center gap-4",children:[t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsxs("div",{className:"text-sm font-medium",children:[n("documentPanel.pipelineStatus.busy"),":"]}),t.jsx("div",{className:`h-2 w-2 rounded-full ${i!=null&&i.busy?"bg-green-500":"bg-gray-300"}`})]}),t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsxs("div",{className:"text-sm font-medium",children:[n("documentPanel.pipelineStatus.requestPending"),":"]}),t.jsx("div",{className:`h-2 w-2 rounded-full ${i!=null&&i.request_pending?"bg-green-500":"bg-gray-300"}`})]})]}),t.jsxs("div",{className:"rounded-md border p-3 space-y-2",children:[t.jsxs("div",{children:[n("documentPanel.pipelineStatus.jobName"),": ",(i==null?void 0:i.job_name)||"-"]}),t.jsxs("div",{className:"flex justify-between",children:[t.jsxs("span",{children:[n("documentPanel.pipelineStatus.startTime"),": ",i!=null&&i.job_start?new Date(i.job_start).toLocaleString(void 0,{year:"numeric",month:"numeric",day:"numeric",hour:"numeric",minute:"numeric",second:"numeric"}):"-"]}),t.jsxs("span",{children:[n("documentPanel.pipelineStatus.progress"),": ",i?`${i.cur_batch}/${i.batchs} ${n("documentPanel.pipelineStatus.unit")}`:"-"]})]})]}),t.jsxs("div",{className:"space-y-2",children:[t.jsxs("div",{className:"text-sm font-medium",children:[n("documentPanel.pipelineStatus.latestMessage"),":"]}),t.jsx("div",{className:"font-mono text-xs rounded-md bg-zinc-800 text-zinc-100 p-3 whitespace-pre-wrap break-words",children:(i==null?void 0:i.latest_message)||"-"})]}),t.jsxs("div",{className:"space-y-2",children:[t.jsxs("div",{className:"text-sm font-medium",children:[n("documentPanel.pipelineStatus.historyMessages"),":"]}),t.jsx("div",{ref:x,onScroll:g,className:"font-mono text-xs rounded-md bg-zinc-800 text-zinc-100 p-3 overflow-y-auto min-h-[7.5em] max-h-[40vh]",children:(F=i==null?void 0:i.history_messages)!=null&&F.length?i.history_messages.map((y,j)=>t.jsx("div",{className:"whitespace-pre-wrap break-words",children:y},j)):"-"})]})]})]})})}const oa=(e,a=20)=>{if(!e.file_path||typeof e.file_path!="string"||e.file_path.trim()==="")return e.id;const n=e.file_path.split("/"),i=n[n.length-1];return!i||i.trim()===""?e.id:i.length>a?i.slice(0,a)+"...":i},gi=e=>{const a={...e};if(a.processing_start_time&&typeof a.processing_start_time=="number"){const n=new Date(a.processing_start_time*1e3);isNaN(n.getTime())||(a.processing_start_time=n.toLocaleString())}if(a.processing_end_time&&typeof a.processing_end_time=="number"){const n=new Date(a.processing_end_time*1e3);isNaN(n.getTime())||(a.processing_end_time=n.toLocaleString())}return JSON.stringify(a,null,2)},hi=`
/* Tooltip styles */
.tooltip-container {
  position: relative;
  overflow: visible !important;
}

.tooltip {
  position: fixed; /* Use fixed positioning to escape overflow constraints */
  z-index: 9999; /* Ensure tooltip appears above all other elements */
  max-width: 600px;
  white-space: normal;
  border-radius: 0.375rem;
  padding: 0.5rem 0.75rem;
  font-size: 0.75rem; /* 12px */
  background-color: rgba(0, 0, 0, 0.95);
  color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  pointer-events: none; /* Prevent tooltip from interfering with mouse events */
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.15s, visibility 0.15s;
}

.tooltip.visible {
  opacity: 1;
  visibility: visible;
}

.dark .tooltip {
  background-color: rgba(255, 255, 255, 0.95);
  color: black;
}

/* Position tooltip helper class */
.tooltip-helper {
  position: absolute;
  visibility: hidden;
  pointer-events: none;
  top: 0;
  left: 0;
  width: 100%;
  height: 0;
}

@keyframes pulse {
  0% {
    background-color: rgb(255 0 0 / 0.1);
    border-color: rgb(255 0 0 / 0.2);
  }
  50% {
    background-color: rgb(255 0 0 / 0.2);
    border-color: rgb(255 0 0 / 0.4);
  }
  100% {
    background-color: rgb(255 0 0 / 0.1);
    border-color: rgb(255 0 0 / 0.2);
  }
}

.dark .pipeline-busy {
  animation: dark-pulse 2s infinite;
}

@keyframes dark-pulse {
  0% {
    background-color: rgb(255 0 0 / 0.2);
    border-color: rgb(255 0 0 / 0.4);
  }
  50% {
    background-color: rgb(255 0 0 / 0.3);
    border-color: rgb(255 0 0 / 0.6);
  }
  100% {
    background-color: rgb(255 0 0 / 0.2);
    border-color: rgb(255 0 0 / 0.4);
  }
}

.pipeline-busy {
  animation: pulse 2s infinite;
  border: 1px solid;
}
`;function ki(){const e=s.useRef(!0);s.useEffect(()=>{e.current=!0;const o=()=>{e.current=!1};return window.addEventListener("beforeunload",o),()=>{e.current=!1,window.removeEventListener("beforeunload",o)}},[]);const[a,n]=s.useState(!1),{t:i,i18n:l}=me(),r=_e.use.health(),c=_e.use.pipelineBusy(),[p,k]=s.useState(null),x=Ee.use.currentTab(),g=Ee.use.showFileName(),F=Ee.use.setShowFileName(),y=Ee.use.documentsPageSize(),j=Ee.use.setDocumentsPageSize(),[C,L]=s.useState([]),[u,S]=s.useState({page:1,page_size:y,total_count:0,total_pages:0,has_next:!1,has_prev:!1}),[N,K]=s.useState({all:0}),[z,h]=s.useState(!1),[v,_]=s.useState("updated_at"),[P,J]=s.useState("desc"),[w,je]=s.useState("all"),[$,W]=s.useState({all:1,processed:1,processing:1,pending:1,failed:1}),[I,B]=s.useState([]),ue=I.length>0,fe=s.useCallback((o,d)=>{B(f=>d?[...f,o]:f.filter(D=>D!==o))},[]),se=s.useCallback(()=>{B([])},[]),U=o=>{let d=o;o==="id"&&(d=g?"file_path":"id");const f=v===d&&P==="desc"?"asc":"desc";_(d),J(f),S(D=>({...D,page:1})),W({all:1,processed:1,processing:1,pending:1,failed:1})},Te=s.useCallback(o=>[...o].sort((d,f)=>{let D,T;v==="id"&&g?(D=oa(d),T=oa(f)):v==="id"?(D=d.id,T=f.id):(D=new Date(d[v]).getTime(),T=new Date(f[v]).getTime());const O=P==="asc"?1:-1;return typeof D=="string"&&typeof T=="string"?O*D.localeCompare(T):O*(D>T?1:D<T?-1:0)}),[v,P,g]),ae=s.useMemo(()=>{if(C&&C.length>0)return C.map(d=>({...d,status:d.status}));if(!p)return null;const o=[];return w==="all"?Object.entries(p.statuses).forEach(([d,f])=>{f.forEach(D=>{o.push({...D,status:d})})}):(p.statuses[w]||[]).forEach(f=>{o.push({...f,status:w})}),v&&P?Te(o):o},[C,p,v,P,w,Te]),G=s.useMemo(()=>(ae==null?void 0:ae.map(o=>o.id))||[],[ae]),xe=s.useMemo(()=>G.filter(o=>I.includes(o)).length,[G,I]),te=s.useMemo(()=>G.length>0&&xe===G.length,[G,xe]),we=s.useMemo(()=>xe>0,[xe]),Q=s.useCallback(()=>{B(G)},[G]),Oe=s.useCallback(()=>we?te?{text:i("documentPanel.selectDocuments.deselectAll",{count:G.length}),action:se,icon:it}:{text:i("documentPanel.selectDocuments.selectCurrentPage",{count:G.length}),action:Q,icon:ka}:{text:i("documentPanel.selectDocuments.selectCurrentPage",{count:G.length}),action:Q,icon:ka},[we,te,G.length,Q,se,i]),le=s.useMemo(()=>{if(!p)return{all:0};const o={all:0};return Object.entries(p.statuses).forEach(([d,f])=>{o[d]=f.length,o.all+=f.length}),o},[p]),Se=s.useRef({processed:0,processing:0,pending:0,failed:0});s.useEffect(()=>{const o=document.createElement("style");return o.textContent=hi,document.head.appendChild(o),()=>{document.head.removeChild(o)}},[]);const ke=s.useRef(null);s.useEffect(()=>{if(!p)return;const o=()=>{document.querySelectorAll(".tooltip-container").forEach(T=>{const O=T.querySelector(".tooltip");if(!O||!O.classList.contains("visible"))return;const H=T.getBoundingClientRect();O.style.left=`${H.left}px`,O.style.top=`${H.top-5}px`,O.style.transform="translateY(-100%)"})},d=D=>{const O=D.target.closest(".tooltip-container");if(!O)return;const H=O.querySelector(".tooltip");H&&(H.classList.add("visible"),o())},f=D=>{const O=D.target.closest(".tooltip-container");if(!O)return;const H=O.querySelector(".tooltip");H&&H.classList.remove("visible")};return document.addEventListener("mouseover",d),document.addEventListener("mouseout",f),()=>{document.removeEventListener("mouseover",d),document.removeEventListener("mouseout",f)}},[p]);const re=s.useCallback(o=>{S(o.pagination),L(o.documents),K(o.status_counts);const d={statuses:{processed:o.documents.filter(f=>f.status==="processed"),processing:o.documents.filter(f=>f.status==="processing"),pending:o.documents.filter(f=>f.status==="pending"),failed:o.documents.filter(f=>f.status==="failed")}};k(o.pagination.total_count>0?d:null)},[]),X=s.useCallback(async(o,d)=>{try{if(!e.current)return;h(!0);const f=d?1:o||u.page,D={status_filter:w==="all"?null:w,page:f,page_size:u.page_size,sort_field:v,sort_direction:P},T=await Qe(D);if(!e.current)return;if(T.documents.length===0&&T.pagination.total_count>0){const O=Math.max(1,T.pagination.total_pages);if(f!==O){const H={...D,page:O},de=await Qe(H);if(!e.current)return;W(ne=>({...ne,[w]:O})),re(de);return}}f!==u.page&&W(O=>({...O,[w]:f})),re(T)}catch(f){e.current&&M.error(i("documentPanel.documentManager.errors.loadFailed",{error:ee(f)}))}finally{e.current&&h(!1)}},[w,u.page,u.page_size,v,P,i,re]),ve=s.useCallback(async(o,d,f)=>{S(D=>({...D,page:o,page_size:d})),await X(o)},[X]),ce=s.useCallback(async()=>{await ve(u.page,u.page_size,w)},[ve,u.page,u.page_size,w]),De=s.useRef(void 0),ge=s.useRef(null),Y=s.useCallback(()=>{ge.current&&(clearInterval(ge.current),ge.current=null)},[]),V=s.useCallback(o=>{Y(),ge.current=setInterval(async()=>{try{e.current&&await ce()}catch(d){e.current&&M.error(i("documentPanel.documentManager.errors.scanProgressFailed",{error:ee(d)}))}},o)},[ce,i,Y]),Ne=s.useCallback(async()=>{try{if(!e.current)return;const{status:o,message:d,track_id:f}=await Xt();if(!e.current)return;M.message(d||o),_e.getState().resetHealthCheckTimerDelayed(1e3),V(2e3),setTimeout(()=>{if(e.current&&x==="documents"&&r){const T=(N.processing||0)>0||(N.pending||0)>0?5e3:3e4;V(T)}},15e3)}catch(o){e.current&&M.error(i("documentPanel.documentManager.errors.scanFailed",{error:ee(o)}))}},[i,V,x,r,N]),pe=s.useCallback(o=>{o!==u.page_size&&(j(o),W({all:1,processed:1,processing:1,pending:1,failed:1}),S(d=>({...d,page:1,page_size:o})))},[u.page_size,j]),Ge=s.useCallback(async()=>{try{h(!0);const o={status_filter:w==="all"?null:w,page:1,page_size:u.page_size,sort_field:v,sort_direction:P},d=await Qe(o);if(!e.current)return;if(d.pagination.total_count<u.page_size&&u.page_size!==10)pe(10);else{S(d.pagination),L(d.documents),K(d.status_counts);const f={statuses:{processed:d.documents.filter(D=>D.status==="processed"),processing:d.documents.filter(D=>D.status==="processing"),pending:d.documents.filter(D=>D.status==="pending"),failed:d.documents.filter(D=>D.status==="failed")}};d.pagination.total_count>0?k(f):k(null)}}catch(o){e.current&&M.error(i("documentPanel.documentManager.errors.loadFailed",{error:ee(o)}))}finally{e.current&&h(!1)}},[w,u.page_size,v,P,pe,i]);s.useEffect(()=>{if(De.current!==void 0&&De.current!==c&&x==="documents"&&r&&e.current){X();const d=(N.processing||0)>0||(N.pending||0)>0?5e3:3e4;V(d)}De.current=c},[c,x,r,X,N.processing,N.pending,V]),s.useEffect(()=>{if(x!=="documents"||!r){Y();return}const d=(N.processing||0)>0||(N.pending||0)>0?5e3:3e4;return V(d),()=>{Y()}},[r,i,x,N,V,Y]),s.useEffect(()=>{var f,D,T,O,H,de,ne,ze;if(!p)return;const o={processed:((D=(f=p==null?void 0:p.statuses)==null?void 0:f.processed)==null?void 0:D.length)||0,processing:((O=(T=p==null?void 0:p.statuses)==null?void 0:T.processing)==null?void 0:O.length)||0,pending:((de=(H=p==null?void 0:p.statuses)==null?void 0:H.pending)==null?void 0:de.length)||0,failed:((ze=(ne=p==null?void 0:p.statuses)==null?void 0:ne.failed)==null?void 0:ze.length)||0};Object.keys(o).some(Ce=>o[Ce]!==Se.current[Ce])&&e.current&&_e.getState().check(),Se.current=o},[p]);const Ve=s.useCallback(o=>{o!==u.page&&(W(d=>({...d,[w]:o})),S(d=>({...d,page:o})))},[u.page,w]),he=s.useCallback(o=>{if(o===w)return;W(f=>({...f,[w]:u.page}));const d=$[o];je(o),S(f=>({...f,page:d}))},[w,u.page,$]),m=s.useCallback(async()=>{B([]),_e.getState().resetHealthCheckTimerDelayed(1e3),V(2e3)},[V]),b=s.useCallback(async()=>{if(Y(),K({all:0,processed:0,processing:0,pending:0,failed:0}),e.current)try{await ce()}catch(o){console.error("Error fetching documents after clear:",o)}x==="documents"&&r&&e.current&&V(3e4)},[Y,K,ce,x,r,V]);return s.useEffect(()=>{if(v==="id"||v==="file_path"){const o=g?"file_path":"id";v!==o&&_(o)}},[g,v]),s.useEffect(()=>{B([])},[u.page,w,v,P]),s.useEffect(()=>{x==="documents"&&ve(u.page,u.page_size,w)},[x,u.page,u.page_size,w,v,P,ve]),t.jsxs(sa,{className:"!rounded-none !overflow-hidden flex flex-col h-full min-h-0",children:[t.jsx(Da,{className:"py-2 px-6",children:t.jsx(la,{className:"text-lg",children:i("documentPanel.documentManager.title")})}),t.jsxs(Na,{className:"flex-1 flex flex-col min-h-0 overflow-auto",children:[t.jsxs("div",{className:"flex justify-between items-center gap-2 mb-2",children:[t.jsxs("div",{className:"flex gap-2",children:[t.jsxs(R,{variant:"outline",onClick:Ne,side:"bottom",tooltip:i("documentPanel.documentManager.scanTooltip"),size:"sm",children:[t.jsx(Zt,{})," ",i("documentPanel.documentManager.scanButton")]}),t.jsxs(R,{variant:"outline",onClick:()=>n(!0),side:"bottom",tooltip:i("documentPanel.documentManager.pipelineStatusTooltip"),size:"sm",className:E(c&&"pipeline-busy"),children:[t.jsx(en,{})," ",i("documentPanel.documentManager.pipelineStatusButton")]})]}),u.total_pages>1&&t.jsx(xi,{currentPage:u.page,totalPages:u.total_pages,pageSize:u.page_size,totalCount:u.total_count,onPageChange:Ve,onPageSizeChange:pe,isLoading:z,compact:!0}),t.jsxs("div",{className:"flex gap-2",children:[ue&&t.jsx(fi,{selectedDocIds:I,onDocumentsDeleted:m}),ue&&we?(()=>{const o=Oe(),d=o.icon;return t.jsxs(R,{variant:"outline",size:"sm",onClick:o.action,side:"bottom",tooltip:o.text,children:[t.jsx(d,{className:"h-4 w-4"}),o.text]})})():ue?null:t.jsx(ui,{onDocumentsCleared:b}),t.jsx(mi,{onDocumentsUploaded:ce}),t.jsx(vi,{open:a,onOpenChange:n})]})]}),t.jsxs(sa,{className:"flex-1 flex flex-col border rounded-md min-h-0 mb-2",children:[t.jsxs(Da,{className:"flex-none py-2 px-4",children:[t.jsxs("div",{className:"flex justify-between items-center",children:[t.jsx(la,{children:i("documentPanel.documentManager.uploadedTitle")}),t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsxs("div",{className:"flex gap-1",dir:l.dir(),children:[t.jsxs(R,{size:"sm",variant:w==="all"?"secondary":"outline",onClick:()=>he("all"),disabled:z,className:E(w==="all"&&"bg-gray-100 dark:bg-gray-900 font-medium border border-gray-400 dark:border-gray-500 shadow-sm"),children:[i("documentPanel.documentManager.status.all")," (",N.all||le.all,")"]}),t.jsxs(R,{size:"sm",variant:w==="processed"?"secondary":"outline",onClick:()=>he("processed"),disabled:z,className:E((N.PROCESSED||N.processed||le.processed)>0?"text-green-600":"text-gray-500",w==="processed"&&"bg-green-100 dark:bg-green-900/30 font-medium border border-green-400 dark:border-green-600 shadow-sm"),children:[i("documentPanel.documentManager.status.completed")," (",N.PROCESSED||N.processed||0,")"]}),t.jsxs(R,{size:"sm",variant:w==="processing"?"secondary":"outline",onClick:()=>he("processing"),disabled:z,className:E((N.PROCESSING||N.processing||le.processing)>0?"text-blue-600":"text-gray-500",w==="processing"&&"bg-blue-100 dark:bg-blue-900/30 font-medium border border-blue-400 dark:border-blue-600 shadow-sm"),children:[i("documentPanel.documentManager.status.processing")," (",N.PROCESSING||N.processing||0,")"]}),t.jsxs(R,{size:"sm",variant:w==="pending"?"secondary":"outline",onClick:()=>he("pending"),disabled:z,className:E((N.PENDING||N.pending||le.pending)>0?"text-yellow-600":"text-gray-500",w==="pending"&&"bg-yellow-100 dark:bg-yellow-900/30 font-medium border border-yellow-400 dark:border-yellow-600 shadow-sm"),children:[i("documentPanel.documentManager.status.pending")," (",N.PENDING||N.pending||0,")"]}),t.jsxs(R,{size:"sm",variant:w==="failed"?"secondary":"outline",onClick:()=>he("failed"),disabled:z,className:E((N.FAILED||N.failed||le.failed)>0?"text-red-600":"text-gray-500",w==="failed"&&"bg-red-100 dark:bg-red-900/30 font-medium border border-red-400 dark:border-red-600 shadow-sm"),children:[i("documentPanel.documentManager.status.failed")," (",N.FAILED||N.failed||0,")"]})]}),t.jsx(R,{variant:"ghost",size:"sm",onClick:Ge,disabled:z,side:"bottom",tooltip:i("documentPanel.documentManager.refreshTooltip"),children:t.jsx(an,{className:"h-4 w-4"})})]}),t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsx("label",{htmlFor:"toggle-filename-btn",className:"text-sm text-gray-500",children:i("documentPanel.documentManager.fileNameLabel")}),t.jsx(R,{id:"toggle-filename-btn",variant:"outline",size:"sm",onClick:()=>F(!g),className:"border-gray-200 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-800",children:i(g?"documentPanel.documentManager.hideButton":"documentPanel.documentManager.showButton")})]})]}),t.jsx(tt,{"aria-hidden":"true",className:"hidden",children:i("documentPanel.documentManager.uploadedDescription")})]}),t.jsxs(Na,{className:"flex-1 relative p-0",ref:ke,children:[!p&&t.jsx("div",{className:"absolute inset-0 p-0",children:t.jsx(rn,{title:i("documentPanel.documentManager.emptyTitle"),description:i("documentPanel.documentManager.emptyDescription")})}),p&&t.jsx("div",{className:"absolute inset-0 flex flex-col p-0",children:t.jsx("div",{className:"absolute inset-[-1px] flex flex-col p-0 border rounded-md border-gray-200 dark:border-gray-700 overflow-hidden",children:t.jsxs(ct,{className:"w-full",children:[t.jsx(pt,{className:"sticky top-0 bg-background z-10 shadow-sm",children:t.jsxs(ma,{className:"border-b bg-card/95 backdrop-blur supports-[backdrop-filter]:bg-card/75 shadow-[inset_0_-1px_0_rgba(0,0,0,0.1)]",children:[t.jsx(ie,{onClick:()=>U("id"),className:"cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-800 select-none",children:t.jsxs("div",{className:"flex items-center",children:[i(g?"documentPanel.documentManager.columns.fileName":"documentPanel.documentManager.columns.id"),(v==="id"&&!g||v==="file_path"&&g)&&t.jsx("span",{className:"ml-1",children:P==="asc"?t.jsx(Xe,{size:14}):t.jsx(Ze,{size:14})})]})}),t.jsx(ie,{children:i("documentPanel.documentManager.columns.summary")}),t.jsx(ie,{children:i("documentPanel.documentManager.columns.status")}),t.jsx(ie,{children:i("documentPanel.documentManager.columns.length")}),t.jsx(ie,{children:i("documentPanel.documentManager.columns.chunks")}),t.jsx(ie,{onClick:()=>U("created_at"),className:"cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-800 select-none",children:t.jsxs("div",{className:"flex items-center",children:[i("documentPanel.documentManager.columns.created"),v==="created_at"&&t.jsx("span",{className:"ml-1",children:P==="asc"?t.jsx(Xe,{size:14}):t.jsx(Ze,{size:14})})]})}),t.jsx(ie,{onClick:()=>U("updated_at"),className:"cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-800 select-none",children:t.jsxs("div",{className:"flex items-center",children:[i("documentPanel.documentManager.columns.updated"),v==="updated_at"&&t.jsx("span",{className:"ml-1",children:P==="asc"?t.jsx(Xe,{size:14}):t.jsx(Ze,{size:14})})]})}),t.jsx(ie,{className:"w-16 text-center",children:i("documentPanel.documentManager.columns.select")})]})}),t.jsx(dt,{className:"text-sm overflow-auto",children:ae&&ae.map(o=>t.jsxs(ma,{children:[t.jsx(oe,{className:"truncate font-mono overflow-visible max-w-[250px]",children:g?t.jsxs(t.Fragment,{children:[t.jsxs("div",{className:"group relative overflow-visible tooltip-container",children:[t.jsx("div",{className:"truncate",children:oa(o,30)}),t.jsx("div",{className:"invisible group-hover:visible tooltip",children:o.file_path})]}),t.jsx("div",{className:"text-xs text-gray-500",children:o.id})]}):t.jsxs("div",{className:"group relative overflow-visible tooltip-container",children:[t.jsx("div",{className:"truncate",children:o.id}),t.jsx("div",{className:"invisible group-hover:visible tooltip",children:o.file_path})]})}),t.jsx(oe,{className:"max-w-xs min-w-45 truncate overflow-visible",children:t.jsxs("div",{className:"group relative overflow-visible tooltip-container",children:[t.jsx("div",{className:"truncate",children:o.content_summary}),t.jsx("div",{className:"invisible group-hover:visible tooltip",children:o.content_summary})]})}),t.jsx(oe,{children:t.jsxs("div",{className:"group relative flex items-center overflow-visible tooltip-container",children:[o.status==="processed"&&t.jsx("span",{className:"text-green-600",children:i("documentPanel.documentManager.status.completed")}),o.status==="processing"&&t.jsx("span",{className:"text-blue-600",children:i("documentPanel.documentManager.status.processing")}),o.status==="pending"&&t.jsx("span",{className:"text-yellow-600",children:i("documentPanel.documentManager.status.pending")}),o.status==="failed"&&t.jsx("span",{className:"text-red-600",children:i("documentPanel.documentManager.status.failed")}),o.error_msg?t.jsx(ya,{className:"ml-2 h-4 w-4 text-yellow-500"}):o.metadata&&Object.keys(o.metadata).length>0&&t.jsx(tn,{className:"ml-2 h-4 w-4 text-blue-500"}),(o.error_msg||o.metadata&&Object.keys(o.metadata).length>0)&&t.jsxs("div",{className:"invisible group-hover:visible tooltip",children:[o.error_msg&&t.jsx("pre",{children:o.error_msg}),o.metadata&&Object.keys(o.metadata).length>0&&t.jsx("pre",{children:gi(o.metadata)})]})]})}),t.jsx(oe,{children:o.content_length??"-"}),t.jsx(oe,{children:o.chunks_count??"-"}),t.jsx(oe,{className:"truncate",children:new Date(o.created_at).toLocaleString()}),t.jsx(oe,{className:"truncate",children:new Date(o.updated_at).toLocaleString()}),t.jsx(oe,{className:"text-center",children:t.jsx(ot,{checked:I.includes(o.id),onCheckedChange:d=>fe(o.id,d===!0),className:"mx-auto"})})]},o.id))})]})})})]})]})]})]})}export{ki as D,za as S,ca as a,Ca as b,pa as c,wi as d,da as e};
