name: Linting and Formatting

on:
    push:
        branches:
            - main
    pull_request:
        branches:
            - main

jobs:
    lint-and-format:
        runs-on: ubuntu-latest

        steps:
            - name: Checkout code
              uses: actions/checkout@v2

            - name: Set up Python
              uses: actions/setup-python@v2
              with:
                python-version: '3.x'

            - name: Install dependencies
              run: |
                python -m pip install --upgrade pip
                pip install pre-commit

            - name: Run pre-commit
              run: pre-commit run --all-files --show-diff-on-failure
