import{p as U,c as z,d as fn,v as hn,S as gn}from"./markdown-vendor-DmIvJdn7.js";import F from"./katex-Bs9BEMzR.js";import"./ui-vendor-CeCm8EER.js";import"./react-vendor-DEwriMA6.js";class P{constructor(e,l,o){this.normal=l,this.property=e,o&&(this.space=o)}}P.prototype.normal={};P.prototype.property={};P.prototype.space=void 0;function $(n,e){const l={},o={};for(const a of n)Object.assign(l,a.property),Object.assign(o,a.normal);return new P(l,o,e)}function C(n){return n.toLowerCase()}class h{constructor(e,l){this.attribute=l,this.property=e}}h.prototype.attribute="";h.prototype.booleanish=!1;h.prototype.boolean=!1;h.prototype.commaOrSpaceSeparated=!1;h.prototype.commaSeparated=!1;h.prototype.defined=!1;h.prototype.mustUseProperty=!1;h.prototype.number=!1;h.prototype.overloadedBoolean=!1;h.prototype.property="";h.prototype.spaceSeparated=!1;h.prototype.space=void 0;let mn=0;const s=k(),f=k(),D=k(),t=k(),p=k(),v=k(),g=k();function k(){return 2**++mn}const T=Object.freeze(Object.defineProperty({__proto__:null,boolean:s,booleanish:f,commaOrSpaceSeparated:g,commaSeparated:v,number:t,overloadedBoolean:D,spaceSeparated:p},Symbol.toStringTag,{value:"Module"})),L=Object.keys(T);class R extends h{constructor(e,l,o,a){let r=-1;if(super(e,l),j(this,"space",a),typeof o=="number")for(;++r<L.length;){const i=L[r];j(this,L[r],(o&T[i])===T[i])}}}R.prototype.defined=!0;function j(n,e,l){l&&(n[e]=l)}function x(n){const e={},l={};for(const[o,a]of Object.entries(n.properties)){const r=new R(o,n.transform(n.attributes||{},o),a,n.space);n.mustUseProperty&&n.mustUseProperty.includes(o)&&(r.mustUseProperty=!0),e[o]=r,l[C(o)]=o,l[C(r.attribute)]=o}return new P(e,l,n.space)}const Z=x({properties:{ariaActiveDescendant:null,ariaAtomic:f,ariaAutoComplete:null,ariaBusy:f,ariaChecked:f,ariaColCount:t,ariaColIndex:t,ariaColSpan:t,ariaControls:p,ariaCurrent:null,ariaDescribedBy:p,ariaDetails:null,ariaDisabled:f,ariaDropEffect:p,ariaErrorMessage:null,ariaExpanded:f,ariaFlowTo:p,ariaGrabbed:f,ariaHasPopup:null,ariaHidden:f,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:p,ariaLevel:t,ariaLive:null,ariaModal:f,ariaMultiLine:f,ariaMultiSelectable:f,ariaOrientation:null,ariaOwns:p,ariaPlaceholder:null,ariaPosInSet:t,ariaPressed:f,ariaReadOnly:f,ariaRelevant:null,ariaRequired:f,ariaRoleDescription:p,ariaRowCount:t,ariaRowIndex:t,ariaRowSpan:t,ariaSelected:f,ariaSetSize:t,ariaSort:null,ariaValueMax:t,ariaValueMin:t,ariaValueNow:t,ariaValueText:null,role:null},transform(n,e){return e==="role"?e:"aria-"+e.slice(4).toLowerCase()}});function J(n,e){return e in n?n[e]:e}function Q(n,e){return J(n,e.toLowerCase())}const yn=x({attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:v,acceptCharset:p,accessKey:p,action:null,allow:null,allowFullScreen:s,allowPaymentRequest:s,allowUserMedia:s,alt:null,as:null,async:s,autoCapitalize:null,autoComplete:p,autoFocus:s,autoPlay:s,blocking:p,capture:null,charSet:null,checked:s,cite:null,className:p,cols:t,colSpan:null,content:null,contentEditable:f,controls:s,controlsList:p,coords:t|v,crossOrigin:null,data:null,dateTime:null,decoding:null,default:s,defer:s,dir:null,dirName:null,disabled:s,download:D,draggable:f,encType:null,enterKeyHint:null,fetchPriority:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:s,formTarget:null,headers:p,height:t,hidden:D,high:t,href:null,hrefLang:null,htmlFor:p,httpEquiv:p,id:null,imageSizes:null,imageSrcSet:null,inert:s,inputMode:null,integrity:null,is:null,isMap:s,itemId:null,itemProp:p,itemRef:p,itemScope:s,itemType:p,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:s,low:t,manifest:null,max:null,maxLength:t,media:null,method:null,min:null,minLength:t,multiple:s,muted:s,name:null,nonce:null,noModule:s,noValidate:s,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforeMatch:null,onBeforePrint:null,onBeforeToggle:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextLost:null,onContextMenu:null,onContextRestored:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onScrollEnd:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:s,optimum:t,pattern:null,ping:p,placeholder:null,playsInline:s,popover:null,popoverTarget:null,popoverTargetAction:null,poster:null,preload:null,readOnly:s,referrerPolicy:null,rel:p,required:s,reversed:s,rows:t,rowSpan:t,sandbox:p,scope:null,scoped:s,seamless:s,selected:s,shadowRootClonable:s,shadowRootDelegatesFocus:s,shadowRootMode:null,shape:null,size:t,sizes:null,slot:null,span:t,spellCheck:f,src:null,srcDoc:null,srcLang:null,srcSet:null,start:t,step:null,style:null,tabIndex:t,target:null,title:null,translate:null,type:null,typeMustMatch:s,useMap:null,value:f,width:t,wrap:null,writingSuggestions:null,align:null,aLink:null,archive:p,axis:null,background:null,bgColor:null,border:t,borderColor:null,bottomMargin:t,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:s,declare:s,event:null,face:null,frame:null,frameBorder:null,hSpace:t,leftMargin:t,link:null,longDesc:null,lowSrc:null,marginHeight:t,marginWidth:t,noResize:s,noHref:s,noShade:s,noWrap:s,object:null,profile:null,prompt:null,rev:null,rightMargin:t,rules:null,scheme:null,scrolling:f,standby:null,summary:null,text:null,topMargin:t,valueType:null,version:null,vAlign:null,vLink:null,vSpace:t,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:s,disableRemotePlayback:s,prefix:null,property:null,results:t,security:null,unselectable:null},space:"html",transform:Q}),bn=x({attributes:{accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",className:"class",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",crossOrigin:"crossorigin",dataType:"datatype",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",hrefLang:"hreflang",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",horizOriginY:"horiz-origin-y",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",navDown:"nav-down",navDownLeft:"nav-down-left",navDownRight:"nav-down-right",navLeft:"nav-left",navNext:"nav-next",navPrev:"nav-prev",navRight:"nav-right",navUp:"nav-up",navUpLeft:"nav-up-left",navUpRight:"nav-up-right",onAbort:"onabort",onActivate:"onactivate",onAfterPrint:"onafterprint",onBeforePrint:"onbeforeprint",onBegin:"onbegin",onCancel:"oncancel",onCanPlay:"oncanplay",onCanPlayThrough:"oncanplaythrough",onChange:"onchange",onClick:"onclick",onClose:"onclose",onCopy:"oncopy",onCueChange:"oncuechange",onCut:"oncut",onDblClick:"ondblclick",onDrag:"ondrag",onDragEnd:"ondragend",onDragEnter:"ondragenter",onDragExit:"ondragexit",onDragLeave:"ondragleave",onDragOver:"ondragover",onDragStart:"ondragstart",onDrop:"ondrop",onDurationChange:"ondurationchange",onEmptied:"onemptied",onEnd:"onend",onEnded:"onended",onError:"onerror",onFocus:"onfocus",onFocusIn:"onfocusin",onFocusOut:"onfocusout",onHashChange:"onhashchange",onInput:"oninput",onInvalid:"oninvalid",onKeyDown:"onkeydown",onKeyPress:"onkeypress",onKeyUp:"onkeyup",onLoad:"onload",onLoadedData:"onloadeddata",onLoadedMetadata:"onloadedmetadata",onLoadStart:"onloadstart",onMessage:"onmessage",onMouseDown:"onmousedown",onMouseEnter:"onmouseenter",onMouseLeave:"onmouseleave",onMouseMove:"onmousemove",onMouseOut:"onmouseout",onMouseOver:"onmouseover",onMouseUp:"onmouseup",onMouseWheel:"onmousewheel",onOffline:"onoffline",onOnline:"ononline",onPageHide:"onpagehide",onPageShow:"onpageshow",onPaste:"onpaste",onPause:"onpause",onPlay:"onplay",onPlaying:"onplaying",onPopState:"onpopstate",onProgress:"onprogress",onRateChange:"onratechange",onRepeat:"onrepeat",onReset:"onreset",onResize:"onresize",onScroll:"onscroll",onSeeked:"onseeked",onSeeking:"onseeking",onSelect:"onselect",onShow:"onshow",onStalled:"onstalled",onStorage:"onstorage",onSubmit:"onsubmit",onSuspend:"onsuspend",onTimeUpdate:"ontimeupdate",onToggle:"ontoggle",onUnload:"onunload",onVolumeChange:"onvolumechange",onWaiting:"onwaiting",onZoom:"onzoom",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",referrerPolicy:"referrerpolicy",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDashArray:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeLineCap:"stroke-linecap",strokeLineJoin:"stroke-linejoin",strokeMiterLimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tabIndex:"tabindex",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",transformOrigin:"transform-origin",typeOf:"typeof",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",playbackOrder:"playbackorder",timelineBegin:"timelinebegin"},properties:{about:g,accentHeight:t,accumulate:null,additive:null,alignmentBaseline:null,alphabetic:t,amplitude:t,arabicForm:null,ascent:t,attributeName:null,attributeType:null,azimuth:t,bandwidth:null,baselineShift:null,baseFrequency:null,baseProfile:null,bbox:null,begin:null,bias:t,by:null,calcMode:null,capHeight:t,className:p,clip:null,clipPath:null,clipPathUnits:null,clipRule:null,color:null,colorInterpolation:null,colorInterpolationFilters:null,colorProfile:null,colorRendering:null,content:null,contentScriptType:null,contentStyleType:null,crossOrigin:null,cursor:null,cx:null,cy:null,d:null,dataType:null,defaultAction:null,descent:t,diffuseConstant:t,direction:null,display:null,dur:null,divisor:t,dominantBaseline:null,download:s,dx:null,dy:null,edgeMode:null,editable:null,elevation:t,enableBackground:null,end:null,event:null,exponent:t,externalResourcesRequired:null,fill:null,fillOpacity:t,fillRule:null,filter:null,filterRes:null,filterUnits:null,floodColor:null,floodOpacity:null,focusable:null,focusHighlight:null,fontFamily:null,fontSize:null,fontSizeAdjust:null,fontStretch:null,fontStyle:null,fontVariant:null,fontWeight:null,format:null,fr:null,from:null,fx:null,fy:null,g1:v,g2:v,glyphName:v,glyphOrientationHorizontal:null,glyphOrientationVertical:null,glyphRef:null,gradientTransform:null,gradientUnits:null,handler:null,hanging:t,hatchContentUnits:null,hatchUnits:null,height:null,href:null,hrefLang:null,horizAdvX:t,horizOriginX:t,horizOriginY:t,id:null,ideographic:t,imageRendering:null,initialVisibility:null,in:null,in2:null,intercept:t,k:t,k1:t,k2:t,k3:t,k4:t,kernelMatrix:g,kernelUnitLength:null,keyPoints:null,keySplines:null,keyTimes:null,kerning:null,lang:null,lengthAdjust:null,letterSpacing:null,lightingColor:null,limitingConeAngle:t,local:null,markerEnd:null,markerMid:null,markerStart:null,markerHeight:null,markerUnits:null,markerWidth:null,mask:null,maskContentUnits:null,maskUnits:null,mathematical:null,max:null,media:null,mediaCharacterEncoding:null,mediaContentEncodings:null,mediaSize:t,mediaTime:null,method:null,min:null,mode:null,name:null,navDown:null,navDownLeft:null,navDownRight:null,navLeft:null,navNext:null,navPrev:null,navRight:null,navUp:null,navUpLeft:null,navUpRight:null,numOctaves:null,observer:null,offset:null,onAbort:null,onActivate:null,onAfterPrint:null,onBeforePrint:null,onBegin:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnd:null,onEnded:null,onError:null,onFocus:null,onFocusIn:null,onFocusOut:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadStart:null,onMessage:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onMouseWheel:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRepeat:null,onReset:null,onResize:null,onScroll:null,onSeeked:null,onSeeking:null,onSelect:null,onShow:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnload:null,onVolumeChange:null,onWaiting:null,onZoom:null,opacity:null,operator:null,order:null,orient:null,orientation:null,origin:null,overflow:null,overlay:null,overlinePosition:t,overlineThickness:t,paintOrder:null,panose1:null,path:null,pathLength:t,patternContentUnits:null,patternTransform:null,patternUnits:null,phase:null,ping:p,pitch:null,playbackOrder:null,pointerEvents:null,points:null,pointsAtX:t,pointsAtY:t,pointsAtZ:t,preserveAlpha:null,preserveAspectRatio:null,primitiveUnits:null,propagate:null,property:g,r:null,radius:null,referrerPolicy:null,refX:null,refY:null,rel:g,rev:g,renderingIntent:null,repeatCount:null,repeatDur:null,requiredExtensions:g,requiredFeatures:g,requiredFonts:g,requiredFormats:g,resource:null,restart:null,result:null,rotate:null,rx:null,ry:null,scale:null,seed:null,shapeRendering:null,side:null,slope:null,snapshotTime:null,specularConstant:t,specularExponent:t,spreadMethod:null,spacing:null,startOffset:null,stdDeviation:null,stemh:null,stemv:null,stitchTiles:null,stopColor:null,stopOpacity:null,strikethroughPosition:t,strikethroughThickness:t,string:null,stroke:null,strokeDashArray:g,strokeDashOffset:null,strokeLineCap:null,strokeLineJoin:null,strokeMiterLimit:t,strokeOpacity:t,strokeWidth:null,style:null,surfaceScale:t,syncBehavior:null,syncBehaviorDefault:null,syncMaster:null,syncTolerance:null,syncToleranceDefault:null,systemLanguage:g,tabIndex:t,tableValues:null,target:null,targetX:t,targetY:t,textAnchor:null,textDecoration:null,textRendering:null,textLength:null,timelineBegin:null,title:null,transformBehavior:null,type:null,typeOf:g,to:null,transform:null,transformOrigin:null,u1:null,u2:null,underlinePosition:t,underlineThickness:t,unicode:null,unicodeBidi:null,unicodeRange:null,unitsPerEm:t,values:null,vAlphabetic:t,vMathematical:t,vectorEffect:null,vHanging:t,vIdeographic:t,version:null,vertAdvY:t,vertOriginX:t,vertOriginY:t,viewBox:null,viewTarget:null,visibility:null,width:null,widths:null,wordSpacing:null,writingMode:null,x:null,x1:null,x2:null,xChannelSelector:null,xHeight:t,y:null,y1:null,y2:null,yChannelSelector:null,z:null,zoomAndPan:null},space:"svg",transform:J}),nn=x({properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null},space:"xlink",transform(n,e){return"xlink:"+e.slice(5).toLowerCase()}}),en=x({attributes:{xmlnsxlink:"xmlns:xlink"},properties:{xmlnsXLink:null,xmlns:null},space:"xmlns",transform:Q}),ln=x({properties:{xmlBase:null,xmlLang:null,xmlSpace:null},space:"xml",transform(n,e){return"xml:"+e.slice(3).toLowerCase()}}),kn=/[A-Z]/g,H=/-[a-z]/g,wn=/^data[-\w.:]+$/i;function vn(n,e){const l=C(e);let o=e,a=h;if(l in n.normal)return n.property[n.normal[l]];if(l.length>4&&l.slice(0,4)==="data"&&wn.test(e)){if(e.charAt(4)==="-"){const r=e.slice(5).replace(H,Sn);o="data"+r.charAt(0).toUpperCase()+r.slice(1)}else{const r=e.slice(4);if(!H.test(r)){let i=r.replace(kn,xn);i.charAt(0)!=="-"&&(i="-"+i),e="data"+i}}a=R}return new a(o,e)}function xn(n){return"-"+n.toLowerCase()}function Sn(n){return n.charAt(1).toUpperCase()}const Cn=$([Z,yn,nn,en,ln],"html"),Pn=$([Z,bn,nn,en,ln],"svg"),V=/[#.]/g;function Mn(n,e){const l=n||"",o={};let a=0,r,i;for(;a<l.length;){V.lastIndex=a;const c=V.exec(l),u=l.slice(a,c?c.index:l.length);u&&(r?r==="#"?o.id=u:Array.isArray(o.className)?o.className.push(u):o.className=[u]:i=u,a+=u.length),c&&(r=c[0],a++)}return{type:"element",tagName:i||e||"div",properties:o,children:[]}}function tn(n,e,l){const o=l?Dn(l):void 0;function a(r,i,...c){let u;if(r==null){u={type:"root",children:[]};const d=i;c.unshift(d)}else{u=Mn(r,e);const d=u.tagName.toLowerCase(),m=o?o.get(d):void 0;if(u.tagName=m||d,On(i))c.unshift(i);else for(const[y,S]of Object.entries(i))Ln(n,u.properties,y,S)}for(const d of c)N(u.children,d);return u.type==="element"&&u.tagName==="template"&&(u.content={type:"root",children:u.children},u.children=[]),u}return a}function On(n){if(n===null||typeof n!="object"||Array.isArray(n))return!0;if(typeof n.type!="string")return!1;const e=n,l=Object.keys(n);for(const o of l){const a=e[o];if(a&&typeof a=="object"){if(!Array.isArray(a))return!0;const r=a;for(const i of r)if(typeof i!="number"&&typeof i!="string")return!0}}return!!("children"in n&&Array.isArray(n.children))}function Ln(n,e,l,o){const a=vn(n,l);let r;if(o!=null){if(typeof o=="number"){if(Number.isNaN(o))return;r=o}else typeof o=="boolean"?r=o:typeof o=="string"?a.spaceSeparated?r=U(o):a.commaSeparated?r=z(o):a.commaOrSpaceSeparated?r=U(z(o).join(" ")):r=K(a,a.property,o):Array.isArray(o)?r=[...o]:r=a.property==="style"?An(o):String(o);if(Array.isArray(r)){const i=[];for(const c of r)i.push(K(a,a.property,c));r=i}a.property==="className"&&Array.isArray(e.className)&&(r=e.className.concat(r)),e[a.property]=r}}function N(n,e){if(e!=null)if(typeof e=="number"||typeof e=="string")n.push({type:"text",value:String(e)});else if(Array.isArray(e))for(const l of e)N(n,l);else if(typeof e=="object"&&"type"in e)e.type==="root"?N(n,e.children):n.push(e);else throw new Error("Expected node, nodes, or string, got `"+e+"`")}function K(n,e,l){if(typeof l=="string"){if(n.number&&l&&!Number.isNaN(Number(l)))return Number(l);if((n.boolean||n.overloadedBoolean)&&(l===""||C(l)===C(e)))return!0}return l}function An(n){const e=[];for(const[l,o]of Object.entries(n))e.push([l,o].join(": "));return e.join("; ")}function Dn(n){const e=new Map;for(const l of n)e.set(l.toLowerCase(),l);return e}const Tn=["altGlyph","altGlyphDef","altGlyphItem","animateColor","animateMotion","animateTransform","clipPath","feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","foreignObject","glyphRef","linearGradient","radialGradient","solidColor","textArea","textPath"],Nn=tn(Cn,"div"),En=tn(Pn,"g",Tn),A={html:"http://www.w3.org/1999/xhtml",svg:"http://www.w3.org/2000/svg"};function Rn(n,e){return on(n,{})||{type:"root",children:[]}}function on(n,e){const l=In(n,e);return l&&e.afterTransform&&e.afterTransform(n,l),l}function In(n,e){switch(n.nodeType){case 1:return Fn(n,e);case 3:return Un(n);case 8:return zn(n);case 9:return W(n,e);case 10:return Bn();case 11:return W(n,e);default:return}}function W(n,e){return{type:"root",children:rn(n,e)}}function Bn(){return{type:"doctype"}}function Un(n){return{type:"text",value:n.nodeValue||""}}function zn(n){return{type:"comment",value:n.nodeValue||""}}function Fn(n,e){const l=n.namespaceURI,o=l===A.svg?En:Nn,a=l===A.html?n.tagName.toLowerCase():n.tagName,r=l===A.html&&a==="template"?n.content:n,i=n.getAttributeNames(),c={};let u=-1;for(;++u<i.length;)c[i[u]]=n.getAttribute(i[u])||"";return o(a,c,rn(r,e))}function rn(n,e){const l=n.childNodes,o=[];let a=-1;for(;++a<l.length;){const r=on(l[a],e);r!==void 0&&o.push(r)}return o}new DOMParser;function jn(n,e){const l=Hn(n);return Rn(l)}function Hn(n){const e=document.createElement("template");return e.innerHTML=n,e.content}const q=function(n,e,l){const o=fn(l);if(!n||!n.type||!n.children)throw new Error("Expected parent node");if(typeof e=="number"){if(e<0||e===Number.POSITIVE_INFINITY)throw new Error("Expected positive finite number as index")}else if(e=n.children.indexOf(e),e<0)throw new Error("Expected child node or index");for(;++e<n.children.length;)if(o(n.children[e],e,n))return n.children[e]},w=function(n){if(n==null)return Wn;if(typeof n=="string")return Kn(n);if(typeof n=="object")return Vn(n);if(typeof n=="function")return I(n);throw new Error("Expected function, string, or array as `test`")};function Vn(n){const e=[];let l=-1;for(;++l<n.length;)e[l]=w(n[l]);return I(o);function o(...a){let r=-1;for(;++r<e.length;)if(e[r].apply(this,a))return!0;return!1}}function Kn(n){return I(e);function e(l){return l.tagName===n}}function I(n){return e;function e(l,o,a){return!!(qn(l)&&n.call(this,l,typeof o=="number"?o:void 0,a||void 0))}}function Wn(n){return!!(n&&typeof n=="object"&&"type"in n&&n.type==="element"&&"tagName"in n&&typeof n.tagName=="string")}function qn(n){return n!==null&&typeof n=="object"&&"type"in n&&"tagName"in n}const X=/\n/g,_=/[\t ]+/g,E=w("br"),Y=w(Qn),Xn=w("p"),G=w("tr"),_n=w(["datalist","head","noembed","noframes","noscript","rp","script","style","template","title",Jn,ne]),an=w(["address","article","aside","blockquote","body","caption","center","dd","dialog","dir","dl","dt","div","figure","figcaption","footer","form,","h1","h2","h3","h4","h5","h6","header","hgroup","hr","html","legend","li","listing","main","menu","nav","ol","p","plaintext","pre","section","ul","xmp"]);function Yn(n,e){const l=e||{},o="children"in n?n.children:[],a=an(n),r=cn(n,{whitespace:l.whitespace||"normal"}),i=[];(n.type==="text"||n.type==="comment")&&i.push(...sn(n,{breakBefore:!0,breakAfter:!0}));let c=-1;for(;++c<o.length;)i.push(...un(o[c],n,{whitespace:r,breakBefore:c?void 0:a,breakAfter:c<o.length-1?E(o[c+1]):a}));const u=[];let d;for(c=-1;++c<i.length;){const m=i[c];typeof m=="number"?d!==void 0&&m>d&&(d=m):m&&(d!==void 0&&d>-1&&u.push(`
`.repeat(d)||" "),d=-1,u.push(m))}return u.join("")}function un(n,e,l){return n.type==="element"?Gn(n,e,l):n.type==="text"?l.whitespace==="normal"?sn(n,l):$n(n):[]}function Gn(n,e,l){const o=cn(n,l),a=n.children||[];let r=-1,i=[];if(_n(n))return i;let c,u;for(E(n)||G(n)&&q(e,n,G)?u=`
`:Xn(n)?(c=2,u=2):an(n)&&(c=1,u=1);++r<a.length;)i=i.concat(un(a[r],n,{whitespace:o,breakBefore:r?void 0:c,breakAfter:r<a.length-1?E(a[r+1]):u}));return Y(n)&&q(e,n,Y)&&i.push("	"),c&&i.unshift(c),u&&i.push(u),i}function sn(n,e){const l=String(n.value),o=[],a=[];let r=0;for(;r<=l.length;){X.lastIndex=r;const u=X.exec(l),d=u&&"index"in u?u.index:l.length;o.push(Zn(l.slice(r,d).replace(/[\u061C\u200E\u200F\u202A-\u202E\u2066-\u2069]/g,""),r===0?e.breakBefore:!0,d===l.length?e.breakAfter:!0)),r=d+1}let i=-1,c;for(;++i<o.length;)o[i].charCodeAt(o[i].length-1)===8203||i<o.length-1&&o[i+1].charCodeAt(0)===8203?(a.push(o[i]),c=void 0):o[i]?(typeof c=="number"&&a.push(c),a.push(o[i]),c=0):(i===0||i===o.length-1)&&a.push(0);return a}function $n(n){return[String(n.value)]}function Zn(n,e,l){const o=[];let a=0,r;for(;a<n.length;){_.lastIndex=a;const i=_.exec(n);r=i?i.index:n.length,!a&&!r&&i&&!e&&o.push(""),a!==r&&o.push(n.slice(a,r)),a=i?r+i[0].length:r}return a!==r&&!l&&o.push(""),o.join(" ")}function cn(n,e){if(n.type==="element"){const l=n.properties||{};switch(n.tagName){case"listing":case"plaintext":case"xmp":return"pre";case"nobr":return"nowrap";case"pre":return l.wrap?"pre-wrap":"pre";case"td":case"th":return l.noWrap?"nowrap":e.whitespace;case"textarea":return"pre-wrap"}}return e.whitespace}function Jn(n){return!!(n.properties||{}).hidden}function Qn(n){return n.tagName==="td"||n.tagName==="th"}function ne(n){return n.tagName==="dialog"&&!(n.properties||{}).open}const ee={},le=[];function ie(n){const e=n||ee;return function(l,o){hn(l,"element",function(a,r){const i=Array.isArray(a.properties.className)?a.properties.className:le,c=i.includes("language-math"),u=i.includes("math-display"),d=i.includes("math-inline");let m=u;if(!c&&!u&&!d)return;let y=r[r.length-1],S=a;if(a.tagName==="code"&&c&&y&&y.type==="element"&&y.tagName==="pre"&&(S=y,y=r[r.length-2],m=!0),!y)return;const M=Yn(S,{whitespace:"pre"});let b;try{b=F.renderToString(M,{...e,displayMode:m,throwOnError:!0})}catch(O){const B=O,dn=B.name.toLowerCase();o.message("Could not render math with KaTeX",{ancestors:[...r,a],cause:B,place:a.position,ruleId:dn,source:"rehype-katex"});try{b=F.renderToString(M,{...e,displayMode:m,strict:"ignore",throwOnError:!1})}catch{b=[{type:"element",tagName:"span",properties:{className:["katex-error"],style:"color:"+(e.errorColor||"#cc0000"),title:String(O)},children:[{type:"text",value:M}]}]}}typeof b=="string"&&(b=jn(b).children);const pn=y.children.indexOf(S);return y.children.splice(pn,1,...b),gn})}}export{ie as default};
