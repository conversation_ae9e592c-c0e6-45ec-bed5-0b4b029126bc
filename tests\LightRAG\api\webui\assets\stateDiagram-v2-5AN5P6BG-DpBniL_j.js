import{s as r,b as e,a,S as i}from"./chunk-OW32GOEJ-DFogk2Dj.js";import{_ as s}from"./mermaid-vendor-BNDdXxLk.js";import"./chunk-BFAMUDN2-U-jvyawD.js";import"./chunk-SKB7J2MH-Dh8Tfa_E.js";import"./feature-graph-BWr9U7tw.js";import"./react-vendor-DEwriMA6.js";import"./graph-vendor-B-X5JegA.js";import"./ui-vendor-CeCm8EER.js";import"./utils-vendor-BysuhMZA.js";var f={parser:a,get db(){return new i(2)},renderer:e,styles:r,init:s(t=>{t.state||(t.state={}),t.state.arrowMarkerAbsolute=t.arrowMarkerAbsolute},"init")};export{f as diagram};
