import{g as de}from"./chunk-E2GYISFI-Cy7NDsKx.js";import{_ as d,G as at,d as R,e as ge,l as m,z as ue,B as pe,C as fe,c as z,ab as xe,U as ye,a0 as be,X as we,ac as Z,ad as Yt,ae as me,u as tt,k as Le,af as Se,ag as xt,ah as ve,i as Tt}from"./mermaid-vendor-BNDdXxLk.js";import{c as Ee}from"./clone-DwaenhoE.js";import{G as _e}from"./graph-Ay_vBBag.js";import"./feature-graph-BWr9U7tw.js";import"./react-vendor-DEwriMA6.js";import"./graph-vendor-B-X5JegA.js";import"./ui-vendor-CeCm8EER.js";import"./utils-vendor-BysuhMZA.js";import"./_baseUniq-I-HKKcQv.js";var yt=function(){var e=d(function(N,x,g,u){for(g=g||{},u=N.length;u--;g[N[u]]=x);return g},"o"),t=[1,7],r=[1,13],n=[1,14],i=[1,15],a=[1,19],s=[1,16],l=[1,17],o=[1,18],f=[8,30],h=[8,21,28,29,30,31,32,40,44,47],y=[1,23],b=[1,24],L=[8,15,16,21,28,29,30,31,32,40,44,47],E=[8,15,16,21,27,28,29,30,31,32,40,44,47],D=[1,49],v={trace:d(function(){},"trace"),yy:{},symbols_:{error:2,spaceLines:3,SPACELINE:4,NL:5,separator:6,SPACE:7,EOF:8,start:9,BLOCK_DIAGRAM_KEY:10,document:11,stop:12,statement:13,link:14,LINK:15,START_LINK:16,LINK_LABEL:17,STR:18,nodeStatement:19,columnsStatement:20,SPACE_BLOCK:21,blockStatement:22,classDefStatement:23,cssClassStatement:24,styleStatement:25,node:26,SIZE:27,COLUMNS:28,"id-block":29,end:30,block:31,NODE_ID:32,nodeShapeNLabel:33,dirList:34,DIR:35,NODE_DSTART:36,NODE_DEND:37,BLOCK_ARROW_START:38,BLOCK_ARROW_END:39,classDef:40,CLASSDEF_ID:41,CLASSDEF_STYLEOPTS:42,DEFAULT:43,class:44,CLASSENTITY_IDS:45,STYLECLASS:46,style:47,STYLE_ENTITY_IDS:48,STYLE_DEFINITION_DATA:49,$accept:0,$end:1},terminals_:{2:"error",4:"SPACELINE",5:"NL",7:"SPACE",8:"EOF",10:"BLOCK_DIAGRAM_KEY",15:"LINK",16:"START_LINK",17:"LINK_LABEL",18:"STR",21:"SPACE_BLOCK",27:"SIZE",28:"COLUMNS",29:"id-block",30:"end",31:"block",32:"NODE_ID",35:"DIR",36:"NODE_DSTART",37:"NODE_DEND",38:"BLOCK_ARROW_START",39:"BLOCK_ARROW_END",40:"classDef",41:"CLASSDEF_ID",42:"CLASSDEF_STYLEOPTS",43:"DEFAULT",44:"class",45:"CLASSENTITY_IDS",46:"STYLECLASS",47:"style",48:"STYLE_ENTITY_IDS",49:"STYLE_DEFINITION_DATA"},productions_:[0,[3,1],[3,2],[3,2],[6,1],[6,1],[6,1],[9,3],[12,1],[12,1],[12,2],[12,2],[11,1],[11,2],[14,1],[14,4],[13,1],[13,1],[13,1],[13,1],[13,1],[13,1],[13,1],[19,3],[19,2],[19,1],[20,1],[22,4],[22,3],[26,1],[26,2],[34,1],[34,2],[33,3],[33,4],[23,3],[23,3],[24,3],[25,3]],performAction:d(function(x,g,u,w,S,c,_){var p=c.length-1;switch(S){case 4:w.getLogger().debug("Rule: separator (NL) ");break;case 5:w.getLogger().debug("Rule: separator (Space) ");break;case 6:w.getLogger().debug("Rule: separator (EOF) ");break;case 7:w.getLogger().debug("Rule: hierarchy: ",c[p-1]),w.setHierarchy(c[p-1]);break;case 8:w.getLogger().debug("Stop NL ");break;case 9:w.getLogger().debug("Stop EOF ");break;case 10:w.getLogger().debug("Stop NL2 ");break;case 11:w.getLogger().debug("Stop EOF2 ");break;case 12:w.getLogger().debug("Rule: statement: ",c[p]),typeof c[p].length=="number"?this.$=c[p]:this.$=[c[p]];break;case 13:w.getLogger().debug("Rule: statement #2: ",c[p-1]),this.$=[c[p-1]].concat(c[p]);break;case 14:w.getLogger().debug("Rule: link: ",c[p],x),this.$={edgeTypeStr:c[p],label:""};break;case 15:w.getLogger().debug("Rule: LABEL link: ",c[p-3],c[p-1],c[p]),this.$={edgeTypeStr:c[p],label:c[p-1]};break;case 18:const A=parseInt(c[p]),O=w.generateId();this.$={id:O,type:"space",label:"",width:A,children:[]};break;case 23:w.getLogger().debug("Rule: (nodeStatement link node) ",c[p-2],c[p-1],c[p]," typestr: ",c[p-1].edgeTypeStr);const X=w.edgeStrToEdgeData(c[p-1].edgeTypeStr);this.$=[{id:c[p-2].id,label:c[p-2].label,type:c[p-2].type,directions:c[p-2].directions},{id:c[p-2].id+"-"+c[p].id,start:c[p-2].id,end:c[p].id,label:c[p-1].label,type:"edge",directions:c[p].directions,arrowTypeEnd:X,arrowTypeStart:"arrow_open"},{id:c[p].id,label:c[p].label,type:w.typeStr2Type(c[p].typeStr),directions:c[p].directions}];break;case 24:w.getLogger().debug("Rule: nodeStatement (abc88 node size) ",c[p-1],c[p]),this.$={id:c[p-1].id,label:c[p-1].label,type:w.typeStr2Type(c[p-1].typeStr),directions:c[p-1].directions,widthInColumns:parseInt(c[p],10)};break;case 25:w.getLogger().debug("Rule: nodeStatement (node) ",c[p]),this.$={id:c[p].id,label:c[p].label,type:w.typeStr2Type(c[p].typeStr),directions:c[p].directions,widthInColumns:1};break;case 26:w.getLogger().debug("APA123",this?this:"na"),w.getLogger().debug("COLUMNS: ",c[p]),this.$={type:"column-setting",columns:c[p]==="auto"?-1:parseInt(c[p])};break;case 27:w.getLogger().debug("Rule: id-block statement : ",c[p-2],c[p-1]),w.generateId(),this.$={...c[p-2],type:"composite",children:c[p-1]};break;case 28:w.getLogger().debug("Rule: blockStatement : ",c[p-2],c[p-1],c[p]);const W=w.generateId();this.$={id:W,type:"composite",label:"",children:c[p-1]};break;case 29:w.getLogger().debug("Rule: node (NODE_ID separator): ",c[p]),this.$={id:c[p]};break;case 30:w.getLogger().debug("Rule: node (NODE_ID nodeShapeNLabel separator): ",c[p-1],c[p]),this.$={id:c[p-1],label:c[p].label,typeStr:c[p].typeStr,directions:c[p].directions};break;case 31:w.getLogger().debug("Rule: dirList: ",c[p]),this.$=[c[p]];break;case 32:w.getLogger().debug("Rule: dirList: ",c[p-1],c[p]),this.$=[c[p-1]].concat(c[p]);break;case 33:w.getLogger().debug("Rule: nodeShapeNLabel: ",c[p-2],c[p-1],c[p]),this.$={typeStr:c[p-2]+c[p],label:c[p-1]};break;case 34:w.getLogger().debug("Rule: BLOCK_ARROW nodeShapeNLabel: ",c[p-3],c[p-2]," #3:",c[p-1],c[p]),this.$={typeStr:c[p-3]+c[p],label:c[p-2],directions:c[p-1]};break;case 35:case 36:this.$={type:"classDef",id:c[p-1].trim(),css:c[p].trim()};break;case 37:this.$={type:"applyClass",id:c[p-1].trim(),styleClass:c[p].trim()};break;case 38:this.$={type:"applyStyles",id:c[p-1].trim(),stylesStr:c[p].trim()};break}},"anonymous"),table:[{9:1,10:[1,2]},{1:[3]},{11:3,13:4,19:5,20:6,21:t,22:8,23:9,24:10,25:11,26:12,28:r,29:n,31:i,32:a,40:s,44:l,47:o},{8:[1,20]},e(f,[2,12],{13:4,19:5,20:6,22:8,23:9,24:10,25:11,26:12,11:21,21:t,28:r,29:n,31:i,32:a,40:s,44:l,47:o}),e(h,[2,16],{14:22,15:y,16:b}),e(h,[2,17]),e(h,[2,18]),e(h,[2,19]),e(h,[2,20]),e(h,[2,21]),e(h,[2,22]),e(L,[2,25],{27:[1,25]}),e(h,[2,26]),{19:26,26:12,32:a},{11:27,13:4,19:5,20:6,21:t,22:8,23:9,24:10,25:11,26:12,28:r,29:n,31:i,32:a,40:s,44:l,47:o},{41:[1,28],43:[1,29]},{45:[1,30]},{48:[1,31]},e(E,[2,29],{33:32,36:[1,33],38:[1,34]}),{1:[2,7]},e(f,[2,13]),{26:35,32:a},{32:[2,14]},{17:[1,36]},e(L,[2,24]),{11:37,13:4,14:22,15:y,16:b,19:5,20:6,21:t,22:8,23:9,24:10,25:11,26:12,28:r,29:n,31:i,32:a,40:s,44:l,47:o},{30:[1,38]},{42:[1,39]},{42:[1,40]},{46:[1,41]},{49:[1,42]},e(E,[2,30]),{18:[1,43]},{18:[1,44]},e(L,[2,23]),{18:[1,45]},{30:[1,46]},e(h,[2,28]),e(h,[2,35]),e(h,[2,36]),e(h,[2,37]),e(h,[2,38]),{37:[1,47]},{34:48,35:D},{15:[1,50]},e(h,[2,27]),e(E,[2,33]),{39:[1,51]},{34:52,35:D,39:[2,31]},{32:[2,15]},e(E,[2,34]),{39:[2,32]}],defaultActions:{20:[2,7],23:[2,14],50:[2,15],52:[2,32]},parseError:d(function(x,g){if(g.recoverable)this.trace(x);else{var u=new Error(x);throw u.hash=g,u}},"parseError"),parse:d(function(x){var g=this,u=[0],w=[],S=[null],c=[],_=this.table,p="",A=0,O=0,X=2,W=1,ce=c.slice.call(arguments,1),M=Object.create(this.lexer),J={yy:{}};for(var gt in this.yy)Object.prototype.hasOwnProperty.call(this.yy,gt)&&(J.yy[gt]=this.yy[gt]);M.setInput(x,J.yy),J.yy.lexer=M,J.yy.parser=this,typeof M.yylloc>"u"&&(M.yylloc={});var ut=M.yylloc;c.push(ut);var oe=M.options&&M.options.ranges;typeof J.yy.parseError=="function"?this.parseError=J.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function he(H){u.length=u.length-2*H,S.length=S.length-H,c.length=c.length-H}d(he,"popStack");function Dt(){var H;return H=w.pop()||M.lex()||W,typeof H!="number"&&(H instanceof Array&&(w=H,H=w.pop()),H=g.symbols_[H]||H),H}d(Dt,"lex");for(var Y,Q,U,pt,$={},st,q,Nt,it;;){if(Q=u[u.length-1],this.defaultActions[Q]?U=this.defaultActions[Q]:((Y===null||typeof Y>"u")&&(Y=Dt()),U=_[Q]&&_[Q][Y]),typeof U>"u"||!U.length||!U[0]){var ft="";it=[];for(st in _[Q])this.terminals_[st]&&st>X&&it.push("'"+this.terminals_[st]+"'");M.showPosition?ft="Parse error on line "+(A+1)+`:
`+M.showPosition()+`
Expecting `+it.join(", ")+", got '"+(this.terminals_[Y]||Y)+"'":ft="Parse error on line "+(A+1)+": Unexpected "+(Y==W?"end of input":"'"+(this.terminals_[Y]||Y)+"'"),this.parseError(ft,{text:M.match,token:this.terminals_[Y]||Y,line:M.yylineno,loc:ut,expected:it})}if(U[0]instanceof Array&&U.length>1)throw new Error("Parse Error: multiple actions possible at state: "+Q+", token: "+Y);switch(U[0]){case 1:u.push(Y),S.push(M.yytext),c.push(M.yylloc),u.push(U[1]),Y=null,O=M.yyleng,p=M.yytext,A=M.yylineno,ut=M.yylloc;break;case 2:if(q=this.productions_[U[1]][1],$.$=S[S.length-q],$._$={first_line:c[c.length-(q||1)].first_line,last_line:c[c.length-1].last_line,first_column:c[c.length-(q||1)].first_column,last_column:c[c.length-1].last_column},oe&&($._$.range=[c[c.length-(q||1)].range[0],c[c.length-1].range[1]]),pt=this.performAction.apply($,[p,O,A,J.yy,U[1],S,c].concat(ce)),typeof pt<"u")return pt;q&&(u=u.slice(0,-1*q*2),S=S.slice(0,-1*q),c=c.slice(0,-1*q)),u.push(this.productions_[U[1]][0]),S.push($.$),c.push($._$),Nt=_[u[u.length-2]][u[u.length-1]],u.push(Nt);break;case 3:return!0}}return!0},"parse")},T=function(){var N={EOF:1,parseError:d(function(g,u){if(this.yy.parser)this.yy.parser.parseError(g,u);else throw new Error(g)},"parseError"),setInput:d(function(x,g){return this.yy=g||this.yy||{},this._input=x,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:d(function(){var x=this._input[0];this.yytext+=x,this.yyleng++,this.offset++,this.match+=x,this.matched+=x;var g=x.match(/(?:\r\n?|\n).*/g);return g?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),x},"input"),unput:d(function(x){var g=x.length,u=x.split(/(?:\r\n?|\n)/g);this._input=x+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-g),this.offset-=g;var w=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),u.length-1&&(this.yylineno-=u.length-1);var S=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:u?(u.length===w.length?this.yylloc.first_column:0)+w[w.length-u.length].length-u[0].length:this.yylloc.first_column-g},this.options.ranges&&(this.yylloc.range=[S[0],S[0]+this.yyleng-g]),this.yyleng=this.yytext.length,this},"unput"),more:d(function(){return this._more=!0,this},"more"),reject:d(function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},"reject"),less:d(function(x){this.unput(this.match.slice(x))},"less"),pastInput:d(function(){var x=this.matched.substr(0,this.matched.length-this.match.length);return(x.length>20?"...":"")+x.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:d(function(){var x=this.match;return x.length<20&&(x+=this._input.substr(0,20-x.length)),(x.substr(0,20)+(x.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:d(function(){var x=this.pastInput(),g=new Array(x.length+1).join("-");return x+this.upcomingInput()+`
`+g+"^"},"showPosition"),test_match:d(function(x,g){var u,w,S;if(this.options.backtrack_lexer&&(S={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(S.yylloc.range=this.yylloc.range.slice(0))),w=x[0].match(/(?:\r\n?|\n).*/g),w&&(this.yylineno+=w.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:w?w[w.length-1].length-w[w.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+x[0].length},this.yytext+=x[0],this.match+=x[0],this.matches=x,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(x[0].length),this.matched+=x[0],u=this.performAction.call(this,this.yy,this,g,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),u)return u;if(this._backtrack){for(var c in S)this[c]=S[c];return!1}return!1},"test_match"),next:d(function(){if(this.done)return this.EOF;this._input||(this.done=!0);var x,g,u,w;this._more||(this.yytext="",this.match="");for(var S=this._currentRules(),c=0;c<S.length;c++)if(u=this._input.match(this.rules[S[c]]),u&&(!g||u[0].length>g[0].length)){if(g=u,w=c,this.options.backtrack_lexer){if(x=this.test_match(u,S[c]),x!==!1)return x;if(this._backtrack){g=!1;continue}else return!1}else if(!this.options.flex)break}return g?(x=this.test_match(g,S[w]),x!==!1?x:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:d(function(){var g=this.next();return g||this.lex()},"lex"),begin:d(function(g){this.conditionStack.push(g)},"begin"),popState:d(function(){var g=this.conditionStack.length-1;return g>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:d(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:d(function(g){return g=this.conditionStack.length-1-Math.abs(g||0),g>=0?this.conditionStack[g]:"INITIAL"},"topState"),pushState:d(function(g){this.begin(g)},"pushState"),stateStackSize:d(function(){return this.conditionStack.length},"stateStackSize"),options:{},performAction:d(function(g,u,w,S){switch(w){case 0:return 10;case 1:return g.getLogger().debug("Found space-block"),31;case 2:return g.getLogger().debug("Found nl-block"),31;case 3:return g.getLogger().debug("Found space-block"),29;case 4:g.getLogger().debug(".",u.yytext);break;case 5:g.getLogger().debug("_",u.yytext);break;case 6:return 5;case 7:return u.yytext=-1,28;case 8:return u.yytext=u.yytext.replace(/columns\s+/,""),g.getLogger().debug("COLUMNS (LEX)",u.yytext),28;case 9:this.pushState("md_string");break;case 10:return"MD_STR";case 11:this.popState();break;case 12:this.pushState("string");break;case 13:g.getLogger().debug("LEX: POPPING STR:",u.yytext),this.popState();break;case 14:return g.getLogger().debug("LEX: STR end:",u.yytext),"STR";case 15:return u.yytext=u.yytext.replace(/space\:/,""),g.getLogger().debug("SPACE NUM (LEX)",u.yytext),21;case 16:return u.yytext="1",g.getLogger().debug("COLUMNS (LEX)",u.yytext),21;case 17:return 43;case 18:return"LINKSTYLE";case 19:return"INTERPOLATE";case 20:return this.pushState("CLASSDEF"),40;case 21:return this.popState(),this.pushState("CLASSDEFID"),"DEFAULT_CLASSDEF_ID";case 22:return this.popState(),this.pushState("CLASSDEFID"),41;case 23:return this.popState(),42;case 24:return this.pushState("CLASS"),44;case 25:return this.popState(),this.pushState("CLASS_STYLE"),45;case 26:return this.popState(),46;case 27:return this.pushState("STYLE_STMNT"),47;case 28:return this.popState(),this.pushState("STYLE_DEFINITION"),48;case 29:return this.popState(),49;case 30:return this.pushState("acc_title"),"acc_title";case 31:return this.popState(),"acc_title_value";case 32:return this.pushState("acc_descr"),"acc_descr";case 33:return this.popState(),"acc_descr_value";case 34:this.pushState("acc_descr_multiline");break;case 35:this.popState();break;case 36:return"acc_descr_multiline_value";case 37:return 30;case 38:return this.popState(),g.getLogger().debug("Lex: (("),"NODE_DEND";case 39:return this.popState(),g.getLogger().debug("Lex: (("),"NODE_DEND";case 40:return this.popState(),g.getLogger().debug("Lex: ))"),"NODE_DEND";case 41:return this.popState(),g.getLogger().debug("Lex: (("),"NODE_DEND";case 42:return this.popState(),g.getLogger().debug("Lex: (("),"NODE_DEND";case 43:return this.popState(),g.getLogger().debug("Lex: (-"),"NODE_DEND";case 44:return this.popState(),g.getLogger().debug("Lex: -)"),"NODE_DEND";case 45:return this.popState(),g.getLogger().debug("Lex: (("),"NODE_DEND";case 46:return this.popState(),g.getLogger().debug("Lex: ]]"),"NODE_DEND";case 47:return this.popState(),g.getLogger().debug("Lex: ("),"NODE_DEND";case 48:return this.popState(),g.getLogger().debug("Lex: ])"),"NODE_DEND";case 49:return this.popState(),g.getLogger().debug("Lex: /]"),"NODE_DEND";case 50:return this.popState(),g.getLogger().debug("Lex: /]"),"NODE_DEND";case 51:return this.popState(),g.getLogger().debug("Lex: )]"),"NODE_DEND";case 52:return this.popState(),g.getLogger().debug("Lex: )"),"NODE_DEND";case 53:return this.popState(),g.getLogger().debug("Lex: ]>"),"NODE_DEND";case 54:return this.popState(),g.getLogger().debug("Lex: ]"),"NODE_DEND";case 55:return g.getLogger().debug("Lexa: -)"),this.pushState("NODE"),36;case 56:return g.getLogger().debug("Lexa: (-"),this.pushState("NODE"),36;case 57:return g.getLogger().debug("Lexa: ))"),this.pushState("NODE"),36;case 58:return g.getLogger().debug("Lexa: )"),this.pushState("NODE"),36;case 59:return g.getLogger().debug("Lex: ((("),this.pushState("NODE"),36;case 60:return g.getLogger().debug("Lexa: )"),this.pushState("NODE"),36;case 61:return g.getLogger().debug("Lexa: )"),this.pushState("NODE"),36;case 62:return g.getLogger().debug("Lexa: )"),this.pushState("NODE"),36;case 63:return g.getLogger().debug("Lexc: >"),this.pushState("NODE"),36;case 64:return g.getLogger().debug("Lexa: (["),this.pushState("NODE"),36;case 65:return g.getLogger().debug("Lexa: )"),this.pushState("NODE"),36;case 66:return this.pushState("NODE"),36;case 67:return this.pushState("NODE"),36;case 68:return this.pushState("NODE"),36;case 69:return this.pushState("NODE"),36;case 70:return this.pushState("NODE"),36;case 71:return this.pushState("NODE"),36;case 72:return this.pushState("NODE"),36;case 73:return g.getLogger().debug("Lexa: ["),this.pushState("NODE"),36;case 74:return this.pushState("BLOCK_ARROW"),g.getLogger().debug("LEX ARR START"),38;case 75:return g.getLogger().debug("Lex: NODE_ID",u.yytext),32;case 76:return g.getLogger().debug("Lex: EOF",u.yytext),8;case 77:this.pushState("md_string");break;case 78:this.pushState("md_string");break;case 79:return"NODE_DESCR";case 80:this.popState();break;case 81:g.getLogger().debug("Lex: Starting string"),this.pushState("string");break;case 82:g.getLogger().debug("LEX ARR: Starting string"),this.pushState("string");break;case 83:return g.getLogger().debug("LEX: NODE_DESCR:",u.yytext),"NODE_DESCR";case 84:g.getLogger().debug("LEX POPPING"),this.popState();break;case 85:g.getLogger().debug("Lex: =>BAE"),this.pushState("ARROW_DIR");break;case 86:return u.yytext=u.yytext.replace(/^,\s*/,""),g.getLogger().debug("Lex (right): dir:",u.yytext),"DIR";case 87:return u.yytext=u.yytext.replace(/^,\s*/,""),g.getLogger().debug("Lex (left):",u.yytext),"DIR";case 88:return u.yytext=u.yytext.replace(/^,\s*/,""),g.getLogger().debug("Lex (x):",u.yytext),"DIR";case 89:return u.yytext=u.yytext.replace(/^,\s*/,""),g.getLogger().debug("Lex (y):",u.yytext),"DIR";case 90:return u.yytext=u.yytext.replace(/^,\s*/,""),g.getLogger().debug("Lex (up):",u.yytext),"DIR";case 91:return u.yytext=u.yytext.replace(/^,\s*/,""),g.getLogger().debug("Lex (down):",u.yytext),"DIR";case 92:return u.yytext="]>",g.getLogger().debug("Lex (ARROW_DIR end):",u.yytext),this.popState(),this.popState(),"BLOCK_ARROW_END";case 93:return g.getLogger().debug("Lex: LINK","#"+u.yytext+"#"),15;case 94:return g.getLogger().debug("Lex: LINK",u.yytext),15;case 95:return g.getLogger().debug("Lex: LINK",u.yytext),15;case 96:return g.getLogger().debug("Lex: LINK",u.yytext),15;case 97:return g.getLogger().debug("Lex: START_LINK",u.yytext),this.pushState("LLABEL"),16;case 98:return g.getLogger().debug("Lex: START_LINK",u.yytext),this.pushState("LLABEL"),16;case 99:return g.getLogger().debug("Lex: START_LINK",u.yytext),this.pushState("LLABEL"),16;case 100:this.pushState("md_string");break;case 101:return g.getLogger().debug("Lex: Starting string"),this.pushState("string"),"LINK_LABEL";case 102:return this.popState(),g.getLogger().debug("Lex: LINK","#"+u.yytext+"#"),15;case 103:return this.popState(),g.getLogger().debug("Lex: LINK",u.yytext),15;case 104:return this.popState(),g.getLogger().debug("Lex: LINK",u.yytext),15;case 105:return g.getLogger().debug("Lex: COLON",u.yytext),u.yytext=u.yytext.slice(1),27}},"anonymous"),rules:[/^(?:block-beta\b)/,/^(?:block\s+)/,/^(?:block\n+)/,/^(?:block:)/,/^(?:[\s]+)/,/^(?:[\n]+)/,/^(?:((\u000D\u000A)|(\u000A)))/,/^(?:columns\s+auto\b)/,/^(?:columns\s+[\d]+)/,/^(?:["][`])/,/^(?:[^`"]+)/,/^(?:[`]["])/,/^(?:["])/,/^(?:["])/,/^(?:[^"]*)/,/^(?:space[:]\d+)/,/^(?:space\b)/,/^(?:default\b)/,/^(?:linkStyle\b)/,/^(?:interpolate\b)/,/^(?:classDef\s+)/,/^(?:DEFAULT\s+)/,/^(?:\w+\s+)/,/^(?:[^\n]*)/,/^(?:class\s+)/,/^(?:(\w+)+((,\s*\w+)*))/,/^(?:[^\n]*)/,/^(?:style\s+)/,/^(?:(\w+)+((,\s*\w+)*))/,/^(?:[^\n]*)/,/^(?:accTitle\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*\{\s*)/,/^(?:[\}])/,/^(?:[^\}]*)/,/^(?:end\b\s*)/,/^(?:\(\(\()/,/^(?:\)\)\))/,/^(?:[\)]\))/,/^(?:\}\})/,/^(?:\})/,/^(?:\(-)/,/^(?:-\))/,/^(?:\(\()/,/^(?:\]\])/,/^(?:\()/,/^(?:\]\))/,/^(?:\\\])/,/^(?:\/\])/,/^(?:\)\])/,/^(?:[\)])/,/^(?:\]>)/,/^(?:[\]])/,/^(?:-\))/,/^(?:\(-)/,/^(?:\)\))/,/^(?:\))/,/^(?:\(\(\()/,/^(?:\(\()/,/^(?:\{\{)/,/^(?:\{)/,/^(?:>)/,/^(?:\(\[)/,/^(?:\()/,/^(?:\[\[)/,/^(?:\[\|)/,/^(?:\[\()/,/^(?:\)\)\))/,/^(?:\[\\)/,/^(?:\[\/)/,/^(?:\[\\)/,/^(?:\[)/,/^(?:<\[)/,/^(?:[^\(\[\n\-\)\{\}\s\<\>:]+)/,/^(?:$)/,/^(?:["][`])/,/^(?:["][`])/,/^(?:[^`"]+)/,/^(?:[`]["])/,/^(?:["])/,/^(?:["])/,/^(?:[^"]+)/,/^(?:["])/,/^(?:\]>\s*\()/,/^(?:,?\s*right\s*)/,/^(?:,?\s*left\s*)/,/^(?:,?\s*x\s*)/,/^(?:,?\s*y\s*)/,/^(?:,?\s*up\s*)/,/^(?:,?\s*down\s*)/,/^(?:\)\s*)/,/^(?:\s*[xo<]?--+[-xo>]\s*)/,/^(?:\s*[xo<]?==+[=xo>]\s*)/,/^(?:\s*[xo<]?-?\.+-[xo>]?\s*)/,/^(?:\s*~~[\~]+\s*)/,/^(?:\s*[xo<]?--\s*)/,/^(?:\s*[xo<]?==\s*)/,/^(?:\s*[xo<]?-\.\s*)/,/^(?:["][`])/,/^(?:["])/,/^(?:\s*[xo<]?--+[-xo>]\s*)/,/^(?:\s*[xo<]?==+[=xo>]\s*)/,/^(?:\s*[xo<]?-?\.+-[xo>]?\s*)/,/^(?::\d+)/],conditions:{STYLE_DEFINITION:{rules:[29],inclusive:!1},STYLE_STMNT:{rules:[28],inclusive:!1},CLASSDEFID:{rules:[23],inclusive:!1},CLASSDEF:{rules:[21,22],inclusive:!1},CLASS_STYLE:{rules:[26],inclusive:!1},CLASS:{rules:[25],inclusive:!1},LLABEL:{rules:[100,101,102,103,104],inclusive:!1},ARROW_DIR:{rules:[86,87,88,89,90,91,92],inclusive:!1},BLOCK_ARROW:{rules:[77,82,85],inclusive:!1},NODE:{rules:[38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,78,81],inclusive:!1},md_string:{rules:[10,11,79,80],inclusive:!1},space:{rules:[],inclusive:!1},string:{rules:[13,14,83,84],inclusive:!1},acc_descr_multiline:{rules:[35,36],inclusive:!1},acc_descr:{rules:[33],inclusive:!1},acc_title:{rules:[31],inclusive:!1},INITIAL:{rules:[0,1,2,3,4,5,6,7,8,9,12,15,16,17,18,19,20,24,27,30,32,34,37,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,93,94,95,96,97,98,99,105],inclusive:!0}}};return N}();v.lexer=T;function k(){this.yy={}}return d(k,"Parser"),k.prototype=v,v.Parser=k,new k}();yt.parser=yt;var ke=yt,V=new Map,St=[],bt=new Map,Ct="color",Bt="fill",De="bgFill",Ht=",",Ne=z(),ct=new Map,Te=d(e=>Le.sanitizeText(e,Ne),"sanitizeText"),Ce=d(function(e,t=""){let r=ct.get(e);r||(r={id:e,styles:[],textStyles:[]},ct.set(e,r)),t!=null&&t.split(Ht).forEach(n=>{const i=n.replace(/([^;]*);/,"$1").trim();if(RegExp(Ct).exec(n)){const s=i.replace(Bt,De).replace(Ct,Bt);r.textStyles.push(s)}r.styles.push(i)})},"addStyleClass"),Be=d(function(e,t=""){const r=V.get(e);t!=null&&(r.styles=t.split(Ht))},"addStyle2Node"),Ie=d(function(e,t){e.split(",").forEach(function(r){let n=V.get(r);if(n===void 0){const i=r.trim();n={id:i,type:"na",children:[]},V.set(i,n)}n.classes||(n.classes=[]),n.classes.push(t)})},"setCssClass"),Kt=d((e,t)=>{const r=e.flat(),n=[];for(const i of r){if(i.label&&(i.label=Te(i.label)),i.type==="classDef"){Ce(i.id,i.css);continue}if(i.type==="applyClass"){Ie(i.id,(i==null?void 0:i.styleClass)??"");continue}if(i.type==="applyStyles"){i!=null&&i.stylesStr&&Be(i.id,i==null?void 0:i.stylesStr);continue}if(i.type==="column-setting")t.columns=i.columns??-1;else if(i.type==="edge"){const a=(bt.get(i.id)??0)+1;bt.set(i.id,a),i.id=a+"-"+i.id,St.push(i)}else{i.label||(i.type==="composite"?i.label="":i.label=i.id);const a=V.get(i.id);if(a===void 0?V.set(i.id,i):(i.type!=="na"&&(a.type=i.type),i.label!==i.id&&(a.label=i.label)),i.children&&Kt(i.children,i),i.type==="space"){const s=i.width??1;for(let l=0;l<s;l++){const o=Ee(i);o.id=o.id+"-"+l,V.set(o.id,o),n.push(o)}}else a===void 0&&n.push(i)}}t.children=n},"populateBlockDatabase"),vt=[],rt={id:"root",type:"composite",children:[],columns:-1},Oe=d(()=>{m.debug("Clear called"),ue(),rt={id:"root",type:"composite",children:[],columns:-1},V=new Map([["root",rt]]),vt=[],ct=new Map,St=[],bt=new Map},"clear");function Xt(e){switch(m.debug("typeStr2Type",e),e){case"[]":return"square";case"()":return m.debug("we have a round"),"round";case"(())":return"circle";case">]":return"rect_left_inv_arrow";case"{}":return"diamond";case"{{}}":return"hexagon";case"([])":return"stadium";case"[[]]":return"subroutine";case"[()]":return"cylinder";case"((()))":return"doublecircle";case"[//]":return"lean_right";case"[\\\\]":return"lean_left";case"[/\\]":return"trapezoid";case"[\\/]":return"inv_trapezoid";case"<[]>":return"block_arrow";default:return"na"}}d(Xt,"typeStr2Type");function Ut(e){switch(m.debug("typeStr2Type",e),e){case"==":return"thick";default:return"normal"}}d(Ut,"edgeTypeStr2Type");function jt(e){switch(e.trim()){case"--x":return"arrow_cross";case"--o":return"arrow_circle";default:return"arrow_point"}}d(jt,"edgeStrToEdgeData");var It=0,Re=d(()=>(It++,"id-"+Math.random().toString(36).substr(2,12)+"-"+It),"generateId"),ze=d(e=>{rt.children=e,Kt(e,rt),vt=rt.children},"setHierarchy"),Ae=d(e=>{const t=V.get(e);return t?t.columns?t.columns:t.children?t.children.length:-1:-1},"getColumns"),Me=d(()=>[...V.values()],"getBlocksFlat"),Fe=d(()=>vt||[],"getBlocks"),We=d(()=>St,"getEdges"),Pe=d(e=>V.get(e),"getBlock"),Ye=d(e=>{V.set(e.id,e)},"setBlock"),He=d(()=>m,"getLogger"),Ke=d(function(){return ct},"getClasses"),Xe={getConfig:d(()=>at().block,"getConfig"),typeStr2Type:Xt,edgeTypeStr2Type:Ut,edgeStrToEdgeData:jt,getLogger:He,getBlocksFlat:Me,getBlocks:Fe,getEdges:We,setHierarchy:ze,getBlock:Pe,setBlock:Ye,getColumns:Ae,getClasses:Ke,clear:Oe,generateId:Re},Ue=Xe,nt=d((e,t)=>{const r=pe,n=r(e,"r"),i=r(e,"g"),a=r(e,"b");return fe(n,i,a,t)},"fade"),je=d(e=>`.label {
    font-family: ${e.fontFamily};
    color: ${e.nodeTextColor||e.textColor};
  }
  .cluster-label text {
    fill: ${e.titleColor};
  }
  .cluster-label span,p {
    color: ${e.titleColor};
  }



  .label text,span,p {
    fill: ${e.nodeTextColor||e.textColor};
    color: ${e.nodeTextColor||e.textColor};
  }

  .node rect,
  .node circle,
  .node ellipse,
  .node polygon,
  .node path {
    fill: ${e.mainBkg};
    stroke: ${e.nodeBorder};
    stroke-width: 1px;
  }
  .flowchart-label text {
    text-anchor: middle;
  }
  // .flowchart-label .text-outer-tspan {
  //   text-anchor: middle;
  // }
  // .flowchart-label .text-inner-tspan {
  //   text-anchor: start;
  // }

  .node .label {
    text-align: center;
  }
  .node.clickable {
    cursor: pointer;
  }

  .arrowheadPath {
    fill: ${e.arrowheadColor};
  }

  .edgePath .path {
    stroke: ${e.lineColor};
    stroke-width: 2.0px;
  }

  .flowchart-link {
    stroke: ${e.lineColor};
    fill: none;
  }

  .edgeLabel {
    background-color: ${e.edgeLabelBackground};
    rect {
      opacity: 0.5;
      background-color: ${e.edgeLabelBackground};
      fill: ${e.edgeLabelBackground};
    }
    text-align: center;
  }

  /* For html labels only */
  .labelBkg {
    background-color: ${nt(e.edgeLabelBackground,.5)};
    // background-color:
  }

  .node .cluster {
    // fill: ${nt(e.mainBkg,.5)};
    fill: ${nt(e.clusterBkg,.5)};
    stroke: ${nt(e.clusterBorder,.2)};
    box-shadow: rgba(50, 50, 93, 0.25) 0px 13px 27px -5px, rgba(0, 0, 0, 0.3) 0px 8px 16px -8px;
    stroke-width: 1px;
  }

  .cluster text {
    fill: ${e.titleColor};
  }

  .cluster span,p {
    color: ${e.titleColor};
  }
  /* .cluster div {
    color: ${e.titleColor};
  } */

  div.mermaidTooltip {
    position: absolute;
    text-align: center;
    max-width: 200px;
    padding: 2px;
    font-family: ${e.fontFamily};
    font-size: 12px;
    background: ${e.tertiaryColor};
    border: 1px solid ${e.border2};
    border-radius: 2px;
    pointer-events: none;
    z-index: 100;
  }

  .flowchartTitleText {
    text-anchor: middle;
    font-size: 18px;
    fill: ${e.textColor};
  }
  ${de()}
`,"getStyles"),Ve=je,Ge=d((e,t,r,n)=>{t.forEach(i=>{sr[i](e,r,n)})},"insertMarkers"),Ze=d((e,t,r)=>{m.trace("Making markers for ",r),e.append("defs").append("marker").attr("id",r+"_"+t+"-extensionStart").attr("class","marker extension "+t).attr("refX",18).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 1,7 L18,13 V 1 Z"),e.append("defs").append("marker").attr("id",r+"_"+t+"-extensionEnd").attr("class","marker extension "+t).attr("refX",1).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 1,1 V 13 L18,7 Z")},"extension"),qe=d((e,t,r)=>{e.append("defs").append("marker").attr("id",r+"_"+t+"-compositionStart").attr("class","marker composition "+t).attr("refX",18).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z"),e.append("defs").append("marker").attr("id",r+"_"+t+"-compositionEnd").attr("class","marker composition "+t).attr("refX",1).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z")},"composition"),Je=d((e,t,r)=>{e.append("defs").append("marker").attr("id",r+"_"+t+"-aggregationStart").attr("class","marker aggregation "+t).attr("refX",18).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z"),e.append("defs").append("marker").attr("id",r+"_"+t+"-aggregationEnd").attr("class","marker aggregation "+t).attr("refX",1).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z")},"aggregation"),Qe=d((e,t,r)=>{e.append("defs").append("marker").attr("id",r+"_"+t+"-dependencyStart").attr("class","marker dependency "+t).attr("refX",6).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 5,7 L9,13 L1,7 L9,1 Z"),e.append("defs").append("marker").attr("id",r+"_"+t+"-dependencyEnd").attr("class","marker dependency "+t).attr("refX",13).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L14,7 L9,1 Z")},"dependency"),$e=d((e,t,r)=>{e.append("defs").append("marker").attr("id",r+"_"+t+"-lollipopStart").attr("class","marker lollipop "+t).attr("refX",13).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("circle").attr("stroke","black").attr("fill","transparent").attr("cx",7).attr("cy",7).attr("r",6),e.append("defs").append("marker").attr("id",r+"_"+t+"-lollipopEnd").attr("class","marker lollipop "+t).attr("refX",1).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("circle").attr("stroke","black").attr("fill","transparent").attr("cx",7).attr("cy",7).attr("r",6)},"lollipop"),tr=d((e,t,r)=>{e.append("marker").attr("id",r+"_"+t+"-pointEnd").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",6).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",12).attr("markerHeight",12).attr("orient","auto").append("path").attr("d","M 0 0 L 10 5 L 0 10 z").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0"),e.append("marker").attr("id",r+"_"+t+"-pointStart").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",4.5).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",12).attr("markerHeight",12).attr("orient","auto").append("path").attr("d","M 0 5 L 10 10 L 10 0 z").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0")},"point"),er=d((e,t,r)=>{e.append("marker").attr("id",r+"_"+t+"-circleEnd").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",11).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("circle").attr("cx","5").attr("cy","5").attr("r","5").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0"),e.append("marker").attr("id",r+"_"+t+"-circleStart").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",-1).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("circle").attr("cx","5").attr("cy","5").attr("r","5").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0")},"circle"),rr=d((e,t,r)=>{e.append("marker").attr("id",r+"_"+t+"-crossEnd").attr("class","marker cross "+t).attr("viewBox","0 0 11 11").attr("refX",12).attr("refY",5.2).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("path").attr("d","M 1,1 l 9,9 M 10,1 l -9,9").attr("class","arrowMarkerPath").style("stroke-width",2).style("stroke-dasharray","1,0"),e.append("marker").attr("id",r+"_"+t+"-crossStart").attr("class","marker cross "+t).attr("viewBox","0 0 11 11").attr("refX",-1).attr("refY",5.2).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("path").attr("d","M 1,1 l 9,9 M 10,1 l -9,9").attr("class","arrowMarkerPath").style("stroke-width",2).style("stroke-dasharray","1,0")},"cross"),ar=d((e,t,r)=>{e.append("defs").append("marker").attr("id",r+"_"+t+"-barbEnd").attr("refX",19).attr("refY",7).attr("markerWidth",20).attr("markerHeight",14).attr("markerUnits","strokeWidth").attr("orient","auto").append("path").attr("d","M 19,7 L9,13 L14,7 L9,1 Z")},"barb"),sr={extension:Ze,composition:qe,aggregation:Je,dependency:Qe,lollipop:$e,point:tr,circle:er,cross:rr,barb:ar},ir=Ge,Wt,Pt,I=((Pt=(Wt=z())==null?void 0:Wt.block)==null?void 0:Pt.padding)??8;function Vt(e,t){if(e===0||!Number.isInteger(e))throw new Error("Columns must be an integer !== 0.");if(t<0||!Number.isInteger(t))throw new Error("Position must be a non-negative integer."+t);if(e<0)return{px:t,py:0};if(e===1)return{px:0,py:t};const r=t%e,n=Math.floor(t/e);return{px:r,py:n}}d(Vt,"calculateBlockPosition");var nr=d(e=>{let t=0,r=0;for(const n of e.children){const{width:i,height:a,x:s,y:l}=n.size??{width:0,height:0,x:0,y:0};m.debug("getMaxChildSize abc95 child:",n.id,"width:",i,"height:",a,"x:",s,"y:",l,n.type),n.type!=="space"&&(i>t&&(t=i/(e.widthInColumns??1)),a>r&&(r=a))}return{width:t,height:r}},"getMaxChildSize");function ot(e,t,r=0,n=0){var s,l,o,f,h,y,b,L,E,D,v;m.debug("setBlockSizes abc95 (start)",e.id,(s=e==null?void 0:e.size)==null?void 0:s.x,"block width =",e==null?void 0:e.size,"siblingWidth",r),(l=e==null?void 0:e.size)!=null&&l.width||(e.size={width:r,height:n,x:0,y:0});let i=0,a=0;if(((o=e.children)==null?void 0:o.length)>0){for(const S of e.children)ot(S,t);const T=nr(e);i=T.width,a=T.height,m.debug("setBlockSizes abc95 maxWidth of",e.id,":s children is ",i,a);for(const S of e.children)S.size&&(m.debug(`abc95 Setting size of children of ${e.id} id=${S.id} ${i} ${a} ${JSON.stringify(S.size)}`),S.size.width=i*(S.widthInColumns??1)+I*((S.widthInColumns??1)-1),S.size.height=a,S.size.x=0,S.size.y=0,m.debug(`abc95 updating size of ${e.id} children child:${S.id} maxWidth:${i} maxHeight:${a}`));for(const S of e.children)ot(S,t,i,a);const k=e.columns??-1;let N=0;for(const S of e.children)N+=S.widthInColumns??1;let x=e.children.length;k>0&&k<N&&(x=k);const g=Math.ceil(N/x);let u=x*(i+I)+I,w=g*(a+I)+I;if(u<r){m.debug(`Detected to small sibling: abc95 ${e.id} siblingWidth ${r} siblingHeight ${n} width ${u}`),u=r,w=n;const S=(r-x*I-I)/x,c=(n-g*I-I)/g;m.debug("Size indata abc88",e.id,"childWidth",S,"maxWidth",i),m.debug("Size indata abc88",e.id,"childHeight",c,"maxHeight",a),m.debug("Size indata abc88 xSize",x,"padding",I);for(const _ of e.children)_.size&&(_.size.width=S,_.size.height=c,_.size.x=0,_.size.y=0)}if(m.debug(`abc95 (finale calc) ${e.id} xSize ${x} ySize ${g} columns ${k}${e.children.length} width=${Math.max(u,((f=e.size)==null?void 0:f.width)||0)}`),u<(((h=e==null?void 0:e.size)==null?void 0:h.width)||0)){u=((y=e==null?void 0:e.size)==null?void 0:y.width)||0;const S=k>0?Math.min(e.children.length,k):e.children.length;if(S>0){const c=(u-S*I-I)/S;m.debug("abc95 (growing to fit) width",e.id,u,(b=e.size)==null?void 0:b.width,c);for(const _ of e.children)_.size&&(_.size.width=c)}}e.size={width:u,height:w,x:0,y:0}}m.debug("setBlockSizes abc94 (done)",e.id,(L=e==null?void 0:e.size)==null?void 0:L.x,(E=e==null?void 0:e.size)==null?void 0:E.width,(D=e==null?void 0:e.size)==null?void 0:D.y,(v=e==null?void 0:e.size)==null?void 0:v.height)}d(ot,"setBlockSizes");function Et(e,t){var n,i,a,s,l,o,f,h,y,b,L,E,D,v,T,k,N;m.debug(`abc85 layout blocks (=>layoutBlocks) ${e.id} x: ${(n=e==null?void 0:e.size)==null?void 0:n.x} y: ${(i=e==null?void 0:e.size)==null?void 0:i.y} width: ${(a=e==null?void 0:e.size)==null?void 0:a.width}`);const r=e.columns??-1;if(m.debug("layoutBlocks columns abc95",e.id,"=>",r,e),e.children&&e.children.length>0){const x=((l=(s=e==null?void 0:e.children[0])==null?void 0:s.size)==null?void 0:l.width)??0,g=e.children.length*x+(e.children.length-1)*I;m.debug("widthOfChildren 88",g,"posX");let u=0;m.debug("abc91 block?.size?.x",e.id,(o=e==null?void 0:e.size)==null?void 0:o.x);let w=(f=e==null?void 0:e.size)!=null&&f.x?((h=e==null?void 0:e.size)==null?void 0:h.x)+(-((y=e==null?void 0:e.size)==null?void 0:y.width)/2||0):-I,S=0;for(const c of e.children){const _=e;if(!c.size)continue;const{width:p,height:A}=c.size,{px:O,py:X}=Vt(r,u);if(X!=S&&(S=X,w=(b=e==null?void 0:e.size)!=null&&b.x?((L=e==null?void 0:e.size)==null?void 0:L.x)+(-((E=e==null?void 0:e.size)==null?void 0:E.width)/2||0):-I,m.debug("New row in layout for block",e.id," and child ",c.id,S)),m.debug(`abc89 layout blocks (child) id: ${c.id} Pos: ${u} (px, py) ${O},${X} (${(D=_==null?void 0:_.size)==null?void 0:D.x},${(v=_==null?void 0:_.size)==null?void 0:v.y}) parent: ${_.id} width: ${p}${I}`),_.size){const W=p/2;c.size.x=w+I+W,m.debug(`abc91 layout blocks (calc) px, pyid:${c.id} startingPos=X${w} new startingPosX${c.size.x} ${W} padding=${I} width=${p} halfWidth=${W} => x:${c.size.x} y:${c.size.y} ${c.widthInColumns} (width * (child?.w || 1)) / 2 ${p*((c==null?void 0:c.widthInColumns)??1)/2}`),w=c.size.x+W,c.size.y=_.size.y-_.size.height/2+X*(A+I)+A/2+I,m.debug(`abc88 layout blocks (calc) px, pyid:${c.id}startingPosX${w}${I}${W}=>x:${c.size.x}y:${c.size.y}${c.widthInColumns}(width * (child?.w || 1)) / 2${p*((c==null?void 0:c.widthInColumns)??1)/2}`)}c.children&&Et(c),u+=(c==null?void 0:c.widthInColumns)??1,m.debug("abc88 columnsPos",c,u)}}m.debug(`layout blocks (<==layoutBlocks) ${e.id} x: ${(T=e==null?void 0:e.size)==null?void 0:T.x} y: ${(k=e==null?void 0:e.size)==null?void 0:k.y} width: ${(N=e==null?void 0:e.size)==null?void 0:N.width}`)}d(Et,"layoutBlocks");function _t(e,{minX:t,minY:r,maxX:n,maxY:i}={minX:0,minY:0,maxX:0,maxY:0}){if(e.size&&e.id!=="root"){const{x:a,y:s,width:l,height:o}=e.size;a-l/2<t&&(t=a-l/2),s-o/2<r&&(r=s-o/2),a+l/2>n&&(n=a+l/2),s+o/2>i&&(i=s+o/2)}if(e.children)for(const a of e.children)({minX:t,minY:r,maxX:n,maxY:i}=_t(a,{minX:t,minY:r,maxX:n,maxY:i}));return{minX:t,minY:r,maxX:n,maxY:i}}d(_t,"findBounds");function Gt(e){const t=e.getBlock("root");if(!t)return;ot(t,e,0,0),Et(t),m.debug("getBlocks",JSON.stringify(t,null,2));const{minX:r,minY:n,maxX:i,maxY:a}=_t(t),s=a-n,l=i-r;return{x:r,y:n,width:l,height:s}}d(Gt,"layout");function wt(e,t){t&&e.attr("style",t)}d(wt,"applyStyle");function Zt(e){const t=R(document.createElementNS("http://www.w3.org/2000/svg","foreignObject")),r=t.append("xhtml:div"),n=e.label,i=e.isNode?"nodeLabel":"edgeLabel",a=r.append("span");return a.html(n),wt(a,e.labelStyle),a.attr("class",i),wt(r,e.labelStyle),r.style("display","inline-block"),r.style("white-space","nowrap"),r.attr("xmlns","http://www.w3.org/1999/xhtml"),t.node()}d(Zt,"addHtmlLabel");var lr=d(async(e,t,r,n)=>{let i=e||"";if(typeof i=="object"&&(i=i[0]),Z(z().flowchart.htmlLabels)){i=i.replace(/\\n|\n/g,"<br />"),m.debug("vertexText"+i);const a=await Se(xt(i)),s={isNode:n,label:a,labelStyle:t.replace("fill:","color:")};return Zt(s)}else{const a=document.createElementNS("http://www.w3.org/2000/svg","text");a.setAttribute("style",t.replace("color:","fill:"));let s=[];typeof i=="string"?s=i.split(/\\n|\n|<br\s*\/?>/gi):Array.isArray(i)?s=i:s=[];for(const l of s){const o=document.createElementNS("http://www.w3.org/2000/svg","tspan");o.setAttributeNS("http://www.w3.org/XML/1998/namespace","xml:space","preserve"),o.setAttribute("dy","1em"),o.setAttribute("x","0"),r?o.setAttribute("class","title-row"):o.setAttribute("class","row"),o.textContent=l.trim(),a.appendChild(o)}return a}},"createLabel"),j=lr,cr=d((e,t,r,n,i)=>{t.arrowTypeStart&&Ot(e,"start",t.arrowTypeStart,r,n,i),t.arrowTypeEnd&&Ot(e,"end",t.arrowTypeEnd,r,n,i)},"addEdgeMarkers"),or={arrow_cross:"cross",arrow_point:"point",arrow_barb:"barb",arrow_circle:"circle",aggregation:"aggregation",extension:"extension",composition:"composition",dependency:"dependency",lollipop:"lollipop"},Ot=d((e,t,r,n,i,a)=>{const s=or[r];if(!s){m.warn(`Unknown arrow type: ${r}`);return}const l=t==="start"?"Start":"End";e.attr(`marker-${t}`,`url(${n}#${i}_${a}-${s}${l})`)},"addEdgeMarker"),mt={},P={},hr=d(async(e,t)=>{const r=z(),n=Z(r.flowchart.htmlLabels),i=t.labelType==="markdown"?Yt(e,t.label,{style:t.labelStyle,useHtmlLabels:n,addSvgBackground:!0},r):await j(t.label,t.labelStyle),a=e.insert("g").attr("class","edgeLabel"),s=a.insert("g").attr("class","label");s.node().appendChild(i);let l=i.getBBox();if(n){const f=i.children[0],h=R(i);l=f.getBoundingClientRect(),h.attr("width",l.width),h.attr("height",l.height)}s.attr("transform","translate("+-l.width/2+", "+-l.height/2+")"),mt[t.id]=a,t.width=l.width,t.height=l.height;let o;if(t.startLabelLeft){const f=await j(t.startLabelLeft,t.labelStyle),h=e.insert("g").attr("class","edgeTerminals"),y=h.insert("g").attr("class","inner");o=y.node().appendChild(f);const b=f.getBBox();y.attr("transform","translate("+-b.width/2+", "+-b.height/2+")"),P[t.id]||(P[t.id]={}),P[t.id].startLeft=h,et(o,t.startLabelLeft)}if(t.startLabelRight){const f=await j(t.startLabelRight,t.labelStyle),h=e.insert("g").attr("class","edgeTerminals"),y=h.insert("g").attr("class","inner");o=h.node().appendChild(f),y.node().appendChild(f);const b=f.getBBox();y.attr("transform","translate("+-b.width/2+", "+-b.height/2+")"),P[t.id]||(P[t.id]={}),P[t.id].startRight=h,et(o,t.startLabelRight)}if(t.endLabelLeft){const f=await j(t.endLabelLeft,t.labelStyle),h=e.insert("g").attr("class","edgeTerminals"),y=h.insert("g").attr("class","inner");o=y.node().appendChild(f);const b=f.getBBox();y.attr("transform","translate("+-b.width/2+", "+-b.height/2+")"),h.node().appendChild(f),P[t.id]||(P[t.id]={}),P[t.id].endLeft=h,et(o,t.endLabelLeft)}if(t.endLabelRight){const f=await j(t.endLabelRight,t.labelStyle),h=e.insert("g").attr("class","edgeTerminals"),y=h.insert("g").attr("class","inner");o=y.node().appendChild(f);const b=f.getBBox();y.attr("transform","translate("+-b.width/2+", "+-b.height/2+")"),h.node().appendChild(f),P[t.id]||(P[t.id]={}),P[t.id].endRight=h,et(o,t.endLabelRight)}return i},"insertEdgeLabel");function et(e,t){z().flowchart.htmlLabels&&e&&(e.style.width=t.length*9+"px",e.style.height="12px")}d(et,"setTerminalWidth");var dr=d((e,t)=>{m.debug("Moving label abc88 ",e.id,e.label,mt[e.id],t);let r=t.updatedPath?t.updatedPath:t.originalPath;const n=z(),{subGraphTitleTotalMargin:i}=me(n);if(e.label){const a=mt[e.id];let s=e.x,l=e.y;if(r){const o=tt.calcLabelPosition(r);m.debug("Moving label "+e.label+" from (",s,",",l,") to (",o.x,",",o.y,") abc88"),t.updatedPath&&(s=o.x,l=o.y)}a.attr("transform",`translate(${s}, ${l+i/2})`)}if(e.startLabelLeft){const a=P[e.id].startLeft;let s=e.x,l=e.y;if(r){const o=tt.calcTerminalLabelPosition(e.arrowTypeStart?10:0,"start_left",r);s=o.x,l=o.y}a.attr("transform",`translate(${s}, ${l})`)}if(e.startLabelRight){const a=P[e.id].startRight;let s=e.x,l=e.y;if(r){const o=tt.calcTerminalLabelPosition(e.arrowTypeStart?10:0,"start_right",r);s=o.x,l=o.y}a.attr("transform",`translate(${s}, ${l})`)}if(e.endLabelLeft){const a=P[e.id].endLeft;let s=e.x,l=e.y;if(r){const o=tt.calcTerminalLabelPosition(e.arrowTypeEnd?10:0,"end_left",r);s=o.x,l=o.y}a.attr("transform",`translate(${s}, ${l})`)}if(e.endLabelRight){const a=P[e.id].endRight;let s=e.x,l=e.y;if(r){const o=tt.calcTerminalLabelPosition(e.arrowTypeEnd?10:0,"end_right",r);s=o.x,l=o.y}a.attr("transform",`translate(${s}, ${l})`)}},"positionEdgeLabel"),gr=d((e,t)=>{const r=e.x,n=e.y,i=Math.abs(t.x-r),a=Math.abs(t.y-n),s=e.width/2,l=e.height/2;return i>=s||a>=l},"outsideNode"),ur=d((e,t,r)=>{m.debug(`intersection calc abc89:
  outsidePoint: ${JSON.stringify(t)}
  insidePoint : ${JSON.stringify(r)}
  node        : x:${e.x} y:${e.y} w:${e.width} h:${e.height}`);const n=e.x,i=e.y,a=Math.abs(n-r.x),s=e.width/2;let l=r.x<t.x?s-a:s+a;const o=e.height/2,f=Math.abs(t.y-r.y),h=Math.abs(t.x-r.x);if(Math.abs(i-t.y)*s>Math.abs(n-t.x)*o){let y=r.y<t.y?t.y-o-i:i-o-t.y;l=h*y/f;const b={x:r.x<t.x?r.x+l:r.x-h+l,y:r.y<t.y?r.y+f-y:r.y-f+y};return l===0&&(b.x=t.x,b.y=t.y),h===0&&(b.x=t.x),f===0&&(b.y=t.y),m.debug(`abc89 topp/bott calc, Q ${f}, q ${y}, R ${h}, r ${l}`,b),b}else{r.x<t.x?l=t.x-s-n:l=n-s-t.x;let y=f*l/h,b=r.x<t.x?r.x+h-l:r.x-h+l,L=r.y<t.y?r.y+y:r.y-y;return m.debug(`sides calc abc89, Q ${f}, q ${y}, R ${h}, r ${l}`,{_x:b,_y:L}),l===0&&(b=t.x,L=t.y),h===0&&(b=t.x),f===0&&(L=t.y),{x:b,y:L}}},"intersection"),Rt=d((e,t)=>{m.debug("abc88 cutPathAtIntersect",e,t);let r=[],n=e[0],i=!1;return e.forEach(a=>{if(!gr(t,a)&&!i){const s=ur(t,n,a);let l=!1;r.forEach(o=>{l=l||o.x===s.x&&o.y===s.y}),r.some(o=>o.x===s.x&&o.y===s.y)||r.push(s),i=!0}else n=a,i||r.push(a)}),r},"cutPathAtIntersect"),pr=d(function(e,t,r,n,i,a,s){let l=r.points;m.debug("abc88 InsertEdge: edge=",r,"e=",t);let o=!1;const f=a.node(t.v);var h=a.node(t.w);h!=null&&h.intersect&&(f!=null&&f.intersect)&&(l=l.slice(1,r.points.length-1),l.unshift(f.intersect(l[0])),l.push(h.intersect(l[l.length-1]))),r.toCluster&&(m.debug("to cluster abc88",n[r.toCluster]),l=Rt(r.points,n[r.toCluster].node),o=!0),r.fromCluster&&(m.debug("from cluster abc88",n[r.fromCluster]),l=Rt(l.reverse(),n[r.fromCluster].node).reverse(),o=!0);const y=l.filter(x=>!Number.isNaN(x.y));let b=be;r.curve&&(i==="graph"||i==="flowchart")&&(b=r.curve);const{x:L,y:E}=xe(r),D=ye().x(L).y(E).curve(b);let v;switch(r.thickness){case"normal":v="edge-thickness-normal";break;case"thick":v="edge-thickness-thick";break;case"invisible":v="edge-thickness-thick";break;default:v=""}switch(r.pattern){case"solid":v+=" edge-pattern-solid";break;case"dotted":v+=" edge-pattern-dotted";break;case"dashed":v+=" edge-pattern-dashed";break}const T=e.append("path").attr("d",D(y)).attr("id",r.id).attr("class"," "+v+(r.classes?" "+r.classes:"")).attr("style",r.style);let k="";(z().flowchart.arrowMarkerAbsolute||z().state.arrowMarkerAbsolute)&&(k=we(!0)),cr(T,r,k,s,i);let N={};return o&&(N.updatedPath=l),N.originalPath=r.points,N},"insertEdge"),fr=d(e=>{const t=new Set;for(const r of e)switch(r){case"x":t.add("right"),t.add("left");break;case"y":t.add("up"),t.add("down");break;default:t.add(r);break}return t},"expandAndDeduplicateDirections"),xr=d((e,t,r)=>{const n=fr(e),i=2,a=t.height+2*r.padding,s=a/i,l=t.width+2*s+r.padding,o=r.padding/2;return n.has("right")&&n.has("left")&&n.has("up")&&n.has("down")?[{x:0,y:0},{x:s,y:0},{x:l/2,y:2*o},{x:l-s,y:0},{x:l,y:0},{x:l,y:-a/3},{x:l+2*o,y:-a/2},{x:l,y:-2*a/3},{x:l,y:-a},{x:l-s,y:-a},{x:l/2,y:-a-2*o},{x:s,y:-a},{x:0,y:-a},{x:0,y:-2*a/3},{x:-2*o,y:-a/2},{x:0,y:-a/3}]:n.has("right")&&n.has("left")&&n.has("up")?[{x:s,y:0},{x:l-s,y:0},{x:l,y:-a/2},{x:l-s,y:-a},{x:s,y:-a},{x:0,y:-a/2}]:n.has("right")&&n.has("left")&&n.has("down")?[{x:0,y:0},{x:s,y:-a},{x:l-s,y:-a},{x:l,y:0}]:n.has("right")&&n.has("up")&&n.has("down")?[{x:0,y:0},{x:l,y:-s},{x:l,y:-a+s},{x:0,y:-a}]:n.has("left")&&n.has("up")&&n.has("down")?[{x:l,y:0},{x:0,y:-s},{x:0,y:-a+s},{x:l,y:-a}]:n.has("right")&&n.has("left")?[{x:s,y:0},{x:s,y:-o},{x:l-s,y:-o},{x:l-s,y:0},{x:l,y:-a/2},{x:l-s,y:-a},{x:l-s,y:-a+o},{x:s,y:-a+o},{x:s,y:-a},{x:0,y:-a/2}]:n.has("up")&&n.has("down")?[{x:l/2,y:0},{x:0,y:-o},{x:s,y:-o},{x:s,y:-a+o},{x:0,y:-a+o},{x:l/2,y:-a},{x:l,y:-a+o},{x:l-s,y:-a+o},{x:l-s,y:-o},{x:l,y:-o}]:n.has("right")&&n.has("up")?[{x:0,y:0},{x:l,y:-s},{x:0,y:-a}]:n.has("right")&&n.has("down")?[{x:0,y:0},{x:l,y:0},{x:0,y:-a}]:n.has("left")&&n.has("up")?[{x:l,y:0},{x:0,y:-s},{x:l,y:-a}]:n.has("left")&&n.has("down")?[{x:l,y:0},{x:0,y:0},{x:l,y:-a}]:n.has("right")?[{x:s,y:-o},{x:s,y:-o},{x:l-s,y:-o},{x:l-s,y:0},{x:l,y:-a/2},{x:l-s,y:-a},{x:l-s,y:-a+o},{x:s,y:-a+o},{x:s,y:-a+o}]:n.has("left")?[{x:s,y:0},{x:s,y:-o},{x:l-s,y:-o},{x:l-s,y:-a+o},{x:s,y:-a+o},{x:s,y:-a},{x:0,y:-a/2}]:n.has("up")?[{x:s,y:-o},{x:s,y:-a+o},{x:0,y:-a+o},{x:l/2,y:-a},{x:l,y:-a+o},{x:l-s,y:-a+o},{x:l-s,y:-o}]:n.has("down")?[{x:l/2,y:0},{x:0,y:-o},{x:s,y:-o},{x:s,y:-a+o},{x:l-s,y:-a+o},{x:l-s,y:-o},{x:l,y:-o}]:[{x:0,y:0}]},"getArrowPoints");function qt(e,t){return e.intersect(t)}d(qt,"intersectNode");var yr=qt;function Jt(e,t,r,n){var i=e.x,a=e.y,s=i-n.x,l=a-n.y,o=Math.sqrt(t*t*l*l+r*r*s*s),f=Math.abs(t*r*s/o);n.x<i&&(f=-f);var h=Math.abs(t*r*l/o);return n.y<a&&(h=-h),{x:i+f,y:a+h}}d(Jt,"intersectEllipse");var Qt=Jt;function $t(e,t,r){return Qt(e,t,t,r)}d($t,"intersectCircle");var br=$t;function te(e,t,r,n){var i,a,s,l,o,f,h,y,b,L,E,D,v,T,k;if(i=t.y-e.y,s=e.x-t.x,o=t.x*e.y-e.x*t.y,b=i*r.x+s*r.y+o,L=i*n.x+s*n.y+o,!(b!==0&&L!==0&&Lt(b,L))&&(a=n.y-r.y,l=r.x-n.x,f=n.x*r.y-r.x*n.y,h=a*e.x+l*e.y+f,y=a*t.x+l*t.y+f,!(h!==0&&y!==0&&Lt(h,y))&&(E=i*l-a*s,E!==0)))return D=Math.abs(E/2),v=s*f-l*o,T=v<0?(v-D)/E:(v+D)/E,v=a*o-i*f,k=v<0?(v-D)/E:(v+D)/E,{x:T,y:k}}d(te,"intersectLine");function Lt(e,t){return e*t>0}d(Lt,"sameSign");var wr=te,mr=ee;function ee(e,t,r){var n=e.x,i=e.y,a=[],s=Number.POSITIVE_INFINITY,l=Number.POSITIVE_INFINITY;typeof t.forEach=="function"?t.forEach(function(E){s=Math.min(s,E.x),l=Math.min(l,E.y)}):(s=Math.min(s,t.x),l=Math.min(l,t.y));for(var o=n-e.width/2-s,f=i-e.height/2-l,h=0;h<t.length;h++){var y=t[h],b=t[h<t.length-1?h+1:0],L=wr(e,r,{x:o+y.x,y:f+y.y},{x:o+b.x,y:f+b.y});L&&a.push(L)}return a.length?(a.length>1&&a.sort(function(E,D){var v=E.x-r.x,T=E.y-r.y,k=Math.sqrt(v*v+T*T),N=D.x-r.x,x=D.y-r.y,g=Math.sqrt(N*N+x*x);return k<g?-1:k===g?0:1}),a[0]):e}d(ee,"intersectPolygon");var Lr=d((e,t)=>{var r=e.x,n=e.y,i=t.x-r,a=t.y-n,s=e.width/2,l=e.height/2,o,f;return Math.abs(a)*s>Math.abs(i)*l?(a<0&&(l=-l),o=a===0?0:l*i/a,f=l):(i<0&&(s=-s),o=s,f=i===0?0:s*a/i),{x:r+o,y:n+f}},"intersectRect"),Sr=Lr,C={node:yr,circle:br,ellipse:Qt,polygon:mr,rect:Sr},F=d(async(e,t,r,n)=>{const i=z();let a;const s=t.useHtmlLabels||Z(i.flowchart.htmlLabels);r?a=r:a="node default";const l=e.insert("g").attr("class",a).attr("id",t.domId||t.id),o=l.insert("g").attr("class","label").attr("style",t.labelStyle);let f;t.labelText===void 0?f="":f=typeof t.labelText=="string"?t.labelText:t.labelText[0];const h=o.node();let y;t.labelType==="markdown"?y=Yt(o,Tt(xt(f),i),{useHtmlLabels:s,width:t.width||i.flowchart.wrappingWidth,classes:"markdown-node-label"},i):y=h.appendChild(await j(Tt(xt(f),i),t.labelStyle,!1,n));let b=y.getBBox();const L=t.padding/2;if(Z(i.flowchart.htmlLabels)){const E=y.children[0],D=R(y),v=E.getElementsByTagName("img");if(v){const T=f.replace(/<img[^>]*>/g,"").trim()==="";await Promise.all([...v].map(k=>new Promise(N=>{function x(){if(k.style.display="flex",k.style.flexDirection="column",T){const g=i.fontSize?i.fontSize:window.getComputedStyle(document.body).fontSize,w=parseInt(g,10)*5+"px";k.style.minWidth=w,k.style.maxWidth=w}else k.style.width="100%";N(k)}d(x,"setupImage"),setTimeout(()=>{k.complete&&x()}),k.addEventListener("error",x),k.addEventListener("load",x)})))}b=E.getBoundingClientRect(),D.attr("width",b.width),D.attr("height",b.height)}return s?o.attr("transform","translate("+-b.width/2+", "+-b.height/2+")"):o.attr("transform","translate(0, "+-b.height/2+")"),t.centerLabel&&o.attr("transform","translate("+-b.width/2+", "+-b.height/2+")"),o.insert("rect",":first-child"),{shapeSvg:l,bbox:b,halfPadding:L,label:o}},"labelHelper"),B=d((e,t)=>{const r=t.node().getBBox();e.width=r.width,e.height=r.height},"updateNodeBounds");function G(e,t,r,n){return e.insert("polygon",":first-child").attr("points",n.map(function(i){return i.x+","+i.y}).join(" ")).attr("class","label-container").attr("transform","translate("+-t/2+","+r/2+")")}d(G,"insertPolygonShape");var vr=d(async(e,t)=>{t.useHtmlLabels||z().flowchart.htmlLabels||(t.centerLabel=!0);const{shapeSvg:n,bbox:i,halfPadding:a}=await F(e,t,"node "+t.classes,!0);m.info("Classes = ",t.classes);const s=n.insert("rect",":first-child");return s.attr("rx",t.rx).attr("ry",t.ry).attr("x",-i.width/2-a).attr("y",-i.height/2-a).attr("width",i.width+t.padding).attr("height",i.height+t.padding),B(t,s),t.intersect=function(l){return C.rect(t,l)},n},"note"),Er=vr,zt=d(e=>e?" "+e:"","formatClass"),K=d((e,t)=>`${t||"node default"}${zt(e.classes)} ${zt(e.class)}`,"getClassesFromNode"),At=d(async(e,t)=>{const{shapeSvg:r,bbox:n}=await F(e,t,K(t,void 0),!0),i=n.width+t.padding,a=n.height+t.padding,s=i+a,l=[{x:s/2,y:0},{x:s,y:-s/2},{x:s/2,y:-s},{x:0,y:-s/2}];m.info("Question main (Circle)");const o=G(r,s,s,l);return o.attr("style",t.style),B(t,o),t.intersect=function(f){return m.warn("Intersect called"),C.polygon(t,l,f)},r},"question"),_r=d((e,t)=>{const r=e.insert("g").attr("class","node default").attr("id",t.domId||t.id),n=28,i=[{x:0,y:n/2},{x:n/2,y:0},{x:0,y:-28/2},{x:-28/2,y:0}];return r.insert("polygon",":first-child").attr("points",i.map(function(s){return s.x+","+s.y}).join(" ")).attr("class","state-start").attr("r",7).attr("width",28).attr("height",28),t.width=28,t.height=28,t.intersect=function(s){return C.circle(t,14,s)},r},"choice"),kr=d(async(e,t)=>{const{shapeSvg:r,bbox:n}=await F(e,t,K(t,void 0),!0),i=4,a=n.height+t.padding,s=a/i,l=n.width+2*s+t.padding,o=[{x:s,y:0},{x:l-s,y:0},{x:l,y:-a/2},{x:l-s,y:-a},{x:s,y:-a},{x:0,y:-a/2}],f=G(r,l,a,o);return f.attr("style",t.style),B(t,f),t.intersect=function(h){return C.polygon(t,o,h)},r},"hexagon"),Dr=d(async(e,t)=>{const{shapeSvg:r,bbox:n}=await F(e,t,void 0,!0),i=2,a=n.height+2*t.padding,s=a/i,l=n.width+2*s+t.padding,o=xr(t.directions,n,t),f=G(r,l,a,o);return f.attr("style",t.style),B(t,f),t.intersect=function(h){return C.polygon(t,o,h)},r},"block_arrow"),Nr=d(async(e,t)=>{const{shapeSvg:r,bbox:n}=await F(e,t,K(t,void 0),!0),i=n.width+t.padding,a=n.height+t.padding,s=[{x:-a/2,y:0},{x:i,y:0},{x:i,y:-a},{x:-a/2,y:-a},{x:0,y:-a/2}];return G(r,i,a,s).attr("style",t.style),t.width=i+a,t.height=a,t.intersect=function(o){return C.polygon(t,s,o)},r},"rect_left_inv_arrow"),Tr=d(async(e,t)=>{const{shapeSvg:r,bbox:n}=await F(e,t,K(t),!0),i=n.width+t.padding,a=n.height+t.padding,s=[{x:-2*a/6,y:0},{x:i-a/6,y:0},{x:i+2*a/6,y:-a},{x:a/6,y:-a}],l=G(r,i,a,s);return l.attr("style",t.style),B(t,l),t.intersect=function(o){return C.polygon(t,s,o)},r},"lean_right"),Cr=d(async(e,t)=>{const{shapeSvg:r,bbox:n}=await F(e,t,K(t,void 0),!0),i=n.width+t.padding,a=n.height+t.padding,s=[{x:2*a/6,y:0},{x:i+a/6,y:0},{x:i-2*a/6,y:-a},{x:-a/6,y:-a}],l=G(r,i,a,s);return l.attr("style",t.style),B(t,l),t.intersect=function(o){return C.polygon(t,s,o)},r},"lean_left"),Br=d(async(e,t)=>{const{shapeSvg:r,bbox:n}=await F(e,t,K(t,void 0),!0),i=n.width+t.padding,a=n.height+t.padding,s=[{x:-2*a/6,y:0},{x:i+2*a/6,y:0},{x:i-a/6,y:-a},{x:a/6,y:-a}],l=G(r,i,a,s);return l.attr("style",t.style),B(t,l),t.intersect=function(o){return C.polygon(t,s,o)},r},"trapezoid"),Ir=d(async(e,t)=>{const{shapeSvg:r,bbox:n}=await F(e,t,K(t,void 0),!0),i=n.width+t.padding,a=n.height+t.padding,s=[{x:a/6,y:0},{x:i-a/6,y:0},{x:i+2*a/6,y:-a},{x:-2*a/6,y:-a}],l=G(r,i,a,s);return l.attr("style",t.style),B(t,l),t.intersect=function(o){return C.polygon(t,s,o)},r},"inv_trapezoid"),Or=d(async(e,t)=>{const{shapeSvg:r,bbox:n}=await F(e,t,K(t,void 0),!0),i=n.width+t.padding,a=n.height+t.padding,s=[{x:0,y:0},{x:i+a/2,y:0},{x:i,y:-a/2},{x:i+a/2,y:-a},{x:0,y:-a}],l=G(r,i,a,s);return l.attr("style",t.style),B(t,l),t.intersect=function(o){return C.polygon(t,s,o)},r},"rect_right_inv_arrow"),Rr=d(async(e,t)=>{const{shapeSvg:r,bbox:n}=await F(e,t,K(t,void 0),!0),i=n.width+t.padding,a=i/2,s=a/(2.5+i/50),l=n.height+s+t.padding,o="M 0,"+s+" a "+a+","+s+" 0,0,0 "+i+" 0 a "+a+","+s+" 0,0,0 "+-i+" 0 l 0,"+l+" a "+a+","+s+" 0,0,0 "+i+" 0 l 0,"+-l,f=r.attr("label-offset-y",s).insert("path",":first-child").attr("style",t.style).attr("d",o).attr("transform","translate("+-i/2+","+-(l/2+s)+")");return B(t,f),t.intersect=function(h){const y=C.rect(t,h),b=y.x-t.x;if(a!=0&&(Math.abs(b)<t.width/2||Math.abs(b)==t.width/2&&Math.abs(y.y-t.y)>t.height/2-s)){let L=s*s*(1-b*b/(a*a));L!=0&&(L=Math.sqrt(L)),L=s-L,h.y-t.y>0&&(L=-L),y.y+=L}return y},r},"cylinder"),zr=d(async(e,t)=>{const{shapeSvg:r,bbox:n,halfPadding:i}=await F(e,t,"node "+t.classes+" "+t.class,!0),a=r.insert("rect",":first-child"),s=t.positioned?t.width:n.width+t.padding,l=t.positioned?t.height:n.height+t.padding,o=t.positioned?-s/2:-n.width/2-i,f=t.positioned?-l/2:-n.height/2-i;if(a.attr("class","basic label-container").attr("style",t.style).attr("rx",t.rx).attr("ry",t.ry).attr("x",o).attr("y",f).attr("width",s).attr("height",l),t.props){const h=new Set(Object.keys(t.props));t.props.borders&&(ht(a,t.props.borders,s,l),h.delete("borders")),h.forEach(y=>{m.warn(`Unknown node property ${y}`)})}return B(t,a),t.intersect=function(h){return C.rect(t,h)},r},"rect"),Ar=d(async(e,t)=>{const{shapeSvg:r,bbox:n,halfPadding:i}=await F(e,t,"node "+t.classes,!0),a=r.insert("rect",":first-child"),s=t.positioned?t.width:n.width+t.padding,l=t.positioned?t.height:n.height+t.padding,o=t.positioned?-s/2:-n.width/2-i,f=t.positioned?-l/2:-n.height/2-i;if(a.attr("class","basic cluster composite label-container").attr("style",t.style).attr("rx",t.rx).attr("ry",t.ry).attr("x",o).attr("y",f).attr("width",s).attr("height",l),t.props){const h=new Set(Object.keys(t.props));t.props.borders&&(ht(a,t.props.borders,s,l),h.delete("borders")),h.forEach(y=>{m.warn(`Unknown node property ${y}`)})}return B(t,a),t.intersect=function(h){return C.rect(t,h)},r},"composite"),Mr=d(async(e,t)=>{const{shapeSvg:r}=await F(e,t,"label",!0);m.trace("Classes = ",t.class);const n=r.insert("rect",":first-child"),i=0,a=0;if(n.attr("width",i).attr("height",a),r.attr("class","label edgeLabel"),t.props){const s=new Set(Object.keys(t.props));t.props.borders&&(ht(n,t.props.borders,i,a),s.delete("borders")),s.forEach(l=>{m.warn(`Unknown node property ${l}`)})}return B(t,n),t.intersect=function(s){return C.rect(t,s)},r},"labelRect");function ht(e,t,r,n){const i=[],a=d(l=>{i.push(l,0)},"addBorder"),s=d(l=>{i.push(0,l)},"skipBorder");t.includes("t")?(m.debug("add top border"),a(r)):s(r),t.includes("r")?(m.debug("add right border"),a(n)):s(n),t.includes("b")?(m.debug("add bottom border"),a(r)):s(r),t.includes("l")?(m.debug("add left border"),a(n)):s(n),e.attr("stroke-dasharray",i.join(" "))}d(ht,"applyNodePropertyBorders");var Fr=d(async(e,t)=>{let r;t.classes?r="node "+t.classes:r="node default";const n=e.insert("g").attr("class",r).attr("id",t.domId||t.id),i=n.insert("rect",":first-child"),a=n.insert("line"),s=n.insert("g").attr("class","label"),l=t.labelText.flat?t.labelText.flat():t.labelText;let o="";typeof l=="object"?o=l[0]:o=l,m.info("Label text abc79",o,l,typeof l=="object");const f=s.node().appendChild(await j(o,t.labelStyle,!0,!0));let h={width:0,height:0};if(Z(z().flowchart.htmlLabels)){const D=f.children[0],v=R(f);h=D.getBoundingClientRect(),v.attr("width",h.width),v.attr("height",h.height)}m.info("Text 2",l);const y=l.slice(1,l.length);let b=f.getBBox();const L=s.node().appendChild(await j(y.join?y.join("<br/>"):y,t.labelStyle,!0,!0));if(Z(z().flowchart.htmlLabels)){const D=L.children[0],v=R(L);h=D.getBoundingClientRect(),v.attr("width",h.width),v.attr("height",h.height)}const E=t.padding/2;return R(L).attr("transform","translate( "+(h.width>b.width?0:(b.width-h.width)/2)+", "+(b.height+E+5)+")"),R(f).attr("transform","translate( "+(h.width<b.width?0:-(b.width-h.width)/2)+", 0)"),h=s.node().getBBox(),s.attr("transform","translate("+-h.width/2+", "+(-h.height/2-E+3)+")"),i.attr("class","outer title-state").attr("x",-h.width/2-E).attr("y",-h.height/2-E).attr("width",h.width+t.padding).attr("height",h.height+t.padding),a.attr("class","divider").attr("x1",-h.width/2-E).attr("x2",h.width/2+E).attr("y1",-h.height/2-E+b.height+E).attr("y2",-h.height/2-E+b.height+E),B(t,i),t.intersect=function(D){return C.rect(t,D)},n},"rectWithTitle"),Wr=d(async(e,t)=>{const{shapeSvg:r,bbox:n}=await F(e,t,K(t,void 0),!0),i=n.height+t.padding,a=n.width+i/4+t.padding,s=r.insert("rect",":first-child").attr("style",t.style).attr("rx",i/2).attr("ry",i/2).attr("x",-a/2).attr("y",-i/2).attr("width",a).attr("height",i);return B(t,s),t.intersect=function(l){return C.rect(t,l)},r},"stadium"),Pr=d(async(e,t)=>{const{shapeSvg:r,bbox:n,halfPadding:i}=await F(e,t,K(t,void 0),!0),a=r.insert("circle",":first-child");return a.attr("style",t.style).attr("rx",t.rx).attr("ry",t.ry).attr("r",n.width/2+i).attr("width",n.width+t.padding).attr("height",n.height+t.padding),m.info("Circle main"),B(t,a),t.intersect=function(s){return m.info("Circle intersect",t,n.width/2+i,s),C.circle(t,n.width/2+i,s)},r},"circle"),Yr=d(async(e,t)=>{const{shapeSvg:r,bbox:n,halfPadding:i}=await F(e,t,K(t,void 0),!0),a=5,s=r.insert("g",":first-child"),l=s.insert("circle"),o=s.insert("circle");return s.attr("class",t.class),l.attr("style",t.style).attr("rx",t.rx).attr("ry",t.ry).attr("r",n.width/2+i+a).attr("width",n.width+t.padding+a*2).attr("height",n.height+t.padding+a*2),o.attr("style",t.style).attr("rx",t.rx).attr("ry",t.ry).attr("r",n.width/2+i).attr("width",n.width+t.padding).attr("height",n.height+t.padding),m.info("DoubleCircle main"),B(t,l),t.intersect=function(f){return m.info("DoubleCircle intersect",t,n.width/2+i+a,f),C.circle(t,n.width/2+i+a,f)},r},"doublecircle"),Hr=d(async(e,t)=>{const{shapeSvg:r,bbox:n}=await F(e,t,K(t,void 0),!0),i=n.width+t.padding,a=n.height+t.padding,s=[{x:0,y:0},{x:i,y:0},{x:i,y:-a},{x:0,y:-a},{x:0,y:0},{x:-8,y:0},{x:i+8,y:0},{x:i+8,y:-a},{x:-8,y:-a},{x:-8,y:0}],l=G(r,i,a,s);return l.attr("style",t.style),B(t,l),t.intersect=function(o){return C.polygon(t,s,o)},r},"subroutine"),Kr=d((e,t)=>{const r=e.insert("g").attr("class","node default").attr("id",t.domId||t.id),n=r.insert("circle",":first-child");return n.attr("class","state-start").attr("r",7).attr("width",14).attr("height",14),B(t,n),t.intersect=function(i){return C.circle(t,7,i)},r},"start"),Mt=d((e,t,r)=>{const n=e.insert("g").attr("class","node default").attr("id",t.domId||t.id);let i=70,a=10;r==="LR"&&(i=10,a=70);const s=n.append("rect").attr("x",-1*i/2).attr("y",-1*a/2).attr("width",i).attr("height",a).attr("class","fork-join");return B(t,s),t.height=t.height+t.padding/2,t.width=t.width+t.padding/2,t.intersect=function(l){return C.rect(t,l)},n},"forkJoin"),Xr=d((e,t)=>{const r=e.insert("g").attr("class","node default").attr("id",t.domId||t.id),n=r.insert("circle",":first-child"),i=r.insert("circle",":first-child");return i.attr("class","state-start").attr("r",7).attr("width",14).attr("height",14),n.attr("class","state-end").attr("r",5).attr("width",10).attr("height",10),B(t,i),t.intersect=function(a){return C.circle(t,7,a)},r},"end"),Ur=d(async(e,t)=>{var S;const r=t.padding/2,n=4,i=8;let a;t.classes?a="node "+t.classes:a="node default";const s=e.insert("g").attr("class",a).attr("id",t.domId||t.id),l=s.insert("rect",":first-child"),o=s.insert("line"),f=s.insert("line");let h=0,y=n;const b=s.insert("g").attr("class","label");let L=0;const E=(S=t.classData.annotations)==null?void 0:S[0],D=t.classData.annotations[0]?"«"+t.classData.annotations[0]+"»":"",v=b.node().appendChild(await j(D,t.labelStyle,!0,!0));let T=v.getBBox();if(Z(z().flowchart.htmlLabels)){const c=v.children[0],_=R(v);T=c.getBoundingClientRect(),_.attr("width",T.width),_.attr("height",T.height)}t.classData.annotations[0]&&(y+=T.height+n,h+=T.width);let k=t.classData.label;t.classData.type!==void 0&&t.classData.type!==""&&(z().flowchart.htmlLabels?k+="&lt;"+t.classData.type+"&gt;":k+="<"+t.classData.type+">");const N=b.node().appendChild(await j(k,t.labelStyle,!0,!0));R(N).attr("class","classTitle");let x=N.getBBox();if(Z(z().flowchart.htmlLabels)){const c=N.children[0],_=R(N);x=c.getBoundingClientRect(),_.attr("width",x.width),_.attr("height",x.height)}y+=x.height+n,x.width>h&&(h=x.width);const g=[];t.classData.members.forEach(async c=>{const _=c.getDisplayDetails();let p=_.displayText;z().flowchart.htmlLabels&&(p=p.replace(/</g,"&lt;").replace(/>/g,"&gt;"));const A=b.node().appendChild(await j(p,_.cssStyle?_.cssStyle:t.labelStyle,!0,!0));let O=A.getBBox();if(Z(z().flowchart.htmlLabels)){const X=A.children[0],W=R(A);O=X.getBoundingClientRect(),W.attr("width",O.width),W.attr("height",O.height)}O.width>h&&(h=O.width),y+=O.height+n,g.push(A)}),y+=i;const u=[];if(t.classData.methods.forEach(async c=>{const _=c.getDisplayDetails();let p=_.displayText;z().flowchart.htmlLabels&&(p=p.replace(/</g,"&lt;").replace(/>/g,"&gt;"));const A=b.node().appendChild(await j(p,_.cssStyle?_.cssStyle:t.labelStyle,!0,!0));let O=A.getBBox();if(Z(z().flowchart.htmlLabels)){const X=A.children[0],W=R(A);O=X.getBoundingClientRect(),W.attr("width",O.width),W.attr("height",O.height)}O.width>h&&(h=O.width),y+=O.height+n,u.push(A)}),y+=i,E){let c=(h-T.width)/2;R(v).attr("transform","translate( "+(-1*h/2+c)+", "+-1*y/2+")"),L=T.height+n}let w=(h-x.width)/2;return R(N).attr("transform","translate( "+(-1*h/2+w)+", "+(-1*y/2+L)+")"),L+=x.height+n,o.attr("class","divider").attr("x1",-h/2-r).attr("x2",h/2+r).attr("y1",-y/2-r+i+L).attr("y2",-y/2-r+i+L),L+=i,g.forEach(c=>{R(c).attr("transform","translate( "+-h/2+", "+(-1*y/2+L+i/2)+")");const _=c==null?void 0:c.getBBox();L+=((_==null?void 0:_.height)??0)+n}),L+=i,f.attr("class","divider").attr("x1",-h/2-r).attr("x2",h/2+r).attr("y1",-y/2-r+i+L).attr("y2",-y/2-r+i+L),L+=i,u.forEach(c=>{R(c).attr("transform","translate( "+-h/2+", "+(-1*y/2+L)+")");const _=c==null?void 0:c.getBBox();L+=((_==null?void 0:_.height)??0)+n}),l.attr("style",t.style).attr("class","outer title-state").attr("x",-h/2-r).attr("y",-(y/2)-r).attr("width",h+t.padding).attr("height",y+t.padding),B(t,l),t.intersect=function(c){return C.rect(t,c)},s},"class_box"),Ft={rhombus:At,composite:Ar,question:At,rect:zr,labelRect:Mr,rectWithTitle:Fr,choice:_r,circle:Pr,doublecircle:Yr,stadium:Wr,hexagon:kr,block_arrow:Dr,rect_left_inv_arrow:Nr,lean_right:Tr,lean_left:Cr,trapezoid:Br,inv_trapezoid:Ir,rect_right_inv_arrow:Or,cylinder:Rr,start:Kr,end:Xr,note:Er,subroutine:Hr,fork:Mt,join:Mt,class_box:Ur},lt={},re=d(async(e,t,r)=>{let n,i;if(t.link){let a;z().securityLevel==="sandbox"?a="_top":t.linkTarget&&(a=t.linkTarget||"_blank"),n=e.insert("svg:a").attr("xlink:href",t.link).attr("target",a),i=await Ft[t.shape](n,t,r)}else i=await Ft[t.shape](e,t,r),n=i;return t.tooltip&&i.attr("title",t.tooltip),t.class&&i.attr("class","node default "+t.class),lt[t.id]=n,t.haveCallback&&lt[t.id].attr("class",lt[t.id].attr("class")+" clickable"),n},"insertNode"),jr=d(e=>{const t=lt[e.id];m.trace("Transforming node",e.diff,e,"translate("+(e.x-e.width/2-5)+", "+e.width/2+")");const r=8,n=e.diff||0;return e.clusterNode?t.attr("transform","translate("+(e.x+n-e.width/2)+", "+(e.y-e.height/2-r)+")"):t.attr("transform","translate("+e.x+", "+e.y+")"),n},"positionNode");function kt(e,t,r=!1){var b,L,E;const n=e;let i="default";(((b=n==null?void 0:n.classes)==null?void 0:b.length)||0)>0&&(i=((n==null?void 0:n.classes)??[]).join(" ")),i=i+" flowchart-label";let a=0,s="",l;switch(n.type){case"round":a=5,s="rect";break;case"composite":a=0,s="composite",l=0;break;case"square":s="rect";break;case"diamond":s="question";break;case"hexagon":s="hexagon";break;case"block_arrow":s="block_arrow";break;case"odd":s="rect_left_inv_arrow";break;case"lean_right":s="lean_right";break;case"lean_left":s="lean_left";break;case"trapezoid":s="trapezoid";break;case"inv_trapezoid":s="inv_trapezoid";break;case"rect_left_inv_arrow":s="rect_left_inv_arrow";break;case"circle":s="circle";break;case"ellipse":s="ellipse";break;case"stadium":s="stadium";break;case"subroutine":s="subroutine";break;case"cylinder":s="cylinder";break;case"group":s="rect";break;case"doublecircle":s="doublecircle";break;default:s="rect"}const o=ve((n==null?void 0:n.styles)??[]),f=n.label,h=n.size??{width:0,height:0,x:0,y:0};return{labelStyle:o.labelStyle,shape:s,labelText:f,rx:a,ry:a,class:i,style:o.style,id:n.id,directions:n.directions,width:h.width,height:h.height,x:h.x,y:h.y,positioned:r,intersect:void 0,type:n.type,padding:l??((E=(L=at())==null?void 0:L.block)==null?void 0:E.padding)??0}}d(kt,"getNodeFromBlock");async function ae(e,t,r){const n=kt(t,r,!1);if(n.type==="group")return;const i=at(),a=await re(e,n,{config:i}),s=a.node().getBBox(),l=r.getBlock(n.id);l.size={width:s.width,height:s.height,x:0,y:0,node:a},r.setBlock(l),a.remove()}d(ae,"calculateBlockSize");async function se(e,t,r){const n=kt(t,r,!0);if(r.getBlock(n.id).type!=="space"){const a=at();await re(e,n,{config:a}),t.intersect=n==null?void 0:n.intersect,jr(n)}}d(se,"insertBlockPositioned");async function dt(e,t,r,n){for(const i of t)await n(e,i,r),i.children&&await dt(e,i.children,r,n)}d(dt,"performOperations");async function ie(e,t,r){await dt(e,t,r,ae)}d(ie,"calculateBlockSizes");async function ne(e,t,r){await dt(e,t,r,se)}d(ne,"insertBlocks");async function le(e,t,r,n,i){const a=new _e({multigraph:!0,compound:!0});a.setGraph({rankdir:"TB",nodesep:10,ranksep:10,marginx:8,marginy:8});for(const s of r)s.size&&a.setNode(s.id,{width:s.size.width,height:s.size.height,intersect:s.intersect});for(const s of t)if(s.start&&s.end){const l=n.getBlock(s.start),o=n.getBlock(s.end);if(l!=null&&l.size&&(o!=null&&o.size)){const f=l.size,h=o.size,y=[{x:f.x,y:f.y},{x:f.x+(h.x-f.x)/2,y:f.y+(h.y-f.y)/2},{x:h.x,y:h.y}];pr(e,{v:s.start,w:s.end,name:s.id},{...s,arrowTypeEnd:s.arrowTypeEnd,arrowTypeStart:s.arrowTypeStart,points:y,classes:"edge-thickness-normal edge-pattern-solid flowchart-link LS-a1 LE-b1"},void 0,"block",a,i),s.label&&(await hr(e,{...s,label:s.label,labelStyle:"stroke: #333; stroke-width: 1.5px;fill:none;",arrowTypeEnd:s.arrowTypeEnd,arrowTypeStart:s.arrowTypeStart,points:y,classes:"edge-thickness-normal edge-pattern-solid flowchart-link LS-a1 LE-b1"}),dr({...s,x:y[1].x,y:y[1].y},{originalPath:y}))}}}d(le,"insertEdges");var Vr=d(function(e,t){return t.db.getClasses()},"getClasses"),Gr=d(async function(e,t,r,n){const{securityLevel:i,block:a}=at(),s=n.db;let l;i==="sandbox"&&(l=R("#i"+t));const o=i==="sandbox"?R(l.nodes()[0].contentDocument.body):R("body"),f=i==="sandbox"?o.select(`[id="${t}"]`):R(`[id="${t}"]`);ir(f,["point","circle","cross"],n.type,t);const y=s.getBlocks(),b=s.getBlocksFlat(),L=s.getEdges(),E=f.insert("g").attr("class","block");await ie(E,y,s);const D=Gt(s);if(await ne(E,y,s),await le(E,L,b,s,t),D){const v=D,T=Math.max(1,Math.round(.125*(v.width/v.height))),k=v.height+T+10,N=v.width+10,{useMaxWidth:x}=a;ge(f,k,N,!!x),m.debug("Here Bounds",D,v),f.attr("viewBox",`${v.x-5} ${v.y-5} ${v.width+10} ${v.height+10}`)}},"draw"),Zr={draw:Gr,getClasses:Vr},na={parser:ke,db:Ue,renderer:Zr,styles:Ve};export{na as diagram};
