import{j as o,Y as ld,O as fg,k as dg,u as ad,Z as mg,c as hg,l as gg,g as pg,S as yg,T as vg,n as bg,m as nd,o as Sg,p as Tg,$ as ud,a0 as id,a1 as cd,a2 as xg}from"./ui-vendor-CeCm8EER.js";import{d as Ag,h as Dg,r as E,u as sd,H as Ng,i as Eg,j as kf}from"./react-vendor-DEwriMA6.js";import{N as we,c as Ve,ae as od,u as Bt,M as st,af as rd,ag as fd,I as us,B as Cn,D as Mg,l as zg,m as Cg,n as Og,o as _g,ah as Rg,ai as jg,aj as Ug,ak as Hg,al as qt,am as dd,an as ss,ao as is,a1 as Lg,a2 as qg,a3 as Bg,a4 as Gg,ap as Yg,aq as Xg,ar as md,as as wg,at as hd,au as Vg,av as gd,d as Qg,R as Kg,V as Zg,g as En,aw as kg,ax as Jg,ay as Fg}from"./feature-graph-BWr9U7tw.js";import{S as Jf,a as Ff,b as Pf,c as $f,e as rl,D as Pg}from"./feature-documents-D26P2AXA.js";import{R as $g}from"./feature-retrieval-Dyng9L25.js";import{i as cs}from"./utils-vendor-BysuhMZA.js";import"./graph-vendor-B-X5JegA.js";import"./mermaid-vendor-BNDdXxLk.js";import"./markdown-vendor-DmIvJdn7.js";(function(){const y=document.createElement("link").relList;if(y&&y.supports&&y.supports("modulepreload"))return;for(const N of document.querySelectorAll('link[rel="modulepreload"]'))d(N);new MutationObserver(N=>{for(const _ of N)if(_.type==="childList")for(const H of _.addedNodes)H.tagName==="LINK"&&H.rel==="modulepreload"&&d(H)}).observe(document,{childList:!0,subtree:!0});function x(N){const _={};return N.integrity&&(_.integrity=N.integrity),N.referrerPolicy&&(_.referrerPolicy=N.referrerPolicy),N.crossOrigin==="use-credentials"?_.credentials="include":N.crossOrigin==="anonymous"?_.credentials="omit":_.credentials="same-origin",_}function d(N){if(N.ep)return;N.ep=!0;const _=x(N);fetch(N.href,_)}})();var ls={exports:{}},Mn={},as={exports:{}},ns={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Wf;function Wg(){return Wf||(Wf=1,function(m){function y(A,L){var j=A.length;A.push(L);e:for(;0<j;){var te=j-1>>>1,oe=A[te];if(0<N(oe,L))A[te]=L,A[j]=oe,j=te;else break e}}function x(A){return A.length===0?null:A[0]}function d(A){if(A.length===0)return null;var L=A[0],j=A.pop();if(j!==L){A[0]=j;e:for(var te=0,oe=A.length,Xl=oe>>>1;te<Xl;){var yl=2*(te+1)-1,bt=A[yl],Q=yl+1,Qe=A[Q];if(0>N(bt,j))Q<oe&&0>N(Qe,bt)?(A[te]=Qe,A[Q]=j,te=Q):(A[te]=bt,A[yl]=j,te=yl);else if(Q<oe&&0>N(Qe,j))A[te]=Qe,A[Q]=j,te=Q;else break e}}return L}function N(A,L){var j=A.sortIndex-L.sortIndex;return j!==0?j:A.id-L.id}if(m.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var _=performance;m.unstable_now=function(){return _.now()}}else{var H=Date,P=H.now();m.unstable_now=function(){return H.now()-P}}var Y=[],$=[],he=1,ge=null,V=3,pe=!1,ae=!1,C=!1,yt=typeof setTimeout=="function"?setTimeout:null,fl=typeof clearTimeout=="function"?clearTimeout:null,_e=typeof setImmediate<"u"?setImmediate:null;function dl(A){for(var L=x($);L!==null;){if(L.callback===null)d($);else if(L.startTime<=A)d($),L.sortIndex=L.expirationTime,y(Y,L);else break;L=x($)}}function Ea(A){if(C=!1,dl(A),!ae)if(x(Y)!==null)ae=!0,gl();else{var L=x($);L!==null&&pl(Ea,L.startTime-A)}}var ml=!1,at=-1,On=5,Yl=-1;function R(){return!(m.unstable_now()-Yl<On)}function k(){if(ml){var A=m.unstable_now();Yl=A;var L=!0;try{e:{ae=!1,C&&(C=!1,fl(at),at=-1),pe=!0;var j=V;try{t:{for(dl(A),ge=x(Y);ge!==null&&!(ge.expirationTime>A&&R());){var te=ge.callback;if(typeof te=="function"){ge.callback=null,V=ge.priorityLevel;var oe=te(ge.expirationTime<=A);if(A=m.unstable_now(),typeof oe=="function"){ge.callback=oe,dl(A),L=!0;break t}ge===x(Y)&&d(Y),dl(A)}else d(Y);ge=x(Y)}if(ge!==null)L=!0;else{var Xl=x($);Xl!==null&&pl(Ea,Xl.startTime-A),L=!1}}break e}finally{ge=null,V=j,pe=!1}L=void 0}}finally{L?vt():ml=!1}}}var vt;if(typeof _e=="function")vt=function(){_e(k)};else if(typeof MessageChannel<"u"){var Ma=new MessageChannel,hl=Ma.port2;Ma.port1.onmessage=k,vt=function(){hl.postMessage(null)}}else vt=function(){yt(k,0)};function gl(){ml||(ml=!0,vt())}function pl(A,L){at=yt(function(){A(m.unstable_now())},L)}m.unstable_IdlePriority=5,m.unstable_ImmediatePriority=1,m.unstable_LowPriority=4,m.unstable_NormalPriority=3,m.unstable_Profiling=null,m.unstable_UserBlockingPriority=2,m.unstable_cancelCallback=function(A){A.callback=null},m.unstable_continueExecution=function(){ae||pe||(ae=!0,gl())},m.unstable_forceFrameRate=function(A){0>A||125<A?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):On=0<A?Math.floor(1e3/A):5},m.unstable_getCurrentPriorityLevel=function(){return V},m.unstable_getFirstCallbackNode=function(){return x(Y)},m.unstable_next=function(A){switch(V){case 1:case 2:case 3:var L=3;break;default:L=V}var j=V;V=L;try{return A()}finally{V=j}},m.unstable_pauseExecution=function(){},m.unstable_requestPaint=function(){},m.unstable_runWithPriority=function(A,L){switch(A){case 1:case 2:case 3:case 4:case 5:break;default:A=3}var j=V;V=A;try{return L()}finally{V=j}},m.unstable_scheduleCallback=function(A,L,j){var te=m.unstable_now();switch(typeof j=="object"&&j!==null?(j=j.delay,j=typeof j=="number"&&0<j?te+j:te):j=te,A){case 1:var oe=-1;break;case 2:oe=250;break;case 5:oe=1073741823;break;case 4:oe=1e4;break;default:oe=5e3}return oe=j+oe,A={id:he++,callback:L,priorityLevel:A,startTime:j,expirationTime:oe,sortIndex:-1},j>te?(A.sortIndex=j,y($,A),x(Y)===null&&A===x($)&&(C?(fl(at),at=-1):C=!0,pl(Ea,j-te))):(A.sortIndex=oe,y(Y,A),ae||pe||(ae=!0,gl())),A},m.unstable_shouldYield=R,m.unstable_wrapCallback=function(A){var L=V;return function(){var j=V;V=L;try{return A.apply(this,arguments)}finally{V=j}}}}(ns)),ns}var If;function Ig(){return If||(If=1,as.exports=Wg()),as.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ed;function ep(){if(ed)return Mn;ed=1;var m=Ig(),y=Ag(),x=Dg();function d(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var l=2;l<arguments.length;l++)t+="&args[]="+encodeURIComponent(arguments[l])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function N(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}var _=Symbol.for("react.element"),H=Symbol.for("react.transitional.element"),P=Symbol.for("react.portal"),Y=Symbol.for("react.fragment"),$=Symbol.for("react.strict_mode"),he=Symbol.for("react.profiler"),ge=Symbol.for("react.provider"),V=Symbol.for("react.consumer"),pe=Symbol.for("react.context"),ae=Symbol.for("react.forward_ref"),C=Symbol.for("react.suspense"),yt=Symbol.for("react.suspense_list"),fl=Symbol.for("react.memo"),_e=Symbol.for("react.lazy"),dl=Symbol.for("react.offscreen"),Ea=Symbol.for("react.memo_cache_sentinel"),ml=Symbol.iterator;function at(e){return e===null||typeof e!="object"?null:(e=ml&&e[ml]||e["@@iterator"],typeof e=="function"?e:null)}var On=Symbol.for("react.client.reference");function Yl(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===On?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Y:return"Fragment";case P:return"Portal";case he:return"Profiler";case $:return"StrictMode";case C:return"Suspense";case yt:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case pe:return(e.displayName||"Context")+".Provider";case V:return(e._context.displayName||"Context")+".Consumer";case ae:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case fl:return t=e.displayName||null,t!==null?t:Yl(e.type)||"Memo";case _e:t=e._payload,e=e._init;try{return Yl(e(t))}catch{}}return null}var R=y.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,k=Object.assign,vt,Ma;function hl(e){if(vt===void 0)try{throw Error()}catch(l){var t=l.stack.trim().match(/\n( *(at )?)/);vt=t&&t[1]||"",Ma=-1<l.stack.indexOf(`
    at`)?" (<anonymous>)":-1<l.stack.indexOf("@")?"@unknown:0:0":""}return`
`+vt+e+Ma}var gl=!1;function pl(e,t){if(!e||gl)return"";gl=!0;var l=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var a={DetermineComponentFrameRoot:function(){try{if(t){var T=function(){throw Error()};if(Object.defineProperty(T.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(T,[])}catch(v){var p=v}Reflect.construct(e,[],T)}else{try{T.call()}catch(v){p=v}e.call(T.prototype)}}else{try{throw Error()}catch(v){p=v}(T=e())&&typeof T.catch=="function"&&T.catch(function(){})}}catch(v){if(v&&p&&typeof v.stack=="string")return[v.stack,p.stack]}return[null,null]}};a.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var n=Object.getOwnPropertyDescriptor(a.DetermineComponentFrameRoot,"name");n&&n.configurable&&Object.defineProperty(a.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var u=a.DetermineComponentFrameRoot(),i=u[0],c=u[1];if(i&&c){var s=i.split(`
`),f=c.split(`
`);for(n=a=0;a<s.length&&!s[a].includes("DetermineComponentFrameRoot");)a++;for(;n<f.length&&!f[n].includes("DetermineComponentFrameRoot");)n++;if(a===s.length||n===f.length)for(a=s.length-1,n=f.length-1;1<=a&&0<=n&&s[a]!==f[n];)n--;for(;1<=a&&0<=n;a--,n--)if(s[a]!==f[n]){if(a!==1||n!==1)do if(a--,n--,0>n||s[a]!==f[n]){var b=`
`+s[a].replace(" at new "," at ");return e.displayName&&b.includes("<anonymous>")&&(b=b.replace("<anonymous>",e.displayName)),b}while(1<=a&&0<=n);break}}}finally{gl=!1,Error.prepareStackTrace=l}return(l=e?e.displayName||e.name:"")?hl(l):""}function A(e){switch(e.tag){case 26:case 27:case 5:return hl(e.type);case 16:return hl("Lazy");case 13:return hl("Suspense");case 19:return hl("SuspenseList");case 0:case 15:return e=pl(e.type,!1),e;case 11:return e=pl(e.type.render,!1),e;case 1:return e=pl(e.type,!0),e;default:return""}}function L(e){try{var t="";do t+=A(e),e=e.return;while(e);return t}catch(l){return`
Error generating stack: `+l.message+`
`+l.stack}}function j(e){var t=e,l=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(l=t.return),e=t.return;while(e)}return t.tag===3?l:null}function te(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function oe(e){if(j(e)!==e)throw Error(d(188))}function Xl(e){var t=e.alternate;if(!t){if(t=j(e),t===null)throw Error(d(188));return t!==e?null:e}for(var l=e,a=t;;){var n=l.return;if(n===null)break;var u=n.alternate;if(u===null){if(a=n.return,a!==null){l=a;continue}break}if(n.child===u.child){for(u=n.child;u;){if(u===l)return oe(n),e;if(u===a)return oe(n),t;u=u.sibling}throw Error(d(188))}if(l.return!==a.return)l=n,a=u;else{for(var i=!1,c=n.child;c;){if(c===l){i=!0,l=n,a=u;break}if(c===a){i=!0,a=n,l=u;break}c=c.sibling}if(!i){for(c=u.child;c;){if(c===l){i=!0,l=u,a=n;break}if(c===a){i=!0,a=u,l=n;break}c=c.sibling}if(!i)throw Error(d(189))}}if(l.alternate!==a)throw Error(d(190))}if(l.tag!==3)throw Error(d(188));return l.stateNode.current===l?e:t}function yl(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=yl(e),t!==null)return t;e=e.sibling}return null}var bt=Array.isArray,Q=x.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,Qe={pending:!1,data:null,method:null,action:null},Zu=[],wl=-1;function ot(e){return{current:e}}function be(e){0>wl||(e.current=Zu[wl],Zu[wl]=null,wl--)}function le(e,t){wl++,Zu[wl]=e.current,e.current=t}var rt=ot(null),za=ot(null),Yt=ot(null),_n=ot(null);function Rn(e,t){switch(le(Yt,t),le(za,e),le(rt,null),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)&&(t=t.namespaceURI)?xf(t):0;break;default:if(e=e===8?t.parentNode:t,t=e.tagName,e=e.namespaceURI)e=xf(e),t=Af(e,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}be(rt),le(rt,t)}function Vl(){be(rt),be(za),be(Yt)}function ku(e){e.memoizedState!==null&&le(_n,e);var t=rt.current,l=Af(t,e.type);t!==l&&(le(za,e),le(rt,l))}function jn(e){za.current===e&&(be(rt),be(za)),_n.current===e&&(be(_n),Tn._currentValue=Qe)}var Ju=Object.prototype.hasOwnProperty,Fu=m.unstable_scheduleCallback,Pu=m.unstable_cancelCallback,Vd=m.unstable_shouldYield,Qd=m.unstable_requestPaint,ft=m.unstable_now,Kd=m.unstable_getCurrentPriorityLevel,os=m.unstable_ImmediatePriority,rs=m.unstable_UserBlockingPriority,Un=m.unstable_NormalPriority,Zd=m.unstable_LowPriority,fs=m.unstable_IdlePriority,kd=m.log,Jd=m.unstable_setDisableYieldValue,Ca=null,He=null;function Fd(e){if(He&&typeof He.onCommitFiberRoot=="function")try{He.onCommitFiberRoot(Ca,e,void 0,(e.current.flags&128)===128)}catch{}}function Xt(e){if(typeof kd=="function"&&Jd(e),He&&typeof He.setStrictMode=="function")try{He.setStrictMode(Ca,e)}catch{}}var Le=Math.clz32?Math.clz32:Wd,Pd=Math.log,$d=Math.LN2;function Wd(e){return e>>>=0,e===0?32:31-(Pd(e)/$d|0)|0}var Hn=128,Ln=4194304;function vl(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194176;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function qn(e,t){var l=e.pendingLanes;if(l===0)return 0;var a=0,n=e.suspendedLanes,u=e.pingedLanes,i=e.warmLanes;e=e.finishedLanes!==0;var c=l&134217727;return c!==0?(l=c&~n,l!==0?a=vl(l):(u&=c,u!==0?a=vl(u):e||(i=c&~i,i!==0&&(a=vl(i))))):(c=l&~n,c!==0?a=vl(c):u!==0?a=vl(u):e||(i=l&~i,i!==0&&(a=vl(i)))),a===0?0:t!==0&&t!==a&&!(t&n)&&(n=a&-a,i=t&-t,n>=i||n===32&&(i&4194176)!==0)?t:a}function Oa(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function Id(e,t){switch(e){case 1:case 2:case 4:case 8:return t+250;case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function ds(){var e=Hn;return Hn<<=1,!(Hn&4194176)&&(Hn=128),e}function ms(){var e=Ln;return Ln<<=1,!(Ln&62914560)&&(Ln=4194304),e}function $u(e){for(var t=[],l=0;31>l;l++)t.push(e);return t}function _a(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function em(e,t,l,a,n,u){var i=e.pendingLanes;e.pendingLanes=l,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=l,e.entangledLanes&=l,e.errorRecoveryDisabledLanes&=l,e.shellSuspendCounter=0;var c=e.entanglements,s=e.expirationTimes,f=e.hiddenUpdates;for(l=i&~l;0<l;){var b=31-Le(l),T=1<<b;c[b]=0,s[b]=-1;var p=f[b];if(p!==null)for(f[b]=null,b=0;b<p.length;b++){var v=p[b];v!==null&&(v.lane&=-536870913)}l&=~T}a!==0&&hs(e,a,0),u!==0&&n===0&&e.tag!==0&&(e.suspendedLanes|=u&~(i&~t))}function hs(e,t,l){e.pendingLanes|=t,e.suspendedLanes&=~t;var a=31-Le(t);e.entangledLanes|=t,e.entanglements[a]=e.entanglements[a]|1073741824|l&4194218}function gs(e,t){var l=e.entangledLanes|=t;for(e=e.entanglements;l;){var a=31-Le(l),n=1<<a;n&t|e[a]&t&&(e[a]|=t),l&=~n}}function ps(e){return e&=-e,2<e?8<e?e&134217727?32:268435456:8:2}function ys(){var e=Q.p;return e!==0?e:(e=window.event,e===void 0?32:Xf(e.type))}function tm(e,t){var l=Q.p;try{return Q.p=e,t()}finally{Q.p=l}}var wt=Math.random().toString(36).slice(2),Me="__reactFiber$"+wt,Re="__reactProps$"+wt,Ql="__reactContainer$"+wt,Wu="__reactEvents$"+wt,lm="__reactListeners$"+wt,am="__reactHandles$"+wt,vs="__reactResources$"+wt,Ra="__reactMarker$"+wt;function Iu(e){delete e[Me],delete e[Re],delete e[Wu],delete e[lm],delete e[am]}function bl(e){var t=e[Me];if(t)return t;for(var l=e.parentNode;l;){if(t=l[Ql]||l[Me]){if(l=t.alternate,t.child!==null||l!==null&&l.child!==null)for(e=Ef(e);e!==null;){if(l=e[Me])return l;e=Ef(e)}return t}e=l,l=e.parentNode}return null}function Kl(e){if(e=e[Me]||e[Ql]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function ja(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(d(33))}function Zl(e){var t=e[vs];return t||(t=e[vs]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function Se(e){e[Ra]=!0}var bs=new Set,Ss={};function Sl(e,t){kl(e,t),kl(e+"Capture",t)}function kl(e,t){for(Ss[e]=t,e=0;e<t.length;e++)bs.add(t[e])}var St=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),nm=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Ts={},xs={};function um(e){return Ju.call(xs,e)?!0:Ju.call(Ts,e)?!1:nm.test(e)?xs[e]=!0:(Ts[e]=!0,!1)}function Bn(e,t,l){if(um(t))if(l===null)e.removeAttribute(t);else{switch(typeof l){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var a=t.toLowerCase().slice(0,5);if(a!=="data-"&&a!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+l)}}function Gn(e,t,l){if(l===null)e.removeAttribute(t);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+l)}}function Tt(e,t,l,a){if(a===null)e.removeAttribute(l);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(l);return}e.setAttributeNS(t,l,""+a)}}function Ke(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function As(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function im(e){var t=As(e)?"checked":"value",l=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),a=""+e[t];if(!e.hasOwnProperty(t)&&typeof l<"u"&&typeof l.get=="function"&&typeof l.set=="function"){var n=l.get,u=l.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return n.call(this)},set:function(i){a=""+i,u.call(this,i)}}),Object.defineProperty(e,t,{enumerable:l.enumerable}),{getValue:function(){return a},setValue:function(i){a=""+i},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Yn(e){e._valueTracker||(e._valueTracker=im(e))}function Ds(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var l=t.getValue(),a="";return e&&(a=As(e)?e.checked?"true":"false":e.value),e=a,e!==l?(t.setValue(e),!0):!1}function Xn(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var cm=/[\n"\\]/g;function Ze(e){return e.replace(cm,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function ei(e,t,l,a,n,u,i,c){e.name="",i!=null&&typeof i!="function"&&typeof i!="symbol"&&typeof i!="boolean"?e.type=i:e.removeAttribute("type"),t!=null?i==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+Ke(t)):e.value!==""+Ke(t)&&(e.value=""+Ke(t)):i!=="submit"&&i!=="reset"||e.removeAttribute("value"),t!=null?ti(e,i,Ke(t)):l!=null?ti(e,i,Ke(l)):a!=null&&e.removeAttribute("value"),n==null&&u!=null&&(e.defaultChecked=!!u),n!=null&&(e.checked=n&&typeof n!="function"&&typeof n!="symbol"),c!=null&&typeof c!="function"&&typeof c!="symbol"&&typeof c!="boolean"?e.name=""+Ke(c):e.removeAttribute("name")}function Ns(e,t,l,a,n,u,i,c){if(u!=null&&typeof u!="function"&&typeof u!="symbol"&&typeof u!="boolean"&&(e.type=u),t!=null||l!=null){if(!(u!=="submit"&&u!=="reset"||t!=null))return;l=l!=null?""+Ke(l):"",t=t!=null?""+Ke(t):l,c||t===e.value||(e.value=t),e.defaultValue=t}a=a??n,a=typeof a!="function"&&typeof a!="symbol"&&!!a,e.checked=c?e.checked:!!a,e.defaultChecked=!!a,i!=null&&typeof i!="function"&&typeof i!="symbol"&&typeof i!="boolean"&&(e.name=i)}function ti(e,t,l){t==="number"&&Xn(e.ownerDocument)===e||e.defaultValue===""+l||(e.defaultValue=""+l)}function Jl(e,t,l,a){if(e=e.options,t){t={};for(var n=0;n<l.length;n++)t["$"+l[n]]=!0;for(l=0;l<e.length;l++)n=t.hasOwnProperty("$"+e[l].value),e[l].selected!==n&&(e[l].selected=n),n&&a&&(e[l].defaultSelected=!0)}else{for(l=""+Ke(l),t=null,n=0;n<e.length;n++){if(e[n].value===l){e[n].selected=!0,a&&(e[n].defaultSelected=!0);return}t!==null||e[n].disabled||(t=e[n])}t!==null&&(t.selected=!0)}}function Es(e,t,l){if(t!=null&&(t=""+Ke(t),t!==e.value&&(e.value=t),l==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=l!=null?""+Ke(l):""}function Ms(e,t,l,a){if(t==null){if(a!=null){if(l!=null)throw Error(d(92));if(bt(a)){if(1<a.length)throw Error(d(93));a=a[0]}l=a}l==null&&(l=""),t=l}l=Ke(t),e.defaultValue=l,a=e.textContent,a===l&&a!==""&&a!==null&&(e.value=a)}function Fl(e,t){if(t){var l=e.firstChild;if(l&&l===e.lastChild&&l.nodeType===3){l.nodeValue=t;return}}e.textContent=t}var sm=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function zs(e,t,l){var a=t.indexOf("--")===0;l==null||typeof l=="boolean"||l===""?a?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":a?e.setProperty(t,l):typeof l!="number"||l===0||sm.has(t)?t==="float"?e.cssFloat=l:e[t]=(""+l).trim():e[t]=l+"px"}function Cs(e,t,l){if(t!=null&&typeof t!="object")throw Error(d(62));if(e=e.style,l!=null){for(var a in l)!l.hasOwnProperty(a)||t!=null&&t.hasOwnProperty(a)||(a.indexOf("--")===0?e.setProperty(a,""):a==="float"?e.cssFloat="":e[a]="");for(var n in t)a=t[n],t.hasOwnProperty(n)&&l[n]!==a&&zs(e,n,a)}else for(var u in t)t.hasOwnProperty(u)&&zs(e,u,t[u])}function li(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var om=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),rm=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function wn(e){return rm.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var ai=null;function ni(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Pl=null,$l=null;function Os(e){var t=Kl(e);if(t&&(e=t.stateNode)){var l=e[Re]||null;e:switch(e=t.stateNode,t.type){case"input":if(ei(e,l.value,l.defaultValue,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name),t=l.name,l.type==="radio"&&t!=null){for(l=e;l.parentNode;)l=l.parentNode;for(l=l.querySelectorAll('input[name="'+Ze(""+t)+'"][type="radio"]'),t=0;t<l.length;t++){var a=l[t];if(a!==e&&a.form===e.form){var n=a[Re]||null;if(!n)throw Error(d(90));ei(a,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name)}}for(t=0;t<l.length;t++)a=l[t],a.form===e.form&&Ds(a)}break e;case"textarea":Es(e,l.value,l.defaultValue);break e;case"select":t=l.value,t!=null&&Jl(e,!!l.multiple,t,!1)}}}var ui=!1;function _s(e,t,l){if(ui)return e(t,l);ui=!0;try{var a=e(t);return a}finally{if(ui=!1,(Pl!==null||$l!==null)&&(Nu(),Pl&&(t=Pl,e=$l,$l=Pl=null,Os(t),e)))for(t=0;t<e.length;t++)Os(e[t])}}function Ua(e,t){var l=e.stateNode;if(l===null)return null;var a=l[Re]||null;if(a===null)return null;l=a[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(a=!a.disabled)||(e=e.type,a=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!a;break e;default:e=!1}if(e)return null;if(l&&typeof l!="function")throw Error(d(231,t,typeof l));return l}var ii=!1;if(St)try{var Ha={};Object.defineProperty(Ha,"passive",{get:function(){ii=!0}}),window.addEventListener("test",Ha,Ha),window.removeEventListener("test",Ha,Ha)}catch{ii=!1}var Vt=null,ci=null,Vn=null;function Rs(){if(Vn)return Vn;var e,t=ci,l=t.length,a,n="value"in Vt?Vt.value:Vt.textContent,u=n.length;for(e=0;e<l&&t[e]===n[e];e++);var i=l-e;for(a=1;a<=i&&t[l-a]===n[u-a];a++);return Vn=n.slice(e,1<a?1-a:void 0)}function Qn(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Kn(){return!0}function js(){return!1}function je(e){function t(l,a,n,u,i){this._reactName=l,this._targetInst=n,this.type=a,this.nativeEvent=u,this.target=i,this.currentTarget=null;for(var c in e)e.hasOwnProperty(c)&&(l=e[c],this[c]=l?l(u):u[c]);return this.isDefaultPrevented=(u.defaultPrevented!=null?u.defaultPrevented:u.returnValue===!1)?Kn:js,this.isPropagationStopped=js,this}return k(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var l=this.nativeEvent;l&&(l.preventDefault?l.preventDefault():typeof l.returnValue!="unknown"&&(l.returnValue=!1),this.isDefaultPrevented=Kn)},stopPropagation:function(){var l=this.nativeEvent;l&&(l.stopPropagation?l.stopPropagation():typeof l.cancelBubble!="unknown"&&(l.cancelBubble=!0),this.isPropagationStopped=Kn)},persist:function(){},isPersistent:Kn}),t}var Tl={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Zn=je(Tl),La=k({},Tl,{view:0,detail:0}),fm=je(La),si,oi,qa,kn=k({},La,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:fi,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==qa&&(qa&&e.type==="mousemove"?(si=e.screenX-qa.screenX,oi=e.screenY-qa.screenY):oi=si=0,qa=e),si)},movementY:function(e){return"movementY"in e?e.movementY:oi}}),Us=je(kn),dm=k({},kn,{dataTransfer:0}),mm=je(dm),hm=k({},La,{relatedTarget:0}),ri=je(hm),gm=k({},Tl,{animationName:0,elapsedTime:0,pseudoElement:0}),pm=je(gm),ym=k({},Tl,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),vm=je(ym),bm=k({},Tl,{data:0}),Hs=je(bm),Sm={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Tm={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},xm={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Am(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=xm[e])?!!t[e]:!1}function fi(){return Am}var Dm=k({},La,{key:function(e){if(e.key){var t=Sm[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Qn(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Tm[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:fi,charCode:function(e){return e.type==="keypress"?Qn(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Qn(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Nm=je(Dm),Em=k({},kn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Ls=je(Em),Mm=k({},La,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:fi}),zm=je(Mm),Cm=k({},Tl,{propertyName:0,elapsedTime:0,pseudoElement:0}),Om=je(Cm),_m=k({},kn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Rm=je(_m),jm=k({},Tl,{newState:0,oldState:0}),Um=je(jm),Hm=[9,13,27,32],di=St&&"CompositionEvent"in window,Ba=null;St&&"documentMode"in document&&(Ba=document.documentMode);var Lm=St&&"TextEvent"in window&&!Ba,qs=St&&(!di||Ba&&8<Ba&&11>=Ba),Bs=" ",Gs=!1;function Ys(e,t){switch(e){case"keyup":return Hm.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Xs(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Wl=!1;function qm(e,t){switch(e){case"compositionend":return Xs(t);case"keypress":return t.which!==32?null:(Gs=!0,Bs);case"textInput":return e=t.data,e===Bs&&Gs?null:e;default:return null}}function Bm(e,t){if(Wl)return e==="compositionend"||!di&&Ys(e,t)?(e=Rs(),Vn=ci=Vt=null,Wl=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return qs&&t.locale!=="ko"?null:t.data;default:return null}}var Gm={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function ws(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Gm[e.type]:t==="textarea"}function Vs(e,t,l,a){Pl?$l?$l.push(a):$l=[a]:Pl=a,t=Ou(t,"onChange"),0<t.length&&(l=new Zn("onChange","change",null,l,a),e.push({event:l,listeners:t}))}var Ga=null,Ya=null;function Ym(e){yf(e,0)}function Jn(e){var t=ja(e);if(Ds(t))return e}function Qs(e,t){if(e==="change")return t}var Ks=!1;if(St){var mi;if(St){var hi="oninput"in document;if(!hi){var Zs=document.createElement("div");Zs.setAttribute("oninput","return;"),hi=typeof Zs.oninput=="function"}mi=hi}else mi=!1;Ks=mi&&(!document.documentMode||9<document.documentMode)}function ks(){Ga&&(Ga.detachEvent("onpropertychange",Js),Ya=Ga=null)}function Js(e){if(e.propertyName==="value"&&Jn(Ya)){var t=[];Vs(t,Ya,e,ni(e)),_s(Ym,t)}}function Xm(e,t,l){e==="focusin"?(ks(),Ga=t,Ya=l,Ga.attachEvent("onpropertychange",Js)):e==="focusout"&&ks()}function wm(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Jn(Ya)}function Vm(e,t){if(e==="click")return Jn(t)}function Qm(e,t){if(e==="input"||e==="change")return Jn(t)}function Km(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var qe=typeof Object.is=="function"?Object.is:Km;function Xa(e,t){if(qe(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var l=Object.keys(e),a=Object.keys(t);if(l.length!==a.length)return!1;for(a=0;a<l.length;a++){var n=l[a];if(!Ju.call(t,n)||!qe(e[n],t[n]))return!1}return!0}function Fs(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Ps(e,t){var l=Fs(e);e=0;for(var a;l;){if(l.nodeType===3){if(a=e+l.textContent.length,e<=t&&a>=t)return{node:l,offset:t-e};e=a}e:{for(;l;){if(l.nextSibling){l=l.nextSibling;break e}l=l.parentNode}l=void 0}l=Fs(l)}}function $s(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?$s(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Ws(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=Xn(e.document);t instanceof e.HTMLIFrameElement;){try{var l=typeof t.contentWindow.location.href=="string"}catch{l=!1}if(l)e=t.contentWindow;else break;t=Xn(e.document)}return t}function gi(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Zm(e,t){var l=Ws(t);t=e.focusedElem;var a=e.selectionRange;if(l!==t&&t&&t.ownerDocument&&$s(t.ownerDocument.documentElement,t)){if(a!==null&&gi(t)){if(e=a.start,l=a.end,l===void 0&&(l=e),"selectionStart"in t)t.selectionStart=e,t.selectionEnd=Math.min(l,t.value.length);else if(l=(e=t.ownerDocument||document)&&e.defaultView||window,l.getSelection){l=l.getSelection();var n=t.textContent.length,u=Math.min(a.start,n);a=a.end===void 0?u:Math.min(a.end,n),!l.extend&&u>a&&(n=a,a=u,u=n),n=Ps(t,u);var i=Ps(t,a);n&&i&&(l.rangeCount!==1||l.anchorNode!==n.node||l.anchorOffset!==n.offset||l.focusNode!==i.node||l.focusOffset!==i.offset)&&(e=e.createRange(),e.setStart(n.node,n.offset),l.removeAllRanges(),u>a?(l.addRange(e),l.extend(i.node,i.offset)):(e.setEnd(i.node,i.offset),l.addRange(e)))}}for(e=[],l=t;l=l.parentNode;)l.nodeType===1&&e.push({element:l,left:l.scrollLeft,top:l.scrollTop});for(typeof t.focus=="function"&&t.focus(),t=0;t<e.length;t++)l=e[t],l.element.scrollLeft=l.left,l.element.scrollTop=l.top}}var km=St&&"documentMode"in document&&11>=document.documentMode,Il=null,pi=null,wa=null,yi=!1;function Is(e,t,l){var a=l.window===l?l.document:l.nodeType===9?l:l.ownerDocument;yi||Il==null||Il!==Xn(a)||(a=Il,"selectionStart"in a&&gi(a)?a={start:a.selectionStart,end:a.selectionEnd}:(a=(a.ownerDocument&&a.ownerDocument.defaultView||window).getSelection(),a={anchorNode:a.anchorNode,anchorOffset:a.anchorOffset,focusNode:a.focusNode,focusOffset:a.focusOffset}),wa&&Xa(wa,a)||(wa=a,a=Ou(pi,"onSelect"),0<a.length&&(t=new Zn("onSelect","select",null,t,l),e.push({event:t,listeners:a}),t.target=Il)))}function xl(e,t){var l={};return l[e.toLowerCase()]=t.toLowerCase(),l["Webkit"+e]="webkit"+t,l["Moz"+e]="moz"+t,l}var ea={animationend:xl("Animation","AnimationEnd"),animationiteration:xl("Animation","AnimationIteration"),animationstart:xl("Animation","AnimationStart"),transitionrun:xl("Transition","TransitionRun"),transitionstart:xl("Transition","TransitionStart"),transitioncancel:xl("Transition","TransitionCancel"),transitionend:xl("Transition","TransitionEnd")},vi={},eo={};St&&(eo=document.createElement("div").style,"AnimationEvent"in window||(delete ea.animationend.animation,delete ea.animationiteration.animation,delete ea.animationstart.animation),"TransitionEvent"in window||delete ea.transitionend.transition);function Al(e){if(vi[e])return vi[e];if(!ea[e])return e;var t=ea[e],l;for(l in t)if(t.hasOwnProperty(l)&&l in eo)return vi[e]=t[l];return e}var to=Al("animationend"),lo=Al("animationiteration"),ao=Al("animationstart"),Jm=Al("transitionrun"),Fm=Al("transitionstart"),Pm=Al("transitioncancel"),no=Al("transitionend"),uo=new Map,io="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll scrollEnd toggle touchMove waiting wheel".split(" ");function nt(e,t){uo.set(e,t),Sl(t,[e])}var ke=[],ta=0,bi=0;function Fn(){for(var e=ta,t=bi=ta=0;t<e;){var l=ke[t];ke[t++]=null;var a=ke[t];ke[t++]=null;var n=ke[t];ke[t++]=null;var u=ke[t];if(ke[t++]=null,a!==null&&n!==null){var i=a.pending;i===null?n.next=n:(n.next=i.next,i.next=n),a.pending=n}u!==0&&co(l,n,u)}}function Pn(e,t,l,a){ke[ta++]=e,ke[ta++]=t,ke[ta++]=l,ke[ta++]=a,bi|=a,e.lanes|=a,e=e.alternate,e!==null&&(e.lanes|=a)}function Si(e,t,l,a){return Pn(e,t,l,a),$n(e)}function Qt(e,t){return Pn(e,null,null,t),$n(e)}function co(e,t,l){e.lanes|=l;var a=e.alternate;a!==null&&(a.lanes|=l);for(var n=!1,u=e.return;u!==null;)u.childLanes|=l,a=u.alternate,a!==null&&(a.childLanes|=l),u.tag===22&&(e=u.stateNode,e===null||e._visibility&1||(n=!0)),e=u,u=u.return;n&&t!==null&&e.tag===3&&(u=e.stateNode,n=31-Le(l),u=u.hiddenUpdates,e=u[n],e===null?u[n]=[t]:e.push(t),t.lane=l|536870912)}function $n(e){if(50<hn)throw hn=0,Ec=null,Error(d(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var la={},so=new WeakMap;function Je(e,t){if(typeof e=="object"&&e!==null){var l=so.get(e);return l!==void 0?l:(t={value:e,source:t,stack:L(t)},so.set(e,t),t)}return{value:e,source:t,stack:L(t)}}var aa=[],na=0,Wn=null,In=0,Fe=[],Pe=0,Dl=null,xt=1,At="";function Nl(e,t){aa[na++]=In,aa[na++]=Wn,Wn=e,In=t}function oo(e,t,l){Fe[Pe++]=xt,Fe[Pe++]=At,Fe[Pe++]=Dl,Dl=e;var a=xt;e=At;var n=32-Le(a)-1;a&=~(1<<n),l+=1;var u=32-Le(t)+n;if(30<u){var i=n-n%5;u=(a&(1<<i)-1).toString(32),a>>=i,n-=i,xt=1<<32-Le(t)+n|l<<n|a,At=u+e}else xt=1<<u|l<<n|a,At=e}function Ti(e){e.return!==null&&(Nl(e,1),oo(e,1,0))}function xi(e){for(;e===Wn;)Wn=aa[--na],aa[na]=null,In=aa[--na],aa[na]=null;for(;e===Dl;)Dl=Fe[--Pe],Fe[Pe]=null,At=Fe[--Pe],Fe[Pe]=null,xt=Fe[--Pe],Fe[Pe]=null}var Ce=null,De=null,K=!1,ut=null,dt=!1,Ai=Error(d(519));function El(e){var t=Error(d(418,""));throw Ka(Je(t,e)),Ai}function ro(e){var t=e.stateNode,l=e.type,a=e.memoizedProps;switch(t[Me]=e,t[Re]=a,l){case"dialog":X("cancel",t),X("close",t);break;case"iframe":case"object":case"embed":X("load",t);break;case"video":case"audio":for(l=0;l<pn.length;l++)X(pn[l],t);break;case"source":X("error",t);break;case"img":case"image":case"link":X("error",t),X("load",t);break;case"details":X("toggle",t);break;case"input":X("invalid",t),Ns(t,a.value,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name,!0),Yn(t);break;case"select":X("invalid",t);break;case"textarea":X("invalid",t),Ms(t,a.value,a.defaultValue,a.children),Yn(t)}l=a.children,typeof l!="string"&&typeof l!="number"&&typeof l!="bigint"||t.textContent===""+l||a.suppressHydrationWarning===!0||Tf(t.textContent,l)?(a.popover!=null&&(X("beforetoggle",t),X("toggle",t)),a.onScroll!=null&&X("scroll",t),a.onScrollEnd!=null&&X("scrollend",t),a.onClick!=null&&(t.onclick=_u),t=!0):t=!1,t||El(e)}function fo(e){for(Ce=e.return;Ce;)switch(Ce.tag){case 3:case 27:dt=!0;return;case 5:case 13:dt=!1;return;default:Ce=Ce.return}}function Va(e){if(e!==Ce)return!1;if(!K)return fo(e),K=!0,!1;var t=!1,l;if((l=e.tag!==3&&e.tag!==27)&&((l=e.tag===5)&&(l=e.type,l=!(l!=="form"&&l!=="button")||Vc(e.type,e.memoizedProps)),l=!l),l&&(t=!0),t&&De&&El(e),fo(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(d(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(l=e.data,l==="/$"){if(t===0){De=ct(e.nextSibling);break e}t--}else l!=="$"&&l!=="$!"&&l!=="$?"||t++;e=e.nextSibling}De=null}}else De=Ce?ct(e.stateNode.nextSibling):null;return!0}function Qa(){De=Ce=null,K=!1}function Ka(e){ut===null?ut=[e]:ut.push(e)}var Za=Error(d(460)),mo=Error(d(474)),Di={then:function(){}};function ho(e){return e=e.status,e==="fulfilled"||e==="rejected"}function eu(){}function go(e,t,l){switch(l=e[l],l===void 0?e.push(t):l!==t&&(t.then(eu,eu),t=l),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,e===Za?Error(d(483)):e;default:if(typeof t.status=="string")t.then(eu,eu);else{if(e=I,e!==null&&100<e.shellSuspendCounter)throw Error(d(482));e=t,e.status="pending",e.then(function(a){if(t.status==="pending"){var n=t;n.status="fulfilled",n.value=a}},function(a){if(t.status==="pending"){var n=t;n.status="rejected",n.reason=a}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,e===Za?Error(d(483)):e}throw ka=t,Za}}var ka=null;function po(){if(ka===null)throw Error(d(459));var e=ka;return ka=null,e}var ua=null,Ja=0;function tu(e){var t=Ja;return Ja+=1,ua===null&&(ua=[]),go(ua,e,t)}function Fa(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function lu(e,t){throw t.$$typeof===_?Error(d(525)):(e=Object.prototype.toString.call(t),Error(d(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function yo(e){var t=e._init;return t(e._payload)}function vo(e){function t(h,r){if(e){var g=h.deletions;g===null?(h.deletions=[r],h.flags|=16):g.push(r)}}function l(h,r){if(!e)return null;for(;r!==null;)t(h,r),r=r.sibling;return null}function a(h){for(var r=new Map;h!==null;)h.key!==null?r.set(h.key,h):r.set(h.index,h),h=h.sibling;return r}function n(h,r){return h=ll(h,r),h.index=0,h.sibling=null,h}function u(h,r,g){return h.index=g,e?(g=h.alternate,g!==null?(g=g.index,g<r?(h.flags|=33554434,r):g):(h.flags|=33554434,r)):(h.flags|=1048576,r)}function i(h){return e&&h.alternate===null&&(h.flags|=33554434),h}function c(h,r,g,S){return r===null||r.tag!==6?(r=vc(g,h.mode,S),r.return=h,r):(r=n(r,g),r.return=h,r)}function s(h,r,g,S){var D=g.type;return D===Y?b(h,r,g.props.children,S,g.key):r!==null&&(r.elementType===D||typeof D=="object"&&D!==null&&D.$$typeof===_e&&yo(D)===r.type)?(r=n(r,g.props),Fa(r,g),r.return=h,r):(r=Su(g.type,g.key,g.props,null,h.mode,S),Fa(r,g),r.return=h,r)}function f(h,r,g,S){return r===null||r.tag!==4||r.stateNode.containerInfo!==g.containerInfo||r.stateNode.implementation!==g.implementation?(r=bc(g,h.mode,S),r.return=h,r):(r=n(r,g.children||[]),r.return=h,r)}function b(h,r,g,S,D){return r===null||r.tag!==7?(r=Ll(g,h.mode,S,D),r.return=h,r):(r=n(r,g),r.return=h,r)}function T(h,r,g){if(typeof r=="string"&&r!==""||typeof r=="number"||typeof r=="bigint")return r=vc(""+r,h.mode,g),r.return=h,r;if(typeof r=="object"&&r!==null){switch(r.$$typeof){case H:return g=Su(r.type,r.key,r.props,null,h.mode,g),Fa(g,r),g.return=h,g;case P:return r=bc(r,h.mode,g),r.return=h,r;case _e:var S=r._init;return r=S(r._payload),T(h,r,g)}if(bt(r)||at(r))return r=Ll(r,h.mode,g,null),r.return=h,r;if(typeof r.then=="function")return T(h,tu(r),g);if(r.$$typeof===pe)return T(h,yu(h,r),g);lu(h,r)}return null}function p(h,r,g,S){var D=r!==null?r.key:null;if(typeof g=="string"&&g!==""||typeof g=="number"||typeof g=="bigint")return D!==null?null:c(h,r,""+g,S);if(typeof g=="object"&&g!==null){switch(g.$$typeof){case H:return g.key===D?s(h,r,g,S):null;case P:return g.key===D?f(h,r,g,S):null;case _e:return D=g._init,g=D(g._payload),p(h,r,g,S)}if(bt(g)||at(g))return D!==null?null:b(h,r,g,S,null);if(typeof g.then=="function")return p(h,r,tu(g),S);if(g.$$typeof===pe)return p(h,r,yu(h,g),S);lu(h,g)}return null}function v(h,r,g,S,D){if(typeof S=="string"&&S!==""||typeof S=="number"||typeof S=="bigint")return h=h.get(g)||null,c(r,h,""+S,D);if(typeof S=="object"&&S!==null){switch(S.$$typeof){case H:return h=h.get(S.key===null?g:S.key)||null,s(r,h,S,D);case P:return h=h.get(S.key===null?g:S.key)||null,f(r,h,S,D);case _e:var B=S._init;return S=B(S._payload),v(h,r,g,S,D)}if(bt(S)||at(S))return h=h.get(g)||null,b(r,h,S,D,null);if(typeof S.then=="function")return v(h,r,g,tu(S),D);if(S.$$typeof===pe)return v(h,r,g,yu(r,S),D);lu(r,S)}return null}function M(h,r,g,S){for(var D=null,B=null,z=r,O=r=0,Ae=null;z!==null&&O<g.length;O++){z.index>O?(Ae=z,z=null):Ae=z.sibling;var Z=p(h,z,g[O],S);if(Z===null){z===null&&(z=Ae);break}e&&z&&Z.alternate===null&&t(h,z),r=u(Z,r,O),B===null?D=Z:B.sibling=Z,B=Z,z=Ae}if(O===g.length)return l(h,z),K&&Nl(h,O),D;if(z===null){for(;O<g.length;O++)z=T(h,g[O],S),z!==null&&(r=u(z,r,O),B===null?D=z:B.sibling=z,B=z);return K&&Nl(h,O),D}for(z=a(z);O<g.length;O++)Ae=v(z,h,O,g[O],S),Ae!==null&&(e&&Ae.alternate!==null&&z.delete(Ae.key===null?O:Ae.key),r=u(Ae,r,O),B===null?D=Ae:B.sibling=Ae,B=Ae);return e&&z.forEach(function(ol){return t(h,ol)}),K&&Nl(h,O),D}function U(h,r,g,S){if(g==null)throw Error(d(151));for(var D=null,B=null,z=r,O=r=0,Ae=null,Z=g.next();z!==null&&!Z.done;O++,Z=g.next()){z.index>O?(Ae=z,z=null):Ae=z.sibling;var ol=p(h,z,Z.value,S);if(ol===null){z===null&&(z=Ae);break}e&&z&&ol.alternate===null&&t(h,z),r=u(ol,r,O),B===null?D=ol:B.sibling=ol,B=ol,z=Ae}if(Z.done)return l(h,z),K&&Nl(h,O),D;if(z===null){for(;!Z.done;O++,Z=g.next())Z=T(h,Z.value,S),Z!==null&&(r=u(Z,r,O),B===null?D=Z:B.sibling=Z,B=Z);return K&&Nl(h,O),D}for(z=a(z);!Z.done;O++,Z=g.next())Z=v(z,h,O,Z.value,S),Z!==null&&(e&&Z.alternate!==null&&z.delete(Z.key===null?O:Z.key),r=u(Z,r,O),B===null?D=Z:B.sibling=Z,B=Z);return e&&z.forEach(function(rg){return t(h,rg)}),K&&Nl(h,O),D}function se(h,r,g,S){if(typeof g=="object"&&g!==null&&g.type===Y&&g.key===null&&(g=g.props.children),typeof g=="object"&&g!==null){switch(g.$$typeof){case H:e:{for(var D=g.key;r!==null;){if(r.key===D){if(D=g.type,D===Y){if(r.tag===7){l(h,r.sibling),S=n(r,g.props.children),S.return=h,h=S;break e}}else if(r.elementType===D||typeof D=="object"&&D!==null&&D.$$typeof===_e&&yo(D)===r.type){l(h,r.sibling),S=n(r,g.props),Fa(S,g),S.return=h,h=S;break e}l(h,r);break}else t(h,r);r=r.sibling}g.type===Y?(S=Ll(g.props.children,h.mode,S,g.key),S.return=h,h=S):(S=Su(g.type,g.key,g.props,null,h.mode,S),Fa(S,g),S.return=h,h=S)}return i(h);case P:e:{for(D=g.key;r!==null;){if(r.key===D)if(r.tag===4&&r.stateNode.containerInfo===g.containerInfo&&r.stateNode.implementation===g.implementation){l(h,r.sibling),S=n(r,g.children||[]),S.return=h,h=S;break e}else{l(h,r);break}else t(h,r);r=r.sibling}S=bc(g,h.mode,S),S.return=h,h=S}return i(h);case _e:return D=g._init,g=D(g._payload),se(h,r,g,S)}if(bt(g))return M(h,r,g,S);if(at(g)){if(D=at(g),typeof D!="function")throw Error(d(150));return g=D.call(g),U(h,r,g,S)}if(typeof g.then=="function")return se(h,r,tu(g),S);if(g.$$typeof===pe)return se(h,r,yu(h,g),S);lu(h,g)}return typeof g=="string"&&g!==""||typeof g=="number"||typeof g=="bigint"?(g=""+g,r!==null&&r.tag===6?(l(h,r.sibling),S=n(r,g),S.return=h,h=S):(l(h,r),S=vc(g,h.mode,S),S.return=h,h=S),i(h)):l(h,r)}return function(h,r,g,S){try{Ja=0;var D=se(h,r,g,S);return ua=null,D}catch(z){if(z===Za)throw z;var B=et(29,z,null,h.mode);return B.lanes=S,B.return=h,B}finally{}}}var Ml=vo(!0),bo=vo(!1),ia=ot(null),au=ot(0);function So(e,t){e=Ut,le(au,e),le(ia,t),Ut=e|t.baseLanes}function Ni(){le(au,Ut),le(ia,ia.current)}function Ei(){Ut=au.current,be(ia),be(au)}var $e=ot(null),mt=null;function Kt(e){var t=e.alternate;le(ye,ye.current&1),le($e,e),mt===null&&(t===null||ia.current!==null||t.memoizedState!==null)&&(mt=e)}function To(e){if(e.tag===22){if(le(ye,ye.current),le($e,e),mt===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(mt=e)}}else Zt()}function Zt(){le(ye,ye.current),le($e,$e.current)}function Dt(e){be($e),mt===e&&(mt=null),be(ye)}var ye=ot(0);function nu(e){for(var t=e;t!==null;){if(t.tag===13){var l=t.memoizedState;if(l!==null&&(l=l.dehydrated,l===null||l.data==="$?"||l.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var $m=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(l,a){e.push(a)}};this.abort=function(){t.aborted=!0,e.forEach(function(l){return l()})}},Wm=m.unstable_scheduleCallback,Im=m.unstable_NormalPriority,ve={$$typeof:pe,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Mi(){return{controller:new $m,data:new Map,refCount:0}}function Pa(e){e.refCount--,e.refCount===0&&Wm(Im,function(){e.controller.abort()})}var $a=null,zi=0,ca=0,sa=null;function eh(e,t){if($a===null){var l=$a=[];zi=0,ca=Uc(),sa={status:"pending",value:void 0,then:function(a){l.push(a)}}}return zi++,t.then(xo,xo),t}function xo(){if(--zi===0&&$a!==null){sa!==null&&(sa.status="fulfilled");var e=$a;$a=null,ca=0,sa=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function th(e,t){var l=[],a={status:"pending",value:null,reason:null,then:function(n){l.push(n)}};return e.then(function(){a.status="fulfilled",a.value=t;for(var n=0;n<l.length;n++)(0,l[n])(t)},function(n){for(a.status="rejected",a.reason=n,n=0;n<l.length;n++)(0,l[n])(void 0)}),a}var Ao=R.S;R.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&eh(e,t),Ao!==null&&Ao(e,t)};var zl=ot(null);function Ci(){var e=zl.current;return e!==null?e:I.pooledCache}function uu(e,t){t===null?le(zl,zl.current):le(zl,t.pool)}function Do(){var e=Ci();return e===null?null:{parent:ve._currentValue,pool:e}}var kt=0,q=null,J=null,fe=null,iu=!1,oa=!1,Cl=!1,cu=0,Wa=0,ra=null,lh=0;function re(){throw Error(d(321))}function Oi(e,t){if(t===null)return!1;for(var l=0;l<t.length&&l<e.length;l++)if(!qe(e[l],t[l]))return!1;return!0}function _i(e,t,l,a,n,u){return kt=u,q=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,R.H=e===null||e.memoizedState===null?Ol:Jt,Cl=!1,u=l(a,n),Cl=!1,oa&&(u=Eo(t,l,a,n)),No(e),u}function No(e){R.H=ht;var t=J!==null&&J.next!==null;if(kt=0,fe=J=q=null,iu=!1,Wa=0,ra=null,t)throw Error(d(300));e===null||Te||(e=e.dependencies,e!==null&&pu(e)&&(Te=!0))}function Eo(e,t,l,a){q=e;var n=0;do{if(oa&&(ra=null),Wa=0,oa=!1,25<=n)throw Error(d(301));if(n+=1,fe=J=null,e.updateQueue!=null){var u=e.updateQueue;u.lastEffect=null,u.events=null,u.stores=null,u.memoCache!=null&&(u.memoCache.index=0)}R.H=_l,u=t(l,a)}while(oa);return u}function ah(){var e=R.H,t=e.useState()[0];return t=typeof t.then=="function"?Ia(t):t,e=e.useState()[0],(J!==null?J.memoizedState:null)!==e&&(q.flags|=1024),t}function Ri(){var e=cu!==0;return cu=0,e}function ji(e,t,l){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l}function Ui(e){if(iu){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}iu=!1}kt=0,fe=J=q=null,oa=!1,Wa=cu=0,ra=null}function Ue(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return fe===null?q.memoizedState=fe=e:fe=fe.next=e,fe}function de(){if(J===null){var e=q.alternate;e=e!==null?e.memoizedState:null}else e=J.next;var t=fe===null?q.memoizedState:fe.next;if(t!==null)fe=t,J=e;else{if(e===null)throw q.alternate===null?Error(d(467)):Error(d(310));J=e,e={memoizedState:J.memoizedState,baseState:J.baseState,baseQueue:J.baseQueue,queue:J.queue,next:null},fe===null?q.memoizedState=fe=e:fe=fe.next=e}return fe}var su;su=function(){return{lastEffect:null,events:null,stores:null,memoCache:null}};function Ia(e){var t=Wa;return Wa+=1,ra===null&&(ra=[]),e=go(ra,e,t),t=q,(fe===null?t.memoizedState:fe.next)===null&&(t=t.alternate,R.H=t===null||t.memoizedState===null?Ol:Jt),e}function ou(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return Ia(e);if(e.$$typeof===pe)return ze(e)}throw Error(d(438,String(e)))}function Hi(e){var t=null,l=q.updateQueue;if(l!==null&&(t=l.memoCache),t==null){var a=q.alternate;a!==null&&(a=a.updateQueue,a!==null&&(a=a.memoCache,a!=null&&(t={data:a.data.map(function(n){return n.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),l===null&&(l=su(),q.updateQueue=l),l.memoCache=t,l=t.data[t.index],l===void 0)for(l=t.data[t.index]=Array(e),a=0;a<e;a++)l[a]=Ea;return t.index++,l}function Nt(e,t){return typeof t=="function"?t(e):t}function ru(e){var t=de();return Li(t,J,e)}function Li(e,t,l){var a=e.queue;if(a===null)throw Error(d(311));a.lastRenderedReducer=l;var n=e.baseQueue,u=a.pending;if(u!==null){if(n!==null){var i=n.next;n.next=u.next,u.next=i}t.baseQueue=n=u,a.pending=null}if(u=e.baseState,n===null)e.memoizedState=u;else{t=n.next;var c=i=null,s=null,f=t,b=!1;do{var T=f.lane&-536870913;if(T!==f.lane?(w&T)===T:(kt&T)===T){var p=f.revertLane;if(p===0)s!==null&&(s=s.next={lane:0,revertLane:0,action:f.action,hasEagerState:f.hasEagerState,eagerState:f.eagerState,next:null}),T===ca&&(b=!0);else if((kt&p)===p){f=f.next,p===ca&&(b=!0);continue}else T={lane:0,revertLane:f.revertLane,action:f.action,hasEagerState:f.hasEagerState,eagerState:f.eagerState,next:null},s===null?(c=s=T,i=u):s=s.next=T,q.lanes|=p,al|=p;T=f.action,Cl&&l(u,T),u=f.hasEagerState?f.eagerState:l(u,T)}else p={lane:T,revertLane:f.revertLane,action:f.action,hasEagerState:f.hasEagerState,eagerState:f.eagerState,next:null},s===null?(c=s=p,i=u):s=s.next=p,q.lanes|=T,al|=T;f=f.next}while(f!==null&&f!==t);if(s===null?i=u:s.next=c,!qe(u,e.memoizedState)&&(Te=!0,b&&(l=sa,l!==null)))throw l;e.memoizedState=u,e.baseState=i,e.baseQueue=s,a.lastRenderedState=u}return n===null&&(a.lanes=0),[e.memoizedState,a.dispatch]}function qi(e){var t=de(),l=t.queue;if(l===null)throw Error(d(311));l.lastRenderedReducer=e;var a=l.dispatch,n=l.pending,u=t.memoizedState;if(n!==null){l.pending=null;var i=n=n.next;do u=e(u,i.action),i=i.next;while(i!==n);qe(u,t.memoizedState)||(Te=!0),t.memoizedState=u,t.baseQueue===null&&(t.baseState=u),l.lastRenderedState=u}return[u,a]}function Mo(e,t,l){var a=q,n=de(),u=K;if(u){if(l===void 0)throw Error(d(407));l=l()}else l=t();var i=!qe((J||n).memoizedState,l);if(i&&(n.memoizedState=l,Te=!0),n=n.queue,Yi(Oo.bind(null,a,n,e),[e]),n.getSnapshot!==t||i||fe!==null&&fe.memoizedState.tag&1){if(a.flags|=2048,fa(9,Co.bind(null,a,n,l,t),{destroy:void 0},null),I===null)throw Error(d(349));u||kt&60||zo(a,t,l)}return l}function zo(e,t,l){e.flags|=16384,e={getSnapshot:t,value:l},t=q.updateQueue,t===null?(t=su(),q.updateQueue=t,t.stores=[e]):(l=t.stores,l===null?t.stores=[e]:l.push(e))}function Co(e,t,l,a){t.value=l,t.getSnapshot=a,_o(t)&&Ro(e)}function Oo(e,t,l){return l(function(){_o(t)&&Ro(e)})}function _o(e){var t=e.getSnapshot;e=e.value;try{var l=t();return!qe(e,l)}catch{return!0}}function Ro(e){var t=Qt(e,2);t!==null&&Oe(t,e,2)}function Bi(e){var t=Ue();if(typeof e=="function"){var l=e;if(e=l(),Cl){Xt(!0);try{l()}finally{Xt(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Nt,lastRenderedState:e},t}function jo(e,t,l,a){return e.baseState=l,Li(e,J,typeof a=="function"?a:Nt)}function nh(e,t,l,a,n){if(mu(e))throw Error(d(485));if(e=t.action,e!==null){var u={payload:n,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(i){u.listeners.push(i)}};R.T!==null?l(!0):u.isTransition=!1,a(u),l=t.pending,l===null?(u.next=t.pending=u,Uo(t,u)):(u.next=l.next,t.pending=l.next=u)}}function Uo(e,t){var l=t.action,a=t.payload,n=e.state;if(t.isTransition){var u=R.T,i={};R.T=i;try{var c=l(n,a),s=R.S;s!==null&&s(i,c),Ho(e,t,c)}catch(f){Gi(e,t,f)}finally{R.T=u}}else try{u=l(n,a),Ho(e,t,u)}catch(f){Gi(e,t,f)}}function Ho(e,t,l){l!==null&&typeof l=="object"&&typeof l.then=="function"?l.then(function(a){Lo(e,t,a)},function(a){return Gi(e,t,a)}):Lo(e,t,l)}function Lo(e,t,l){t.status="fulfilled",t.value=l,qo(t),e.state=l,t=e.pending,t!==null&&(l=t.next,l===t?e.pending=null:(l=l.next,t.next=l,Uo(e,l)))}function Gi(e,t,l){var a=e.pending;if(e.pending=null,a!==null){a=a.next;do t.status="rejected",t.reason=l,qo(t),t=t.next;while(t!==a)}e.action=null}function qo(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function Bo(e,t){return t}function Go(e,t){if(K){var l=I.formState;if(l!==null){e:{var a=q;if(K){if(De){t:{for(var n=De,u=dt;n.nodeType!==8;){if(!u){n=null;break t}if(n=ct(n.nextSibling),n===null){n=null;break t}}u=n.data,n=u==="F!"||u==="F"?n:null}if(n){De=ct(n.nextSibling),a=n.data==="F!";break e}}El(a)}a=!1}a&&(t=l[0])}}return l=Ue(),l.memoizedState=l.baseState=t,a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Bo,lastRenderedState:t},l.queue=a,l=ar.bind(null,q,a),a.dispatch=l,a=Bi(!1),u=Ki.bind(null,q,!1,a.queue),a=Ue(),n={state:t,dispatch:null,action:e,pending:null},a.queue=n,l=nh.bind(null,q,n,u,l),n.dispatch=l,a.memoizedState=e,[t,l,!1]}function Yo(e){var t=de();return Xo(t,J,e)}function Xo(e,t,l){t=Li(e,t,Bo)[0],e=ru(Nt)[0],t=typeof t=="object"&&t!==null&&typeof t.then=="function"?Ia(t):t;var a=de(),n=a.queue,u=n.dispatch;return l!==a.memoizedState&&(q.flags|=2048,fa(9,uh.bind(null,n,l),{destroy:void 0},null)),[t,u,e]}function uh(e,t){e.action=t}function wo(e){var t=de(),l=J;if(l!==null)return Xo(t,l,e);de(),t=t.memoizedState,l=de();var a=l.queue.dispatch;return l.memoizedState=e,[t,a,!1]}function fa(e,t,l,a){return e={tag:e,create:t,inst:l,deps:a,next:null},t=q.updateQueue,t===null&&(t=su(),q.updateQueue=t),l=t.lastEffect,l===null?t.lastEffect=e.next=e:(a=l.next,l.next=e,e.next=a,t.lastEffect=e),e}function Vo(){return de().memoizedState}function fu(e,t,l,a){var n=Ue();q.flags|=e,n.memoizedState=fa(1|t,l,{destroy:void 0},a===void 0?null:a)}function du(e,t,l,a){var n=de();a=a===void 0?null:a;var u=n.memoizedState.inst;J!==null&&a!==null&&Oi(a,J.memoizedState.deps)?n.memoizedState=fa(t,l,u,a):(q.flags|=e,n.memoizedState=fa(1|t,l,u,a))}function Qo(e,t){fu(8390656,8,e,t)}function Yi(e,t){du(2048,8,e,t)}function Ko(e,t){return du(4,2,e,t)}function Zo(e,t){return du(4,4,e,t)}function ko(e,t){if(typeof t=="function"){e=e();var l=t(e);return function(){typeof l=="function"?l():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Jo(e,t,l){l=l!=null?l.concat([e]):null,du(4,4,ko.bind(null,t,e),l)}function Xi(){}function Fo(e,t){var l=de();t=t===void 0?null:t;var a=l.memoizedState;return t!==null&&Oi(t,a[1])?a[0]:(l.memoizedState=[e,t],e)}function Po(e,t){var l=de();t=t===void 0?null:t;var a=l.memoizedState;if(t!==null&&Oi(t,a[1]))return a[0];if(a=e(),Cl){Xt(!0);try{e()}finally{Xt(!1)}}return l.memoizedState=[a,t],a}function wi(e,t,l){return l===void 0||kt&1073741824?e.memoizedState=t:(e.memoizedState=l,e=Wr(),q.lanes|=e,al|=e,l)}function $o(e,t,l,a){return qe(l,t)?l:ia.current!==null?(e=wi(e,l,a),qe(e,t)||(Te=!0),e):kt&42?(e=Wr(),q.lanes|=e,al|=e,t):(Te=!0,e.memoizedState=l)}function Wo(e,t,l,a,n){var u=Q.p;Q.p=u!==0&&8>u?u:8;var i=R.T,c={};R.T=c,Ki(e,!1,t,l);try{var s=n(),f=R.S;if(f!==null&&f(c,s),s!==null&&typeof s=="object"&&typeof s.then=="function"){var b=th(s,a);en(e,t,b,Xe(e))}else en(e,t,a,Xe(e))}catch(T){en(e,t,{then:function(){},status:"rejected",reason:T},Xe())}finally{Q.p=u,R.T=i}}function ih(){}function Vi(e,t,l,a){if(e.tag!==5)throw Error(d(476));var n=Io(e).queue;Wo(e,n,t,Qe,l===null?ih:function(){return er(e),l(a)})}function Io(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:Qe,baseState:Qe,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Nt,lastRenderedState:Qe},next:null};var l={};return t.next={memoizedState:l,baseState:l,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Nt,lastRenderedState:l},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function er(e){var t=Io(e).next.queue;en(e,t,{},Xe())}function Qi(){return ze(Tn)}function tr(){return de().memoizedState}function lr(){return de().memoizedState}function ch(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var l=Xe();e=$t(l);var a=Wt(t,e,l);a!==null&&(Oe(a,t,l),an(a,t,l)),t={cache:Mi()},e.payload=t;return}t=t.return}}function sh(e,t,l){var a=Xe();l={lane:a,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null},mu(e)?nr(t,l):(l=Si(e,t,l,a),l!==null&&(Oe(l,e,a),ur(l,t,a)))}function ar(e,t,l){var a=Xe();en(e,t,l,a)}function en(e,t,l,a){var n={lane:a,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null};if(mu(e))nr(t,n);else{var u=e.alternate;if(e.lanes===0&&(u===null||u.lanes===0)&&(u=t.lastRenderedReducer,u!==null))try{var i=t.lastRenderedState,c=u(i,l);if(n.hasEagerState=!0,n.eagerState=c,qe(c,i))return Pn(e,t,n,0),I===null&&Fn(),!1}catch{}finally{}if(l=Si(e,t,n,a),l!==null)return Oe(l,e,a),ur(l,t,a),!0}return!1}function Ki(e,t,l,a){if(a={lane:2,revertLane:Uc(),action:a,hasEagerState:!1,eagerState:null,next:null},mu(e)){if(t)throw Error(d(479))}else t=Si(e,l,a,2),t!==null&&Oe(t,e,2)}function mu(e){var t=e.alternate;return e===q||t!==null&&t===q}function nr(e,t){oa=iu=!0;var l=e.pending;l===null?t.next=t:(t.next=l.next,l.next=t),e.pending=t}function ur(e,t,l){if(l&4194176){var a=t.lanes;a&=e.pendingLanes,l|=a,t.lanes=l,gs(e,l)}}var ht={readContext:ze,use:ou,useCallback:re,useContext:re,useEffect:re,useImperativeHandle:re,useLayoutEffect:re,useInsertionEffect:re,useMemo:re,useReducer:re,useRef:re,useState:re,useDebugValue:re,useDeferredValue:re,useTransition:re,useSyncExternalStore:re,useId:re};ht.useCacheRefresh=re,ht.useMemoCache=re,ht.useHostTransitionStatus=re,ht.useFormState=re,ht.useActionState=re,ht.useOptimistic=re;var Ol={readContext:ze,use:ou,useCallback:function(e,t){return Ue().memoizedState=[e,t===void 0?null:t],e},useContext:ze,useEffect:Qo,useImperativeHandle:function(e,t,l){l=l!=null?l.concat([e]):null,fu(4194308,4,ko.bind(null,t,e),l)},useLayoutEffect:function(e,t){return fu(4194308,4,e,t)},useInsertionEffect:function(e,t){fu(4,2,e,t)},useMemo:function(e,t){var l=Ue();t=t===void 0?null:t;var a=e();if(Cl){Xt(!0);try{e()}finally{Xt(!1)}}return l.memoizedState=[a,t],a},useReducer:function(e,t,l){var a=Ue();if(l!==void 0){var n=l(t);if(Cl){Xt(!0);try{l(t)}finally{Xt(!1)}}}else n=t;return a.memoizedState=a.baseState=n,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:n},a.queue=e,e=e.dispatch=sh.bind(null,q,e),[a.memoizedState,e]},useRef:function(e){var t=Ue();return e={current:e},t.memoizedState=e},useState:function(e){e=Bi(e);var t=e.queue,l=ar.bind(null,q,t);return t.dispatch=l,[e.memoizedState,l]},useDebugValue:Xi,useDeferredValue:function(e,t){var l=Ue();return wi(l,e,t)},useTransition:function(){var e=Bi(!1);return e=Wo.bind(null,q,e.queue,!0,!1),Ue().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,l){var a=q,n=Ue();if(K){if(l===void 0)throw Error(d(407));l=l()}else{if(l=t(),I===null)throw Error(d(349));w&60||zo(a,t,l)}n.memoizedState=l;var u={value:l,getSnapshot:t};return n.queue=u,Qo(Oo.bind(null,a,u,e),[e]),a.flags|=2048,fa(9,Co.bind(null,a,u,l,t),{destroy:void 0},null),l},useId:function(){var e=Ue(),t=I.identifierPrefix;if(K){var l=At,a=xt;l=(a&~(1<<32-Le(a)-1)).toString(32)+l,t=":"+t+"R"+l,l=cu++,0<l&&(t+="H"+l.toString(32)),t+=":"}else l=lh++,t=":"+t+"r"+l.toString(32)+":";return e.memoizedState=t},useCacheRefresh:function(){return Ue().memoizedState=ch.bind(null,q)}};Ol.useMemoCache=Hi,Ol.useHostTransitionStatus=Qi,Ol.useFormState=Go,Ol.useActionState=Go,Ol.useOptimistic=function(e){var t=Ue();t.memoizedState=t.baseState=e;var l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=l,t=Ki.bind(null,q,!0,l),l.dispatch=t,[e,t]};var Jt={readContext:ze,use:ou,useCallback:Fo,useContext:ze,useEffect:Yi,useImperativeHandle:Jo,useInsertionEffect:Ko,useLayoutEffect:Zo,useMemo:Po,useReducer:ru,useRef:Vo,useState:function(){return ru(Nt)},useDebugValue:Xi,useDeferredValue:function(e,t){var l=de();return $o(l,J.memoizedState,e,t)},useTransition:function(){var e=ru(Nt)[0],t=de().memoizedState;return[typeof e=="boolean"?e:Ia(e),t]},useSyncExternalStore:Mo,useId:tr};Jt.useCacheRefresh=lr,Jt.useMemoCache=Hi,Jt.useHostTransitionStatus=Qi,Jt.useFormState=Yo,Jt.useActionState=Yo,Jt.useOptimistic=function(e,t){var l=de();return jo(l,J,e,t)};var _l={readContext:ze,use:ou,useCallback:Fo,useContext:ze,useEffect:Yi,useImperativeHandle:Jo,useInsertionEffect:Ko,useLayoutEffect:Zo,useMemo:Po,useReducer:qi,useRef:Vo,useState:function(){return qi(Nt)},useDebugValue:Xi,useDeferredValue:function(e,t){var l=de();return J===null?wi(l,e,t):$o(l,J.memoizedState,e,t)},useTransition:function(){var e=qi(Nt)[0],t=de().memoizedState;return[typeof e=="boolean"?e:Ia(e),t]},useSyncExternalStore:Mo,useId:tr};_l.useCacheRefresh=lr,_l.useMemoCache=Hi,_l.useHostTransitionStatus=Qi,_l.useFormState=wo,_l.useActionState=wo,_l.useOptimistic=function(e,t){var l=de();return J!==null?jo(l,J,e,t):(l.baseState=e,[e,l.queue.dispatch])};function Zi(e,t,l,a){t=e.memoizedState,l=l(a,t),l=l==null?t:k({},t,l),e.memoizedState=l,e.lanes===0&&(e.updateQueue.baseState=l)}var ki={isMounted:function(e){return(e=e._reactInternals)?j(e)===e:!1},enqueueSetState:function(e,t,l){e=e._reactInternals;var a=Xe(),n=$t(a);n.payload=t,l!=null&&(n.callback=l),t=Wt(e,n,a),t!==null&&(Oe(t,e,a),an(t,e,a))},enqueueReplaceState:function(e,t,l){e=e._reactInternals;var a=Xe(),n=$t(a);n.tag=1,n.payload=t,l!=null&&(n.callback=l),t=Wt(e,n,a),t!==null&&(Oe(t,e,a),an(t,e,a))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var l=Xe(),a=$t(l);a.tag=2,t!=null&&(a.callback=t),t=Wt(e,a,l),t!==null&&(Oe(t,e,l),an(t,e,l))}};function ir(e,t,l,a,n,u,i){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(a,u,i):t.prototype&&t.prototype.isPureReactComponent?!Xa(l,a)||!Xa(n,u):!0}function cr(e,t,l,a){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(l,a),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(l,a),t.state!==e&&ki.enqueueReplaceState(t,t.state,null)}function Rl(e,t){var l=t;if("ref"in t){l={};for(var a in t)a!=="ref"&&(l[a]=t[a])}if(e=e.defaultProps){l===t&&(l=k({},l));for(var n in e)l[n]===void 0&&(l[n]=e[n])}return l}var hu=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function sr(e){hu(e)}function or(e){console.error(e)}function rr(e){hu(e)}function gu(e,t){try{var l=e.onUncaughtError;l(t.value,{componentStack:t.stack})}catch(a){setTimeout(function(){throw a})}}function fr(e,t,l){try{var a=e.onCaughtError;a(l.value,{componentStack:l.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(n){setTimeout(function(){throw n})}}function Ji(e,t,l){return l=$t(l),l.tag=3,l.payload={element:null},l.callback=function(){gu(e,t)},l}function dr(e){return e=$t(e),e.tag=3,e}function mr(e,t,l,a){var n=l.type.getDerivedStateFromError;if(typeof n=="function"){var u=a.value;e.payload=function(){return n(u)},e.callback=function(){fr(t,l,a)}}var i=l.stateNode;i!==null&&typeof i.componentDidCatch=="function"&&(e.callback=function(){fr(t,l,a),typeof n!="function"&&(nl===null?nl=new Set([this]):nl.add(this));var c=a.stack;this.componentDidCatch(a.value,{componentStack:c!==null?c:""})})}function oh(e,t,l,a,n){if(l.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){if(t=l.alternate,t!==null&&ln(t,l,n,!0),l=$e.current,l!==null){switch(l.tag){case 13:return mt===null?Cc():l.alternate===null&&ce===0&&(ce=3),l.flags&=-257,l.flags|=65536,l.lanes=n,a===Di?l.flags|=16384:(t=l.updateQueue,t===null?l.updateQueue=new Set([a]):t.add(a),_c(e,a,n)),!1;case 22:return l.flags|=65536,a===Di?l.flags|=16384:(t=l.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([a])},l.updateQueue=t):(l=t.retryQueue,l===null?t.retryQueue=new Set([a]):l.add(a)),_c(e,a,n)),!1}throw Error(d(435,l.tag))}return _c(e,a,n),Cc(),!1}if(K)return t=$e.current,t!==null?(!(t.flags&65536)&&(t.flags|=256),t.flags|=65536,t.lanes=n,a!==Ai&&(e=Error(d(422),{cause:a}),Ka(Je(e,l)))):(a!==Ai&&(t=Error(d(423),{cause:a}),Ka(Je(t,l))),e=e.current.alternate,e.flags|=65536,n&=-n,e.lanes|=n,a=Je(a,l),n=Ji(e.stateNode,a,n),oc(e,n),ce!==4&&(ce=2)),!1;var u=Error(d(520),{cause:a});if(u=Je(u,l),dn===null?dn=[u]:dn.push(u),ce!==4&&(ce=2),t===null)return!0;a=Je(a,l),l=t;do{switch(l.tag){case 3:return l.flags|=65536,e=n&-n,l.lanes|=e,e=Ji(l.stateNode,a,e),oc(l,e),!1;case 1:if(t=l.type,u=l.stateNode,(l.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||u!==null&&typeof u.componentDidCatch=="function"&&(nl===null||!nl.has(u))))return l.flags|=65536,n&=-n,l.lanes|=n,n=dr(n),mr(n,e,l,a),oc(l,n),!1}l=l.return}while(l!==null);return!1}var hr=Error(d(461)),Te=!1;function Ne(e,t,l,a){t.child=e===null?bo(t,null,l,a):Ml(t,e.child,l,a)}function gr(e,t,l,a,n){l=l.render;var u=t.ref;if("ref"in a){var i={};for(var c in a)c!=="ref"&&(i[c]=a[c])}else i=a;return Ul(t),a=_i(e,t,l,i,u,n),c=Ri(),e!==null&&!Te?(ji(e,t,n),Et(e,t,n)):(K&&c&&Ti(t),t.flags|=1,Ne(e,t,a,n),t.child)}function pr(e,t,l,a,n){if(e===null){var u=l.type;return typeof u=="function"&&!yc(u)&&u.defaultProps===void 0&&l.compare===null?(t.tag=15,t.type=u,yr(e,t,u,a,n)):(e=Su(l.type,null,a,t,t.mode,n),e.ref=t.ref,e.return=t,t.child=e)}if(u=e.child,!ac(e,n)){var i=u.memoizedProps;if(l=l.compare,l=l!==null?l:Xa,l(i,a)&&e.ref===t.ref)return Et(e,t,n)}return t.flags|=1,e=ll(u,a),e.ref=t.ref,e.return=t,t.child=e}function yr(e,t,l,a,n){if(e!==null){var u=e.memoizedProps;if(Xa(u,a)&&e.ref===t.ref)if(Te=!1,t.pendingProps=a=u,ac(e,n))e.flags&131072&&(Te=!0);else return t.lanes=e.lanes,Et(e,t,n)}return Fi(e,t,l,a,n)}function vr(e,t,l){var a=t.pendingProps,n=a.children,u=(t.stateNode._pendingVisibility&2)!==0,i=e!==null?e.memoizedState:null;if(tn(e,t),a.mode==="hidden"||u){if(t.flags&128){if(a=i!==null?i.baseLanes|l:l,e!==null){for(n=t.child=e.child,u=0;n!==null;)u=u|n.lanes|n.childLanes,n=n.sibling;t.childLanes=u&~a}else t.childLanes=0,t.child=null;return br(e,t,a,l)}if(l&536870912)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&uu(t,i!==null?i.cachePool:null),i!==null?So(t,i):Ni(),To(t);else return t.lanes=t.childLanes=536870912,br(e,t,i!==null?i.baseLanes|l:l,l)}else i!==null?(uu(t,i.cachePool),So(t,i),Zt(),t.memoizedState=null):(e!==null&&uu(t,null),Ni(),Zt());return Ne(e,t,n,l),t.child}function br(e,t,l,a){var n=Ci();return n=n===null?null:{parent:ve._currentValue,pool:n},t.memoizedState={baseLanes:l,cachePool:n},e!==null&&uu(t,null),Ni(),To(t),e!==null&&ln(e,t,a,!0),null}function tn(e,t){var l=t.ref;if(l===null)e!==null&&e.ref!==null&&(t.flags|=2097664);else{if(typeof l!="function"&&typeof l!="object")throw Error(d(284));(e===null||e.ref!==l)&&(t.flags|=2097664)}}function Fi(e,t,l,a,n){return Ul(t),l=_i(e,t,l,a,void 0,n),a=Ri(),e!==null&&!Te?(ji(e,t,n),Et(e,t,n)):(K&&a&&Ti(t),t.flags|=1,Ne(e,t,l,n),t.child)}function Sr(e,t,l,a,n,u){return Ul(t),t.updateQueue=null,l=Eo(t,a,l,n),No(e),a=Ri(),e!==null&&!Te?(ji(e,t,u),Et(e,t,u)):(K&&a&&Ti(t),t.flags|=1,Ne(e,t,l,u),t.child)}function Tr(e,t,l,a,n){if(Ul(t),t.stateNode===null){var u=la,i=l.contextType;typeof i=="object"&&i!==null&&(u=ze(i)),u=new l(a,u),t.memoizedState=u.state!==null&&u.state!==void 0?u.state:null,u.updater=ki,t.stateNode=u,u._reactInternals=t,u=t.stateNode,u.props=a,u.state=t.memoizedState,u.refs={},cc(t),i=l.contextType,u.context=typeof i=="object"&&i!==null?ze(i):la,u.state=t.memoizedState,i=l.getDerivedStateFromProps,typeof i=="function"&&(Zi(t,l,i,a),u.state=t.memoizedState),typeof l.getDerivedStateFromProps=="function"||typeof u.getSnapshotBeforeUpdate=="function"||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(i=u.state,typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount(),i!==u.state&&ki.enqueueReplaceState(u,u.state,null),un(t,a,u,n),nn(),u.state=t.memoizedState),typeof u.componentDidMount=="function"&&(t.flags|=4194308),a=!0}else if(e===null){u=t.stateNode;var c=t.memoizedProps,s=Rl(l,c);u.props=s;var f=u.context,b=l.contextType;i=la,typeof b=="object"&&b!==null&&(i=ze(b));var T=l.getDerivedStateFromProps;b=typeof T=="function"||typeof u.getSnapshotBeforeUpdate=="function",c=t.pendingProps!==c,b||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(c||f!==i)&&cr(t,u,a,i),Pt=!1;var p=t.memoizedState;u.state=p,un(t,a,u,n),nn(),f=t.memoizedState,c||p!==f||Pt?(typeof T=="function"&&(Zi(t,l,T,a),f=t.memoizedState),(s=Pt||ir(t,l,s,a,p,f,i))?(b||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount()),typeof u.componentDidMount=="function"&&(t.flags|=4194308)):(typeof u.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=a,t.memoizedState=f),u.props=a,u.state=f,u.context=i,a=s):(typeof u.componentDidMount=="function"&&(t.flags|=4194308),a=!1)}else{u=t.stateNode,sc(e,t),i=t.memoizedProps,b=Rl(l,i),u.props=b,T=t.pendingProps,p=u.context,f=l.contextType,s=la,typeof f=="object"&&f!==null&&(s=ze(f)),c=l.getDerivedStateFromProps,(f=typeof c=="function"||typeof u.getSnapshotBeforeUpdate=="function")||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(i!==T||p!==s)&&cr(t,u,a,s),Pt=!1,p=t.memoizedState,u.state=p,un(t,a,u,n),nn();var v=t.memoizedState;i!==T||p!==v||Pt||e!==null&&e.dependencies!==null&&pu(e.dependencies)?(typeof c=="function"&&(Zi(t,l,c,a),v=t.memoizedState),(b=Pt||ir(t,l,b,a,p,v,s)||e!==null&&e.dependencies!==null&&pu(e.dependencies))?(f||typeof u.UNSAFE_componentWillUpdate!="function"&&typeof u.componentWillUpdate!="function"||(typeof u.componentWillUpdate=="function"&&u.componentWillUpdate(a,v,s),typeof u.UNSAFE_componentWillUpdate=="function"&&u.UNSAFE_componentWillUpdate(a,v,s)),typeof u.componentDidUpdate=="function"&&(t.flags|=4),typeof u.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof u.componentDidUpdate!="function"||i===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||i===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),t.memoizedProps=a,t.memoizedState=v),u.props=a,u.state=v,u.context=s,a=b):(typeof u.componentDidUpdate!="function"||i===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||i===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),a=!1)}return u=a,tn(e,t),a=(t.flags&128)!==0,u||a?(u=t.stateNode,l=a&&typeof l.getDerivedStateFromError!="function"?null:u.render(),t.flags|=1,e!==null&&a?(t.child=Ml(t,e.child,null,n),t.child=Ml(t,null,l,n)):Ne(e,t,l,n),t.memoizedState=u.state,e=t.child):e=Et(e,t,n),e}function xr(e,t,l,a){return Qa(),t.flags|=256,Ne(e,t,l,a),t.child}var Pi={dehydrated:null,treeContext:null,retryLane:0};function $i(e){return{baseLanes:e,cachePool:Do()}}function Wi(e,t,l){return e=e!==null?e.childLanes&~l:0,t&&(e|=tt),e}function Ar(e,t,l){var a=t.pendingProps,n=!1,u=(t.flags&128)!==0,i;if((i=u)||(i=e!==null&&e.memoizedState===null?!1:(ye.current&2)!==0),i&&(n=!0,t.flags&=-129),i=(t.flags&32)!==0,t.flags&=-33,e===null){if(K){if(n?Kt(t):Zt(),K){var c=De,s;if(s=c){e:{for(s=c,c=dt;s.nodeType!==8;){if(!c){c=null;break e}if(s=ct(s.nextSibling),s===null){c=null;break e}}c=s}c!==null?(t.memoizedState={dehydrated:c,treeContext:Dl!==null?{id:xt,overflow:At}:null,retryLane:536870912},s=et(18,null,null,0),s.stateNode=c,s.return=t,t.child=s,Ce=t,De=null,s=!0):s=!1}s||El(t)}if(c=t.memoizedState,c!==null&&(c=c.dehydrated,c!==null))return c.data==="$!"?t.lanes=16:t.lanes=536870912,null;Dt(t)}return c=a.children,a=a.fallback,n?(Zt(),n=t.mode,c=ec({mode:"hidden",children:c},n),a=Ll(a,n,l,null),c.return=t,a.return=t,c.sibling=a,t.child=c,n=t.child,n.memoizedState=$i(l),n.childLanes=Wi(e,i,l),t.memoizedState=Pi,a):(Kt(t),Ii(t,c))}if(s=e.memoizedState,s!==null&&(c=s.dehydrated,c!==null)){if(u)t.flags&256?(Kt(t),t.flags&=-257,t=tc(e,t,l)):t.memoizedState!==null?(Zt(),t.child=e.child,t.flags|=128,t=null):(Zt(),n=a.fallback,c=t.mode,a=ec({mode:"visible",children:a.children},c),n=Ll(n,c,l,null),n.flags|=2,a.return=t,n.return=t,a.sibling=n,t.child=a,Ml(t,e.child,null,l),a=t.child,a.memoizedState=$i(l),a.childLanes=Wi(e,i,l),t.memoizedState=Pi,t=n);else if(Kt(t),c.data==="$!"){if(i=c.nextSibling&&c.nextSibling.dataset,i)var f=i.dgst;i=f,a=Error(d(419)),a.stack="",a.digest=i,Ka({value:a,source:null,stack:null}),t=tc(e,t,l)}else if(Te||ln(e,t,l,!1),i=(l&e.childLanes)!==0,Te||i){if(i=I,i!==null){if(a=l&-l,a&42)a=1;else switch(a){case 2:a=1;break;case 8:a=4;break;case 32:a=16;break;case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:a=64;break;case 268435456:a=134217728;break;default:a=0}if(a=a&(i.suspendedLanes|l)?0:a,a!==0&&a!==s.retryLane)throw s.retryLane=a,Qt(e,a),Oe(i,e,a),hr}c.data==="$?"||Cc(),t=tc(e,t,l)}else c.data==="$?"?(t.flags|=128,t.child=e.child,t=Dh.bind(null,e),c._reactRetry=t,t=null):(e=s.treeContext,De=ct(c.nextSibling),Ce=t,K=!0,ut=null,dt=!1,e!==null&&(Fe[Pe++]=xt,Fe[Pe++]=At,Fe[Pe++]=Dl,xt=e.id,At=e.overflow,Dl=t),t=Ii(t,a.children),t.flags|=4096);return t}return n?(Zt(),n=a.fallback,c=t.mode,s=e.child,f=s.sibling,a=ll(s,{mode:"hidden",children:a.children}),a.subtreeFlags=s.subtreeFlags&31457280,f!==null?n=ll(f,n):(n=Ll(n,c,l,null),n.flags|=2),n.return=t,a.return=t,a.sibling=n,t.child=a,a=n,n=t.child,c=e.child.memoizedState,c===null?c=$i(l):(s=c.cachePool,s!==null?(f=ve._currentValue,s=s.parent!==f?{parent:f,pool:f}:s):s=Do(),c={baseLanes:c.baseLanes|l,cachePool:s}),n.memoizedState=c,n.childLanes=Wi(e,i,l),t.memoizedState=Pi,a):(Kt(t),l=e.child,e=l.sibling,l=ll(l,{mode:"visible",children:a.children}),l.return=t,l.sibling=null,e!==null&&(i=t.deletions,i===null?(t.deletions=[e],t.flags|=16):i.push(e)),t.child=l,t.memoizedState=null,l)}function Ii(e,t){return t=ec({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function ec(e,t){return Fr(e,t,0,null)}function tc(e,t,l){return Ml(t,e.child,null,l),e=Ii(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Dr(e,t,l){e.lanes|=t;var a=e.alternate;a!==null&&(a.lanes|=t),uc(e.return,t,l)}function lc(e,t,l,a,n){var u=e.memoizedState;u===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:a,tail:l,tailMode:n}:(u.isBackwards=t,u.rendering=null,u.renderingStartTime=0,u.last=a,u.tail=l,u.tailMode=n)}function Nr(e,t,l){var a=t.pendingProps,n=a.revealOrder,u=a.tail;if(Ne(e,t,a.children,l),a=ye.current,a&2)a=a&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Dr(e,l,t);else if(e.tag===19)Dr(e,l,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}a&=1}switch(le(ye,a),n){case"forwards":for(l=t.child,n=null;l!==null;)e=l.alternate,e!==null&&nu(e)===null&&(n=l),l=l.sibling;l=n,l===null?(n=t.child,t.child=null):(n=l.sibling,l.sibling=null),lc(t,!1,n,l,u);break;case"backwards":for(l=null,n=t.child,t.child=null;n!==null;){if(e=n.alternate,e!==null&&nu(e)===null){t.child=n;break}e=n.sibling,n.sibling=l,l=n,n=e}lc(t,!0,l,null,u);break;case"together":lc(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Et(e,t,l){if(e!==null&&(t.dependencies=e.dependencies),al|=t.lanes,!(l&t.childLanes))if(e!==null){if(ln(e,t,l,!1),(l&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(d(153));if(t.child!==null){for(e=t.child,l=ll(e,e.pendingProps),t.child=l,l.return=t;e.sibling!==null;)e=e.sibling,l=l.sibling=ll(e,e.pendingProps),l.return=t;l.sibling=null}return t.child}function ac(e,t){return e.lanes&t?!0:(e=e.dependencies,!!(e!==null&&pu(e)))}function rh(e,t,l){switch(t.tag){case 3:Rn(t,t.stateNode.containerInfo),Ft(t,ve,e.memoizedState.cache),Qa();break;case 27:case 5:ku(t);break;case 4:Rn(t,t.stateNode.containerInfo);break;case 10:Ft(t,t.type,t.memoizedProps.value);break;case 13:var a=t.memoizedState;if(a!==null)return a.dehydrated!==null?(Kt(t),t.flags|=128,null):l&t.child.childLanes?Ar(e,t,l):(Kt(t),e=Et(e,t,l),e!==null?e.sibling:null);Kt(t);break;case 19:var n=(e.flags&128)!==0;if(a=(l&t.childLanes)!==0,a||(ln(e,t,l,!1),a=(l&t.childLanes)!==0),n){if(a)return Nr(e,t,l);t.flags|=128}if(n=t.memoizedState,n!==null&&(n.rendering=null,n.tail=null,n.lastEffect=null),le(ye,ye.current),a)break;return null;case 22:case 23:return t.lanes=0,vr(e,t,l);case 24:Ft(t,ve,e.memoizedState.cache)}return Et(e,t,l)}function Er(e,t,l){if(e!==null)if(e.memoizedProps!==t.pendingProps)Te=!0;else{if(!ac(e,l)&&!(t.flags&128))return Te=!1,rh(e,t,l);Te=!!(e.flags&131072)}else Te=!1,K&&t.flags&1048576&&oo(t,In,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var a=t.elementType,n=a._init;if(a=n(a._payload),t.type=a,typeof a=="function")yc(a)?(e=Rl(a,e),t.tag=1,t=Tr(null,t,a,e,l)):(t.tag=0,t=Fi(null,t,a,e,l));else{if(a!=null){if(n=a.$$typeof,n===ae){t.tag=11,t=gr(null,t,a,e,l);break e}else if(n===fl){t.tag=14,t=pr(null,t,a,e,l);break e}}throw t=Yl(a)||a,Error(d(306,t,""))}}return t;case 0:return Fi(e,t,t.type,t.pendingProps,l);case 1:return a=t.type,n=Rl(a,t.pendingProps),Tr(e,t,a,n,l);case 3:e:{if(Rn(t,t.stateNode.containerInfo),e===null)throw Error(d(387));var u=t.pendingProps;n=t.memoizedState,a=n.element,sc(e,t),un(t,u,null,l);var i=t.memoizedState;if(u=i.cache,Ft(t,ve,u),u!==n.cache&&ic(t,[ve],l,!0),nn(),u=i.element,n.isDehydrated)if(n={element:u,isDehydrated:!1,cache:i.cache},t.updateQueue.baseState=n,t.memoizedState=n,t.flags&256){t=xr(e,t,u,l);break e}else if(u!==a){a=Je(Error(d(424)),t),Ka(a),t=xr(e,t,u,l);break e}else for(De=ct(t.stateNode.containerInfo.firstChild),Ce=t,K=!0,ut=null,dt=!0,l=bo(t,null,u,l),t.child=l;l;)l.flags=l.flags&-3|4096,l=l.sibling;else{if(Qa(),u===a){t=Et(e,t,l);break e}Ne(e,t,u,l)}t=t.child}return t;case 26:return tn(e,t),e===null?(l=Of(t.type,null,t.pendingProps,null))?t.memoizedState=l:K||(l=t.type,e=t.pendingProps,a=Ru(Yt.current).createElement(l),a[Me]=t,a[Re]=e,Ee(a,l,e),Se(a),t.stateNode=a):t.memoizedState=Of(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return ku(t),e===null&&K&&(a=t.stateNode=Mf(t.type,t.pendingProps,Yt.current),Ce=t,dt=!0,De=ct(a.firstChild)),a=t.pendingProps.children,e!==null||K?Ne(e,t,a,l):t.child=Ml(t,null,a,l),tn(e,t),t.child;case 5:return e===null&&K&&((n=a=De)&&(a=Yh(a,t.type,t.pendingProps,dt),a!==null?(t.stateNode=a,Ce=t,De=ct(a.firstChild),dt=!1,n=!0):n=!1),n||El(t)),ku(t),n=t.type,u=t.pendingProps,i=e!==null?e.memoizedProps:null,a=u.children,Vc(n,u)?a=null:i!==null&&Vc(n,i)&&(t.flags|=32),t.memoizedState!==null&&(n=_i(e,t,ah,null,null,l),Tn._currentValue=n),tn(e,t),Ne(e,t,a,l),t.child;case 6:return e===null&&K&&((e=l=De)&&(l=Xh(l,t.pendingProps,dt),l!==null?(t.stateNode=l,Ce=t,De=null,e=!0):e=!1),e||El(t)),null;case 13:return Ar(e,t,l);case 4:return Rn(t,t.stateNode.containerInfo),a=t.pendingProps,e===null?t.child=Ml(t,null,a,l):Ne(e,t,a,l),t.child;case 11:return gr(e,t,t.type,t.pendingProps,l);case 7:return Ne(e,t,t.pendingProps,l),t.child;case 8:return Ne(e,t,t.pendingProps.children,l),t.child;case 12:return Ne(e,t,t.pendingProps.children,l),t.child;case 10:return a=t.pendingProps,Ft(t,t.type,a.value),Ne(e,t,a.children,l),t.child;case 9:return n=t.type._context,a=t.pendingProps.children,Ul(t),n=ze(n),a=a(n),t.flags|=1,Ne(e,t,a,l),t.child;case 14:return pr(e,t,t.type,t.pendingProps,l);case 15:return yr(e,t,t.type,t.pendingProps,l);case 19:return Nr(e,t,l);case 22:return vr(e,t,l);case 24:return Ul(t),a=ze(ve),e===null?(n=Ci(),n===null&&(n=I,u=Mi(),n.pooledCache=u,u.refCount++,u!==null&&(n.pooledCacheLanes|=l),n=u),t.memoizedState={parent:a,cache:n},cc(t),Ft(t,ve,n)):(e.lanes&l&&(sc(e,t),un(t,null,null,l),nn()),n=e.memoizedState,u=t.memoizedState,n.parent!==a?(n={parent:a,cache:a},t.memoizedState=n,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=n),Ft(t,ve,a)):(a=u.cache,Ft(t,ve,a),a!==n.cache&&ic(t,[ve],l,!0))),Ne(e,t,t.pendingProps.children,l),t.child;case 29:throw t.pendingProps}throw Error(d(156,t.tag))}var nc=ot(null),jl=null,Mt=null;function Ft(e,t,l){le(nc,t._currentValue),t._currentValue=l}function zt(e){e._currentValue=nc.current,be(nc)}function uc(e,t,l){for(;e!==null;){var a=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,a!==null&&(a.childLanes|=t)):a!==null&&(a.childLanes&t)!==t&&(a.childLanes|=t),e===l)break;e=e.return}}function ic(e,t,l,a){var n=e.child;for(n!==null&&(n.return=e);n!==null;){var u=n.dependencies;if(u!==null){var i=n.child;u=u.firstContext;e:for(;u!==null;){var c=u;u=n;for(var s=0;s<t.length;s++)if(c.context===t[s]){u.lanes|=l,c=u.alternate,c!==null&&(c.lanes|=l),uc(u.return,l,e),a||(i=null);break e}u=c.next}}else if(n.tag===18){if(i=n.return,i===null)throw Error(d(341));i.lanes|=l,u=i.alternate,u!==null&&(u.lanes|=l),uc(i,l,e),i=null}else i=n.child;if(i!==null)i.return=n;else for(i=n;i!==null;){if(i===e){i=null;break}if(n=i.sibling,n!==null){n.return=i.return,i=n;break}i=i.return}n=i}}function ln(e,t,l,a){e=null;for(var n=t,u=!1;n!==null;){if(!u){if(n.flags&524288)u=!0;else if(n.flags&262144)break}if(n.tag===10){var i=n.alternate;if(i===null)throw Error(d(387));if(i=i.memoizedProps,i!==null){var c=n.type;qe(n.pendingProps.value,i.value)||(e!==null?e.push(c):e=[c])}}else if(n===_n.current){if(i=n.alternate,i===null)throw Error(d(387));i.memoizedState.memoizedState!==n.memoizedState.memoizedState&&(e!==null?e.push(Tn):e=[Tn])}n=n.return}e!==null&&ic(t,e,l,a),t.flags|=262144}function pu(e){for(e=e.firstContext;e!==null;){if(!qe(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Ul(e){jl=e,Mt=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function ze(e){return Mr(jl,e)}function yu(e,t){return jl===null&&Ul(e),Mr(e,t)}function Mr(e,t){var l=t._currentValue;if(t={context:t,memoizedValue:l,next:null},Mt===null){if(e===null)throw Error(d(308));Mt=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else Mt=Mt.next=t;return l}var Pt=!1;function cc(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function sc(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function $t(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function Wt(e,t,l){var a=e.updateQueue;if(a===null)return null;if(a=a.shared,ue&2){var n=a.pending;return n===null?t.next=t:(t.next=n.next,n.next=t),a.pending=t,t=$n(e),co(e,null,l),t}return Pn(e,a,t,l),$n(e)}function an(e,t,l){if(t=t.updateQueue,t!==null&&(t=t.shared,(l&4194176)!==0)){var a=t.lanes;a&=e.pendingLanes,l|=a,t.lanes=l,gs(e,l)}}function oc(e,t){var l=e.updateQueue,a=e.alternate;if(a!==null&&(a=a.updateQueue,l===a)){var n=null,u=null;if(l=l.firstBaseUpdate,l!==null){do{var i={lane:l.lane,tag:l.tag,payload:l.payload,callback:null,next:null};u===null?n=u=i:u=u.next=i,l=l.next}while(l!==null);u===null?n=u=t:u=u.next=t}else n=u=t;l={baseState:a.baseState,firstBaseUpdate:n,lastBaseUpdate:u,shared:a.shared,callbacks:a.callbacks},e.updateQueue=l;return}e=l.lastBaseUpdate,e===null?l.firstBaseUpdate=t:e.next=t,l.lastBaseUpdate=t}var rc=!1;function nn(){if(rc){var e=sa;if(e!==null)throw e}}function un(e,t,l,a){rc=!1;var n=e.updateQueue;Pt=!1;var u=n.firstBaseUpdate,i=n.lastBaseUpdate,c=n.shared.pending;if(c!==null){n.shared.pending=null;var s=c,f=s.next;s.next=null,i===null?u=f:i.next=f,i=s;var b=e.alternate;b!==null&&(b=b.updateQueue,c=b.lastBaseUpdate,c!==i&&(c===null?b.firstBaseUpdate=f:c.next=f,b.lastBaseUpdate=s))}if(u!==null){var T=n.baseState;i=0,b=f=s=null,c=u;do{var p=c.lane&-536870913,v=p!==c.lane;if(v?(w&p)===p:(a&p)===p){p!==0&&p===ca&&(rc=!0),b!==null&&(b=b.next={lane:0,tag:c.tag,payload:c.payload,callback:null,next:null});e:{var M=e,U=c;p=t;var se=l;switch(U.tag){case 1:if(M=U.payload,typeof M=="function"){T=M.call(se,T,p);break e}T=M;break e;case 3:M.flags=M.flags&-65537|128;case 0:if(M=U.payload,p=typeof M=="function"?M.call(se,T,p):M,p==null)break e;T=k({},T,p);break e;case 2:Pt=!0}}p=c.callback,p!==null&&(e.flags|=64,v&&(e.flags|=8192),v=n.callbacks,v===null?n.callbacks=[p]:v.push(p))}else v={lane:p,tag:c.tag,payload:c.payload,callback:c.callback,next:null},b===null?(f=b=v,s=T):b=b.next=v,i|=p;if(c=c.next,c===null){if(c=n.shared.pending,c===null)break;v=c,c=v.next,v.next=null,n.lastBaseUpdate=v,n.shared.pending=null}}while(!0);b===null&&(s=T),n.baseState=s,n.firstBaseUpdate=f,n.lastBaseUpdate=b,u===null&&(n.shared.lanes=0),al|=i,e.lanes=i,e.memoizedState=T}}function zr(e,t){if(typeof e!="function")throw Error(d(191,e));e.call(t)}function Cr(e,t){var l=e.callbacks;if(l!==null)for(e.callbacks=null,e=0;e<l.length;e++)zr(l[e],t)}function cn(e,t){try{var l=t.updateQueue,a=l!==null?l.lastEffect:null;if(a!==null){var n=a.next;l=n;do{if((l.tag&e)===e){a=void 0;var u=l.create,i=l.inst;a=u(),i.destroy=a}l=l.next}while(l!==n)}}catch(c){W(t,t.return,c)}}function It(e,t,l){try{var a=t.updateQueue,n=a!==null?a.lastEffect:null;if(n!==null){var u=n.next;a=u;do{if((a.tag&e)===e){var i=a.inst,c=i.destroy;if(c!==void 0){i.destroy=void 0,n=t;var s=l;try{c()}catch(f){W(n,s,f)}}}a=a.next}while(a!==u)}}catch(f){W(t,t.return,f)}}function Or(e){var t=e.updateQueue;if(t!==null){var l=e.stateNode;try{Cr(t,l)}catch(a){W(e,e.return,a)}}}function _r(e,t,l){l.props=Rl(e.type,e.memoizedProps),l.state=e.memoizedState;try{l.componentWillUnmount()}catch(a){W(e,t,a)}}function Hl(e,t){try{var l=e.ref;if(l!==null){var a=e.stateNode;switch(e.tag){case 26:case 27:case 5:var n=a;break;default:n=a}typeof l=="function"?e.refCleanup=l(n):l.current=n}}catch(u){W(e,t,u)}}function Be(e,t){var l=e.ref,a=e.refCleanup;if(l!==null)if(typeof a=="function")try{a()}catch(n){W(e,t,n)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof l=="function")try{l(null)}catch(n){W(e,t,n)}else l.current=null}function Rr(e){var t=e.type,l=e.memoizedProps,a=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":l.autoFocus&&a.focus();break e;case"img":l.src?a.src=l.src:l.srcSet&&(a.srcset=l.srcSet)}}catch(n){W(e,e.return,n)}}function jr(e,t,l){try{var a=e.stateNode;Hh(a,e.type,l,t),a[Re]=t}catch(n){W(e,e.return,n)}}function Ur(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27||e.tag===4}function fc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Ur(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==27&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function dc(e,t,l){var a=e.tag;if(a===5||a===6)e=e.stateNode,t?l.nodeType===8?l.parentNode.insertBefore(e,t):l.insertBefore(e,t):(l.nodeType===8?(t=l.parentNode,t.insertBefore(e,l)):(t=l,t.appendChild(e)),l=l._reactRootContainer,l!=null||t.onclick!==null||(t.onclick=_u));else if(a!==4&&a!==27&&(e=e.child,e!==null))for(dc(e,t,l),e=e.sibling;e!==null;)dc(e,t,l),e=e.sibling}function vu(e,t,l){var a=e.tag;if(a===5||a===6)e=e.stateNode,t?l.insertBefore(e,t):l.appendChild(e);else if(a!==4&&a!==27&&(e=e.child,e!==null))for(vu(e,t,l),e=e.sibling;e!==null;)vu(e,t,l),e=e.sibling}var Ct=!1,ie=!1,mc=!1,Hr=typeof WeakSet=="function"?WeakSet:Set,xe=null,Lr=!1;function fh(e,t){if(e=e.containerInfo,Xc=Bu,e=Ws(e),gi(e)){if("selectionStart"in e)var l={start:e.selectionStart,end:e.selectionEnd};else e:{l=(l=e.ownerDocument)&&l.defaultView||window;var a=l.getSelection&&l.getSelection();if(a&&a.rangeCount!==0){l=a.anchorNode;var n=a.anchorOffset,u=a.focusNode;a=a.focusOffset;try{l.nodeType,u.nodeType}catch{l=null;break e}var i=0,c=-1,s=-1,f=0,b=0,T=e,p=null;t:for(;;){for(var v;T!==l||n!==0&&T.nodeType!==3||(c=i+n),T!==u||a!==0&&T.nodeType!==3||(s=i+a),T.nodeType===3&&(i+=T.nodeValue.length),(v=T.firstChild)!==null;)p=T,T=v;for(;;){if(T===e)break t;if(p===l&&++f===n&&(c=i),p===u&&++b===a&&(s=i),(v=T.nextSibling)!==null)break;T=p,p=T.parentNode}T=v}l=c===-1||s===-1?null:{start:c,end:s}}else l=null}l=l||{start:0,end:0}}else l=null;for(wc={focusedElem:e,selectionRange:l},Bu=!1,xe=t;xe!==null;)if(t=xe,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,xe=e;else for(;xe!==null;){switch(t=xe,u=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if(e&1024&&u!==null){e=void 0,l=t,n=u.memoizedProps,u=u.memoizedState,a=l.stateNode;try{var M=Rl(l.type,n,l.elementType===l.type);e=a.getSnapshotBeforeUpdate(M,u),a.__reactInternalSnapshotBeforeUpdate=e}catch(U){W(l,l.return,U)}}break;case 3:if(e&1024){if(e=t.stateNode.containerInfo,l=e.nodeType,l===9)Zc(e);else if(l===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":Zc(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if(e&1024)throw Error(d(163))}if(e=t.sibling,e!==null){e.return=t.return,xe=e;break}xe=t.return}return M=Lr,Lr=!1,M}function qr(e,t,l){var a=l.flags;switch(l.tag){case 0:case 11:case 15:_t(e,l),a&4&&cn(5,l);break;case 1:if(_t(e,l),a&4)if(e=l.stateNode,t===null)try{e.componentDidMount()}catch(c){W(l,l.return,c)}else{var n=Rl(l.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(n,t,e.__reactInternalSnapshotBeforeUpdate)}catch(c){W(l,l.return,c)}}a&64&&Or(l),a&512&&Hl(l,l.return);break;case 3:if(_t(e,l),a&64&&(a=l.updateQueue,a!==null)){if(e=null,l.child!==null)switch(l.child.tag){case 27:case 5:e=l.child.stateNode;break;case 1:e=l.child.stateNode}try{Cr(a,e)}catch(c){W(l,l.return,c)}}break;case 26:_t(e,l),a&512&&Hl(l,l.return);break;case 27:case 5:_t(e,l),t===null&&a&4&&Rr(l),a&512&&Hl(l,l.return);break;case 12:_t(e,l);break;case 13:_t(e,l),a&4&&Yr(e,l);break;case 22:if(n=l.memoizedState!==null||Ct,!n){t=t!==null&&t.memoizedState!==null||ie;var u=Ct,i=ie;Ct=n,(ie=t)&&!i?el(e,l,(l.subtreeFlags&8772)!==0):_t(e,l),Ct=u,ie=i}a&512&&(l.memoizedProps.mode==="manual"?Hl(l,l.return):Be(l,l.return));break;default:_t(e,l)}}function Br(e){var t=e.alternate;t!==null&&(e.alternate=null,Br(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&Iu(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var me=null,Ge=!1;function Ot(e,t,l){for(l=l.child;l!==null;)Gr(e,t,l),l=l.sibling}function Gr(e,t,l){if(He&&typeof He.onCommitFiberUnmount=="function")try{He.onCommitFiberUnmount(Ca,l)}catch{}switch(l.tag){case 26:ie||Be(l,t),Ot(e,t,l),l.memoizedState?l.memoizedState.count--:l.stateNode&&(l=l.stateNode,l.parentNode.removeChild(l));break;case 27:ie||Be(l,t);var a=me,n=Ge;for(me=l.stateNode,Ot(e,t,l),l=l.stateNode,t=l.attributes;t.length;)l.removeAttributeNode(t[0]);Iu(l),me=a,Ge=n;break;case 5:ie||Be(l,t);case 6:n=me;var u=Ge;if(me=null,Ot(e,t,l),me=n,Ge=u,me!==null)if(Ge)try{e=me,a=l.stateNode,e.nodeType===8?e.parentNode.removeChild(a):e.removeChild(a)}catch(i){W(l,t,i)}else try{me.removeChild(l.stateNode)}catch(i){W(l,t,i)}break;case 18:me!==null&&(Ge?(t=me,l=l.stateNode,t.nodeType===8?Kc(t.parentNode,l):t.nodeType===1&&Kc(t,l),Nn(t)):Kc(me,l.stateNode));break;case 4:a=me,n=Ge,me=l.stateNode.containerInfo,Ge=!0,Ot(e,t,l),me=a,Ge=n;break;case 0:case 11:case 14:case 15:ie||It(2,l,t),ie||It(4,l,t),Ot(e,t,l);break;case 1:ie||(Be(l,t),a=l.stateNode,typeof a.componentWillUnmount=="function"&&_r(l,t,a)),Ot(e,t,l);break;case 21:Ot(e,t,l);break;case 22:ie||Be(l,t),ie=(a=ie)||l.memoizedState!==null,Ot(e,t,l),ie=a;break;default:Ot(e,t,l)}}function Yr(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{Nn(e)}catch(l){W(t,t.return,l)}}function dh(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new Hr),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new Hr),t;default:throw Error(d(435,e.tag))}}function hc(e,t){var l=dh(e);t.forEach(function(a){var n=Nh.bind(null,e,a);l.has(a)||(l.add(a),a.then(n,n))})}function We(e,t){var l=t.deletions;if(l!==null)for(var a=0;a<l.length;a++){var n=l[a],u=e,i=t,c=i;e:for(;c!==null;){switch(c.tag){case 27:case 5:me=c.stateNode,Ge=!1;break e;case 3:me=c.stateNode.containerInfo,Ge=!0;break e;case 4:me=c.stateNode.containerInfo,Ge=!0;break e}c=c.return}if(me===null)throw Error(d(160));Gr(u,i,n),me=null,Ge=!1,u=n.alternate,u!==null&&(u.return=null),n.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)Xr(t,e),t=t.sibling}var it=null;function Xr(e,t){var l=e.alternate,a=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:We(t,e),Ie(e),a&4&&(It(3,e,e.return),cn(3,e),It(5,e,e.return));break;case 1:We(t,e),Ie(e),a&512&&(ie||l===null||Be(l,l.return)),a&64&&Ct&&(e=e.updateQueue,e!==null&&(a=e.callbacks,a!==null&&(l=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=l===null?a:l.concat(a))));break;case 26:var n=it;if(We(t,e),Ie(e),a&512&&(ie||l===null||Be(l,l.return)),a&4){var u=l!==null?l.memoizedState:null;if(a=e.memoizedState,l===null)if(a===null)if(e.stateNode===null){e:{a=e.type,l=e.memoizedProps,n=n.ownerDocument||n;t:switch(a){case"title":u=n.getElementsByTagName("title")[0],(!u||u[Ra]||u[Me]||u.namespaceURI==="http://www.w3.org/2000/svg"||u.hasAttribute("itemprop"))&&(u=n.createElement(a),n.head.insertBefore(u,n.querySelector("head > title"))),Ee(u,a,l),u[Me]=e,Se(u),a=u;break e;case"link":var i=jf("link","href",n).get(a+(l.href||""));if(i){for(var c=0;c<i.length;c++)if(u=i[c],u.getAttribute("href")===(l.href==null?null:l.href)&&u.getAttribute("rel")===(l.rel==null?null:l.rel)&&u.getAttribute("title")===(l.title==null?null:l.title)&&u.getAttribute("crossorigin")===(l.crossOrigin==null?null:l.crossOrigin)){i.splice(c,1);break t}}u=n.createElement(a),Ee(u,a,l),n.head.appendChild(u);break;case"meta":if(i=jf("meta","content",n).get(a+(l.content||""))){for(c=0;c<i.length;c++)if(u=i[c],u.getAttribute("content")===(l.content==null?null:""+l.content)&&u.getAttribute("name")===(l.name==null?null:l.name)&&u.getAttribute("property")===(l.property==null?null:l.property)&&u.getAttribute("http-equiv")===(l.httpEquiv==null?null:l.httpEquiv)&&u.getAttribute("charset")===(l.charSet==null?null:l.charSet)){i.splice(c,1);break t}}u=n.createElement(a),Ee(u,a,l),n.head.appendChild(u);break;default:throw Error(d(468,a))}u[Me]=e,Se(u),a=u}e.stateNode=a}else Uf(n,e.type,e.stateNode);else e.stateNode=Rf(n,a,e.memoizedProps);else u!==a?(u===null?l.stateNode!==null&&(l=l.stateNode,l.parentNode.removeChild(l)):u.count--,a===null?Uf(n,e.type,e.stateNode):Rf(n,a,e.memoizedProps)):a===null&&e.stateNode!==null&&jr(e,e.memoizedProps,l.memoizedProps)}break;case 27:if(a&4&&e.alternate===null){n=e.stateNode,u=e.memoizedProps;try{for(var s=n.firstChild;s;){var f=s.nextSibling,b=s.nodeName;s[Ra]||b==="HEAD"||b==="BODY"||b==="SCRIPT"||b==="STYLE"||b==="LINK"&&s.rel.toLowerCase()==="stylesheet"||n.removeChild(s),s=f}for(var T=e.type,p=n.attributes;p.length;)n.removeAttributeNode(p[0]);Ee(n,T,u),n[Me]=e,n[Re]=u}catch(M){W(e,e.return,M)}}case 5:if(We(t,e),Ie(e),a&512&&(ie||l===null||Be(l,l.return)),e.flags&32){n=e.stateNode;try{Fl(n,"")}catch(M){W(e,e.return,M)}}a&4&&e.stateNode!=null&&(n=e.memoizedProps,jr(e,n,l!==null?l.memoizedProps:n)),a&1024&&(mc=!0);break;case 6:if(We(t,e),Ie(e),a&4){if(e.stateNode===null)throw Error(d(162));a=e.memoizedProps,l=e.stateNode;try{l.nodeValue=a}catch(M){W(e,e.return,M)}}break;case 3:if(Hu=null,n=it,it=ju(t.containerInfo),We(t,e),it=n,Ie(e),a&4&&l!==null&&l.memoizedState.isDehydrated)try{Nn(t.containerInfo)}catch(M){W(e,e.return,M)}mc&&(mc=!1,wr(e));break;case 4:a=it,it=ju(e.stateNode.containerInfo),We(t,e),Ie(e),it=a;break;case 12:We(t,e),Ie(e);break;case 13:We(t,e),Ie(e),e.child.flags&8192&&e.memoizedState!==null!=(l!==null&&l.memoizedState!==null)&&(Ac=ft()),a&4&&(a=e.updateQueue,a!==null&&(e.updateQueue=null,hc(e,a)));break;case 22:if(a&512&&(ie||l===null||Be(l,l.return)),s=e.memoizedState!==null,f=l!==null&&l.memoizedState!==null,b=Ct,T=ie,Ct=b||s,ie=T||f,We(t,e),ie=T,Ct=b,Ie(e),t=e.stateNode,t._current=e,t._visibility&=-3,t._visibility|=t._pendingVisibility&2,a&8192&&(t._visibility=s?t._visibility&-2:t._visibility|1,s&&(t=Ct||ie,l===null||f||t||da(e)),e.memoizedProps===null||e.memoizedProps.mode!=="manual"))e:for(l=null,t=e;;){if(t.tag===5||t.tag===26||t.tag===27){if(l===null){f=l=t;try{if(n=f.stateNode,s)u=n.style,typeof u.setProperty=="function"?u.setProperty("display","none","important"):u.display="none";else{i=f.stateNode,c=f.memoizedProps.style;var v=c!=null&&c.hasOwnProperty("display")?c.display:null;i.style.display=v==null||typeof v=="boolean"?"":(""+v).trim()}}catch(M){W(f,f.return,M)}}}else if(t.tag===6){if(l===null){f=t;try{f.stateNode.nodeValue=s?"":f.memoizedProps}catch(M){W(f,f.return,M)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;l===t&&(l=null),t=t.return}l===t&&(l=null),t.sibling.return=t.return,t=t.sibling}a&4&&(a=e.updateQueue,a!==null&&(l=a.retryQueue,l!==null&&(a.retryQueue=null,hc(e,l))));break;case 19:We(t,e),Ie(e),a&4&&(a=e.updateQueue,a!==null&&(e.updateQueue=null,hc(e,a)));break;case 21:break;default:We(t,e),Ie(e)}}function Ie(e){var t=e.flags;if(t&2){try{if(e.tag!==27){e:{for(var l=e.return;l!==null;){if(Ur(l)){var a=l;break e}l=l.return}throw Error(d(160))}switch(a.tag){case 27:var n=a.stateNode,u=fc(e);vu(e,u,n);break;case 5:var i=a.stateNode;a.flags&32&&(Fl(i,""),a.flags&=-33);var c=fc(e);vu(e,c,i);break;case 3:case 4:var s=a.stateNode.containerInfo,f=fc(e);dc(e,f,s);break;default:throw Error(d(161))}}}catch(b){W(e,e.return,b)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function wr(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;wr(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function _t(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)qr(e,t.alternate,t),t=t.sibling}function da(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:It(4,t,t.return),da(t);break;case 1:Be(t,t.return);var l=t.stateNode;typeof l.componentWillUnmount=="function"&&_r(t,t.return,l),da(t);break;case 26:case 27:case 5:Be(t,t.return),da(t);break;case 22:Be(t,t.return),t.memoizedState===null&&da(t);break;default:da(t)}e=e.sibling}}function el(e,t,l){for(l=l&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var a=t.alternate,n=e,u=t,i=u.flags;switch(u.tag){case 0:case 11:case 15:el(n,u,l),cn(4,u);break;case 1:if(el(n,u,l),a=u,n=a.stateNode,typeof n.componentDidMount=="function")try{n.componentDidMount()}catch(f){W(a,a.return,f)}if(a=u,n=a.updateQueue,n!==null){var c=a.stateNode;try{var s=n.shared.hiddenCallbacks;if(s!==null)for(n.shared.hiddenCallbacks=null,n=0;n<s.length;n++)zr(s[n],c)}catch(f){W(a,a.return,f)}}l&&i&64&&Or(u),Hl(u,u.return);break;case 26:case 27:case 5:el(n,u,l),l&&a===null&&i&4&&Rr(u),Hl(u,u.return);break;case 12:el(n,u,l);break;case 13:el(n,u,l),l&&i&4&&Yr(n,u);break;case 22:u.memoizedState===null&&el(n,u,l),Hl(u,u.return);break;default:el(n,u,l)}t=t.sibling}}function gc(e,t){var l=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(l=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==l&&(e!=null&&e.refCount++,l!=null&&Pa(l))}function pc(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Pa(e))}function tl(e,t,l,a){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Vr(e,t,l,a),t=t.sibling}function Vr(e,t,l,a){var n=t.flags;switch(t.tag){case 0:case 11:case 15:tl(e,t,l,a),n&2048&&cn(9,t);break;case 3:tl(e,t,l,a),n&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Pa(e)));break;case 12:if(n&2048){tl(e,t,l,a),e=t.stateNode;try{var u=t.memoizedProps,i=u.id,c=u.onPostCommit;typeof c=="function"&&c(i,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(s){W(t,t.return,s)}}else tl(e,t,l,a);break;case 23:break;case 22:u=t.stateNode,t.memoizedState!==null?u._visibility&4?tl(e,t,l,a):sn(e,t):u._visibility&4?tl(e,t,l,a):(u._visibility|=4,ma(e,t,l,a,(t.subtreeFlags&10256)!==0)),n&2048&&gc(t.alternate,t);break;case 24:tl(e,t,l,a),n&2048&&pc(t.alternate,t);break;default:tl(e,t,l,a)}}function ma(e,t,l,a,n){for(n=n&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var u=e,i=t,c=l,s=a,f=i.flags;switch(i.tag){case 0:case 11:case 15:ma(u,i,c,s,n),cn(8,i);break;case 23:break;case 22:var b=i.stateNode;i.memoizedState!==null?b._visibility&4?ma(u,i,c,s,n):sn(u,i):(b._visibility|=4,ma(u,i,c,s,n)),n&&f&2048&&gc(i.alternate,i);break;case 24:ma(u,i,c,s,n),n&&f&2048&&pc(i.alternate,i);break;default:ma(u,i,c,s,n)}t=t.sibling}}function sn(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var l=e,a=t,n=a.flags;switch(a.tag){case 22:sn(l,a),n&2048&&gc(a.alternate,a);break;case 24:sn(l,a),n&2048&&pc(a.alternate,a);break;default:sn(l,a)}t=t.sibling}}var on=8192;function ha(e){if(e.subtreeFlags&on)for(e=e.child;e!==null;)Qr(e),e=e.sibling}function Qr(e){switch(e.tag){case 26:ha(e),e.flags&on&&e.memoizedState!==null&&eg(it,e.memoizedState,e.memoizedProps);break;case 5:ha(e);break;case 3:case 4:var t=it;it=ju(e.stateNode.containerInfo),ha(e),it=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=on,on=16777216,ha(e),on=t):ha(e));break;default:ha(e)}}function Kr(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function rn(e){var t=e.deletions;if(e.flags&16){if(t!==null)for(var l=0;l<t.length;l++){var a=t[l];xe=a,kr(a,e)}Kr(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Zr(e),e=e.sibling}function Zr(e){switch(e.tag){case 0:case 11:case 15:rn(e),e.flags&2048&&It(9,e,e.return);break;case 3:rn(e);break;case 12:rn(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&4&&(e.return===null||e.return.tag!==13)?(t._visibility&=-5,bu(e)):rn(e);break;default:rn(e)}}function bu(e){var t=e.deletions;if(e.flags&16){if(t!==null)for(var l=0;l<t.length;l++){var a=t[l];xe=a,kr(a,e)}Kr(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:It(8,t,t.return),bu(t);break;case 22:l=t.stateNode,l._visibility&4&&(l._visibility&=-5,bu(t));break;default:bu(t)}e=e.sibling}}function kr(e,t){for(;xe!==null;){var l=xe;switch(l.tag){case 0:case 11:case 15:It(8,l,t);break;case 23:case 22:if(l.memoizedState!==null&&l.memoizedState.cachePool!==null){var a=l.memoizedState.cachePool.pool;a!=null&&a.refCount++}break;case 24:Pa(l.memoizedState.cache)}if(a=l.child,a!==null)a.return=l,xe=a;else e:for(l=e;xe!==null;){a=xe;var n=a.sibling,u=a.return;if(Br(a),a===l){xe=null;break e}if(n!==null){n.return=u,xe=n;break e}xe=u}}}function mh(e,t,l,a){this.tag=e,this.key=l,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=a,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function et(e,t,l,a){return new mh(e,t,l,a)}function yc(e){return e=e.prototype,!(!e||!e.isReactComponent)}function ll(e,t){var l=e.alternate;return l===null?(l=et(e.tag,t,e.key,e.mode),l.elementType=e.elementType,l.type=e.type,l.stateNode=e.stateNode,l.alternate=e,e.alternate=l):(l.pendingProps=t,l.type=e.type,l.flags=0,l.subtreeFlags=0,l.deletions=null),l.flags=e.flags&31457280,l.childLanes=e.childLanes,l.lanes=e.lanes,l.child=e.child,l.memoizedProps=e.memoizedProps,l.memoizedState=e.memoizedState,l.updateQueue=e.updateQueue,t=e.dependencies,l.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},l.sibling=e.sibling,l.index=e.index,l.ref=e.ref,l.refCleanup=e.refCleanup,l}function Jr(e,t){e.flags&=31457282;var l=e.alternate;return l===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=l.childLanes,e.lanes=l.lanes,e.child=l.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=l.memoizedProps,e.memoizedState=l.memoizedState,e.updateQueue=l.updateQueue,e.type=l.type,t=l.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function Su(e,t,l,a,n,u){var i=0;if(a=e,typeof e=="function")yc(e)&&(i=1);else if(typeof e=="string")i=Wh(e,l,rt.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case Y:return Ll(l.children,n,u,t);case $:i=8,n|=24;break;case he:return e=et(12,l,t,n|2),e.elementType=he,e.lanes=u,e;case C:return e=et(13,l,t,n),e.elementType=C,e.lanes=u,e;case yt:return e=et(19,l,t,n),e.elementType=yt,e.lanes=u,e;case dl:return Fr(l,n,u,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case ge:case pe:i=10;break e;case V:i=9;break e;case ae:i=11;break e;case fl:i=14;break e;case _e:i=16,a=null;break e}i=29,l=Error(d(130,e===null?"null":typeof e,"")),a=null}return t=et(i,l,t,n),t.elementType=e,t.type=a,t.lanes=u,t}function Ll(e,t,l,a){return e=et(7,e,a,t),e.lanes=l,e}function Fr(e,t,l,a){e=et(22,e,a,t),e.elementType=dl,e.lanes=l;var n={_visibility:1,_pendingVisibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null,_current:null,detach:function(){var u=n._current;if(u===null)throw Error(d(456));if(!(n._pendingVisibility&2)){var i=Qt(u,2);i!==null&&(n._pendingVisibility|=2,Oe(i,u,2))}},attach:function(){var u=n._current;if(u===null)throw Error(d(456));if(n._pendingVisibility&2){var i=Qt(u,2);i!==null&&(n._pendingVisibility&=-3,Oe(i,u,2))}}};return e.stateNode=n,e}function vc(e,t,l){return e=et(6,e,null,t),e.lanes=l,e}function bc(e,t,l){return t=et(4,e.children!==null?e.children:[],e.key,t),t.lanes=l,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Rt(e){e.flags|=4}function Pr(e,t){if(t.type!=="stylesheet"||t.state.loading&4)e.flags&=-16777217;else if(e.flags|=16777216,!Hf(t)){if(t=$e.current,t!==null&&((w&4194176)===w?mt!==null:(w&62914560)!==w&&!(w&536870912)||t!==mt))throw ka=Di,mo;e.flags|=8192}}function Tu(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?ms():536870912,e.lanes|=t,pa|=t)}function fn(e,t){if(!K)switch(e.tailMode){case"hidden":t=e.tail;for(var l=null;t!==null;)t.alternate!==null&&(l=t),t=t.sibling;l===null?e.tail=null:l.sibling=null;break;case"collapsed":l=e.tail;for(var a=null;l!==null;)l.alternate!==null&&(a=l),l=l.sibling;a===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:a.sibling=null}}function ne(e){var t=e.alternate!==null&&e.alternate.child===e.child,l=0,a=0;if(t)for(var n=e.child;n!==null;)l|=n.lanes|n.childLanes,a|=n.subtreeFlags&31457280,a|=n.flags&31457280,n.return=e,n=n.sibling;else for(n=e.child;n!==null;)l|=n.lanes|n.childLanes,a|=n.subtreeFlags,a|=n.flags,n.return=e,n=n.sibling;return e.subtreeFlags|=a,e.childLanes=l,t}function hh(e,t,l){var a=t.pendingProps;switch(xi(t),t.tag){case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return ne(t),null;case 1:return ne(t),null;case 3:return l=t.stateNode,a=null,e!==null&&(a=e.memoizedState.cache),t.memoizedState.cache!==a&&(t.flags|=2048),zt(ve),Vl(),l.pendingContext&&(l.context=l.pendingContext,l.pendingContext=null),(e===null||e.child===null)&&(Va(t)?Rt(t):e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,ut!==null&&(Mc(ut),ut=null))),ne(t),null;case 26:return l=t.memoizedState,e===null?(Rt(t),l!==null?(ne(t),Pr(t,l)):(ne(t),t.flags&=-16777217)):l?l!==e.memoizedState?(Rt(t),ne(t),Pr(t,l)):(ne(t),t.flags&=-16777217):(e.memoizedProps!==a&&Rt(t),ne(t),t.flags&=-16777217),null;case 27:jn(t),l=Yt.current;var n=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==a&&Rt(t);else{if(!a){if(t.stateNode===null)throw Error(d(166));return ne(t),null}e=rt.current,Va(t)?ro(t):(e=Mf(n,a,l),t.stateNode=e,Rt(t))}return ne(t),null;case 5:if(jn(t),l=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==a&&Rt(t);else{if(!a){if(t.stateNode===null)throw Error(d(166));return ne(t),null}if(e=rt.current,Va(t))ro(t);else{switch(n=Ru(Yt.current),e){case 1:e=n.createElementNS("http://www.w3.org/2000/svg",l);break;case 2:e=n.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;default:switch(l){case"svg":e=n.createElementNS("http://www.w3.org/2000/svg",l);break;case"math":e=n.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;case"script":e=n.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof a.is=="string"?n.createElement("select",{is:a.is}):n.createElement("select"),a.multiple?e.multiple=!0:a.size&&(e.size=a.size);break;default:e=typeof a.is=="string"?n.createElement(l,{is:a.is}):n.createElement(l)}}e[Me]=t,e[Re]=a;e:for(n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.tag!==27&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break e;for(;n.sibling===null;){if(n.return===null||n.return===t)break e;n=n.return}n.sibling.return=n.return,n=n.sibling}t.stateNode=e;e:switch(Ee(e,l,a),l){case"button":case"input":case"select":case"textarea":e=!!a.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&Rt(t)}}return ne(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==a&&Rt(t);else{if(typeof a!="string"&&t.stateNode===null)throw Error(d(166));if(e=Yt.current,Va(t)){if(e=t.stateNode,l=t.memoizedProps,a=null,n=Ce,n!==null)switch(n.tag){case 27:case 5:a=n.memoizedProps}e[Me]=t,e=!!(e.nodeValue===l||a!==null&&a.suppressHydrationWarning===!0||Tf(e.nodeValue,l)),e||El(t)}else e=Ru(e).createTextNode(a),e[Me]=t,t.stateNode=e}return ne(t),null;case 13:if(a=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(n=Va(t),a!==null&&a.dehydrated!==null){if(e===null){if(!n)throw Error(d(318));if(n=t.memoizedState,n=n!==null?n.dehydrated:null,!n)throw Error(d(317));n[Me]=t}else Qa(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;ne(t),n=!1}else ut!==null&&(Mc(ut),ut=null),n=!0;if(!n)return t.flags&256?(Dt(t),t):(Dt(t),null)}if(Dt(t),t.flags&128)return t.lanes=l,t;if(l=a!==null,e=e!==null&&e.memoizedState!==null,l){a=t.child,n=null,a.alternate!==null&&a.alternate.memoizedState!==null&&a.alternate.memoizedState.cachePool!==null&&(n=a.alternate.memoizedState.cachePool.pool);var u=null;a.memoizedState!==null&&a.memoizedState.cachePool!==null&&(u=a.memoizedState.cachePool.pool),u!==n&&(a.flags|=2048)}return l!==e&&l&&(t.child.flags|=8192),Tu(t,t.updateQueue),ne(t),null;case 4:return Vl(),e===null&&Bc(t.stateNode.containerInfo),ne(t),null;case 10:return zt(t.type),ne(t),null;case 19:if(be(ye),n=t.memoizedState,n===null)return ne(t),null;if(a=(t.flags&128)!==0,u=n.rendering,u===null)if(a)fn(n,!1);else{if(ce!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(u=nu(e),u!==null){for(t.flags|=128,fn(n,!1),e=u.updateQueue,t.updateQueue=e,Tu(t,e),t.subtreeFlags=0,e=l,l=t.child;l!==null;)Jr(l,e),l=l.sibling;return le(ye,ye.current&1|2),t.child}e=e.sibling}n.tail!==null&&ft()>xu&&(t.flags|=128,a=!0,fn(n,!1),t.lanes=4194304)}else{if(!a)if(e=nu(u),e!==null){if(t.flags|=128,a=!0,e=e.updateQueue,t.updateQueue=e,Tu(t,e),fn(n,!0),n.tail===null&&n.tailMode==="hidden"&&!u.alternate&&!K)return ne(t),null}else 2*ft()-n.renderingStartTime>xu&&l!==536870912&&(t.flags|=128,a=!0,fn(n,!1),t.lanes=4194304);n.isBackwards?(u.sibling=t.child,t.child=u):(e=n.last,e!==null?e.sibling=u:t.child=u,n.last=u)}return n.tail!==null?(t=n.tail,n.rendering=t,n.tail=t.sibling,n.renderingStartTime=ft(),t.sibling=null,e=ye.current,le(ye,a?e&1|2:e&1),t):(ne(t),null);case 22:case 23:return Dt(t),Ei(),a=t.memoizedState!==null,e!==null?e.memoizedState!==null!==a&&(t.flags|=8192):a&&(t.flags|=8192),a?l&536870912&&!(t.flags&128)&&(ne(t),t.subtreeFlags&6&&(t.flags|=8192)):ne(t),l=t.updateQueue,l!==null&&Tu(t,l.retryQueue),l=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(l=e.memoizedState.cachePool.pool),a=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(a=t.memoizedState.cachePool.pool),a!==l&&(t.flags|=2048),e!==null&&be(zl),null;case 24:return l=null,e!==null&&(l=e.memoizedState.cache),t.memoizedState.cache!==l&&(t.flags|=2048),zt(ve),ne(t),null;case 25:return null}throw Error(d(156,t.tag))}function gh(e,t){switch(xi(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return zt(ve),Vl(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return jn(t),null;case 13:if(Dt(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(d(340));Qa()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return be(ye),null;case 4:return Vl(),null;case 10:return zt(t.type),null;case 22:case 23:return Dt(t),Ei(),e!==null&&be(zl),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return zt(ve),null;case 25:return null;default:return null}}function $r(e,t){switch(xi(t),t.tag){case 3:zt(ve),Vl();break;case 26:case 27:case 5:jn(t);break;case 4:Vl();break;case 13:Dt(t);break;case 19:be(ye);break;case 10:zt(t.type);break;case 22:case 23:Dt(t),Ei(),e!==null&&be(zl);break;case 24:zt(ve)}}var ph={getCacheForType:function(e){var t=ze(ve),l=t.data.get(e);return l===void 0&&(l=e(),t.data.set(e,l)),l}},yh=typeof WeakMap=="function"?WeakMap:Map,ue=0,I=null,G=null,w=0,ee=0,Ye=null,jt=!1,ga=!1,Sc=!1,Ut=0,ce=0,al=0,ql=0,Tc=0,tt=0,pa=0,dn=null,gt=null,xc=!1,Ac=0,xu=1/0,Au=null,nl=null,Du=!1,Bl=null,mn=0,Dc=0,Nc=null,hn=0,Ec=null;function Xe(){if(ue&2&&w!==0)return w&-w;if(R.T!==null){var e=ca;return e!==0?e:Uc()}return ys()}function Wr(){tt===0&&(tt=!(w&536870912)||K?ds():536870912);var e=$e.current;return e!==null&&(e.flags|=32),tt}function Oe(e,t,l){(e===I&&ee===2||e.cancelPendingCommit!==null)&&(ya(e,0),Ht(e,w,tt,!1)),_a(e,l),(!(ue&2)||e!==I)&&(e===I&&(!(ue&2)&&(ql|=l),ce===4&&Ht(e,w,tt,!1)),pt(e))}function Ir(e,t,l){if(ue&6)throw Error(d(327));var a=!l&&(t&60)===0&&(t&e.expiredLanes)===0||Oa(e,t),n=a?Sh(e,t):Oc(e,t,!0),u=a;do{if(n===0){ga&&!a&&Ht(e,t,0,!1);break}else if(n===6)Ht(e,t,0,!jt);else{if(l=e.current.alternate,u&&!vh(l)){n=Oc(e,t,!1),u=!1;continue}if(n===2){if(u=t,e.errorRecoveryDisabledLanes&u)var i=0;else i=e.pendingLanes&-536870913,i=i!==0?i:i&536870912?536870912:0;if(i!==0){t=i;e:{var c=e;n=dn;var s=c.current.memoizedState.isDehydrated;if(s&&(ya(c,i).flags|=256),i=Oc(c,i,!1),i!==2){if(Sc&&!s){c.errorRecoveryDisabledLanes|=u,ql|=u,n=4;break e}u=gt,gt=n,u!==null&&Mc(u)}n=i}if(u=!1,n!==2)continue}}if(n===1){ya(e,0),Ht(e,t,0,!0);break}e:{switch(a=e,n){case 0:case 1:throw Error(d(345));case 4:if((t&4194176)===t){Ht(a,t,tt,!jt);break e}break;case 2:gt=null;break;case 3:case 5:break;default:throw Error(d(329))}if(a.finishedWork=l,a.finishedLanes=t,(t&62914560)===t&&(u=Ac+300-ft(),10<u)){if(Ht(a,t,tt,!jt),qn(a,0)!==0)break e;a.timeoutHandle=Df(ef.bind(null,a,l,gt,Au,xc,t,tt,ql,pa,jt,2,-0,0),u);break e}ef(a,l,gt,Au,xc,t,tt,ql,pa,jt,0,-0,0)}}break}while(!0);pt(e)}function Mc(e){gt===null?gt=e:gt.push.apply(gt,e)}function ef(e,t,l,a,n,u,i,c,s,f,b,T,p){var v=t.subtreeFlags;if((v&8192||(v&16785408)===16785408)&&(Sn={stylesheets:null,count:0,unsuspend:Ih},Qr(t),t=tg(),t!==null)){e.cancelPendingCommit=t(sf.bind(null,e,l,a,n,i,c,s,1,T,p)),Ht(e,u,i,!f);return}sf(e,l,a,n,i,c,s,b,T,p)}function vh(e){for(var t=e;;){var l=t.tag;if((l===0||l===11||l===15)&&t.flags&16384&&(l=t.updateQueue,l!==null&&(l=l.stores,l!==null)))for(var a=0;a<l.length;a++){var n=l[a],u=n.getSnapshot;n=n.value;try{if(!qe(u(),n))return!1}catch{return!1}}if(l=t.child,t.subtreeFlags&16384&&l!==null)l.return=t,t=l;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Ht(e,t,l,a){t&=~Tc,t&=~ql,e.suspendedLanes|=t,e.pingedLanes&=~t,a&&(e.warmLanes|=t),a=e.expirationTimes;for(var n=t;0<n;){var u=31-Le(n),i=1<<u;a[u]=-1,n&=~i}l!==0&&hs(e,l,t)}function Nu(){return ue&6?!0:(gn(0),!1)}function zc(){if(G!==null){if(ee===0)var e=G.return;else e=G,Mt=jl=null,Ui(e),ua=null,Ja=0,e=G;for(;e!==null;)$r(e.alternate,e),e=e.return;G=null}}function ya(e,t){e.finishedWork=null,e.finishedLanes=0;var l=e.timeoutHandle;l!==-1&&(e.timeoutHandle=-1,qh(l)),l=e.cancelPendingCommit,l!==null&&(e.cancelPendingCommit=null,l()),zc(),I=e,G=l=ll(e.current,null),w=t,ee=0,Ye=null,jt=!1,ga=Oa(e,t),Sc=!1,pa=tt=Tc=ql=al=ce=0,gt=dn=null,xc=!1,t&8&&(t|=t&32);var a=e.entangledLanes;if(a!==0)for(e=e.entanglements,a&=t;0<a;){var n=31-Le(a),u=1<<n;t|=e[n],a&=~u}return Ut=t,Fn(),l}function tf(e,t){q=null,R.H=ht,t===Za?(t=po(),ee=3):t===mo?(t=po(),ee=4):ee=t===hr?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,Ye=t,G===null&&(ce=1,gu(e,Je(t,e.current)))}function lf(){var e=R.H;return R.H=ht,e===null?ht:e}function af(){var e=R.A;return R.A=ph,e}function Cc(){ce=4,jt||(w&4194176)!==w&&$e.current!==null||(ga=!0),!(al&134217727)&&!(ql&134217727)||I===null||Ht(I,w,tt,!1)}function Oc(e,t,l){var a=ue;ue|=2;var n=lf(),u=af();(I!==e||w!==t)&&(Au=null,ya(e,t)),t=!1;var i=ce;e:do try{if(ee!==0&&G!==null){var c=G,s=Ye;switch(ee){case 8:zc(),i=6;break e;case 3:case 2:case 6:$e.current===null&&(t=!0);var f=ee;if(ee=0,Ye=null,va(e,c,s,f),l&&ga){i=0;break e}break;default:f=ee,ee=0,Ye=null,va(e,c,s,f)}}bh(),i=ce;break}catch(b){tf(e,b)}while(!0);return t&&e.shellSuspendCounter++,Mt=jl=null,ue=a,R.H=n,R.A=u,G===null&&(I=null,w=0,Fn()),i}function bh(){for(;G!==null;)nf(G)}function Sh(e,t){var l=ue;ue|=2;var a=lf(),n=af();I!==e||w!==t?(Au=null,xu=ft()+500,ya(e,t)):ga=Oa(e,t);e:do try{if(ee!==0&&G!==null){t=G;var u=Ye;t:switch(ee){case 1:ee=0,Ye=null,va(e,t,u,1);break;case 2:if(ho(u)){ee=0,Ye=null,uf(t);break}t=function(){ee===2&&I===e&&(ee=7),pt(e)},u.then(t,t);break e;case 3:ee=7;break e;case 4:ee=5;break e;case 7:ho(u)?(ee=0,Ye=null,uf(t)):(ee=0,Ye=null,va(e,t,u,7));break;case 5:var i=null;switch(G.tag){case 26:i=G.memoizedState;case 5:case 27:var c=G;if(!i||Hf(i)){ee=0,Ye=null;var s=c.sibling;if(s!==null)G=s;else{var f=c.return;f!==null?(G=f,Eu(f)):G=null}break t}}ee=0,Ye=null,va(e,t,u,5);break;case 6:ee=0,Ye=null,va(e,t,u,6);break;case 8:zc(),ce=6;break e;default:throw Error(d(462))}}Th();break}catch(b){tf(e,b)}while(!0);return Mt=jl=null,R.H=a,R.A=n,ue=l,G!==null?0:(I=null,w=0,Fn(),ce)}function Th(){for(;G!==null&&!Vd();)nf(G)}function nf(e){var t=Er(e.alternate,e,Ut);e.memoizedProps=e.pendingProps,t===null?Eu(e):G=t}function uf(e){var t=e,l=t.alternate;switch(t.tag){case 15:case 0:t=Sr(l,t,t.pendingProps,t.type,void 0,w);break;case 11:t=Sr(l,t,t.pendingProps,t.type.render,t.ref,w);break;case 5:Ui(t);default:$r(l,t),t=G=Jr(t,Ut),t=Er(l,t,Ut)}e.memoizedProps=e.pendingProps,t===null?Eu(e):G=t}function va(e,t,l,a){Mt=jl=null,Ui(t),ua=null,Ja=0;var n=t.return;try{if(oh(e,n,t,l,w)){ce=1,gu(e,Je(l,e.current)),G=null;return}}catch(u){if(n!==null)throw G=n,u;ce=1,gu(e,Je(l,e.current)),G=null;return}t.flags&32768?(K||a===1?e=!0:ga||w&536870912?e=!1:(jt=e=!0,(a===2||a===3||a===6)&&(a=$e.current,a!==null&&a.tag===13&&(a.flags|=16384))),cf(t,e)):Eu(t)}function Eu(e){var t=e;do{if(t.flags&32768){cf(t,jt);return}e=t.return;var l=hh(t.alternate,t,Ut);if(l!==null){G=l;return}if(t=t.sibling,t!==null){G=t;return}G=t=e}while(t!==null);ce===0&&(ce=5)}function cf(e,t){do{var l=gh(e.alternate,e);if(l!==null){l.flags&=32767,G=l;return}if(l=e.return,l!==null&&(l.flags|=32768,l.subtreeFlags=0,l.deletions=null),!t&&(e=e.sibling,e!==null)){G=e;return}G=e=l}while(e!==null);ce=6,G=null}function sf(e,t,l,a,n,u,i,c,s,f){var b=R.T,T=Q.p;try{Q.p=2,R.T=null,xh(e,t,l,a,T,n,u,i,c,s,f)}finally{R.T=b,Q.p=T}}function xh(e,t,l,a,n,u,i,c){do ba();while(Bl!==null);if(ue&6)throw Error(d(327));var s=e.finishedWork;if(a=e.finishedLanes,s===null)return null;if(e.finishedWork=null,e.finishedLanes=0,s===e.current)throw Error(d(177));e.callbackNode=null,e.callbackPriority=0,e.cancelPendingCommit=null;var f=s.lanes|s.childLanes;if(f|=bi,em(e,a,f,u,i,c),e===I&&(G=I=null,w=0),!(s.subtreeFlags&10256)&&!(s.flags&10256)||Du||(Du=!0,Dc=f,Nc=l,Eh(Un,function(){return ba(),null})),l=(s.flags&15990)!==0,s.subtreeFlags&15990||l?(l=R.T,R.T=null,u=Q.p,Q.p=2,i=ue,ue|=4,fh(e,s),Xr(s,e),Zm(wc,e.containerInfo),Bu=!!Xc,wc=Xc=null,e.current=s,qr(e,s.alternate,s),Qd(),ue=i,Q.p=u,R.T=l):e.current=s,Du?(Du=!1,Bl=e,mn=a):of(e,f),f=e.pendingLanes,f===0&&(nl=null),Fd(s.stateNode),pt(e),t!==null)for(n=e.onRecoverableError,s=0;s<t.length;s++)f=t[s],n(f.value,{componentStack:f.stack});return mn&3&&ba(),f=e.pendingLanes,a&4194218&&f&42?e===Ec?hn++:(hn=0,Ec=e):hn=0,gn(0),null}function of(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,Pa(t)))}function ba(){if(Bl!==null){var e=Bl,t=Dc;Dc=0;var l=ps(mn),a=R.T,n=Q.p;try{if(Q.p=32>l?32:l,R.T=null,Bl===null)var u=!1;else{l=Nc,Nc=null;var i=Bl,c=mn;if(Bl=null,mn=0,ue&6)throw Error(d(331));var s=ue;if(ue|=4,Zr(i.current),Vr(i,i.current,c,l),ue=s,gn(0,!1),He&&typeof He.onPostCommitFiberRoot=="function")try{He.onPostCommitFiberRoot(Ca,i)}catch{}u=!0}return u}finally{Q.p=n,R.T=a,of(e,t)}}return!1}function rf(e,t,l){t=Je(l,t),t=Ji(e.stateNode,t,2),e=Wt(e,t,2),e!==null&&(_a(e,2),pt(e))}function W(e,t,l){if(e.tag===3)rf(e,e,l);else for(;t!==null;){if(t.tag===3){rf(t,e,l);break}else if(t.tag===1){var a=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof a.componentDidCatch=="function"&&(nl===null||!nl.has(a))){e=Je(l,e),l=dr(2),a=Wt(t,l,2),a!==null&&(mr(l,a,t,e),_a(a,2),pt(a));break}}t=t.return}}function _c(e,t,l){var a=e.pingCache;if(a===null){a=e.pingCache=new yh;var n=new Set;a.set(t,n)}else n=a.get(t),n===void 0&&(n=new Set,a.set(t,n));n.has(l)||(Sc=!0,n.add(l),e=Ah.bind(null,e,t,l),t.then(e,e))}function Ah(e,t,l){var a=e.pingCache;a!==null&&a.delete(t),e.pingedLanes|=e.suspendedLanes&l,e.warmLanes&=~l,I===e&&(w&l)===l&&(ce===4||ce===3&&(w&62914560)===w&&300>ft()-Ac?!(ue&2)&&ya(e,0):Tc|=l,pa===w&&(pa=0)),pt(e)}function ff(e,t){t===0&&(t=ms()),e=Qt(e,t),e!==null&&(_a(e,t),pt(e))}function Dh(e){var t=e.memoizedState,l=0;t!==null&&(l=t.retryLane),ff(e,l)}function Nh(e,t){var l=0;switch(e.tag){case 13:var a=e.stateNode,n=e.memoizedState;n!==null&&(l=n.retryLane);break;case 19:a=e.stateNode;break;case 22:a=e.stateNode._retryCache;break;default:throw Error(d(314))}a!==null&&a.delete(t),ff(e,l)}function Eh(e,t){return Fu(e,t)}var Mu=null,Sa=null,Rc=!1,zu=!1,jc=!1,Gl=0;function pt(e){e!==Sa&&e.next===null&&(Sa===null?Mu=Sa=e:Sa=Sa.next=e),zu=!0,Rc||(Rc=!0,zh(Mh))}function gn(e,t){if(!jc&&zu){jc=!0;do for(var l=!1,a=Mu;a!==null;){if(e!==0){var n=a.pendingLanes;if(n===0)var u=0;else{var i=a.suspendedLanes,c=a.pingedLanes;u=(1<<31-Le(42|e)+1)-1,u&=n&~(i&~c),u=u&201326677?u&201326677|1:u?u|2:0}u!==0&&(l=!0,hf(a,u))}else u=w,u=qn(a,a===I?u:0),!(u&3)||Oa(a,u)||(l=!0,hf(a,u));a=a.next}while(l);jc=!1}}function Mh(){zu=Rc=!1;var e=0;Gl!==0&&(Lh()&&(e=Gl),Gl=0);for(var t=ft(),l=null,a=Mu;a!==null;){var n=a.next,u=df(a,t);u===0?(a.next=null,l===null?Mu=n:l.next=n,n===null&&(Sa=l)):(l=a,(e!==0||u&3)&&(zu=!0)),a=n}gn(e)}function df(e,t){for(var l=e.suspendedLanes,a=e.pingedLanes,n=e.expirationTimes,u=e.pendingLanes&-62914561;0<u;){var i=31-Le(u),c=1<<i,s=n[i];s===-1?(!(c&l)||c&a)&&(n[i]=Id(c,t)):s<=t&&(e.expiredLanes|=c),u&=~c}if(t=I,l=w,l=qn(e,e===t?l:0),a=e.callbackNode,l===0||e===t&&ee===2||e.cancelPendingCommit!==null)return a!==null&&a!==null&&Pu(a),e.callbackNode=null,e.callbackPriority=0;if(!(l&3)||Oa(e,l)){if(t=l&-l,t===e.callbackPriority)return t;switch(a!==null&&Pu(a),ps(l)){case 2:case 8:l=rs;break;case 32:l=Un;break;case 268435456:l=fs;break;default:l=Un}return a=mf.bind(null,e),l=Fu(l,a),e.callbackPriority=t,e.callbackNode=l,t}return a!==null&&a!==null&&Pu(a),e.callbackPriority=2,e.callbackNode=null,2}function mf(e,t){var l=e.callbackNode;if(ba()&&e.callbackNode!==l)return null;var a=w;return a=qn(e,e===I?a:0),a===0?null:(Ir(e,a,t),df(e,ft()),e.callbackNode!=null&&e.callbackNode===l?mf.bind(null,e):null)}function hf(e,t){if(ba())return null;Ir(e,t,!0)}function zh(e){Bh(function(){ue&6?Fu(os,e):e()})}function Uc(){return Gl===0&&(Gl=ds()),Gl}function gf(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:wn(""+e)}function pf(e,t){var l=t.ownerDocument.createElement("input");return l.name=t.name,l.value=t.value,e.id&&l.setAttribute("form",e.id),t.parentNode.insertBefore(l,t),e=new FormData(e),l.parentNode.removeChild(l),e}function Ch(e,t,l,a,n){if(t==="submit"&&l&&l.stateNode===n){var u=gf((n[Re]||null).action),i=a.submitter;i&&(t=(t=i[Re]||null)?gf(t.formAction):i.getAttribute("formAction"),t!==null&&(u=t,i=null));var c=new Zn("action","action",null,a,n);e.push({event:c,listeners:[{instance:null,listener:function(){if(a.defaultPrevented){if(Gl!==0){var s=i?pf(n,i):new FormData(n);Vi(l,{pending:!0,data:s,method:n.method,action:u},null,s)}}else typeof u=="function"&&(c.preventDefault(),s=i?pf(n,i):new FormData(n),Vi(l,{pending:!0,data:s,method:n.method,action:u},u,s))},currentTarget:n}]})}}for(var Hc=0;Hc<io.length;Hc++){var Lc=io[Hc],Oh=Lc.toLowerCase(),_h=Lc[0].toUpperCase()+Lc.slice(1);nt(Oh,"on"+_h)}nt(to,"onAnimationEnd"),nt(lo,"onAnimationIteration"),nt(ao,"onAnimationStart"),nt("dblclick","onDoubleClick"),nt("focusin","onFocus"),nt("focusout","onBlur"),nt(Jm,"onTransitionRun"),nt(Fm,"onTransitionStart"),nt(Pm,"onTransitionCancel"),nt(no,"onTransitionEnd"),kl("onMouseEnter",["mouseout","mouseover"]),kl("onMouseLeave",["mouseout","mouseover"]),kl("onPointerEnter",["pointerout","pointerover"]),kl("onPointerLeave",["pointerout","pointerover"]),Sl("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Sl("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Sl("onBeforeInput",["compositionend","keypress","textInput","paste"]),Sl("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Sl("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Sl("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var pn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Rh=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(pn));function yf(e,t){t=(t&4)!==0;for(var l=0;l<e.length;l++){var a=e[l],n=a.event;a=a.listeners;e:{var u=void 0;if(t)for(var i=a.length-1;0<=i;i--){var c=a[i],s=c.instance,f=c.currentTarget;if(c=c.listener,s!==u&&n.isPropagationStopped())break e;u=c,n.currentTarget=f;try{u(n)}catch(b){hu(b)}n.currentTarget=null,u=s}else for(i=0;i<a.length;i++){if(c=a[i],s=c.instance,f=c.currentTarget,c=c.listener,s!==u&&n.isPropagationStopped())break e;u=c,n.currentTarget=f;try{u(n)}catch(b){hu(b)}n.currentTarget=null,u=s}}}}function X(e,t){var l=t[Wu];l===void 0&&(l=t[Wu]=new Set);var a=e+"__bubble";l.has(a)||(vf(t,e,2,!1),l.add(a))}function qc(e,t,l){var a=0;t&&(a|=4),vf(l,e,a,t)}var Cu="_reactListening"+Math.random().toString(36).slice(2);function Bc(e){if(!e[Cu]){e[Cu]=!0,bs.forEach(function(l){l!=="selectionchange"&&(Rh.has(l)||qc(l,!1,e),qc(l,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Cu]||(t[Cu]=!0,qc("selectionchange",!1,t))}}function vf(e,t,l,a){switch(Xf(t)){case 2:var n=ng;break;case 8:n=ug;break;default:n=$c}l=n.bind(null,t,l,e),n=void 0,!ii||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(n=!0),a?n!==void 0?e.addEventListener(t,l,{capture:!0,passive:n}):e.addEventListener(t,l,!0):n!==void 0?e.addEventListener(t,l,{passive:n}):e.addEventListener(t,l,!1)}function Gc(e,t,l,a,n){var u=a;if(!(t&1)&&!(t&2)&&a!==null)e:for(;;){if(a===null)return;var i=a.tag;if(i===3||i===4){var c=a.stateNode.containerInfo;if(c===n||c.nodeType===8&&c.parentNode===n)break;if(i===4)for(i=a.return;i!==null;){var s=i.tag;if((s===3||s===4)&&(s=i.stateNode.containerInfo,s===n||s.nodeType===8&&s.parentNode===n))return;i=i.return}for(;c!==null;){if(i=bl(c),i===null)return;if(s=i.tag,s===5||s===6||s===26||s===27){a=u=i;continue e}c=c.parentNode}}a=a.return}_s(function(){var f=u,b=ni(l),T=[];e:{var p=uo.get(e);if(p!==void 0){var v=Zn,M=e;switch(e){case"keypress":if(Qn(l)===0)break e;case"keydown":case"keyup":v=Nm;break;case"focusin":M="focus",v=ri;break;case"focusout":M="blur",v=ri;break;case"beforeblur":case"afterblur":v=ri;break;case"click":if(l.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":v=Us;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":v=mm;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":v=zm;break;case to:case lo:case ao:v=pm;break;case no:v=Om;break;case"scroll":case"scrollend":v=fm;break;case"wheel":v=Rm;break;case"copy":case"cut":case"paste":v=vm;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":v=Ls;break;case"toggle":case"beforetoggle":v=Um}var U=(t&4)!==0,se=!U&&(e==="scroll"||e==="scrollend"),h=U?p!==null?p+"Capture":null:p;U=[];for(var r=f,g;r!==null;){var S=r;if(g=S.stateNode,S=S.tag,S!==5&&S!==26&&S!==27||g===null||h===null||(S=Ua(r,h),S!=null&&U.push(yn(r,S,g))),se)break;r=r.return}0<U.length&&(p=new v(p,M,null,l,b),T.push({event:p,listeners:U}))}}if(!(t&7)){e:{if(p=e==="mouseover"||e==="pointerover",v=e==="mouseout"||e==="pointerout",p&&l!==ai&&(M=l.relatedTarget||l.fromElement)&&(bl(M)||M[Ql]))break e;if((v||p)&&(p=b.window===b?b:(p=b.ownerDocument)?p.defaultView||p.parentWindow:window,v?(M=l.relatedTarget||l.toElement,v=f,M=M?bl(M):null,M!==null&&(se=j(M),U=M.tag,M!==se||U!==5&&U!==27&&U!==6)&&(M=null)):(v=null,M=f),v!==M)){if(U=Us,S="onMouseLeave",h="onMouseEnter",r="mouse",(e==="pointerout"||e==="pointerover")&&(U=Ls,S="onPointerLeave",h="onPointerEnter",r="pointer"),se=v==null?p:ja(v),g=M==null?p:ja(M),p=new U(S,r+"leave",v,l,b),p.target=se,p.relatedTarget=g,S=null,bl(b)===f&&(U=new U(h,r+"enter",M,l,b),U.target=g,U.relatedTarget=se,S=U),se=S,v&&M)t:{for(U=v,h=M,r=0,g=U;g;g=Ta(g))r++;for(g=0,S=h;S;S=Ta(S))g++;for(;0<r-g;)U=Ta(U),r--;for(;0<g-r;)h=Ta(h),g--;for(;r--;){if(U===h||h!==null&&U===h.alternate)break t;U=Ta(U),h=Ta(h)}U=null}else U=null;v!==null&&bf(T,p,v,U,!1),M!==null&&se!==null&&bf(T,se,M,U,!0)}}e:{if(p=f?ja(f):window,v=p.nodeName&&p.nodeName.toLowerCase(),v==="select"||v==="input"&&p.type==="file")var D=Qs;else if(ws(p))if(Ks)D=Qm;else{D=wm;var B=Xm}else v=p.nodeName,!v||v.toLowerCase()!=="input"||p.type!=="checkbox"&&p.type!=="radio"?f&&li(f.elementType)&&(D=Qs):D=Vm;if(D&&(D=D(e,f))){Vs(T,D,l,b);break e}B&&B(e,p,f),e==="focusout"&&f&&p.type==="number"&&f.memoizedProps.value!=null&&ti(p,"number",p.value)}switch(B=f?ja(f):window,e){case"focusin":(ws(B)||B.contentEditable==="true")&&(Il=B,pi=f,wa=null);break;case"focusout":wa=pi=Il=null;break;case"mousedown":yi=!0;break;case"contextmenu":case"mouseup":case"dragend":yi=!1,Is(T,l,b);break;case"selectionchange":if(km)break;case"keydown":case"keyup":Is(T,l,b)}var z;if(di)e:{switch(e){case"compositionstart":var O="onCompositionStart";break e;case"compositionend":O="onCompositionEnd";break e;case"compositionupdate":O="onCompositionUpdate";break e}O=void 0}else Wl?Ys(e,l)&&(O="onCompositionEnd"):e==="keydown"&&l.keyCode===229&&(O="onCompositionStart");O&&(qs&&l.locale!=="ko"&&(Wl||O!=="onCompositionStart"?O==="onCompositionEnd"&&Wl&&(z=Rs()):(Vt=b,ci="value"in Vt?Vt.value:Vt.textContent,Wl=!0)),B=Ou(f,O),0<B.length&&(O=new Hs(O,e,null,l,b),T.push({event:O,listeners:B}),z?O.data=z:(z=Xs(l),z!==null&&(O.data=z)))),(z=Lm?qm(e,l):Bm(e,l))&&(O=Ou(f,"onBeforeInput"),0<O.length&&(B=new Hs("onBeforeInput","beforeinput",null,l,b),T.push({event:B,listeners:O}),B.data=z)),Ch(T,e,f,l,b)}yf(T,t)})}function yn(e,t,l){return{instance:e,listener:t,currentTarget:l}}function Ou(e,t){for(var l=t+"Capture",a=[];e!==null;){var n=e,u=n.stateNode;n=n.tag,n!==5&&n!==26&&n!==27||u===null||(n=Ua(e,l),n!=null&&a.unshift(yn(e,n,u)),n=Ua(e,t),n!=null&&a.push(yn(e,n,u))),e=e.return}return a}function Ta(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function bf(e,t,l,a,n){for(var u=t._reactName,i=[];l!==null&&l!==a;){var c=l,s=c.alternate,f=c.stateNode;if(c=c.tag,s!==null&&s===a)break;c!==5&&c!==26&&c!==27||f===null||(s=f,n?(f=Ua(l,u),f!=null&&i.unshift(yn(l,f,s))):n||(f=Ua(l,u),f!=null&&i.push(yn(l,f,s)))),l=l.return}i.length!==0&&e.push({event:t,listeners:i})}var jh=/\r\n?/g,Uh=/\u0000|\uFFFD/g;function Sf(e){return(typeof e=="string"?e:""+e).replace(jh,`
`).replace(Uh,"")}function Tf(e,t){return t=Sf(t),Sf(e)===t}function _u(){}function F(e,t,l,a,n,u){switch(l){case"children":typeof a=="string"?t==="body"||t==="textarea"&&a===""||Fl(e,a):(typeof a=="number"||typeof a=="bigint")&&t!=="body"&&Fl(e,""+a);break;case"className":Gn(e,"class",a);break;case"tabIndex":Gn(e,"tabindex",a);break;case"dir":case"role":case"viewBox":case"width":case"height":Gn(e,l,a);break;case"style":Cs(e,a,u);break;case"data":if(t!=="object"){Gn(e,"data",a);break}case"src":case"href":if(a===""&&(t!=="a"||l!=="href")){e.removeAttribute(l);break}if(a==null||typeof a=="function"||typeof a=="symbol"||typeof a=="boolean"){e.removeAttribute(l);break}a=wn(""+a),e.setAttribute(l,a);break;case"action":case"formAction":if(typeof a=="function"){e.setAttribute(l,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof u=="function"&&(l==="formAction"?(t!=="input"&&F(e,t,"name",n.name,n,null),F(e,t,"formEncType",n.formEncType,n,null),F(e,t,"formMethod",n.formMethod,n,null),F(e,t,"formTarget",n.formTarget,n,null)):(F(e,t,"encType",n.encType,n,null),F(e,t,"method",n.method,n,null),F(e,t,"target",n.target,n,null)));if(a==null||typeof a=="symbol"||typeof a=="boolean"){e.removeAttribute(l);break}a=wn(""+a),e.setAttribute(l,a);break;case"onClick":a!=null&&(e.onclick=_u);break;case"onScroll":a!=null&&X("scroll",e);break;case"onScrollEnd":a!=null&&X("scrollend",e);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(d(61));if(l=a.__html,l!=null){if(n.children!=null)throw Error(d(60));e.innerHTML=l}}break;case"multiple":e.multiple=a&&typeof a!="function"&&typeof a!="symbol";break;case"muted":e.muted=a&&typeof a!="function"&&typeof a!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(a==null||typeof a=="function"||typeof a=="boolean"||typeof a=="symbol"){e.removeAttribute("xlink:href");break}l=wn(""+a),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",l);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":a!=null&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(l,""+a):e.removeAttribute(l);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":a&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(l,""):e.removeAttribute(l);break;case"capture":case"download":a===!0?e.setAttribute(l,""):a!==!1&&a!=null&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(l,a):e.removeAttribute(l);break;case"cols":case"rows":case"size":case"span":a!=null&&typeof a!="function"&&typeof a!="symbol"&&!isNaN(a)&&1<=a?e.setAttribute(l,a):e.removeAttribute(l);break;case"rowSpan":case"start":a==null||typeof a=="function"||typeof a=="symbol"||isNaN(a)?e.removeAttribute(l):e.setAttribute(l,a);break;case"popover":X("beforetoggle",e),X("toggle",e),Bn(e,"popover",a);break;case"xlinkActuate":Tt(e,"http://www.w3.org/1999/xlink","xlink:actuate",a);break;case"xlinkArcrole":Tt(e,"http://www.w3.org/1999/xlink","xlink:arcrole",a);break;case"xlinkRole":Tt(e,"http://www.w3.org/1999/xlink","xlink:role",a);break;case"xlinkShow":Tt(e,"http://www.w3.org/1999/xlink","xlink:show",a);break;case"xlinkTitle":Tt(e,"http://www.w3.org/1999/xlink","xlink:title",a);break;case"xlinkType":Tt(e,"http://www.w3.org/1999/xlink","xlink:type",a);break;case"xmlBase":Tt(e,"http://www.w3.org/XML/1998/namespace","xml:base",a);break;case"xmlLang":Tt(e,"http://www.w3.org/XML/1998/namespace","xml:lang",a);break;case"xmlSpace":Tt(e,"http://www.w3.org/XML/1998/namespace","xml:space",a);break;case"is":Bn(e,"is",a);break;case"innerText":case"textContent":break;default:(!(2<l.length)||l[0]!=="o"&&l[0]!=="O"||l[1]!=="n"&&l[1]!=="N")&&(l=om.get(l)||l,Bn(e,l,a))}}function Yc(e,t,l,a,n,u){switch(l){case"style":Cs(e,a,u);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(d(61));if(l=a.__html,l!=null){if(n.children!=null)throw Error(d(60));e.innerHTML=l}}break;case"children":typeof a=="string"?Fl(e,a):(typeof a=="number"||typeof a=="bigint")&&Fl(e,""+a);break;case"onScroll":a!=null&&X("scroll",e);break;case"onScrollEnd":a!=null&&X("scrollend",e);break;case"onClick":a!=null&&(e.onclick=_u);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Ss.hasOwnProperty(l))e:{if(l[0]==="o"&&l[1]==="n"&&(n=l.endsWith("Capture"),t=l.slice(2,n?l.length-7:void 0),u=e[Re]||null,u=u!=null?u[l]:null,typeof u=="function"&&e.removeEventListener(t,u,n),typeof a=="function")){typeof u!="function"&&u!==null&&(l in e?e[l]=null:e.hasAttribute(l)&&e.removeAttribute(l)),e.addEventListener(t,a,n);break e}l in e?e[l]=a:a===!0?e.setAttribute(l,""):Bn(e,l,a)}}}function Ee(e,t,l){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":X("error",e),X("load",e);var a=!1,n=!1,u;for(u in l)if(l.hasOwnProperty(u)){var i=l[u];if(i!=null)switch(u){case"src":a=!0;break;case"srcSet":n=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(d(137,t));default:F(e,t,u,i,l,null)}}n&&F(e,t,"srcSet",l.srcSet,l,null),a&&F(e,t,"src",l.src,l,null);return;case"input":X("invalid",e);var c=u=i=n=null,s=null,f=null;for(a in l)if(l.hasOwnProperty(a)){var b=l[a];if(b!=null)switch(a){case"name":n=b;break;case"type":i=b;break;case"checked":s=b;break;case"defaultChecked":f=b;break;case"value":u=b;break;case"defaultValue":c=b;break;case"children":case"dangerouslySetInnerHTML":if(b!=null)throw Error(d(137,t));break;default:F(e,t,a,b,l,null)}}Ns(e,u,c,s,f,i,n,!1),Yn(e);return;case"select":X("invalid",e),a=i=u=null;for(n in l)if(l.hasOwnProperty(n)&&(c=l[n],c!=null))switch(n){case"value":u=c;break;case"defaultValue":i=c;break;case"multiple":a=c;default:F(e,t,n,c,l,null)}t=u,l=i,e.multiple=!!a,t!=null?Jl(e,!!a,t,!1):l!=null&&Jl(e,!!a,l,!0);return;case"textarea":X("invalid",e),u=n=a=null;for(i in l)if(l.hasOwnProperty(i)&&(c=l[i],c!=null))switch(i){case"value":a=c;break;case"defaultValue":n=c;break;case"children":u=c;break;case"dangerouslySetInnerHTML":if(c!=null)throw Error(d(91));break;default:F(e,t,i,c,l,null)}Ms(e,a,n,u),Yn(e);return;case"option":for(s in l)if(l.hasOwnProperty(s)&&(a=l[s],a!=null))switch(s){case"selected":e.selected=a&&typeof a!="function"&&typeof a!="symbol";break;default:F(e,t,s,a,l,null)}return;case"dialog":X("cancel",e),X("close",e);break;case"iframe":case"object":X("load",e);break;case"video":case"audio":for(a=0;a<pn.length;a++)X(pn[a],e);break;case"image":X("error",e),X("load",e);break;case"details":X("toggle",e);break;case"embed":case"source":case"link":X("error",e),X("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(f in l)if(l.hasOwnProperty(f)&&(a=l[f],a!=null))switch(f){case"children":case"dangerouslySetInnerHTML":throw Error(d(137,t));default:F(e,t,f,a,l,null)}return;default:if(li(t)){for(b in l)l.hasOwnProperty(b)&&(a=l[b],a!==void 0&&Yc(e,t,b,a,l,void 0));return}}for(c in l)l.hasOwnProperty(c)&&(a=l[c],a!=null&&F(e,t,c,a,l,null))}function Hh(e,t,l,a){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var n=null,u=null,i=null,c=null,s=null,f=null,b=null;for(v in l){var T=l[v];if(l.hasOwnProperty(v)&&T!=null)switch(v){case"checked":break;case"value":break;case"defaultValue":s=T;default:a.hasOwnProperty(v)||F(e,t,v,null,a,T)}}for(var p in a){var v=a[p];if(T=l[p],a.hasOwnProperty(p)&&(v!=null||T!=null))switch(p){case"type":u=v;break;case"name":n=v;break;case"checked":f=v;break;case"defaultChecked":b=v;break;case"value":i=v;break;case"defaultValue":c=v;break;case"children":case"dangerouslySetInnerHTML":if(v!=null)throw Error(d(137,t));break;default:v!==T&&F(e,t,p,v,a,T)}}ei(e,i,c,s,f,b,u,n);return;case"select":v=i=c=p=null;for(u in l)if(s=l[u],l.hasOwnProperty(u)&&s!=null)switch(u){case"value":break;case"multiple":v=s;default:a.hasOwnProperty(u)||F(e,t,u,null,a,s)}for(n in a)if(u=a[n],s=l[n],a.hasOwnProperty(n)&&(u!=null||s!=null))switch(n){case"value":p=u;break;case"defaultValue":c=u;break;case"multiple":i=u;default:u!==s&&F(e,t,n,u,a,s)}t=c,l=i,a=v,p!=null?Jl(e,!!l,p,!1):!!a!=!!l&&(t!=null?Jl(e,!!l,t,!0):Jl(e,!!l,l?[]:"",!1));return;case"textarea":v=p=null;for(c in l)if(n=l[c],l.hasOwnProperty(c)&&n!=null&&!a.hasOwnProperty(c))switch(c){case"value":break;case"children":break;default:F(e,t,c,null,a,n)}for(i in a)if(n=a[i],u=l[i],a.hasOwnProperty(i)&&(n!=null||u!=null))switch(i){case"value":p=n;break;case"defaultValue":v=n;break;case"children":break;case"dangerouslySetInnerHTML":if(n!=null)throw Error(d(91));break;default:n!==u&&F(e,t,i,n,a,u)}Es(e,p,v);return;case"option":for(var M in l)if(p=l[M],l.hasOwnProperty(M)&&p!=null&&!a.hasOwnProperty(M))switch(M){case"selected":e.selected=!1;break;default:F(e,t,M,null,a,p)}for(s in a)if(p=a[s],v=l[s],a.hasOwnProperty(s)&&p!==v&&(p!=null||v!=null))switch(s){case"selected":e.selected=p&&typeof p!="function"&&typeof p!="symbol";break;default:F(e,t,s,p,a,v)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var U in l)p=l[U],l.hasOwnProperty(U)&&p!=null&&!a.hasOwnProperty(U)&&F(e,t,U,null,a,p);for(f in a)if(p=a[f],v=l[f],a.hasOwnProperty(f)&&p!==v&&(p!=null||v!=null))switch(f){case"children":case"dangerouslySetInnerHTML":if(p!=null)throw Error(d(137,t));break;default:F(e,t,f,p,a,v)}return;default:if(li(t)){for(var se in l)p=l[se],l.hasOwnProperty(se)&&p!==void 0&&!a.hasOwnProperty(se)&&Yc(e,t,se,void 0,a,p);for(b in a)p=a[b],v=l[b],!a.hasOwnProperty(b)||p===v||p===void 0&&v===void 0||Yc(e,t,b,p,a,v);return}}for(var h in l)p=l[h],l.hasOwnProperty(h)&&p!=null&&!a.hasOwnProperty(h)&&F(e,t,h,null,a,p);for(T in a)p=a[T],v=l[T],!a.hasOwnProperty(T)||p===v||p==null&&v==null||F(e,t,T,p,a,v)}var Xc=null,wc=null;function Ru(e){return e.nodeType===9?e:e.ownerDocument}function xf(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function Af(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function Vc(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Qc=null;function Lh(){var e=window.event;return e&&e.type==="popstate"?e===Qc?!1:(Qc=e,!0):(Qc=null,!1)}var Df=typeof setTimeout=="function"?setTimeout:void 0,qh=typeof clearTimeout=="function"?clearTimeout:void 0,Nf=typeof Promise=="function"?Promise:void 0,Bh=typeof queueMicrotask=="function"?queueMicrotask:typeof Nf<"u"?function(e){return Nf.resolve(null).then(e).catch(Gh)}:Df;function Gh(e){setTimeout(function(){throw e})}function Kc(e,t){var l=t,a=0;do{var n=l.nextSibling;if(e.removeChild(l),n&&n.nodeType===8)if(l=n.data,l==="/$"){if(a===0){e.removeChild(n),Nn(t);return}a--}else l!=="$"&&l!=="$?"&&l!=="$!"||a++;l=n}while(l);Nn(t)}function Zc(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var l=t;switch(t=t.nextSibling,l.nodeName){case"HTML":case"HEAD":case"BODY":Zc(l),Iu(l);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(l.rel.toLowerCase()==="stylesheet")continue}e.removeChild(l)}}function Yh(e,t,l,a){for(;e.nodeType===1;){var n=l;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!a&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(a){if(!e[Ra])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(u=e.getAttribute("rel"),u==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(u!==n.rel||e.getAttribute("href")!==(n.href==null?null:n.href)||e.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin)||e.getAttribute("title")!==(n.title==null?null:n.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(u=e.getAttribute("src"),(u!==(n.src==null?null:n.src)||e.getAttribute("type")!==(n.type==null?null:n.type)||e.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin))&&u&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var u=n.name==null?null:""+n.name;if(n.type==="hidden"&&e.getAttribute("name")===u)return e}else return e;if(e=ct(e.nextSibling),e===null)break}return null}function Xh(e,t,l){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!l||(e=ct(e.nextSibling),e===null))return null;return e}function ct(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}function Ef(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var l=e.data;if(l==="$"||l==="$!"||l==="$?"){if(t===0)return e;t--}else l==="/$"&&t++}e=e.previousSibling}return null}function Mf(e,t,l){switch(t=Ru(l),e){case"html":if(e=t.documentElement,!e)throw Error(d(452));return e;case"head":if(e=t.head,!e)throw Error(d(453));return e;case"body":if(e=t.body,!e)throw Error(d(454));return e;default:throw Error(d(451))}}var lt=new Map,zf=new Set;function ju(e){return typeof e.getRootNode=="function"?e.getRootNode():e.ownerDocument}var Lt=Q.d;Q.d={f:wh,r:Vh,D:Qh,C:Kh,L:Zh,m:kh,X:Fh,S:Jh,M:Ph};function wh(){var e=Lt.f(),t=Nu();return e||t}function Vh(e){var t=Kl(e);t!==null&&t.tag===5&&t.type==="form"?er(t):Lt.r(e)}var xa=typeof document>"u"?null:document;function Cf(e,t,l){var a=xa;if(a&&typeof t=="string"&&t){var n=Ze(t);n='link[rel="'+e+'"][href="'+n+'"]',typeof l=="string"&&(n+='[crossorigin="'+l+'"]'),zf.has(n)||(zf.add(n),e={rel:e,crossOrigin:l,href:t},a.querySelector(n)===null&&(t=a.createElement("link"),Ee(t,"link",e),Se(t),a.head.appendChild(t)))}}function Qh(e){Lt.D(e),Cf("dns-prefetch",e,null)}function Kh(e,t){Lt.C(e,t),Cf("preconnect",e,t)}function Zh(e,t,l){Lt.L(e,t,l);var a=xa;if(a&&e&&t){var n='link[rel="preload"][as="'+Ze(t)+'"]';t==="image"&&l&&l.imageSrcSet?(n+='[imagesrcset="'+Ze(l.imageSrcSet)+'"]',typeof l.imageSizes=="string"&&(n+='[imagesizes="'+Ze(l.imageSizes)+'"]')):n+='[href="'+Ze(e)+'"]';var u=n;switch(t){case"style":u=Aa(e);break;case"script":u=Da(e)}lt.has(u)||(e=k({rel:"preload",href:t==="image"&&l&&l.imageSrcSet?void 0:e,as:t},l),lt.set(u,e),a.querySelector(n)!==null||t==="style"&&a.querySelector(vn(u))||t==="script"&&a.querySelector(bn(u))||(t=a.createElement("link"),Ee(t,"link",e),Se(t),a.head.appendChild(t)))}}function kh(e,t){Lt.m(e,t);var l=xa;if(l&&e){var a=t&&typeof t.as=="string"?t.as:"script",n='link[rel="modulepreload"][as="'+Ze(a)+'"][href="'+Ze(e)+'"]',u=n;switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":u=Da(e)}if(!lt.has(u)&&(e=k({rel:"modulepreload",href:e},t),lt.set(u,e),l.querySelector(n)===null)){switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(l.querySelector(bn(u)))return}a=l.createElement("link"),Ee(a,"link",e),Se(a),l.head.appendChild(a)}}}function Jh(e,t,l){Lt.S(e,t,l);var a=xa;if(a&&e){var n=Zl(a).hoistableStyles,u=Aa(e);t=t||"default";var i=n.get(u);if(!i){var c={loading:0,preload:null};if(i=a.querySelector(vn(u)))c.loading=5;else{e=k({rel:"stylesheet",href:e,"data-precedence":t},l),(l=lt.get(u))&&kc(e,l);var s=i=a.createElement("link");Se(s),Ee(s,"link",e),s._p=new Promise(function(f,b){s.onload=f,s.onerror=b}),s.addEventListener("load",function(){c.loading|=1}),s.addEventListener("error",function(){c.loading|=2}),c.loading|=4,Uu(i,t,a)}i={type:"stylesheet",instance:i,count:1,state:c},n.set(u,i)}}}function Fh(e,t){Lt.X(e,t);var l=xa;if(l&&e){var a=Zl(l).hoistableScripts,n=Da(e),u=a.get(n);u||(u=l.querySelector(bn(n)),u||(e=k({src:e,async:!0},t),(t=lt.get(n))&&Jc(e,t),u=l.createElement("script"),Se(u),Ee(u,"link",e),l.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},a.set(n,u))}}function Ph(e,t){Lt.M(e,t);var l=xa;if(l&&e){var a=Zl(l).hoistableScripts,n=Da(e),u=a.get(n);u||(u=l.querySelector(bn(n)),u||(e=k({src:e,async:!0,type:"module"},t),(t=lt.get(n))&&Jc(e,t),u=l.createElement("script"),Se(u),Ee(u,"link",e),l.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},a.set(n,u))}}function Of(e,t,l,a){var n=(n=Yt.current)?ju(n):null;if(!n)throw Error(d(446));switch(e){case"meta":case"title":return null;case"style":return typeof l.precedence=="string"&&typeof l.href=="string"?(t=Aa(l.href),l=Zl(n).hoistableStyles,a=l.get(t),a||(a={type:"style",instance:null,count:0,state:null},l.set(t,a)),a):{type:"void",instance:null,count:0,state:null};case"link":if(l.rel==="stylesheet"&&typeof l.href=="string"&&typeof l.precedence=="string"){e=Aa(l.href);var u=Zl(n).hoistableStyles,i=u.get(e);if(i||(n=n.ownerDocument||n,i={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},u.set(e,i),(u=n.querySelector(vn(e)))&&!u._p&&(i.instance=u,i.state.loading=5),lt.has(e)||(l={rel:"preload",as:"style",href:l.href,crossOrigin:l.crossOrigin,integrity:l.integrity,media:l.media,hrefLang:l.hrefLang,referrerPolicy:l.referrerPolicy},lt.set(e,l),u||$h(n,e,l,i.state))),t&&a===null)throw Error(d(528,""));return i}if(t&&a!==null)throw Error(d(529,""));return null;case"script":return t=l.async,l=l.src,typeof l=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=Da(l),l=Zl(n).hoistableScripts,a=l.get(t),a||(a={type:"script",instance:null,count:0,state:null},l.set(t,a)),a):{type:"void",instance:null,count:0,state:null};default:throw Error(d(444,e))}}function Aa(e){return'href="'+Ze(e)+'"'}function vn(e){return'link[rel="stylesheet"]['+e+"]"}function _f(e){return k({},e,{"data-precedence":e.precedence,precedence:null})}function $h(e,t,l,a){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?a.loading=1:(t=e.createElement("link"),a.preload=t,t.addEventListener("load",function(){return a.loading|=1}),t.addEventListener("error",function(){return a.loading|=2}),Ee(t,"link",l),Se(t),e.head.appendChild(t))}function Da(e){return'[src="'+Ze(e)+'"]'}function bn(e){return"script[async]"+e}function Rf(e,t,l){if(t.count++,t.instance===null)switch(t.type){case"style":var a=e.querySelector('style[data-href~="'+Ze(l.href)+'"]');if(a)return t.instance=a,Se(a),a;var n=k({},l,{"data-href":l.href,"data-precedence":l.precedence,href:null,precedence:null});return a=(e.ownerDocument||e).createElement("style"),Se(a),Ee(a,"style",n),Uu(a,l.precedence,e),t.instance=a;case"stylesheet":n=Aa(l.href);var u=e.querySelector(vn(n));if(u)return t.state.loading|=4,t.instance=u,Se(u),u;a=_f(l),(n=lt.get(n))&&kc(a,n),u=(e.ownerDocument||e).createElement("link"),Se(u);var i=u;return i._p=new Promise(function(c,s){i.onload=c,i.onerror=s}),Ee(u,"link",a),t.state.loading|=4,Uu(u,l.precedence,e),t.instance=u;case"script":return u=Da(l.src),(n=e.querySelector(bn(u)))?(t.instance=n,Se(n),n):(a=l,(n=lt.get(u))&&(a=k({},l),Jc(a,n)),e=e.ownerDocument||e,n=e.createElement("script"),Se(n),Ee(n,"link",a),e.head.appendChild(n),t.instance=n);case"void":return null;default:throw Error(d(443,t.type))}else t.type==="stylesheet"&&!(t.state.loading&4)&&(a=t.instance,t.state.loading|=4,Uu(a,l.precedence,e));return t.instance}function Uu(e,t,l){for(var a=l.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),n=a.length?a[a.length-1]:null,u=n,i=0;i<a.length;i++){var c=a[i];if(c.dataset.precedence===t)u=c;else if(u!==n)break}u?u.parentNode.insertBefore(e,u.nextSibling):(t=l.nodeType===9?l.head:l,t.insertBefore(e,t.firstChild))}function kc(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function Jc(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var Hu=null;function jf(e,t,l){if(Hu===null){var a=new Map,n=Hu=new Map;n.set(l,a)}else n=Hu,a=n.get(l),a||(a=new Map,n.set(l,a));if(a.has(e))return a;for(a.set(e,null),l=l.getElementsByTagName(e),n=0;n<l.length;n++){var u=l[n];if(!(u[Ra]||u[Me]||e==="link"&&u.getAttribute("rel")==="stylesheet")&&u.namespaceURI!=="http://www.w3.org/2000/svg"){var i=u.getAttribute(t)||"";i=e+i;var c=a.get(i);c?c.push(u):a.set(i,[u])}}return a}function Uf(e,t,l){e=e.ownerDocument||e,e.head.insertBefore(l,t==="title"?e.querySelector("head > title"):null)}function Wh(e,t,l){if(l===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function Hf(e){return!(e.type==="stylesheet"&&!(e.state.loading&3))}var Sn=null;function Ih(){}function eg(e,t,l){if(Sn===null)throw Error(d(475));var a=Sn;if(t.type==="stylesheet"&&(typeof l.media!="string"||matchMedia(l.media).matches!==!1)&&!(t.state.loading&4)){if(t.instance===null){var n=Aa(l.href),u=e.querySelector(vn(n));if(u){e=u._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(a.count++,a=Lu.bind(a),e.then(a,a)),t.state.loading|=4,t.instance=u,Se(u);return}u=e.ownerDocument||e,l=_f(l),(n=lt.get(n))&&kc(l,n),u=u.createElement("link"),Se(u);var i=u;i._p=new Promise(function(c,s){i.onload=c,i.onerror=s}),Ee(u,"link",l),t.instance=u}a.stylesheets===null&&(a.stylesheets=new Map),a.stylesheets.set(t,e),(e=t.state.preload)&&!(t.state.loading&3)&&(a.count++,t=Lu.bind(a),e.addEventListener("load",t),e.addEventListener("error",t))}}function tg(){if(Sn===null)throw Error(d(475));var e=Sn;return e.stylesheets&&e.count===0&&Fc(e,e.stylesheets),0<e.count?function(t){var l=setTimeout(function(){if(e.stylesheets&&Fc(e,e.stylesheets),e.unsuspend){var a=e.unsuspend;e.unsuspend=null,a()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(l)}}:null}function Lu(){if(this.count--,this.count===0){if(this.stylesheets)Fc(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var qu=null;function Fc(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,qu=new Map,t.forEach(lg,e),qu=null,Lu.call(e))}function lg(e,t){if(!(t.state.loading&4)){var l=qu.get(e);if(l)var a=l.get(null);else{l=new Map,qu.set(e,l);for(var n=e.querySelectorAll("link[data-precedence],style[data-precedence]"),u=0;u<n.length;u++){var i=n[u];(i.nodeName==="LINK"||i.getAttribute("media")!=="not all")&&(l.set(i.dataset.precedence,i),a=i)}a&&l.set(null,a)}n=t.instance,i=n.getAttribute("data-precedence"),u=l.get(i)||a,u===a&&l.set(null,n),l.set(i,n),this.count++,a=Lu.bind(this),n.addEventListener("load",a),n.addEventListener("error",a),u?u.parentNode.insertBefore(n,u.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(n,e.firstChild)),t.state.loading|=4}}var Tn={$$typeof:pe,Provider:null,Consumer:null,_currentValue:Qe,_currentValue2:Qe,_threadCount:0};function ag(e,t,l,a,n,u,i,c){this.tag=1,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=$u(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.finishedLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=$u(0),this.hiddenUpdates=$u(null),this.identifierPrefix=a,this.onUncaughtError=n,this.onCaughtError=u,this.onRecoverableError=i,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=c,this.incompleteTransitions=new Map}function Lf(e,t,l,a,n,u,i,c,s,f,b,T){return e=new ag(e,t,l,i,c,s,f,T),t=1,u===!0&&(t|=24),u=et(3,null,null,t),e.current=u,u.stateNode=e,t=Mi(),t.refCount++,e.pooledCache=t,t.refCount++,u.memoizedState={element:a,isDehydrated:l,cache:t},cc(u),e}function qf(e){return e?(e=la,e):la}function Bf(e,t,l,a,n,u){n=qf(n),a.context===null?a.context=n:a.pendingContext=n,a=$t(t),a.payload={element:l},u=u===void 0?null:u,u!==null&&(a.callback=u),l=Wt(e,a,t),l!==null&&(Oe(l,e,t),an(l,e,t))}function Gf(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var l=e.retryLane;e.retryLane=l!==0&&l<t?l:t}}function Pc(e,t){Gf(e,t),(e=e.alternate)&&Gf(e,t)}function Yf(e){if(e.tag===13){var t=Qt(e,67108864);t!==null&&Oe(t,e,67108864),Pc(e,67108864)}}var Bu=!0;function ng(e,t,l,a){var n=R.T;R.T=null;var u=Q.p;try{Q.p=2,$c(e,t,l,a)}finally{Q.p=u,R.T=n}}function ug(e,t,l,a){var n=R.T;R.T=null;var u=Q.p;try{Q.p=8,$c(e,t,l,a)}finally{Q.p=u,R.T=n}}function $c(e,t,l,a){if(Bu){var n=Wc(a);if(n===null)Gc(e,t,a,Gu,l),wf(e,a);else if(cg(n,e,t,l,a))a.stopPropagation();else if(wf(e,a),t&4&&-1<ig.indexOf(e)){for(;n!==null;){var u=Kl(n);if(u!==null)switch(u.tag){case 3:if(u=u.stateNode,u.current.memoizedState.isDehydrated){var i=vl(u.pendingLanes);if(i!==0){var c=u;for(c.pendingLanes|=2,c.entangledLanes|=2;i;){var s=1<<31-Le(i);c.entanglements[1]|=s,i&=~s}pt(u),!(ue&6)&&(xu=ft()+500,gn(0))}}break;case 13:c=Qt(u,2),c!==null&&Oe(c,u,2),Nu(),Pc(u,2)}if(u=Wc(a),u===null&&Gc(e,t,a,Gu,l),u===n)break;n=u}n!==null&&a.stopPropagation()}else Gc(e,t,a,null,l)}}function Wc(e){return e=ni(e),Ic(e)}var Gu=null;function Ic(e){if(Gu=null,e=bl(e),e!==null){var t=j(e);if(t===null)e=null;else{var l=t.tag;if(l===13){if(e=te(t),e!==null)return e;e=null}else if(l===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return Gu=e,null}function Xf(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Kd()){case os:return 2;case rs:return 8;case Un:case Zd:return 32;case fs:return 268435456;default:return 32}default:return 32}}var es=!1,ul=null,il=null,cl=null,xn=new Map,An=new Map,sl=[],ig="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function wf(e,t){switch(e){case"focusin":case"focusout":ul=null;break;case"dragenter":case"dragleave":il=null;break;case"mouseover":case"mouseout":cl=null;break;case"pointerover":case"pointerout":xn.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":An.delete(t.pointerId)}}function Dn(e,t,l,a,n,u){return e===null||e.nativeEvent!==u?(e={blockedOn:t,domEventName:l,eventSystemFlags:a,nativeEvent:u,targetContainers:[n]},t!==null&&(t=Kl(t),t!==null&&Yf(t)),e):(e.eventSystemFlags|=a,t=e.targetContainers,n!==null&&t.indexOf(n)===-1&&t.push(n),e)}function cg(e,t,l,a,n){switch(t){case"focusin":return ul=Dn(ul,e,t,l,a,n),!0;case"dragenter":return il=Dn(il,e,t,l,a,n),!0;case"mouseover":return cl=Dn(cl,e,t,l,a,n),!0;case"pointerover":var u=n.pointerId;return xn.set(u,Dn(xn.get(u)||null,e,t,l,a,n)),!0;case"gotpointercapture":return u=n.pointerId,An.set(u,Dn(An.get(u)||null,e,t,l,a,n)),!0}return!1}function Vf(e){var t=bl(e.target);if(t!==null){var l=j(t);if(l!==null){if(t=l.tag,t===13){if(t=te(l),t!==null){e.blockedOn=t,tm(e.priority,function(){if(l.tag===13){var a=Xe(),n=Qt(l,a);n!==null&&Oe(n,l,a),Pc(l,a)}});return}}else if(t===3&&l.stateNode.current.memoizedState.isDehydrated){e.blockedOn=l.tag===3?l.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Yu(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var l=Wc(e.nativeEvent);if(l===null){l=e.nativeEvent;var a=new l.constructor(l.type,l);ai=a,l.target.dispatchEvent(a),ai=null}else return t=Kl(l),t!==null&&Yf(t),e.blockedOn=l,!1;t.shift()}return!0}function Qf(e,t,l){Yu(e)&&l.delete(t)}function sg(){es=!1,ul!==null&&Yu(ul)&&(ul=null),il!==null&&Yu(il)&&(il=null),cl!==null&&Yu(cl)&&(cl=null),xn.forEach(Qf),An.forEach(Qf)}function Xu(e,t){e.blockedOn===t&&(e.blockedOn=null,es||(es=!0,m.unstable_scheduleCallback(m.unstable_NormalPriority,sg)))}var wu=null;function Kf(e){wu!==e&&(wu=e,m.unstable_scheduleCallback(m.unstable_NormalPriority,function(){wu===e&&(wu=null);for(var t=0;t<e.length;t+=3){var l=e[t],a=e[t+1],n=e[t+2];if(typeof a!="function"){if(Ic(a||l)===null)continue;break}var u=Kl(l);u!==null&&(e.splice(t,3),t-=3,Vi(u,{pending:!0,data:n,method:l.method,action:a},a,n))}}))}function Nn(e){function t(s){return Xu(s,e)}ul!==null&&Xu(ul,e),il!==null&&Xu(il,e),cl!==null&&Xu(cl,e),xn.forEach(t),An.forEach(t);for(var l=0;l<sl.length;l++){var a=sl[l];a.blockedOn===e&&(a.blockedOn=null)}for(;0<sl.length&&(l=sl[0],l.blockedOn===null);)Vf(l),l.blockedOn===null&&sl.shift();if(l=(e.ownerDocument||e).$$reactFormReplay,l!=null)for(a=0;a<l.length;a+=3){var n=l[a],u=l[a+1],i=n[Re]||null;if(typeof u=="function")i||Kf(l);else if(i){var c=null;if(u&&u.hasAttribute("formAction")){if(n=u,i=u[Re]||null)c=i.formAction;else if(Ic(n)!==null)continue}else c=i.action;typeof c=="function"?l[a+1]=c:(l.splice(a,3),a-=3),Kf(l)}}}function ts(e){this._internalRoot=e}Vu.prototype.render=ts.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(d(409));var l=t.current,a=Xe();Bf(l,a,e,t,null,null)},Vu.prototype.unmount=ts.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;e.tag===0&&ba(),Bf(e.current,2,null,e,null,null),Nu(),t[Ql]=null}};function Vu(e){this._internalRoot=e}Vu.prototype.unstable_scheduleHydration=function(e){if(e){var t=ys();e={blockedOn:null,target:e,priority:t};for(var l=0;l<sl.length&&t!==0&&t<sl[l].priority;l++);sl.splice(l,0,e),l===0&&Vf(e)}};var Zf=y.version;if(Zf!=="19.0.0")throw Error(d(527,Zf,"19.0.0"));Q.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(d(188)):(e=Object.keys(e).join(","),Error(d(268,e)));return e=Xl(t),e=e!==null?yl(e):null,e=e===null?null:e.stateNode,e};var og={bundleType:0,version:"19.0.0",rendererPackageName:"react-dom",currentDispatcherRef:R,findFiberByHostInstance:bl,reconcilerVersion:"19.0.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Qu=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Qu.isDisabled&&Qu.supportsFiber)try{Ca=Qu.inject(og),He=Qu}catch{}}return Mn.createRoot=function(e,t){if(!N(e))throw Error(d(299));var l=!1,a="",n=sr,u=or,i=rr,c=null;return t!=null&&(t.unstable_strictMode===!0&&(l=!0),t.identifierPrefix!==void 0&&(a=t.identifierPrefix),t.onUncaughtError!==void 0&&(n=t.onUncaughtError),t.onCaughtError!==void 0&&(u=t.onCaughtError),t.onRecoverableError!==void 0&&(i=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(c=t.unstable_transitionCallbacks)),t=Lf(e,1,!1,null,null,l,a,n,u,i,c,null),e[Ql]=t.current,Bc(e.nodeType===8?e.parentNode:e),new ts(t)},Mn.hydrateRoot=function(e,t,l){if(!N(e))throw Error(d(299));var a=!1,n="",u=sr,i=or,c=rr,s=null,f=null;return l!=null&&(l.unstable_strictMode===!0&&(a=!0),l.identifierPrefix!==void 0&&(n=l.identifierPrefix),l.onUncaughtError!==void 0&&(u=l.onUncaughtError),l.onCaughtError!==void 0&&(i=l.onCaughtError),l.onRecoverableError!==void 0&&(c=l.onRecoverableError),l.unstable_transitionCallbacks!==void 0&&(s=l.unstable_transitionCallbacks),l.formState!==void 0&&(f=l.formState)),t=Lf(e,1,!0,t,l??null,a,n,u,i,c,s,f),t.context=qf(null),l=t.current,a=Xe(),n=$t(a),n.callback=null,Wt(l,n,a),t.current.lanes=a,_a(t,a),pt(t),e[Ql]=t.current,Bc(e),new Vu(t)},Mn.version="19.0.0",Mn}var td;function tp(){if(td)return ls.exports;td=1;function m(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(m)}catch(y){console.error(y)}}return m(),ls.exports=ep(),ls.exports}var lp=tp();const ap={visibleTabs:{},setTabVisibility:()=>{},isTabVisible:()=>!1},pd=E.createContext(ap),np=({children:m})=>{const y=we.use.currentTab(),[x,d]=E.useState(()=>({documents:!0,"knowledge-graph":!0,retrieval:!0,api:!0}));E.useEffect(()=>{d(_=>({..._,documents:!0,"knowledge-graph":!0,retrieval:!0,api:!0}))},[y]);const N=E.useMemo(()=>({visibleTabs:x,setTabVisibility:(_,H)=>{d(P=>({...P,[_]:H}))},isTabVisible:_=>!!x[_]}),[x]);return o.jsx(pd.Provider,{value:N,children:m})};var yd="AlertDialog",[up,Qy]=hg(yd,[ld]),Gt=ld(),vd=m=>{const{__scopeAlertDialog:y,...x}=m,d=Gt(y);return o.jsx(Sg,{...d,...x,modal:!0})};vd.displayName=yd;var ip="AlertDialogTrigger",cp=E.forwardRef((m,y)=>{const{__scopeAlertDialog:x,...d}=m,N=Gt(x);return o.jsx(Tg,{...N,...d,ref:y})});cp.displayName=ip;var sp="AlertDialogPortal",bd=m=>{const{__scopeAlertDialog:y,...x}=m,d=Gt(y);return o.jsx(dg,{...d,...x})};bd.displayName=sp;var op="AlertDialogOverlay",Sd=E.forwardRef((m,y)=>{const{__scopeAlertDialog:x,...d}=m,N=Gt(x);return o.jsx(fg,{...N,...d,ref:y})});Sd.displayName=op;var Na="AlertDialogContent",[rp,fp]=up(Na),Td=E.forwardRef((m,y)=>{const{__scopeAlertDialog:x,children:d,...N}=m,_=Gt(x),H=E.useRef(null),P=ad(y,H),Y=E.useRef(null);return o.jsx(mg,{contentName:Na,titleName:xd,docsSlug:"alert-dialog",children:o.jsx(rp,{scope:x,cancelRef:Y,children:o.jsxs(gg,{role:"alertdialog",..._,...N,ref:P,onOpenAutoFocus:pg(N.onOpenAutoFocus,$=>{var he;$.preventDefault(),(he=Y.current)==null||he.focus({preventScroll:!0})}),onPointerDownOutside:$=>$.preventDefault(),onInteractOutside:$=>$.preventDefault(),children:[o.jsx(yg,{children:d}),o.jsx(mp,{contentRef:H})]})})})});Td.displayName=Na;var xd="AlertDialogTitle",Ad=E.forwardRef((m,y)=>{const{__scopeAlertDialog:x,...d}=m,N=Gt(x);return o.jsx(vg,{...N,...d,ref:y})});Ad.displayName=xd;var Dd="AlertDialogDescription",Nd=E.forwardRef((m,y)=>{const{__scopeAlertDialog:x,...d}=m,N=Gt(x);return o.jsx(bg,{...N,...d,ref:y})});Nd.displayName=Dd;var dp="AlertDialogAction",Ed=E.forwardRef((m,y)=>{const{__scopeAlertDialog:x,...d}=m,N=Gt(x);return o.jsx(nd,{...N,...d,ref:y})});Ed.displayName=dp;var Md="AlertDialogCancel",zd=E.forwardRef((m,y)=>{const{__scopeAlertDialog:x,...d}=m,{cancelRef:N}=fp(Md,x),_=Gt(x),H=ad(y,N);return o.jsx(nd,{..._,...d,ref:H})});zd.displayName=Md;var mp=({contentRef:m})=>{const y=`\`${Na}\` requires a description for the component to be accessible for screen reader users.

You can add a description to the \`${Na}\` by passing a \`${Dd}\` component as a child, which also benefits sighted users by adding visible context to the dialog.

Alternatively, you can use your own component as a description by assigning it an \`id\` and passing the same value to the \`aria-describedby\` prop in \`${Na}\`. If the description is confusing or duplicative for sighted users, you can use the \`@radix-ui/react-visually-hidden\` primitive as a wrapper around your description component.

For more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;return E.useEffect(()=>{var d;document.getElementById((d=m.current)==null?void 0:d.getAttribute("aria-describedby"))||console.warn(y)},[y,m]),null},hp=vd,gp=bd,Cd=Sd,Od=Td,_d=Ed,Rd=zd,jd=Ad,Ud=Nd;const pp=hp,yp=gp,Hd=E.forwardRef(({className:m,...y},x)=>o.jsx(Cd,{className:Ve("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",m),...y,ref:x}));Hd.displayName=Cd.displayName;const Ld=E.forwardRef(({className:m,...y},x)=>o.jsxs(yp,{children:[o.jsx(Hd,{}),o.jsx(Od,{ref:x,className:Ve("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-top-[48%] fixed top-[50%] left-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border p-6 shadow-lg duration-200 sm:rounded-lg",m),...y})]}));Ld.displayName=Od.displayName;const qd=({className:m,...y})=>o.jsx("div",{className:Ve("flex flex-col space-y-2 text-center sm:text-left",m),...y});qd.displayName="AlertDialogHeader";const Bd=E.forwardRef(({className:m,...y},x)=>o.jsx(jd,{ref:x,className:Ve("text-lg font-semibold",m),...y}));Bd.displayName=jd.displayName;const Gd=E.forwardRef(({className:m,...y},x)=>o.jsx(Ud,{ref:x,className:Ve("text-muted-foreground text-sm",m),...y}));Gd.displayName=Ud.displayName;const vp=E.forwardRef(({className:m,...y},x)=>o.jsx(_d,{ref:x,className:Ve(od(),m),...y}));vp.displayName=_d.displayName;const bp=E.forwardRef(({className:m,...y},x)=>o.jsx(Rd,{ref:x,className:Ve(od({variant:"outline"}),"mt-2 sm:mt-0",m),...y}));bp.displayName=Rd.displayName;const Sp=({open:m,onOpenChange:y})=>{const{t:x}=Bt(),d=we.use.apiKey(),[N,_]=E.useState(""),H=st.use.message();E.useEffect(()=>{_(d||"")},[d,m]),E.useEffect(()=>{H&&(H.includes(rd)||H.includes(fd))&&y(!0)},[H,y]);const P=E.useCallback(()=>{we.setState({apiKey:N||null}),y(!1)},[N,y]),Y=E.useCallback($=>{_($.target.value)},[_]);return o.jsx(pp,{open:m,onOpenChange:y,children:o.jsxs(Ld,{children:[o.jsxs(qd,{children:[o.jsx(Bd,{children:x("apiKeyAlert.title")}),o.jsx(Gd,{children:x("apiKeyAlert.description")})]}),o.jsxs("div",{className:"flex flex-col gap-4",children:[o.jsxs("form",{className:"flex gap-2",onSubmit:$=>$.preventDefault(),children:[o.jsx(us,{type:"password",value:N,onChange:Y,placeholder:x("apiKeyAlert.placeholder"),className:"max-h-full w-full min-w-0",autoComplete:"off"}),o.jsx(Cn,{onClick:P,variant:"outline",size:"sm",children:x("apiKeyAlert.save")})]}),H&&o.jsx("div",{className:"text-sm text-red-500",children:H})]})]})})},Tp=({status:m})=>{const{t:y}=Bt();return m?o.jsxs("div",{className:"min-w-[300px] space-y-2 text-xs",children:[o.jsxs("div",{className:"space-y-1",children:[o.jsx("h4",{className:"font-medium",children:y("graphPanel.statusCard.serverInfo")}),o.jsxs("div",{className:"text-foreground grid grid-cols-[160px_1fr] gap-1",children:[o.jsxs("span",{children:[y("graphPanel.statusCard.workingDirectory"),":"]}),o.jsx("span",{className:"truncate",children:m.working_directory}),o.jsxs("span",{children:[y("graphPanel.statusCard.inputDirectory"),":"]}),o.jsx("span",{className:"truncate",children:m.input_directory}),o.jsxs("span",{children:[y("graphPanel.statusCard.summarySettings"),":"]}),o.jsxs("span",{children:[m.configuration.summary_language," / LLM summary on ",m.configuration.force_llm_summary_on_merge.toString()," fragments"]}),o.jsxs("span",{children:[y("graphPanel.statusCard.threshold"),":"]}),o.jsxs("span",{children:["cosine ",m.configuration.cosine_threshold," / rerank_score ",m.configuration.min_rerank_score," / max_related ",m.configuration.related_chunk_number]}),o.jsxs("span",{children:[y("graphPanel.statusCard.maxParallelInsert"),":"]}),o.jsx("span",{children:m.configuration.max_parallel_insert})]})]}),o.jsxs("div",{className:"space-y-1",children:[o.jsx("h4",{className:"font-medium",children:y("graphPanel.statusCard.llmConfig")}),o.jsxs("div",{className:"text-foreground grid grid-cols-[160px_1fr] gap-1",children:[o.jsxs("span",{children:[y("graphPanel.statusCard.llmBindingHost"),":"]}),o.jsx("span",{children:m.configuration.llm_binding_host}),o.jsxs("span",{children:[y("graphPanel.statusCard.llmModel"),":"]}),o.jsxs("span",{children:[m.configuration.llm_binding,": ",m.configuration.llm_model," (#",m.configuration.max_async," Async)"]})]})]}),o.jsxs("div",{className:"space-y-1",children:[o.jsx("h4",{className:"font-medium",children:y("graphPanel.statusCard.embeddingConfig")}),o.jsxs("div",{className:"text-foreground grid grid-cols-[160px_1fr] gap-1",children:[o.jsxs("span",{children:[y("graphPanel.statusCard.embeddingBindingHost"),":"]}),o.jsx("span",{children:m.configuration.embedding_binding_host}),o.jsxs("span",{children:[y("graphPanel.statusCard.embeddingModel"),":"]}),o.jsxs("span",{children:[m.configuration.embedding_binding,": ",m.configuration.embedding_model," (#",m.configuration.embedding_func_max_async," Async * ",m.configuration.embedding_batch_num," batches)"]})]})]}),m.configuration.enable_rerank&&o.jsxs("div",{className:"space-y-1",children:[o.jsx("h4",{className:"font-medium",children:y("graphPanel.statusCard.rerankerConfig")}),o.jsxs("div",{className:"text-foreground grid grid-cols-[160px_1fr] gap-1",children:[o.jsxs("span",{children:[y("graphPanel.statusCard.rerankerBindingHost"),":"]}),o.jsx("span",{children:m.configuration.rerank_binding_host||"-"}),o.jsxs("span",{children:[y("graphPanel.statusCard.rerankerModel"),":"]}),o.jsxs("span",{children:[m.configuration.rerank_binding||"-"," : ",m.configuration.rerank_model||"-"]})]})]}),o.jsxs("div",{className:"space-y-1",children:[o.jsx("h4",{className:"font-medium",children:y("graphPanel.statusCard.storageConfig")}),o.jsxs("div",{className:"text-foreground grid grid-cols-[160px_1fr] gap-1",children:[o.jsxs("span",{children:[y("graphPanel.statusCard.kvStorage"),":"]}),o.jsx("span",{children:m.configuration.kv_storage}),o.jsxs("span",{children:[y("graphPanel.statusCard.docStatusStorage"),":"]}),o.jsx("span",{children:m.configuration.doc_status_storage}),o.jsxs("span",{children:[y("graphPanel.statusCard.graphStorage"),":"]}),o.jsx("span",{children:m.configuration.graph_storage}),o.jsxs("span",{children:[y("graphPanel.statusCard.vectorStorage"),":"]}),o.jsx("span",{children:m.configuration.vector_storage}),o.jsxs("span",{children:[y("graphPanel.statusCard.workspace"),":"]}),o.jsx("span",{children:m.configuration.workspace||"-"}),o.jsxs("span",{children:[y("graphPanel.statusCard.maxGraphNodes"),":"]}),o.jsx("span",{children:m.configuration.max_graph_nodes||"-"}),m.keyed_locks&&o.jsxs(o.Fragment,{children:[o.jsxs("span",{children:[y("graphPanel.statusCard.lockStatus"),":"]}),o.jsxs("span",{children:["mp ",m.keyed_locks.current_status.pending_mp_cleanup,"/",m.keyed_locks.current_status.total_mp_locks," | async ",m.keyed_locks.current_status.pending_async_cleanup,"/",m.keyed_locks.current_status.total_async_locks,"(pid: ",m.keyed_locks.process_id,")"]})]})]})]})]}):o.jsx("div",{className:"text-foreground text-xs",children:y("graphPanel.statusCard.unavailable")})},xp=({open:m,onOpenChange:y,status:x})=>{const{t:d}=Bt();return o.jsx(Mg,{open:m,onOpenChange:y,children:o.jsxs(zg,{className:"sm:max-w-[700px]",children:[o.jsxs(Cg,{children:[o.jsx(Og,{children:d("graphPanel.statusDialog.title")}),o.jsx(_g,{children:d("graphPanel.statusDialog.description")})]}),o.jsx(Tp,{status:x})]})})},Ap=()=>{const{t:m}=Bt(),y=st.use.health(),x=st.use.lastCheckTime(),d=st.use.status(),[N,_]=E.useState(!1),[H,P]=E.useState(!1);return E.useEffect(()=>{_(!0);const Y=setTimeout(()=>_(!1),300);return()=>clearTimeout(Y)},[x]),o.jsxs("div",{className:"fixed right-4 bottom-4 flex items-center gap-2 opacity-80 select-none",children:[o.jsxs("div",{className:"flex cursor-pointer items-center gap-2",onClick:()=>P(!0),children:[o.jsx("div",{className:Ve("h-3 w-3 rounded-full transition-all duration-300","shadow-[0_0_8px_rgba(0,0,0,0.2)]",y?"bg-green-500":"bg-red-500",N&&"scale-125",N&&y&&"shadow-[0_0_12px_rgba(34,197,94,0.4)]",N&&!y&&"shadow-[0_0_12px_rgba(239,68,68,0.4)]")}),o.jsx("span",{className:"text-muted-foreground text-xs",children:m(y?"graphPanel.statusIndicator.connected":"graphPanel.statusIndicator.disconnected")})]}),o.jsx(xp,{open:H,onOpenChange:P,status:d})]})};function Yd({className:m}){const[y,x]=E.useState(!1),{t:d}=Bt(),N=we.use.language(),_=we.use.setLanguage(),H=we.use.theme(),P=we.use.setTheme(),Y=E.useCallback(he=>{_(he)},[_]),$=E.useCallback(he=>{P(he)},[P]);return o.jsxs(Rg,{open:y,onOpenChange:x,children:[o.jsx(jg,{asChild:!0,children:o.jsx(Cn,{variant:"ghost",size:"icon",className:Ve("h-9 w-9",m),children:o.jsx(Ug,{className:"h-5 w-5"})})}),o.jsx(Hg,{side:"bottom",align:"end",className:"w-56",children:o.jsxs("div",{className:"flex flex-col gap-4",children:[o.jsxs("div",{className:"flex flex-col gap-2",children:[o.jsx("label",{className:"text-sm font-medium",children:d("settings.language")}),o.jsxs(Jf,{value:N,onValueChange:Y,children:[o.jsx(Ff,{children:o.jsx(Pf,{})}),o.jsxs($f,{children:[o.jsx(rl,{value:"en",children:"English"}),o.jsx(rl,{value:"zh",children:"中文"}),o.jsx(rl,{value:"fr",children:"Français"}),o.jsx(rl,{value:"ar",children:"العربية"}),o.jsx(rl,{value:"zh_TW",children:"繁體中文"})]})]})]}),o.jsxs("div",{className:"flex flex-col gap-2",children:[o.jsx("label",{className:"text-sm font-medium",children:d("settings.theme")}),o.jsxs(Jf,{value:H,onValueChange:$,children:[o.jsx(Ff,{children:o.jsx(Pf,{})}),o.jsxs($f,{children:[o.jsx(rl,{value:"light",children:d("settings.light")}),o.jsx(rl,{value:"dark",children:d("settings.dark")}),o.jsx(rl,{value:"system",children:d("settings.system")})]})]})]})]})})]})}const Dp=xg,Xd=E.forwardRef(({className:m,...y},x)=>o.jsx(ud,{ref:x,className:Ve("bg-muted text-muted-foreground inline-flex h-10 items-center justify-center rounded-md p-1",m),...y}));Xd.displayName=ud.displayName;const wd=E.forwardRef(({className:m,...y},x)=>o.jsx(id,{ref:x,className:Ve("ring-offset-background focus-visible:ring-ring data-[state=active]:bg-background data-[state=active]:text-foreground inline-flex items-center justify-center rounded-sm px-3 py-1.5 text-sm font-medium whitespace-nowrap transition-all focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm",m),...y}));wd.displayName=id.displayName;const zn=E.forwardRef(({className:m,...y},x)=>o.jsx(cd,{ref:x,className:Ve("ring-offset-background focus-visible:ring-ring focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none","data-[state=inactive]:invisible data-[state=active]:visible","h-full w-full",m),forceMount:!0,...y}));zn.displayName=cd.displayName;function Ku({value:m,currentTab:y,children:x}){return o.jsx(wd,{value:m,className:Ve("cursor-pointer px-2 py-1 transition-all",y===m?"!bg-emerald-400 !text-zinc-50":"hover:bg-background/60"),children:x})}function Np(){const m=we.use.currentTab(),{t:y}=Bt();return o.jsx("div",{className:"flex h-8 self-center",children:o.jsxs(Xd,{className:"h-full gap-2",children:[o.jsx(Ku,{value:"documents",currentTab:m,children:y("header.documents")}),o.jsx(Ku,{value:"knowledge-graph",currentTab:m,children:y("header.knowledgeGraph")}),o.jsx(Ku,{value:"retrieval",currentTab:m,children:y("header.retrieval")}),o.jsx(Ku,{value:"api",currentTab:m,children:y("header.api")})]})})}function Ep(){const{t:m}=Bt(),{isGuestMode:y,coreVersion:x,apiVersion:d,username:N,webuiTitle:_,webuiDescription:H}=qt(),P=x&&d?`${x}/${d}`:null,Y=()=>{md.navigateToLogin()};return o.jsxs("header",{className:"border-border/40 bg-background/95 supports-[backdrop-filter]:bg-background/60 sticky top-0 z-50 flex h-10 w-full border-b px-4 backdrop-blur",children:[o.jsxs("div",{className:"min-w-[200px] w-auto flex items-center",children:[o.jsxs("a",{href:dd,className:"flex items-center gap-2",children:[o.jsx(ss,{className:"size-4 text-emerald-400","aria-hidden":"true"}),o.jsx("span",{className:"font-bold md:inline-block",children:is.name})]}),_&&o.jsxs("div",{className:"flex items-center",children:[o.jsx("span",{className:"mx-1 text-xs text-gray-500 dark:text-gray-400",children:"|"}),o.jsx(Lg,{children:o.jsxs(qg,{children:[o.jsx(Bg,{asChild:!0,children:o.jsx("span",{className:"font-medium text-sm cursor-default",children:_})}),H&&o.jsx(Gg,{side:"bottom",children:H})]})})]})]}),o.jsxs("div",{className:"flex h-10 flex-1 items-center justify-center",children:[o.jsx(Np,{}),y&&o.jsx("div",{className:"ml-2 self-center px-2 py-1 text-xs bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200 rounded-md",children:m("login.guestMode","Guest Mode")})]}),o.jsx("nav",{className:"w-[200px] flex items-center justify-end",children:o.jsxs("div",{className:"flex items-center gap-2",children:[P&&o.jsxs("span",{className:"text-xs text-gray-500 dark:text-gray-400 mr-1",children:["v",P]}),o.jsx(Cn,{variant:"ghost",size:"icon",side:"bottom",tooltip:m("header.projectRepository"),children:o.jsx("a",{href:is.github,target:"_blank",rel:"noopener noreferrer",children:o.jsx(Yg,{className:"size-4","aria-hidden":"true"})})}),o.jsx(Yd,{}),!y&&o.jsx(Cn,{variant:"ghost",size:"icon",side:"bottom",tooltip:`${m("header.logout")} (${N})`,onClick:Y,children:o.jsx(Xg,{className:"size-4","aria-hidden":"true"})})]})})]})}const Mp=()=>{const m=E.useContext(pd);if(!m)throw new Error("useTabVisibility must be used within a TabVisibilityProvider");return m};function zp(){const{t:m}=Bt(),{isTabVisible:y}=Mp(),x=y("api"),[d,N]=E.useState(!1);return E.useEffect(()=>{d||N(!0)},[d]),o.jsx("div",{className:`size-full ${x?"":"hidden"}`,children:d?o.jsx("iframe",{src:wg+"/docs",className:"size-full w-full h-full",style:{width:"100%",height:"100%",border:"none"}},"api-docs-iframe"):o.jsx("div",{className:"flex h-full w-full items-center justify-center bg-background",children:o.jsxs("div",{className:"text-center",children:[o.jsx("div",{className:"mb-2 h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"}),o.jsx("p",{children:m("apiSite.loading")})]})})})}function Cp(){const m=st.use.message(),y=we.use.enableHealthCheck(),x=we.use.currentTab(),[d,N]=E.useState(!1),[_,H]=E.useState(!0),P=E.useRef(!1),Y=E.useRef(!1),$=E.useCallback(V=>{N(V),V||st.getState().clear()},[]),he=E.useRef(!0);E.useEffect(()=>{he.current=!0;const V=()=>{he.current=!1};return window.addEventListener("beforeunload",V),()=>{he.current=!1,window.removeEventListener("beforeunload",V)}},[]),E.useEffect(()=>{const V=async()=>{try{he.current&&await st.getState().check()}catch(pe){console.error("Health check error:",pe)}};if(st.getState().setHealthCheckFunction(V),!y||d){st.getState().clearHealthCheckTimer();return}return Y.current||(Y.current=!0),st.getState().resetHealthCheckTimer(),()=>{st.getState().clearHealthCheckTimer()}},[y,d]),E.useEffect(()=>{(async()=>{if(P.current)return;if(P.current=!0,sessionStorage.getItem("VERSION_CHECKED_FROM_LOGIN")==="true"){H(!1);return}try{H(!0);const ae=localStorage.getItem("LIGHTRAG-API-TOKEN"),C=await gd();if(!C.auth_configured&&C.access_token)qt.getState().login(C.access_token,!0,C.core_version,C.api_version,C.webui_title||null,C.webui_description||null);else if(ae&&(C.core_version||C.api_version||C.webui_title||C.webui_description)){const yt=C.auth_mode==="disabled"||qt.getState().isGuestMode;qt.getState().login(ae,yt,C.core_version,C.api_version,C.webui_title||null,C.webui_description||null)}sessionStorage.setItem("VERSION_CHECKED_FROM_LOGIN","true")}catch(ae){console.error("Failed to get version info:",ae)}finally{H(!1)}})()},[]);const ge=E.useCallback(V=>we.getState().setCurrentTab(V),[]);return E.useEffect(()=>{m&&(m.includes(rd)||m.includes(fd))&&N(!0)},[m]),o.jsx(hd,{children:o.jsx(np,{children:_?o.jsxs("div",{className:"flex h-screen w-screen flex-col",children:[o.jsxs("header",{className:"border-border/40 bg-background/95 supports-[backdrop-filter]:bg-background/60 sticky top-0 z-50 flex h-10 w-full border-b px-4 backdrop-blur",children:[o.jsx("div",{className:"min-w-[200px] w-auto flex items-center",children:o.jsxs("a",{href:dd,className:"flex items-center gap-2",children:[o.jsx(ss,{className:"size-4 text-emerald-400","aria-hidden":"true"}),o.jsx("span",{className:"font-bold md:inline-block",children:is.name})]})}),o.jsx("div",{className:"flex h-10 flex-1 items-center justify-center"}),o.jsx("nav",{className:"w-[200px] flex items-center justify-end"})]}),o.jsx("div",{className:"flex flex-1 items-center justify-center",children:o.jsxs("div",{className:"text-center",children:[o.jsx("div",{className:"mb-2 h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"}),o.jsx("p",{children:"Initializing..."})]})})]}):o.jsxs("main",{className:"flex h-screen w-screen overflow-hidden",children:[o.jsxs(Dp,{defaultValue:x,className:"!m-0 flex grow flex-col !p-0 overflow-hidden",onValueChange:ge,children:[o.jsx(Ep,{}),o.jsxs("div",{className:"relative grow",children:[o.jsx(zn,{value:"documents",className:"absolute top-0 right-0 bottom-0 left-0 overflow-auto",children:o.jsx(Pg,{})}),o.jsx(zn,{value:"knowledge-graph",className:"absolute top-0 right-0 bottom-0 left-0 overflow-hidden",children:o.jsx(Vg,{})}),o.jsx(zn,{value:"retrieval",className:"absolute top-0 right-0 bottom-0 left-0 overflow-hidden",children:o.jsx($g,{})}),o.jsx(zn,{value:"api",className:"absolute top-0 right-0 bottom-0 left-0 overflow-hidden",children:o.jsx(zp,{})})]})]}),y&&o.jsx(Ap,{}),o.jsx(Sp,{open:d,onOpenChange:$})]})})})}const Op=()=>{const m=sd(),{login:y,isAuthenticated:x}=qt(),{t:d}=Bt(),[N,_]=E.useState(!1),[H,P]=E.useState(""),[Y,$]=E.useState(""),[he,ge]=E.useState(!0),V=E.useRef(!1);if(E.useEffect(()=>{console.log("LoginPage mounted")},[]),E.useEffect(()=>((async()=>{if(!V.current){V.current=!0;try{if(x){m("/");return}const C=await gd();if((C.core_version||C.api_version)&&sessionStorage.setItem("VERSION_CHECKED_FROM_LOGIN","true"),!C.auth_configured&&C.access_token){y(C.access_token,!0,C.core_version,C.api_version,C.webui_title||null,C.webui_description||null),C.message&&En.info(C.message),m("/");return}ge(!1)}catch(C){console.error("Failed to check auth configuration:",C),ge(!1)}}})(),()=>{}),[x,y,m]),he)return null;const pe=async ae=>{if(ae.preventDefault(),!H||!Y){En.error(d("login.errorEmptyFields"));return}try{_(!0);const C=await kg(H,Y);localStorage.getItem("LIGHTRAG-PREVIOUS-USER")===H?console.log("Same user logging in, preserving chat history"):(console.log("Different user logging in, clearing chat history"),we.getState().setRetrievalHistory([])),localStorage.setItem("LIGHTRAG-PREVIOUS-USER",H);const _e=C.auth_mode==="disabled";y(C.access_token,_e,C.core_version,C.api_version,C.webui_title||null,C.webui_description||null),(C.core_version||C.api_version)&&sessionStorage.setItem("VERSION_CHECKED_FROM_LOGIN","true"),_e?En.info(C.message||d("login.authDisabled","Authentication is disabled. Using guest access.")):En.success(d("login.successMessage")),m("/")}catch(C){console.error("Login failed...",C),En.error(d("login.errorInvalidCredentials")),qt.getState().logout(),localStorage.removeItem("LIGHTRAG-API-TOKEN")}finally{_(!1)}};return o.jsxs("div",{className:"flex h-screen w-screen items-center justify-center bg-gradient-to-br from-emerald-50 to-teal-100 dark:from-gray-900 dark:to-gray-800",children:[o.jsx("div",{className:"absolute top-4 right-4 flex items-center gap-2",children:o.jsx(Yd,{className:"bg-white/30 dark:bg-gray-800/30 backdrop-blur-sm rounded-md"})}),o.jsxs(Qg,{className:"w-full max-w-[480px] shadow-lg mx-4",children:[o.jsx(Kg,{className:"flex items-center justify-center space-y-2 pb-8 pt-6",children:o.jsxs("div",{className:"flex flex-col items-center space-y-4",children:[o.jsxs("div",{className:"flex items-center gap-3",children:[o.jsx("img",{src:"logo.svg",alt:"LightRAG Logo",className:"h-12 w-12"}),o.jsx(ss,{className:"size-10 text-emerald-400","aria-hidden":"true"})]}),o.jsxs("div",{className:"text-center space-y-2",children:[o.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"LightRAG"}),o.jsx("p",{className:"text-muted-foreground text-sm",children:d("login.description")})]})]})}),o.jsx(Zg,{className:"px-8 pb-8",children:o.jsxs("form",{onSubmit:pe,className:"space-y-6",children:[o.jsxs("div",{className:"flex items-center gap-4",children:[o.jsx("label",{htmlFor:"username-input",className:"text-sm font-medium w-16 shrink-0",children:d("login.username")}),o.jsx(us,{id:"username-input",placeholder:d("login.usernamePlaceholder"),value:H,onChange:ae=>P(ae.target.value),required:!0,className:"h-11 flex-1"})]}),o.jsxs("div",{className:"flex items-center gap-4",children:[o.jsx("label",{htmlFor:"password-input",className:"text-sm font-medium w-16 shrink-0",children:d("login.password")}),o.jsx(us,{id:"password-input",type:"password",placeholder:d("login.passwordPlaceholder"),value:Y,onChange:ae=>$(ae.target.value),required:!0,className:"h-11 flex-1"})]}),o.jsx(Cn,{type:"submit",className:"w-full h-11 text-base font-medium mt-2",disabled:N,children:d(N?"login.loggingIn":"login.loginButton")})]})})]})]})},_p=()=>{const[m,y]=E.useState(!0),{isAuthenticated:x}=qt(),d=sd();return E.useEffect(()=>{md.setNavigate(d)},[d]),E.useEffect(()=>((async()=>{try{const _=localStorage.getItem("LIGHTRAG-API-TOKEN");if(_&&x){y(!1);return}_||qt.getState().logout()}catch(_){console.error("Auth initialization error:",_),x||qt.getState().logout()}finally{y(!1)}})(),()=>{}),[x]),E.useEffect(()=>{!m&&!x&&window.location.hash.slice(1)!=="/login"&&(console.log("Not authenticated, redirecting to login"),d("/login"))},[m,x,d]),m?null:o.jsxs(Eg,{children:[o.jsx(kf,{path:"/login",element:o.jsx(Op,{})}),o.jsx(kf,{path:"/*",element:x?o.jsx(Cp,{}):null})]})},Rp=()=>o.jsx(hd,{children:o.jsxs(Ng,{children:[o.jsx(_p,{}),o.jsx(Jg,{position:"bottom-center",theme:"system",closeButton:!0,richColors:!0})]})}),jp={language:"Language",theme:"Theme",light:"Light",dark:"Dark",system:"System"},Up={documents:"Documents",knowledgeGraph:"Knowledge Graph",retrieval:"Retrieval",api:"API",projectRepository:"Project Repository",logout:"Logout",themeToggle:{switchToLight:"Switch to light theme",switchToDark:"Switch to dark theme"}},Hp={description:"Please enter your account and password to log in to the system",username:"Username",usernamePlaceholder:"Please input a username",password:"Password",passwordPlaceholder:"Please input a password",loginButton:"Login",loggingIn:"Logging in...",successMessage:"Login succeeded",errorEmptyFields:"Please enter your username and password",errorInvalidCredentials:"Login failed, please check username and password",authDisabled:"Authentication is disabled. Using login free mode.",guestMode:"Login Free"},Lp={cancel:"Cancel",save:"Save",saving:"Saving...",saveFailed:"Save failed"},qp={clearDocuments:{button:"Clear",tooltip:"Clear documents",title:"Clear Documents",description:"This will remove all documents from the system",warning:"WARNING: This action will permanently delete all documents and cannot be undone!",confirm:"Do you really want to clear all documents?",confirmPrompt:"Type 'yes' to confirm this action",confirmPlaceholder:"Type yes to confirm",clearCache:"Clear LLM cache",confirmButton:"YES",clearing:"Clearing...",timeout:"Clear operation timed out, please try again",success:"Documents cleared successfully",cacheCleared:"Cache cleared successfully",cacheClearFailed:`Failed to clear cache:
{{error}}`,failed:`Clear Documents Failed:
{{message}}`,error:`Clear Documents Failed:
{{error}}`},deleteDocuments:{button:"Delete",tooltip:"Delete selected documents",title:"Delete Documents",description:"This will permanently delete the selected documents from the system",warning:"WARNING: This action will permanently delete the selected documents and cannot be undone!",confirm:"Do you really want to delete {{count}} selected document(s)?",confirmPrompt:"Type 'yes' to confirm this action",confirmPlaceholder:"Type yes to confirm",confirmButton:"YES",deleteFileOption:"Also delete uploaded files",deleteFileTooltip:"Check this option to also delete the corresponding uploaded files on the server",success:"Document deletion pipeline started successfully",failed:`Delete Documents Failed:
{{message}}`,error:`Delete Documents Failed:
{{error}}`,busy:"Pipeline is busy, please try again later",notAllowed:"No permission to perform this operation"},selectDocuments:{selectCurrentPage:"Select Current Page ({{count}})",deselectAll:"Deselect All ({{count}})"},uploadDocuments:{button:"Upload",tooltip:"Upload documents",title:"Upload Documents",description:"Drag and drop your documents here or click to browse.",single:{uploading:"Uploading {{name}}: {{percent}}%",success:`Upload Success:
{{name}} uploaded successfully`,failed:`Upload Failed:
{{name}}
{{message}}`,error:`Upload Failed:
{{name}}
{{error}}`},batch:{uploading:"Uploading files...",success:"Files uploaded successfully",error:"Some files failed to upload"},generalError:`Upload Failed
{{error}}`,fileTypes:"Supported types: TXT, MD, DOCX, PDF, PPTX, XLSX, RTF, ODT, EPUB, HTML, HTM, TEX, JSON, XML, YAML, YML, CSV, LOG, CONF, INI, PROPERTIES, SQL, BAT, SH, C, CPP, PY, JAVA, JS, TS, SWIFT, GO, RB, PHP, CSS, SCSS, LESS",fileUploader:{singleFileLimit:"Cannot upload more than 1 file at a time",maxFilesLimit:"Cannot upload more than {{count}} files",fileRejected:"File {{name}} was rejected",unsupportedType:"Unsupported file type",fileTooLarge:"File too large, maximum size is {{maxSize}}",dropHere:"Drop the files here",dragAndDrop:"Drag and drop files here, or click to select files",removeFile:"Remove file",uploadDescription:"You can upload {{isMultiple ? 'multiple' : count}} files (up to {{maxSize}} each)",duplicateFile:"File name already exists in server cache"}},documentManager:{title:"Document Management",scanButton:"Scan",scanTooltip:"Scan documents in input folder",refreshTooltip:"Reset document list",pipelineStatusButton:"Pipeline Status",pipelineStatusTooltip:"View pipeline status",uploadedTitle:"Uploaded Documents",uploadedDescription:"List of uploaded documents and their statuses.",emptyTitle:"No Documents",emptyDescription:"There are no uploaded documents yet.",columns:{id:"ID",fileName:"File Name",summary:"Summary",status:"Status",length:"Length",chunks:"Chunks",created:"Created",updated:"Updated",metadata:"Metadata",select:"Select"},status:{all:"All",completed:"Completed",processing:"Processing",pending:"Pending",failed:"Failed"},errors:{loadFailed:`Failed to load documents
{{error}}`,scanFailed:`Failed to scan documents
{{error}}`,scanProgressFailed:`Failed to get scan progress
{{error}}`},fileNameLabel:"File Name",showButton:"Show",hideButton:"Hide",showFileNameTooltip:"Show file name",hideFileNameTooltip:"Hide file name"},pipelineStatus:{title:"Pipeline Status",busy:"Pipeline Busy",requestPending:"Request Pending",jobName:"Job Name",startTime:"Start Time",progress:"Progress",unit:"batch",latestMessage:"Latest Message",historyMessages:"History Messages",errors:{fetchFailed:`Failed to get pipeline status
{{error}}`}}},Bp={dataIsTruncated:"Graph data is truncated to Max Nodes",statusDialog:{title:"LightRAG Server Settings",description:"View current system status and connection information"},legend:"Legend",nodeTypes:{person:"Person",category:"Category",geo:"Geographic",location:"Location",organization:"Organization",event:"Event",equipment:"Equipment",weapon:"Weapon",animal:"Animal",unknown:"Unknown",object:"Object",group:"Group",technology:"Technology",product:"Product",document:"Document",other:"Other"},sideBar:{settings:{settings:"Settings",healthCheck:"Health Check",showPropertyPanel:"Show Property Panel",showSearchBar:"Show Search Bar",showNodeLabel:"Show Node Label",nodeDraggable:"Node Draggable",showEdgeLabel:"Show Edge Label",hideUnselectedEdges:"Hide Unselected Edges",edgeEvents:"Edge Events",maxQueryDepth:"Max Query Depth",maxNodes:"Max Nodes",maxLayoutIterations:"Max Layout Iterations",resetToDefault:"Reset to default",edgeSizeRange:"Edge Size Range",depth:"D",max:"Max",degree:"Degree",apiKey:"API Key",enterYourAPIkey:"Enter your API key",save:"Save",refreshLayout:"Refresh Layout"},zoomControl:{zoomIn:"Zoom In",zoomOut:"Zoom Out",resetZoom:"Reset Zoom",rotateCamera:"Clockwise Rotate",rotateCameraCounterClockwise:"Counter-Clockwise Rotate"},layoutsControl:{startAnimation:"Continue layout animation",stopAnimation:"Stop layout animation",layoutGraph:"Layout Graph",layouts:{Circular:"Circular",Circlepack:"Circlepack",Random:"Random",Noverlaps:"Noverlaps","Force Directed":"Force Directed","Force Atlas":"Force Atlas"}},fullScreenControl:{fullScreen:"Full Screen",windowed:"Windowed"},legendControl:{toggleLegend:"Toggle Legend"}},statusIndicator:{connected:"Connected",disconnected:"Disconnected"},statusCard:{unavailable:"Status information unavailable",serverInfo:"Server Info",workingDirectory:"Working Directory",inputDirectory:"Input Directory",maxParallelInsert:"Concurrent Doc Processing",summarySettings:"Summary Settings",llmConfig:"LLM Configuration",llmBinding:"LLM Binding",llmBindingHost:"LLM Endpoint",llmModel:"LLM Model",embeddingConfig:"Embedding Configuration",embeddingBinding:"Embedding Binding",embeddingBindingHost:"Embedding Endpoint",embeddingModel:"Embedding Model",storageConfig:"Storage Configuration",kvStorage:"KV Storage",docStatusStorage:"Doc Status Storage",graphStorage:"Graph Storage",vectorStorage:"Vector Storage",workspace:"Workspace",maxGraphNodes:"Max Graph Nodes",rerankerConfig:"Reranker Configuration",rerankerBindingHost:"Reranker Endpoint",rerankerModel:"Reranker Model",lockStatus:"Lock Status",threshold:"Threshold"},propertiesView:{editProperty:"Edit {{property}}",editPropertyDescription:"Edit the property value in the text area below.",errors:{duplicateName:"Node name already exists",updateFailed:"Failed to update node",tryAgainLater:"Please try again later"},success:{entityUpdated:"Node updated successfully",relationUpdated:"Relation updated successfully"},node:{title:"Node",id:"ID",labels:"Labels",degree:"Degree",properties:"Properties",relationships:"Relations(within subgraph)",expandNode:"Expand Node",pruneNode:"Prune Node",deleteAllNodesError:"Refuse to delete all nodes in the graph",nodesRemoved:"{{count}} nodes removed, including orphan nodes",noNewNodes:"No expandable nodes found",propertyNames:{description:"Description",entity_id:"Name",entity_type:"Type",source_id:"SrcID",Neighbour:"Neigh",file_path:"Source",keywords:"Keys",weight:"Weight"}},edge:{title:"Relationship",id:"ID",type:"Type",source:"Source",target:"Target",properties:"Properties"}},search:{placeholder:"Search nodes...",message:"And {count} others"},graphLabels:{selectTooltip:"Select query label",noLabels:"No labels found",label:"Label",placeholder:"Search labels...",andOthers:"And {count} others",refreshTooltip:"Reload data(After file added)"},emptyGraph:"Empty(Try Reload Again)"},Gp={chatMessage:{copyTooltip:"Copy to clipboard",copyError:"Failed to copy text to clipboard",thinking:"Thinking...",thinkingTime:"Thinking time {{time}}s",thinkingInProgress:"Thinking in progress..."},retrieval:{startPrompt:"Start a retrieval by typing your query below",clear:"Clear",send:"Send",placeholder:"Enter your query (Support prefix: /<Query Mode>)",error:"Error: Failed to get response",queryModeError:"Only supports the following query modes: {{modes}}",queryModePrefixInvalid:"Invalid query mode prefix. Use: /<mode> [space] your query"},querySettings:{parametersTitle:"Parameters",parametersDescription:"Configure your query parameters",queryMode:"Query Mode",queryModeTooltip:`Select the retrieval strategy:
• Naive: Basic search without advanced techniques
• Local: Context-dependent information retrieval
• Global: Utilizes global knowledge base
• Hybrid: Combines local and global retrieval
• Mix: Integrates knowledge graph with vector retrieval
• Bypass: Passes query directly to LLM without retrieval`,queryModeOptions:{naive:"Naive",local:"Local",global:"Global",hybrid:"Hybrid",mix:"Mix",bypass:"Bypass"},responseFormat:"Response Format",responseFormatTooltip:`Defines the response format. Examples:
• Multiple Paragraphs
• Single Paragraph
• Bullet Points`,responseFormatOptions:{multipleParagraphs:"Multiple Paragraphs",singleParagraph:"Single Paragraph",bulletPoints:"Bullet Points"},topK:"KG Top K",topKTooltip:"Number of entities and relations to retrieve. Applicable for non-naive modes.",topKPlaceholder:"Enter top_k value",chunkTopK:"Chunk Top K",chunkTopKTooltip:"Number of text chunks to retrieve, applicable for all modes.",chunkTopKPlaceholder:"Enter chunk_top_k value",maxEntityTokens:"Max Entity Tokens",maxEntityTokensTooltip:"Maximum number of tokens allocated for entity context in unified token control system",maxRelationTokens:"Max Relation Tokens",maxRelationTokensTooltip:"Maximum number of tokens allocated for relationship context in unified token control system",maxTotalTokens:"Max Total Tokens",maxTotalTokensTooltip:"Maximum total tokens budget for the entire query context (entities + relations + chunks + system prompt)",historyTurns:"History Turns",historyTurnsTooltip:"Number of complete conversation turns (user-assistant pairs) to consider in the response context",historyTurnsPlaceholder:"Number of history turns",onlyNeedContext:"Only Need Context",onlyNeedContextTooltip:"If True, only returns the retrieved context without generating a response",onlyNeedPrompt:"Only Need Prompt",onlyNeedPromptTooltip:"If True, only returns the generated prompt without producing a response",streamResponse:"Stream Response",streamResponseTooltip:"If True, enables streaming output for real-time responses",userPrompt:"User Prompt",userPromptTooltip:"Provide additional response requirements to the LLM (unrelated to query content, only for output processing).",userPromptPlaceholder:"Enter custom prompt (optional)",enableRerank:"Enable Rerank",enableRerankTooltip:"Enable reranking for retrieved text chunks. If True but no rerank model is configured, a warning will be issued. Default is True."}},Yp={loading:"Loading API Documentation..."},Xp={title:"API Key is required",description:"Please enter your API key to access the service",placeholder:"Enter your API key",save:"Save"},wp={showing:"Showing {{start}} to {{end}} of {{total}} entries",page:"Page",pageSize:"Page Size",firstPage:"First Page",prevPage:"Previous Page",nextPage:"Next Page",lastPage:"Last Page"},Vp={settings:jp,header:Up,login:Hp,common:Lp,documentPanel:qp,graphPanel:Bp,retrievePanel:Gp,apiSite:Yp,apiKeyAlert:Xp,pagination:wp},Qp={language:"语言",theme:"主题",light:"浅色",dark:"深色",system:"系统"},Kp={documents:"文档",knowledgeGraph:"知识图谱",retrieval:"检索",api:"API",projectRepository:"项目仓库",logout:"退出登录",themeToggle:{switchToLight:"切换到浅色主题",switchToDark:"切换到深色主题"}},Zp={description:"请输入您的账号和密码登录系统",username:"用户名",usernamePlaceholder:"请输入用户名",password:"密码",passwordPlaceholder:"请输入密码",loginButton:"登录",loggingIn:"登录中...",successMessage:"登录成功",errorEmptyFields:"请输入您的用户名和密码",errorInvalidCredentials:"登录失败，请检查用户名和密码",authDisabled:"认证已禁用，使用无需登陆模式。",guestMode:"无需登陆"},kp={cancel:"取消",save:"保存",saving:"保存中...",saveFailed:"保存失败"},Jp={clearDocuments:{button:"清空",tooltip:"清空文档",title:"清空文档",description:"此操作将从系统中移除所有文档",warning:"警告：此操作将永久删除所有文档，无法恢复！",confirm:"确定要清空所有文档吗？",confirmPrompt:"请输入 yes 确认操作",confirmPlaceholder:"输入 yes 确认",clearCache:"清空LLM缓存",confirmButton:"确定",clearing:"正在清除...",timeout:"清除操作超时，请重试",success:"文档清空成功",cacheCleared:"缓存清空成功",cacheClearFailed:`清空缓存失败：
{{error}}`,failed:`清空文档失败：
{{message}}`,error:`清空文档失败：
{{error}}`},deleteDocuments:{button:"删除",tooltip:"删除选中的文档",title:"删除文档",description:"此操作将永久删除选中的文档",warning:"警告：此操作将永久删除选中的文档，无法恢复！",confirm:"确定要删除 {{count}} 个选中的文档吗？",confirmPrompt:"请输入 yes 确认操作",confirmPlaceholder:"输入 yes 确认",confirmButton:"确定",deleteFileOption:"同时删除上传文件",deleteFileTooltip:"选中此选项将同时删除服务器上对应的上传文件",success:"文档删除流水线启动成功",failed:`删除文档失败：
{{message}}`,error:`删除文档失败：
{{error}}`,busy:"流水线被占用，请稍后再试",notAllowed:"没有操作权限"},selectDocuments:{selectCurrentPage:"全选当前页 ({{count}})",deselectAll:"取消全选 ({{count}})"},uploadDocuments:{button:"上传",tooltip:"上传文档",title:"上传文档",description:"拖拽文件到此处或点击浏览",single:{uploading:"正在上传 {{name}}：{{percent}}%",success:`上传成功：
{{name}} 上传完成`,failed:`上传失败：
{{name}}
{{message}}`,error:`上传失败：
{{name}}
{{error}}`},batch:{uploading:"正在上传文件...",success:"文件上传完成",error:"部分文件上传失败"},generalError:`上传失败
{{error}}`,fileTypes:"支持的文件类型：TXT, MD, DOCX, PDF, PPTX, XLSX, RTF, ODT, EPUB, HTML, HTM, TEX, JSON, XML, YAML, YML, CSV, LOG, CONF, INI, PROPERTIES, SQL, BAT, SH, C, CPP, PY, JAVA, JS, TS, SWIFT, GO, RB, PHP, CSS, SCSS, LESS",fileUploader:{singleFileLimit:"一次只能上传一个文件",maxFilesLimit:"最多只能上传 {{count}} 个文件",fileRejected:"文件 {{name}} 被拒绝",unsupportedType:"不支持的文件类型",fileTooLarge:"文件过大，最大允许 {{maxSize}}",dropHere:"将文件拖放到此处",dragAndDrop:"拖放文件到此处，或点击选择文件",removeFile:"移除文件",uploadDescription:"您可以上传{{isMultiple ? '多个' : count}}个文件（每个文件最大{{maxSize}}）",duplicateFile:"文件名与服务器上的缓存重复"}},documentManager:{title:"文档管理",scanButton:"扫描",scanTooltip:"扫描输入目录中的文档",refreshTooltip:"复位文档清单",pipelineStatusButton:"流水线状态",pipelineStatusTooltip:"查看流水线状态",uploadedTitle:"已上传文档",uploadedDescription:"已上传文档列表及其状态",emptyTitle:"无文档",emptyDescription:"还没有上传任何文档",columns:{id:"ID",fileName:"文件名",summary:"摘要",status:"状态",length:"长度",chunks:"分块",created:"创建时间",updated:"更新时间",metadata:"元数据",select:"选择"},status:{all:"全部",completed:"已完成",processing:"处理中",pending:"等待中",failed:"失败"},errors:{loadFailed:`加载文档失败
{{error}}`,scanFailed:`扫描文档失败
{{error}}`,scanProgressFailed:`获取扫描进度失败
{{error}}`},fileNameLabel:"文件名",showButton:"显示",hideButton:"隐藏",showFileNameTooltip:"显示文件名",hideFileNameTooltip:"隐藏文件名"},pipelineStatus:{title:"流水线状态",busy:"流水线忙碌",requestPending:"待处理请求",jobName:"作业名称",startTime:"开始时间",progress:"进度",unit:"批",latestMessage:"最新消息",historyMessages:"历史消息",errors:{fetchFailed:`获取流水线状态失败
{{error}}`}}},Fp={dataIsTruncated:"图数据已截断至最大返回节点数",statusDialog:{title:"LightRAG 服务器设置",description:"查看当前系统状态和连接信息"},legend:"图例",nodeTypes:{person:"人物角色",category:"分类",geo:"地理名称",location:"位置",organization:"组织机构",event:"事件",equipment:"装备",weapon:"武器",animal:"动物",unknown:"未知",object:"物品",group:"群组",technology:"技术",product:"产品",document:"文档",other:"其他"},sideBar:{settings:{settings:"设置",healthCheck:"健康检查",showPropertyPanel:"显示属性面板",showSearchBar:"显示搜索栏",showNodeLabel:"显示节点标签",nodeDraggable:"节点可拖动",showEdgeLabel:"显示边标签",hideUnselectedEdges:"隐藏未选中的边",edgeEvents:"边事件",maxQueryDepth:"最大查询深度",maxNodes:"最大返回节点数",maxLayoutIterations:"最大布局迭代次数",resetToDefault:"重置为默认值",edgeSizeRange:"边粗细范围",depth:"深",max:"Max",degree:"邻边",apiKey:"API密钥",enterYourAPIkey:"输入您的API密钥",save:"保存",refreshLayout:"刷新布局"},zoomControl:{zoomIn:"放大",zoomOut:"缩小",resetZoom:"重置缩放",rotateCamera:"顺时针旋转图形",rotateCameraCounterClockwise:"逆时针旋转图形"},layoutsControl:{startAnimation:"继续布局动画",stopAnimation:"停止布局动画",layoutGraph:"图布局",layouts:{Circular:"环形",Circlepack:"圆形打包",Random:"随机",Noverlaps:"无重叠","Force Directed":"力导向","Force Atlas":"力地图"}},fullScreenControl:{fullScreen:"全屏",windowed:"窗口"},legendControl:{toggleLegend:"切换图例显示"}},statusIndicator:{connected:"已连接",disconnected:"未连接"},statusCard:{unavailable:"状态信息不可用",serverInfo:"服务器信息",workingDirectory:"工作目录",inputDirectory:"输入目录",maxParallelInsert:"并行处理文档",summarySettings:"摘要设置",llmConfig:"LLM配置",llmBinding:"LLM绑定",llmBindingHost:"LLM端点",llmModel:"LLM模型",embeddingConfig:"嵌入配置",embeddingBinding:"嵌入绑定",embeddingBindingHost:"嵌入端点",embeddingModel:"嵌入模型",storageConfig:"存储配置",kvStorage:"KV存储",docStatusStorage:"文档状态存储",graphStorage:"图存储",vectorStorage:"向量存储",workspace:"工作空间",maxGraphNodes:"最大图节点数",rerankerConfig:"重排序配置",rerankerBindingHost:"重排序端点",rerankerModel:"重排序模型",lockStatus:"锁状态",threshold:"阈值"},propertiesView:{editProperty:"编辑{{property}}",editPropertyDescription:"在下方文本区域编辑属性值。",errors:{duplicateName:"节点名称已存在",updateFailed:"更新节点失败",tryAgainLater:"请稍后重试"},success:{entityUpdated:"节点更新成功",relationUpdated:"关系更新成功"},node:{title:"节点",id:"ID",labels:"标签",degree:"度数",properties:"属性",relationships:"关系(子图内)",expandNode:"扩展节点",pruneNode:"修剪节点",deleteAllNodesError:"拒绝删除图中的所有节点",nodesRemoved:"已删除 {{count}} 个节点，包括孤立节点",noNewNodes:"没有发现可以扩展的节点",propertyNames:{description:"描述",entity_id:"名称",entity_type:"类型",source_id:"信源ID",Neighbour:"邻接",file_path:"信源",keywords:"Keys",weight:"权重"}},edge:{title:"关系",id:"ID",type:"类型",source:"源节点",target:"目标节点",properties:"属性"}},search:{placeholder:"搜索节点...",message:"还有 {count} 个"},graphLabels:{selectTooltip:"选择查询标签",noLabels:"未找到标签",label:"标签",placeholder:"搜索标签...",andOthers:"还有 {count} 个",refreshTooltip:"重载图形数据(添加文件后需重载)"},emptyGraph:"无数据(请重载图形数据)"},Pp={chatMessage:{copyTooltip:"复制到剪贴板",copyError:"复制文本到剪贴板失败",thinking:"正在思考...",thinkingTime:"思考用时 {{time}} 秒",thinkingInProgress:"思考进行中..."},retrieval:{startPrompt:"输入查询开始检索",clear:"清空",send:"发送",placeholder:"输入查询内容 (支持模式前缀: /<Query Mode>)",error:"错误：获取响应失败",queryModeError:"仅支持以下查询模式：{{modes}}",queryModePrefixInvalid:"无效的查询模式前缀。请使用：/<模式> [空格] 查询内容"},querySettings:{parametersTitle:"参数",parametersDescription:"配置查询参数",queryMode:"查询模式",queryModeTooltip:`选择检索策略：
• Naive：基础搜索，无高级技术
• Local：上下文相关信息检索
• Global：利用全局知识库
• Hybrid：结合本地和全局检索
• Mix：整合知识图谱和向量检索
• Bypass：直接传递查询到LLM，不进行检索`,queryModeOptions:{naive:"Naive",local:"Local",global:"Global",hybrid:"Hybrid",mix:"Mix",bypass:"Bypass"},responseFormat:"响应格式",responseFormatTooltip:`定义响应格式。例如：
• 多段落
• 单段落
• 要点`,responseFormatOptions:{multipleParagraphs:"多段落",singleParagraph:"单段落",bulletPoints:"要点"},topK:"KG Top K",topKTooltip:"实体关系检索数量, 适用于非naive模式",topKPlaceholder:"输入top_k值",chunkTopK:"文本块 Top K",chunkTopKTooltip:"文本块检索数量, 适用于所有模式",chunkTopKPlaceholder:"输入文本块chunk_top_k值",maxEntityTokens:"实体令牌数上限",maxEntityTokensTooltip:"统一令牌控制系统中分配给实体上下文的最大令牌数",maxRelationTokens:"关系令牌数上限",maxRelationTokensTooltip:"统一令牌控制系统中分配给关系上下文的最大令牌数",maxTotalTokens:"总令牌数上限",maxTotalTokensTooltip:"整个查询上下文的最大总令牌预算（实体+关系+文档块+系统提示）",historyTurns:"历史轮次",historyTurnsTooltip:"响应上下文中考虑的完整对话轮次（用户-助手对）数量",historyTurnsPlaceholder:"历史轮次数",onlyNeedContext:"仅需上下文",onlyNeedContextTooltip:"如果为True，仅返回检索到的上下文而不生成响应",onlyNeedPrompt:"仅需提示",onlyNeedPromptTooltip:"如果为True，仅返回生成的提示而不产生响应",streamResponse:"流式响应",streamResponseTooltip:"如果为True，启用实时流式输出响应",userPrompt:"用户提示词",userPromptTooltip:"向LLM提供额外的响应要求（与查询内容无关，仅用于处理输出）。",userPromptPlaceholder:"输入自定义提示词（可选）",enableRerank:"启用重排",enableRerankTooltip:"为检索到的文本块启用重排。如果为True但未配置重排模型，将发出警告。默认为True。"}},$p={loading:"正在加载 API 文档..."},Wp={title:"需要 API Key",description:"请输入您的 API Key 以访问服务",placeholder:"请输入 API Key",save:"保存"},Ip={showing:"显示第 {{start}} 到 {{end}} 条，共 {{total}} 条记录",page:"页",pageSize:"每页显示",firstPage:"首页",prevPage:"上一页",nextPage:"下一页",lastPage:"末页"},ey={settings:Qp,header:Kp,login:Zp,common:kp,documentPanel:Jp,graphPanel:Fp,retrievePanel:Pp,apiSite:$p,apiKeyAlert:Wp,pagination:Ip},ty={language:"Langue",theme:"Thème",light:"Clair",dark:"Sombre",system:"Système"},ly={documents:"Documents",knowledgeGraph:"Graphe de connaissances",retrieval:"Récupération",api:"API",projectRepository:"Référentiel du projet",logout:"Déconnexion",themeToggle:{switchToLight:"Passer au thème clair",switchToDark:"Passer au thème sombre"}},ay={description:"Veuillez entrer votre compte et mot de passe pour vous connecter au système",username:"Nom d'utilisateur",usernamePlaceholder:"Veuillez saisir un nom d'utilisateur",password:"Mot de passe",passwordPlaceholder:"Veuillez saisir un mot de passe",loginButton:"Connexion",loggingIn:"Connexion en cours...",successMessage:"Connexion réussie",errorEmptyFields:"Veuillez saisir votre nom d'utilisateur et mot de passe",errorInvalidCredentials:"Échec de la connexion, veuillez vérifier le nom d'utilisateur et le mot de passe",authDisabled:"L'authentification est désactivée. Utilisation du mode sans connexion.",guestMode:"Mode sans connexion"},ny={cancel:"Annuler",save:"Sauvegarder",saving:"Sauvegarde en cours...",saveFailed:"Échec de la sauvegarde"},uy={clearDocuments:{button:"Effacer",tooltip:"Effacer les documents",title:"Effacer les documents",description:"Cette action supprimera tous les documents du système",warning:"ATTENTION : Cette action supprimera définitivement tous les documents et ne peut pas être annulée !",confirm:"Voulez-vous vraiment effacer tous les documents ?",confirmPrompt:"Tapez 'yes' pour confirmer cette action",confirmPlaceholder:"Tapez yes pour confirmer",clearCache:"Effacer le cache LLM",confirmButton:"OUI",clearing:"Effacement en cours...",timeout:"L'opération d'effacement a expiré, veuillez réessayer",success:"Documents effacés avec succès",cacheCleared:"Cache effacé avec succès",cacheClearFailed:`Échec de l'effacement du cache :
{{error}}`,failed:`Échec de l'effacement des documents :
{{message}}`,error:`Échec de l'effacement des documents :
{{error}}`},deleteDocuments:{button:"Supprimer",tooltip:"Supprimer les documents sélectionnés",title:"Supprimer les documents",description:"Cette action supprimera définitivement les documents sélectionnés du système",warning:"ATTENTION : Cette action supprimera définitivement les documents sélectionnés et ne peut pas être annulée !",confirm:"Voulez-vous vraiment supprimer {{count}} document(s) sélectionné(s) ?",confirmPrompt:"Tapez 'yes' pour confirmer cette action",confirmPlaceholder:"Tapez yes pour confirmer",confirmButton:"OUI",deleteFileOption:"Supprimer également les fichiers téléchargés",deleteFileTooltip:"Cochez cette option pour supprimer également les fichiers téléchargés correspondants sur le serveur",success:"Pipeline de suppression de documents démarré avec succès",failed:`Échec de la suppression des documents :
{{message}}`,error:`Échec de la suppression des documents :
{{error}}`,busy:"Le pipeline est occupé, veuillez réessayer plus tard",notAllowed:"Aucune autorisation pour effectuer cette opération"},selectDocuments:{selectCurrentPage:"Sélectionner la page actuelle ({{count}})",deselectAll:"Tout désélectionner ({{count}})"},uploadDocuments:{button:"Télécharger",tooltip:"Télécharger des documents",title:"Télécharger des documents",description:"Glissez-déposez vos documents ici ou cliquez pour parcourir.",single:{uploading:"Téléchargement de {{name}} : {{percent}}%",success:`Succès du téléchargement :
{{name}} téléchargé avec succès`,failed:`Échec du téléchargement :
{{name}}
{{message}}`,error:`Échec du téléchargement :
{{name}}
{{error}}`},batch:{uploading:"Téléchargement des fichiers...",success:"Fichiers téléchargés avec succès",error:"Certains fichiers n'ont pas pu être téléchargés"},generalError:`Échec du téléchargement
{{error}}`,fileTypes:"Types pris en charge : TXT, MD, DOCX, PDF, PPTX, RTF, ODT, EPUB, HTML, HTM, TEX, JSON, XML, YAML, YML, CSV, LOG, CONF, INI, PROPERTIES, SQL, BAT, SH, C, CPP, PY, JAVA, JS, TS, SWIFT, GO, RB, PHP, CSS, SCSS, LESS",fileUploader:{singleFileLimit:"Impossible de télécharger plus d'un fichier à la fois",maxFilesLimit:"Impossible de télécharger plus de {{count}} fichiers",fileRejected:"Le fichier {{name}} a été rejeté",unsupportedType:"Type de fichier non pris en charge",fileTooLarge:"Fichier trop volumineux, taille maximale {{maxSize}}",dropHere:"Déposez les fichiers ici",dragAndDrop:"Glissez et déposez les fichiers ici, ou cliquez pour sélectionner",removeFile:"Supprimer le fichier",uploadDescription:"Vous pouvez télécharger {{isMultiple ? 'plusieurs' : count}} fichiers (jusqu'à {{maxSize}} chacun)",duplicateFile:"Le nom du fichier existe déjà dans le cache du serveur"}},documentManager:{title:"Gestion des documents",scanButton:"Scanner",scanTooltip:"Scanner les documents dans le dossier d'entrée",refreshTooltip:"Réinitialiser la liste des documents",pipelineStatusButton:"État du Pipeline",pipelineStatusTooltip:"Voir l'état du pipeline",uploadedTitle:"Documents téléchargés",uploadedDescription:"Liste des documents téléchargés et leurs statuts.",emptyTitle:"Aucun document",emptyDescription:"Il n'y a pas encore de documents téléchargés.",columns:{id:"ID",fileName:"Nom du fichier",summary:"Résumé",status:"Statut",length:"Longueur",chunks:"Fragments",created:"Créé",updated:"Mis à jour",metadata:"Métadonnées",select:"Sélectionner"},status:{all:"Tous",completed:"Terminé",processing:"En traitement",pending:"En attente",failed:"Échoué"},errors:{loadFailed:`Échec du chargement des documents
{{error}}`,scanFailed:`Échec de la numérisation des documents
{{error}}`,scanProgressFailed:`Échec de l'obtention de la progression de la numérisation
{{error}}`},fileNameLabel:"Nom du fichier",showButton:"Afficher",hideButton:"Masquer",showFileNameTooltip:"Afficher le nom du fichier",hideFileNameTooltip:"Masquer le nom du fichier"},pipelineStatus:{title:"État du Pipeline",busy:"Pipeline occupé",requestPending:"Requête en attente",jobName:"Nom du travail",startTime:"Heure de début",progress:"Progression",unit:"lot",latestMessage:"Dernier message",historyMessages:"Historique des messages",errors:{fetchFailed:`Échec de la récupération de l'état du pipeline
{{error}}`}}},iy={dataIsTruncated:"Les données du graphe sont tronquées au nombre maximum de nœuds",statusDialog:{title:"Paramètres du Serveur LightRAG",description:"Afficher l'état actuel du système et les informations de connexion"},legend:"Légende",nodeTypes:{person:"Personne",category:"Catégorie",geo:"Géographique",location:"Emplacement",organization:"Organisation",event:"Événement",equipment:"Équipement",weapon:"Arme",animal:"Animal",unknown:"Inconnu",object:"Objet",group:"Groupe",technology:"Technologie",product:"Produit",document:"Document",other:"Autre"},sideBar:{settings:{settings:"Paramètres",healthCheck:"Vérification de l'état",showPropertyPanel:"Afficher le panneau des propriétés",showSearchBar:"Afficher la barre de recherche",showNodeLabel:"Afficher l'étiquette du nœud",nodeDraggable:"Nœud déplaçable",showEdgeLabel:"Afficher l'étiquette de l'arête",hideUnselectedEdges:"Masquer les arêtes non sélectionnées",edgeEvents:"Événements des arêtes",maxQueryDepth:"Profondeur maximale de la requête",maxNodes:"Nombre maximum de nœuds",maxLayoutIterations:"Itérations maximales de mise en page",resetToDefault:"Réinitialiser par défaut",edgeSizeRange:"Plage de taille des arêtes",depth:"D",max:"Max",degree:"Degré",apiKey:"Clé API",enterYourAPIkey:"Entrez votre clé API",save:"Sauvegarder",refreshLayout:"Actualiser la mise en page"},zoomControl:{zoomIn:"Zoom avant",zoomOut:"Zoom arrière",resetZoom:"Réinitialiser le zoom",rotateCamera:"Rotation horaire",rotateCameraCounterClockwise:"Rotation antihoraire"},layoutsControl:{startAnimation:"Démarrer l'animation de mise en page",stopAnimation:"Arrêter l'animation de mise en page",layoutGraph:"Mettre en page le graphe",layouts:{Circular:"Circulaire",Circlepack:"Paquet circulaire",Random:"Aléatoire",Noverlaps:"Sans chevauchement","Force Directed":"Dirigé par la force","Force Atlas":"Atlas de force"}},fullScreenControl:{fullScreen:"Plein écran",windowed:"Fenêtré"},legendControl:{toggleLegend:"Basculer la légende"}},statusIndicator:{connected:"Connecté",disconnected:"Déconnecté"},statusCard:{unavailable:"Informations sur l'état indisponibles",serverInfo:"Informations du serveur",workingDirectory:"Répertoire de travail",inputDirectory:"Répertoire d'entrée",maxParallelInsert:"Traitement simultané des documents",summarySettings:"Paramètres de résumé",llmConfig:"Configuration du modèle de langage",llmBinding:"Liaison du modèle de langage",llmBindingHost:"Point de terminaison LLM",llmModel:"Modèle de langage",embeddingConfig:"Configuration d'incorporation",embeddingBinding:"Liaison d'incorporation",embeddingBindingHost:"Point de terminaison d'incorporation",embeddingModel:"Modèle d'incorporation",storageConfig:"Configuration de stockage",kvStorage:"Stockage clé-valeur",docStatusStorage:"Stockage de l'état des documents",graphStorage:"Stockage du graphe",vectorStorage:"Stockage vectoriel",workspace:"Espace de travail",maxGraphNodes:"Nombre maximum de nœuds du graphe",rerankerConfig:"Configuration du reclassement",rerankerBindingHost:"Point de terminaison de reclassement",rerankerModel:"Modèle de reclassement",lockStatus:"État des verrous",threshold:"Seuil"},propertiesView:{editProperty:"Modifier {{property}}",editPropertyDescription:"Modifiez la valeur de la propriété dans la zone de texte ci-dessous.",errors:{duplicateName:"Le nom du nœud existe déjà",updateFailed:"Échec de la mise à jour du nœud",tryAgainLater:"Veuillez réessayer plus tard"},success:{entityUpdated:"Nœud mis à jour avec succès",relationUpdated:"Relation mise à jour avec succès"},node:{title:"Nœud",id:"ID",labels:"Étiquettes",degree:"Degré",properties:"Propriétés",relationships:"Relations(dans le sous-graphe)",expandNode:"Développer le nœud",pruneNode:"Élaguer le nœud",deleteAllNodesError:"Refus de supprimer tous les nœuds du graphe",nodesRemoved:"{{count}} nœuds supprimés, y compris les nœuds orphelins",noNewNodes:"Aucun nœud développable trouvé",propertyNames:{description:"Description",entity_id:"Nom",entity_type:"Type",source_id:"ID source",Neighbour:"Voisin",file_path:"Source",keywords:"Keys",weight:"Poids"}},edge:{title:"Relation",id:"ID",type:"Type",source:"Source",target:"Cible",properties:"Propriétés"}},search:{placeholder:"Rechercher des nœuds...",message:"Et {{count}} autres"},graphLabels:{selectTooltip:"Sélectionner l'étiquette de la requête",noLabels:"Aucune étiquette trouvée",label:"Étiquette",placeholder:"Rechercher des étiquettes...",andOthers:"Et {{count}} autres",refreshTooltip:"Recharger les données (Après l'ajout de fichier)"},emptyGraph:"Vide (Essayez de recharger)"},cy={chatMessage:{copyTooltip:"Copier dans le presse-papiers",copyError:"Échec de la copie du texte dans le presse-papiers",thinking:"Réflexion en cours...",thinkingTime:"Temps de réflexion {{time}}s",thinkingInProgress:"Réflexion en cours..."},retrieval:{startPrompt:"Démarrez une récupération en tapant votre requête ci-dessous",clear:"Effacer",send:"Envoyer",placeholder:"Tapez votre requête (Préfixe de requête : /<Query Mode>)",error:"Erreur : Échec de l'obtention de la réponse",queryModeError:"Seuls les modes de requête suivants sont pris en charge : {{modes}}",queryModePrefixInvalid:"Préfixe de mode de requête invalide. Utilisez : /<mode> [espace] votre requête"},querySettings:{parametersTitle:"Paramètres",parametersDescription:"Configurez vos paramètres de requête",queryMode:"Mode de requête",queryModeTooltip:`Sélectionnez la stratégie de récupération :
• Naïf : Recherche de base sans techniques avancées
• Local : Récupération d'informations dépendante du contexte
• Global : Utilise une base de connaissances globale
• Hybride : Combine récupération locale et globale
• Mixte : Intègre le graphe de connaissances avec la récupération vectorielle
• Bypass : Transmet directement la requête au LLM sans récupération`,queryModeOptions:{naive:"Naïf",local:"Local",global:"Global",hybrid:"Hybride",mix:"Mixte",bypass:"Bypass"},responseFormat:"Format de réponse",responseFormatTooltip:`Définit le format de la réponse. Exemples :
• Plusieurs paragraphes
• Paragraphe unique
• Points à puces`,responseFormatOptions:{multipleParagraphs:"Plusieurs paragraphes",singleParagraph:"Paragraphe unique",bulletPoints:"Points à puces"},topK:"KG Top K",topKTooltip:"Nombre d'entités et de relations à récupérer. Applicable pour les modes non-naïfs.",topKPlaceholder:"Entrez la valeur top_k",chunkTopK:"Top K des Chunks",chunkTopKTooltip:"Nombre de morceaux de texte à récupérer, applicable à tous les modes.",chunkTopKPlaceholder:"Entrez la valeur chunk_top_k",maxEntityTokens:"Limite de jetons d'entité",maxEntityTokensTooltip:"Nombre maximum de jetons alloués au contexte d'entité dans le système de contrôle de jetons unifié",maxRelationTokens:"Limite de jetons de relation",maxRelationTokensTooltip:"Nombre maximum de jetons alloués au contexte de relation dans le système de contrôle de jetons unifié",maxTotalTokens:"Limite totale de jetons",maxTotalTokensTooltip:"Budget total maximum de jetons pour l'ensemble du contexte de requête (entités + relations + blocs + prompt système)",historyTurns:"Tours d'historique",historyTurnsTooltip:"Nombre de tours complets de conversation (paires utilisateur-assistant) à prendre en compte dans le contexte de la réponse",historyTurnsPlaceholder:"Nombre de tours d'historique",onlyNeedContext:"Besoin uniquement du contexte",onlyNeedContextTooltip:"Si vrai, ne renvoie que le contexte récupéré sans générer de réponse",onlyNeedPrompt:"Besoin uniquement de l'invite",onlyNeedPromptTooltip:"Si vrai, ne renvoie que l'invite générée sans produire de réponse",streamResponse:"Réponse en flux",streamResponseTooltip:"Si vrai, active la sortie en flux pour des réponses en temps réel",userPrompt:"Invite personnalisée",userPromptTooltip:"Fournir des exigences de réponse supplémentaires au LLM (sans rapport avec le contenu de la requête, uniquement pour le traitement de sortie).",userPromptPlaceholder:"Entrez une invite personnalisée (facultatif)",enableRerank:"Activer le Reclassement",enableRerankTooltip:"Active le reclassement pour les fragments de texte récupérés. Si True mais qu'aucun modèle de reclassement n'est configuré, un avertissement sera émis. True par défaut."}},sy={loading:"Chargement de la documentation de l'API..."},oy={title:"Clé API requise",description:"Veuillez entrer votre clé API pour accéder au service",placeholder:"Entrez votre clé API",save:"Sauvegarder"},ry={showing:"Affichage de {{start}} à {{end}} sur {{total}} entrées",page:"Page",pageSize:"Taille de la page",firstPage:"Première page",prevPage:"Page précédente",nextPage:"Page suivante",lastPage:"Dernière page"},fy={settings:ty,header:ly,login:ay,common:ny,documentPanel:uy,graphPanel:iy,retrievePanel:cy,apiSite:sy,apiKeyAlert:oy,pagination:ry},dy={language:"اللغة",theme:"السمة",light:"فاتح",dark:"داكن",system:"النظام"},my={documents:"المستندات",knowledgeGraph:"شبكة المعرفة",retrieval:"الاسترجاع",api:"واجهة برمجة التطبيقات",projectRepository:"مستودع المشروع",logout:"تسجيل الخروج",themeToggle:{switchToLight:"التحويل إلى السمة الفاتحة",switchToDark:"التحويل إلى السمة الداكنة"}},hy={description:"الرجاء إدخال حسابك وكلمة المرور لتسجيل الدخول إلى النظام",username:"اسم المستخدم",usernamePlaceholder:"الرجاء إدخال اسم المستخدم",password:"كلمة المرور",passwordPlaceholder:"الرجاء إدخال كلمة المرور",loginButton:"تسجيل الدخول",loggingIn:"جاري تسجيل الدخول...",successMessage:"تم تسجيل الدخول بنجاح",errorEmptyFields:"الرجاء إدخال اسم المستخدم وكلمة المرور",errorInvalidCredentials:"فشل تسجيل الدخول، يرجى التحقق من اسم المستخدم وكلمة المرور",authDisabled:"تم تعطيل المصادقة. استخدام وضع بدون تسجيل دخول.",guestMode:"وضع بدون تسجيل دخول"},gy={cancel:"إلغاء",save:"حفظ",saving:"جارٍ الحفظ...",saveFailed:"فشل الحفظ"},py={clearDocuments:{button:"مسح",tooltip:"مسح المستندات",title:"مسح المستندات",description:"سيؤدي هذا إلى إزالة جميع المستندات من النظام",warning:"تحذير: سيؤدي هذا الإجراء إلى حذف جميع المستندات بشكل دائم ولا يمكن التراجع عنه!",confirm:"هل تريد حقًا مسح جميع المستندات؟",confirmPrompt:"اكتب 'yes' لتأكيد هذا الإجراء",confirmPlaceholder:"اكتب yes للتأكيد",clearCache:"مسح كاش نموذج اللغة",confirmButton:"نعم",clearing:"جارٍ المسح...",timeout:"انتهت مهلة عملية المسح، يرجى المحاولة مرة أخرى",success:"تم مسح المستندات بنجاح",cacheCleared:"تم مسح ذاكرة التخزين المؤقت بنجاح",cacheClearFailed:`فشل مسح ذاكرة التخزين المؤقت:
{{error}}`,failed:`فشل مسح المستندات:
{{message}}`,error:`فشل مسح المستندات:
{{error}}`},deleteDocuments:{button:"حذف",tooltip:"حذف المستندات المحددة",title:"حذف المستندات",description:"سيؤدي هذا إلى حذف المستندات المحددة نهائيًا من النظام",warning:"تحذير: سيؤدي هذا الإجراء إلى حذف المستندات المحددة نهائيًا ولا يمكن التراجع عنه!",confirm:"هل تريد حقًا حذف {{count}} مستند(ات) محدد(ة)؟",confirmPrompt:"اكتب 'yes' لتأكيد هذا الإجراء",confirmPlaceholder:"اكتب yes للتأكيد",confirmButton:"نعم",deleteFileOption:"حذف الملفات المرفوعة أيضًا",deleteFileTooltip:"حدد هذا الخيار لحذف الملفات المرفوعة المقابلة على الخادم أيضًا",success:"تم بدء تشغيل خط معالجة حذف المستندات بنجاح",failed:`فشل حذف المستندات:
{{message}}`,error:`فشل حذف المستندات:
{{error}}`,busy:"خط المعالجة مشغول، يرجى المحاولة مرة أخرى لاحقًا",notAllowed:"لا توجد صلاحية لتنفيذ هذه العملية"},selectDocuments:{selectCurrentPage:"تحديد الصفحة الحالية ({{count}})",deselectAll:"إلغاء تحديد الكل ({{count}})"},uploadDocuments:{button:"رفع",tooltip:"رفع المستندات",title:"رفع المستندات",description:"اسحب وأفلت مستنداتك هنا أو انقر للتصفح.",single:{uploading:"جارٍ الرفع {{name}}: {{percent}}%",success:`نجاح الرفع:
تم رفع {{name}} بنجاح`,failed:`فشل الرفع:
{{name}}
{{message}}`,error:`فشل الرفع:
{{name}}
{{error}}`},batch:{uploading:"جارٍ رفع الملفات...",success:"تم رفع الملفات بنجاح",error:"فشل رفع بعض الملفات"},generalError:`فشل الرفع
{{error}}`,fileTypes:"الأنواع المدعومة: TXT، MD، DOCX، PDF، PPTX، RTF، ODT، EPUB، HTML، HTM، TEX، JSON، XML، YAML، YML، CSV، LOG، CONF، INI، PROPERTIES، SQL، BAT، SH، C، CPP، PY، JAVA، JS، TS، SWIFT، GO، RB، PHP، CSS، SCSS، LESS",fileUploader:{singleFileLimit:"لا يمكن رفع أكثر من ملف واحد في المرة الواحدة",maxFilesLimit:"لا يمكن رفع أكثر من {{count}} ملفات",fileRejected:"تم رفض الملف {{name}}",unsupportedType:"نوع الملف غير مدعوم",fileTooLarge:"حجم الملف كبير جدًا، الحد الأقصى {{maxSize}}",dropHere:"أفلت الملفات هنا",dragAndDrop:"اسحب وأفلت الملفات هنا، أو انقر للاختيار",removeFile:"إزالة الملف",uploadDescription:"يمكنك رفع {{isMultiple ? 'عدة' : count}} ملفات (حتى {{maxSize}} لكل منها)",duplicateFile:"اسم الملف موجود بالفعل في ذاكرة التخزين المؤقت للخادم"}},documentManager:{title:"إدارة المستندات",scanButton:"مسح ضوئي",scanTooltip:"مسح المستندات ضوئيًا في مجلد الإدخال",refreshTooltip:"إعادة تعيين قائمة المستندات",pipelineStatusButton:"حالة خط المعالجة",pipelineStatusTooltip:"عرض حالة خط المعالجة",uploadedTitle:"المستندات المرفوعة",uploadedDescription:"قائمة المستندات المرفوعة وحالاتها.",emptyTitle:"لا توجد مستندات",emptyDescription:"لا توجد مستندات مرفوعة بعد.",columns:{id:"المعرف",fileName:"اسم الملف",summary:"الملخص",status:"الحالة",length:"الطول",chunks:"الأجزاء",created:"تم الإنشاء",updated:"تم التحديث",metadata:"البيانات الوصفية",select:"اختيار"},status:{all:"الكل",completed:"مكتمل",processing:"قيد المعالجة",pending:"معلق",failed:"فشل"},errors:{loadFailed:`فشل تحميل المستندات
{{error}}`,scanFailed:`فشل مسح المستندات
{{error}}`,scanProgressFailed:`فشل الحصول على تقدم المسح
{{error}}`},fileNameLabel:"اسم الملف",showButton:"عرض",hideButton:"إخفاء",showFileNameTooltip:"عرض اسم الملف",hideFileNameTooltip:"إخفاء اسم الملف"},pipelineStatus:{title:"حالة خط المعالجة",busy:"خط المعالجة مشغول",requestPending:"الطلب معلق",jobName:"اسم المهمة",startTime:"وقت البدء",progress:"التقدم",unit:"دفعة",latestMessage:"آخر رسالة",historyMessages:"سجل الرسائل",errors:{fetchFailed:`فشل في جلب حالة خط المعالجة
{{error}}`}}},yy={dataIsTruncated:"تم اقتصار بيانات الرسم البياني على الحد الأقصى للعقد",statusDialog:{title:"إعدادات خادم LightRAG",description:"عرض حالة النظام الحالية ومعلومات الاتصال"},legend:"المفتاح",nodeTypes:{person:"شخص",category:"فئة",geo:"كيان جغرافي",location:"موقع",organization:"منظمة",event:"حدث",equipment:"معدات",weapon:"سلاح",animal:"حيوان",unknown:"غير معروف",object:"مصنوع",group:"مجموعة",technology:"العلوم",product:"منتج",document:"وثيقة",other:"أخرى"},sideBar:{settings:{settings:"الإعدادات",healthCheck:"فحص الحالة",showPropertyPanel:"إظهار لوحة الخصائص",showSearchBar:"إظهار شريط البحث",showNodeLabel:"إظهار تسمية العقدة",nodeDraggable:"العقدة قابلة للسحب",showEdgeLabel:"إظهار تسمية الحافة",hideUnselectedEdges:"إخفاء الحواف غير المحددة",edgeEvents:"أحداث الحافة",maxQueryDepth:"أقصى عمق للاستعلام",maxNodes:"الحد الأقصى للعقد",maxLayoutIterations:"أقصى تكرارات التخطيط",resetToDefault:"إعادة التعيين إلى الافتراضي",edgeSizeRange:"نطاق حجم الحافة",depth:"D",max:"Max",degree:"الدرجة",apiKey:"مفتاح واجهة برمجة التطبيقات",enterYourAPIkey:"أدخل مفتاح واجهة برمجة التطبيقات الخاص بك",save:"حفظ",refreshLayout:"تحديث التخطيط"},zoomControl:{zoomIn:"تكبير",zoomOut:"تصغير",resetZoom:"إعادة تعيين التكبير",rotateCamera:"تدوير في اتجاه عقارب الساعة",rotateCameraCounterClockwise:"تدوير عكس اتجاه عقارب الساعة"},layoutsControl:{startAnimation:"بدء حركة التخطيط",stopAnimation:"إيقاف حركة التخطيط",layoutGraph:"تخطيط الرسم البياني",layouts:{Circular:"دائري",Circlepack:"حزمة دائرية",Random:"عشوائي",Noverlaps:"بدون تداخل","Force Directed":"موجه بالقوة","Force Atlas":"أطلس القوة"}},fullScreenControl:{fullScreen:"شاشة كاملة",windowed:"نوافذ"},legendControl:{toggleLegend:"تبديل المفتاح"}},statusIndicator:{connected:"متصل",disconnected:"غير متصل"},statusCard:{unavailable:"معلومات الحالة غير متوفرة",serverInfo:"معلومات الخادم",workingDirectory:"دليل العمل",inputDirectory:"دليل الإدخال",maxParallelInsert:"معالجة المستندات المتزامنة",summarySettings:"إعدادات الملخص",llmConfig:"تكوين نموذج اللغة الكبير",llmBinding:"ربط نموذج اللغة الكبير",llmBindingHost:"نقطة نهاية نموذج اللغة الكبير",llmModel:"نموذج اللغة الكبير",embeddingConfig:"تكوين التضمين",embeddingBinding:"ربط التضمين",embeddingBindingHost:"نقطة نهاية التضمين",embeddingModel:"نموذج التضمين",storageConfig:"تكوين التخزين",kvStorage:"تخزين المفتاح-القيمة",docStatusStorage:"تخزين حالة المستند",graphStorage:"تخزين الرسم البياني",vectorStorage:"تخزين المتجهات",workspace:"مساحة العمل",maxGraphNodes:"الحد الأقصى لعقد الرسم البياني",rerankerConfig:"تكوين إعادة الترتيب",rerankerBindingHost:"نقطة نهاية إعادة الترتيب",rerankerModel:"نموذج إعادة الترتيب",lockStatus:"حالة القفل",threshold:"العتبة"},propertiesView:{editProperty:"تعديل {{property}}",editPropertyDescription:"قم بتحرير قيمة الخاصية في منطقة النص أدناه.",errors:{duplicateName:"اسم العقدة موجود بالفعل",updateFailed:"فشل تحديث العقدة",tryAgainLater:"يرجى المحاولة مرة أخرى لاحقًا"},success:{entityUpdated:"تم تحديث العقدة بنجاح",relationUpdated:"تم تحديث العلاقة بنجاح"},node:{title:"عقدة",id:"المعرف",labels:"التسميات",degree:"الدرجة",properties:"الخصائص",relationships:"العلاقات (داخل الرسم الفرعي)",expandNode:"توسيع العقدة",pruneNode:"تقليم العقدة",deleteAllNodesError:"رفض حذف جميع العقد في الرسم البياني",nodesRemoved:"تم إزالة {{count}} عقدة، بما في ذلك العقد اليتيمة",noNewNodes:"لم يتم العثور على عقد قابلة للتوسيع",propertyNames:{description:"الوصف",entity_id:"الاسم",entity_type:"النوع",source_id:"معرف المصدر",Neighbour:"الجار",file_path:"المصدر",keywords:"الكلمات الرئيسية",weight:"الوزن"}},edge:{title:"علاقة",id:"المعرف",type:"النوع",source:"المصدر",target:"الهدف",properties:"الخصائص"}},search:{placeholder:"ابحث في العقد...",message:"و {{count}} آخرون"},graphLabels:{selectTooltip:"حدد تسمية الاستعلام",noLabels:"لم يتم العثور على تسميات",label:"التسمية",placeholder:"ابحث في التسميات...",andOthers:"و {{count}} آخرون",refreshTooltip:"إعادة تحميل البيانات (بعد إضافة الملف)"},emptyGraph:"فارغ (حاول إعادة التحميل)"},vy={chatMessage:{copyTooltip:"نسخ إلى الحافظة",copyError:"فشل نسخ النص إلى الحافظة",thinking:"جاري التفكير...",thinkingTime:"وقت التفكير {{time}} ثانية",thinkingInProgress:"التفكير قيد التقدم..."},retrieval:{startPrompt:"ابدأ الاسترجاع بكتابة استفسارك أدناه",clear:"مسح",send:"إرسال",placeholder:"اكتب استفسارك (بادئة وضع الاستعلام: /<Query Mode>)",error:"خطأ: فشل الحصول على الرد",queryModeError:"يُسمح فقط بأنماط الاستعلام التالية: {{modes}}",queryModePrefixInvalid:"بادئة وضع الاستعلام غير صالحة. استخدم: /<الوضع> [مسافة] استفسارك"},querySettings:{parametersTitle:"المعلمات",parametersDescription:"تكوين معلمات الاستعلام الخاص بك",queryMode:"وضع الاستعلام",queryModeTooltip:`حدد استراتيجية الاسترجاع:
• ساذج: بحث أساسي بدون تقنيات متقدمة
• محلي: استرجاع معلومات يعتمد على السياق
• عالمي: يستخدم قاعدة المعرفة العالمية
• مختلط: يجمع بين الاسترجاع المحلي والعالمي
• مزيج: يدمج شبكة المعرفة مع الاسترجاع المتجهي
• تجاوز: يمرر الاستعلام مباشرة إلى LLM بدون استرجاع`,queryModeOptions:{naive:"ساذج",local:"محلي",global:"عالمي",hybrid:"مختلط",mix:"مزيج",bypass:"تجاوز"},responseFormat:"تنسيق الرد",responseFormatTooltip:`يحدد تنسيق الرد. أمثلة:
• فقرات متعددة
• فقرة واحدة
• نقاط نقطية`,responseFormatOptions:{multipleParagraphs:"فقرات متعددة",singleParagraph:"فقرة واحدة",bulletPoints:"نقاط نقطية"},topK:"KG أعلى K",topKTooltip:"عدد الكيانات والعلاقات المطلوب استردادها، لا ينطبق على الوضع наивный.",topKPlaceholder:"أدخل قيمة top_k",chunkTopK:"أعلى K للقطع",chunkTopKTooltip:"عدد أجزاء النص المطلوب استردادها، وينطبق على جميع الأوضاع.",chunkTopKPlaceholder:"أدخل قيمة chunk_top_k",maxEntityTokens:"الحد الأقصى لرموز الكيان",maxEntityTokensTooltip:"الحد الأقصى لعدد الرموز المخصصة لسياق الكيان في نظام التحكم الموحد في الرموز",maxRelationTokens:"الحد الأقصى لرموز العلاقة",maxRelationTokensTooltip:"الحد الأقصى لعدد الرموز المخصصة لسياق العلاقة في نظام التحكم الموحد في الرموز",maxTotalTokens:"إجمالي الحد الأقصى للرموز",maxTotalTokensTooltip:"الحد الأقصى الإجمالي لميزانية الرموز لسياق الاستعلام بالكامل (الكيانات + العلاقات + الأجزاء + موجه النظام)",historyTurns:"أدوار التاريخ",historyTurnsTooltip:"عدد الدورات الكاملة للمحادثة (أزواج المستخدم-المساعد) التي يجب مراعاتها في سياق الرد",historyTurnsPlaceholder:"عدد دورات التاريخ",onlyNeedContext:"تحتاج فقط إلى السياق",onlyNeedContextTooltip:"إذا كان صحيحًا، يتم إرجاع السياق المسترجع فقط دون إنشاء رد",onlyNeedPrompt:"تحتاج فقط إلى المطالبة",onlyNeedPromptTooltip:"إذا كان صحيحًا، يتم إرجاع المطالبة المولدة فقط دون إنتاج رد",streamResponse:"تدفق الرد",streamResponseTooltip:"إذا كان صحيحًا، يتيح إخراج التدفق للردود في الوقت الفعلي",userPrompt:"مطالبة مخصصة",userPromptTooltip:"تقديم متطلبات استجابة إضافية إلى نموذج اللغة الكبير (غير متعلقة بمحتوى الاستعلام، فقط لمعالجة المخرجات).",userPromptPlaceholder:"أدخل مطالبة مخصصة (اختياري)",enableRerank:"تمكين إعادة الترتيب",enableRerankTooltip:"تمكين إعادة ترتيب أجزاء النص المسترجعة. إذا كان True ولكن لم يتم تكوين نموذج إعادة الترتيب، فسيتم إصدار تحذير. افتراضي True."}},by={loading:"جارٍ تحميل وثائق واجهة برمجة التطبيقات..."},Sy={title:"مفتاح واجهة برمجة التطبيقات مطلوب",description:"الرجاء إدخال مفتاح واجهة برمجة التطبيقات للوصول إلى الخدمة",placeholder:"أدخل مفتاح واجهة برمجة التطبيقات",save:"حفظ"},Ty={showing:"عرض {{start}} إلى {{end}} من أصل {{total}} إدخالات",page:"الصفحة",pageSize:"حجم الصفحة",firstPage:"الصفحة الأولى",prevPage:"الصفحة السابقة",nextPage:"الصفحة التالية",lastPage:"الصفحة الأخيرة"},xy={settings:dy,header:my,login:hy,common:gy,documentPanel:py,graphPanel:yy,retrievePanel:vy,apiSite:by,apiKeyAlert:Sy,pagination:Ty},Ay={language:"語言",theme:"主題",light:"淺色",dark:"深色",system:"系統"},Dy={documents:"文件",knowledgeGraph:"知識圖譜",retrieval:"檢索",api:"API",projectRepository:"專案庫",logout:"登出",themeToggle:{switchToLight:"切換至淺色主題",switchToDark:"切換至深色主題"}},Ny={description:"請輸入您的帳號和密碼登入系統",username:"帳號",usernamePlaceholder:"請輸入帳號",password:"密碼",passwordPlaceholder:"請輸入密碼",loginButton:"登入",loggingIn:"登入中...",successMessage:"登入成功",errorEmptyFields:"請輸入您的帳號和密碼",errorInvalidCredentials:"登入失敗，請檢查帳號和密碼",authDisabled:"認證已停用，使用免登入模式",guestMode:"免登入"},Ey={cancel:"取消",save:"儲存",saving:"儲存中...",saveFailed:"儲存失敗"},My={clearDocuments:{button:"清空",tooltip:"清空文件",title:"清空文件",description:"此操作將從系統中移除所有文件",warning:"警告：此操作將永久刪除所有文件，無法復原！",confirm:"確定要清空所有文件嗎？",confirmPrompt:"請輸入 yes 確認操作",confirmPlaceholder:"輸入 yes 以確認",clearCache:"清空 LLM 快取",confirmButton:"確定",clearing:"正在清除...",timeout:"清除操作逾時，請重試",success:"文件清空成功",cacheCleared:"快取清空成功",cacheClearFailed:`清空快取失敗：
{{error}}`,failed:`清空文件失敗：
{{message}}`,error:`清空文件失敗：
{{error}}`},deleteDocuments:{button:"刪除",tooltip:"刪除選取的文件",title:"刪除文件",description:"此操作將永久刪除選取的文件",warning:"警告：此操作將永久刪除選取的文件，無法復原！",confirm:"確定要刪除 {{count}} 個選取的文件嗎？",confirmPrompt:"請輸入 yes 確認操作",confirmPlaceholder:"輸入 yes 以確認",confirmButton:"確定",deleteFileOption:"同時刪除上傳檔案",deleteFileTooltip:"選取此選項將同時刪除伺服器上對應的上傳檔案",success:"文件刪除流水線啟動成功",failed:`刪除文件失敗：
{{message}}`,error:`刪除文件失敗：
{{error}}`,busy:"pipeline 被佔用，請稍後再試",notAllowed:"沒有操作權限"},selectDocuments:{selectCurrentPage:"全選當前頁 ({{count}})",deselectAll:"取消全選 ({{count}})"},uploadDocuments:{button:"上傳",tooltip:"上傳文件",title:"上傳文件",description:"拖曳檔案至此處或點擊瀏覽",single:{uploading:"正在上傳 {{name}}：{{percent}}%",success:`上傳成功：
{{name}} 上傳完成`,failed:`上傳失敗：
{{name}}
{{message}}`,error:`上傳失敗：
{{name}}
{{error}}`},batch:{uploading:"正在上傳檔案...",success:"檔案上傳完成",error:"部分檔案上傳失敗"},generalError:`上傳失敗
{{error}}`,fileTypes:"支援的檔案類型：TXT, MD, DOCX, PDF, PPTX, XLSX, RTF, ODT, EPUB, HTML, HTM, TEX, JSON, XML, YAML, YML, CSV, LOG, CONF, INI, PROPERTIES, SQL, BAT, SH, C, CPP, PY, JAVA, JS, TS, SWIFT, GO, RB, PHP, CSS, SCSS, LESS",fileUploader:{singleFileLimit:"一次只能上傳一個檔案",maxFilesLimit:"最多只能上傳 {{count}} 個檔案",fileRejected:"檔案 {{name}} 被拒絕",unsupportedType:"不支援的檔案類型",fileTooLarge:"檔案過大，最大允許 {{maxSize}}",dropHere:"將檔案拖放至此處",dragAndDrop:"拖放檔案至此處，或點擊選擇檔案",removeFile:"移除檔案",uploadDescription:"您可以上傳{{isMultiple ? '多個' : count}}個檔案（每個檔案最大{{maxSize}}）",duplicateFile:"檔案名稱與伺服器上的快取重複"}},documentManager:{title:"文件管理",scanButton:"掃描",scanTooltip:"掃描輸入目錄中的文件",refreshTooltip:"重設文件清單",pipelineStatusButton:"pipeline 狀態",pipelineStatusTooltip:"查看pipeline 狀態",uploadedTitle:"已上傳文件",uploadedDescription:"已上傳文件清單及其狀態",emptyTitle:"無文件",emptyDescription:"尚未上傳任何文件",columns:{id:"ID",fileName:"檔案名稱",summary:"摘要",status:"狀態",length:"長度",chunks:"分塊",created:"建立時間",updated:"更新時間",metadata:"元資料",select:"選擇"},status:{all:"全部",completed:"已完成",processing:"處理中",pending:"等待中",failed:"失敗"},errors:{loadFailed:`載入文件失敗
{{error}}`,scanFailed:`掃描文件失敗
{{error}}`,scanProgressFailed:`取得掃描進度失敗
{{error}}`},fileNameLabel:"檔案名稱",showButton:"顯示",hideButton:"隱藏",showFileNameTooltip:"顯示檔案名稱",hideFileNameTooltip:"隱藏檔案名稱"},pipelineStatus:{title:"pipeline 狀態",busy:"pipeline 忙碌中",requestPending:"待處理請求",jobName:"工作名稱",startTime:"開始時間",progress:"進度",unit:"梯次",latestMessage:"最新訊息",historyMessages:"歷史訊息",errors:{fetchFailed:`取得pipeline 狀態失敗
{{error}}`}}},zy={dataIsTruncated:"圖資料已截斷至最大回傳節點數",statusDialog:{title:"LightRAG 伺服器設定",description:"查看目前系統狀態和連線資訊"},legend:"圖例",nodeTypes:{person:"人物角色",category:"分類",geo:"地理名稱",location:"位置",organization:"組織機構",event:"事件",equipment:"設備",weapon:"武器",animal:"動物",unknown:"未知",object:"物品",group:"群組",technology:"技術",product:"產品",document:"文檔",other:"其他"},sideBar:{settings:{settings:"設定",healthCheck:"健康檢查",showPropertyPanel:"顯示屬性面板",showSearchBar:"顯示搜尋列",showNodeLabel:"顯示節點標籤",nodeDraggable:"節點可拖曳",showEdgeLabel:"顯示 Edge 標籤",hideUnselectedEdges:"隱藏未選取的 Edge",edgeEvents:"Edge 事件",maxQueryDepth:"最大查詢深度",maxNodes:"最大回傳節點數",maxLayoutIterations:"最大版面配置迭代次數",resetToDefault:"重設為預設值",edgeSizeRange:"Edge 粗細範圍",depth:"深度",max:"最大值",degree:"鄰邊",apiKey:"API key",enterYourAPIkey:"輸入您的 API key",save:"儲存",refreshLayout:"重新整理版面配置"},zoomControl:{zoomIn:"放大",zoomOut:"縮小",resetZoom:"重設縮放",rotateCamera:"順時針旋轉圖形",rotateCameraCounterClockwise:"逆時針旋轉圖形"},layoutsControl:{startAnimation:"繼續版面配置動畫",stopAnimation:"停止版面配置動畫",layoutGraph:"圖形版面配置",layouts:{Circular:"環形",Circlepack:"圓形打包",Random:"隨機",Noverlaps:"無重疊","Force Directed":"力導向","Force Atlas":"力圖"}},fullScreenControl:{fullScreen:"全螢幕",windowed:"視窗"},legendControl:{toggleLegend:"切換圖例顯示"}},statusIndicator:{connected:"已連線",disconnected:"未連線"},statusCard:{unavailable:"狀態資訊不可用",serverInfo:"伺服器資訊",workingDirectory:"工作目錄",inputDirectory:"輸入目錄",maxParallelInsert:"並行處理文档",summarySettings:"摘要設定",llmConfig:"LLM 設定",llmBinding:"LLM 綁定",llmBindingHost:"LLM 端點",llmModel:"LLM 模型",embeddingConfig:"嵌入設定",embeddingBinding:"嵌入綁定",embeddingBindingHost:"嵌入端點",embeddingModel:"嵌入模型",storageConfig:"儲存設定",kvStorage:"KV 儲存",docStatusStorage:"文件狀態儲存",graphStorage:"圖形儲存",vectorStorage:"向量儲存",workspace:"工作空間",maxGraphNodes:"最大圖形節點數",rerankerConfig:"重排序設定",rerankerBindingHost:"重排序端點",rerankerModel:"重排序模型",lockStatus:"鎖定狀態",threshold:"閾值"},propertiesView:{editProperty:"編輯{{property}}",editPropertyDescription:"在下方文字區域編輯屬性值。",errors:{duplicateName:"節點名稱已存在",updateFailed:"更新節點失敗",tryAgainLater:"請稍後重試"},success:{entityUpdated:"節點更新成功",relationUpdated:"關係更新成功"},node:{title:"節點",id:"ID",labels:"標籤",degree:"度數",properties:"屬性",relationships:"關係(子圖內)",expandNode:"展開節點",pruneNode:"修剪節點",deleteAllNodesError:"拒絕刪除圖中的所有節點",nodesRemoved:"已刪除 {{count}} 個節點，包括孤立節點",noNewNodes:"沒有發現可以展開的節點",propertyNames:{description:"描述",entity_id:"名稱",entity_type:"類型",source_id:"來源ID",Neighbour:"鄰接",file_path:"來源",keywords:"Keys",weight:"權重"}},edge:{title:"關係",id:"ID",type:"類型",source:"來源節點",target:"目標節點",properties:"屬性"}},search:{placeholder:"搜尋節點...",message:"還有 {count} 個"},graphLabels:{selectTooltip:"選擇查詢標籤",noLabels:"未找到標籤",label:"標籤",placeholder:"搜尋標籤...",andOthers:"還有 {count} 個",refreshTooltip:"重載圖形數據(新增檔案後需重載)"},emptyGraph:"無數據(請重載圖形數據)"},Cy={chatMessage:{copyTooltip:"複製到剪貼簿",copyError:"複製文字到剪貼簿失敗",thinking:"正在思考...",thinkingTime:"思考用時 {{time}} 秒",thinkingInProgress:"思考進行中..."},retrieval:{startPrompt:"輸入查詢開始檢索",clear:"清空",send:"送出",placeholder:"輸入查詢內容 (支援模式前綴：/<Query Mode>)",error:"錯誤：取得回應失敗",queryModeError:"僅支援以下查詢模式：{{modes}}",queryModePrefixInvalid:"無效的查詢模式前綴。請使用：/<模式> [空格] 查詢內容"},querySettings:{parametersTitle:"參數",parametersDescription:"設定查詢參數",queryMode:"查詢模式",queryModeTooltip:`選擇檢索策略：
• Naive：基礎搜尋，無進階技術
• Local：上下文相關資訊檢索
• Global：利用全域知識庫
• Hybrid：結合本地和全域檢索
• Mix：整合知識圖譜和向量檢索
• Bypass：直接傳遞查詢到LLM，不進行檢索`,queryModeOptions:{naive:"Naive",local:"Local",global:"Global",hybrid:"Hybrid",mix:"Mix",bypass:"Bypass"},responseFormat:"回應格式",responseFormatTooltip:`定義回應格式。例如：
• 多段落
• 單段落
• 重點`,responseFormatOptions:{multipleParagraphs:"多段落",singleParagraph:"單段落",bulletPoints:"重點"},topK:"知識圖譜 Top K",topKTooltip:"實體關係檢索數量，適用於非 naive 模式。",topKPlaceholder:"輸入 top_k 值",chunkTopK:"文本區塊 Top K",chunkTopKTooltip:"文本區塊檢索數量，適用於所有模式。",chunkTopKPlaceholder:"輸入文本區塊 chunk_top_k 值",historyTurns:"歷史輪次",historyTurnsTooltip:"回應上下文中考慮的完整對話輪次（使用者-助手對）數量",historyTurnsPlaceholder:"歷史輪次數",onlyNeedContext:"僅需上下文",onlyNeedContextTooltip:"如果為True，僅回傳檢索到的上下文而不產生回應",onlyNeedPrompt:"僅需提示",onlyNeedPromptTooltip:"如果為True，僅回傳產生的提示而不產生回應",streamResponse:"串流回應",streamResponseTooltip:"如果為True，啟用即時串流輸出回應",userPrompt:"用戶提示詞",userPromptTooltip:"向LLM提供額外的響應要求（與查詢內容無關，僅用於處理輸出）。",userPromptPlaceholder:"輸入自定義提示詞（可選）",enableRerank:"啟用重排",enableRerankTooltip:"為檢索到的文本塊啟用重排。如果為True但未配置重排模型，將發出警告。默認為True。",maxEntityTokens:"實體令牌數上限",maxEntityTokensTooltip:"統一令牌控制系統中分配給實體上下文的最大令牌數",maxRelationTokens:"關係令牌數上限",maxRelationTokensTooltip:"統一令牌控制系統中分配給關係上下文的最大令牌數",maxTotalTokens:"總令牌數上限",maxTotalTokensTooltip:"整個查詢上下文的最大總令牌預算（實體+關係+文檔塊+系統提示）"}},Oy={loading:"正在載入 API 文件..."},_y={title:"需要 API key",description:"請輸入您的 API key 以存取服務",placeholder:"請輸入 API key",save:"儲存"},Ry={showing:"顯示第 {{start}} 到 {{end}} 筆，共 {{total}} 筆記錄",page:"頁",pageSize:"每頁顯示",firstPage:"第一頁",prevPage:"上一頁",nextPage:"下一頁",lastPage:"最後一頁"},jy={settings:Ay,header:Dy,login:Ny,common:Ey,documentPanel:My,graphPanel:zy,retrievePanel:Cy,apiSite:Oy,apiKeyAlert:_y,pagination:Ry},Uy=()=>{var m;try{const y=localStorage.getItem("settings-storage");if(y)return((m=JSON.parse(y).state)==null?void 0:m.language)||"en"}catch(y){console.error("Failed to get stored language:",y)}return"en"};cs.use(Fg).init({resources:{en:{translation:Vp},zh:{translation:ey},fr:{translation:fy},ar:{translation:xy},zh_TW:{translation:jy}},lng:Uy(),fallbackLng:"en",interpolation:{escapeValue:!1},returnEmptyString:!1,returnNull:!1});we.subscribe(m=>{const y=m.language;cs.language!==y&&cs.changeLanguage(y)});lp.createRoot(document.getElementById("root")).render(o.jsx(E.StrictMode,{children:o.jsx(Rp,{})}));
