const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/utils-vendor-BysuhMZA.js","assets/react-vendor-DEwriMA6.js"])))=>i.map(i=>d[i]);
var di=Object.defineProperty;var fi=(e,t,r)=>t in e?di(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r;var Fe=(e,t,r)=>fi(e,typeof t!="symbol"?t+"":t,r);import{R as X,r as p,c as hi,g as He,d as gi,e as pi}from"./react-vendor-DEwriMA6.js";import{_ as aa,a as sa,f as er,N as ia,b as la,c as ca,D as wn,d as qt,F as mi,E as ua,e as vi,g as qn,h as yi,n as Un,v as Be,i as da,j as fa,r as We,k as ha,y as ga,p as bi,l as wi,U as Kr,m as xi,o as _i,S as Si}from"./graph-vendor-B-X5JegA.js";import{j as g,c as xn,P as _t,a as pa,D as Ei,C as ki,S as Ci,R as Ti,u as Xe,b as ft,d as ma,e as Ri,A as Ai,f as Ee,g as ke,h as ji,i as Ii,O as _n,k as va,l as Sn,m as Ni,T as ya,n as ba,o as wa,p as Li,q as zi,r as xa,s as Pi,t as Di,v as Oi,w as Gi,x as Mi,y as ct,z as Fi,B as $i}from"./ui-vendor-CeCm8EER.js";import{t as Hi,c as _a,a as tr,b as Bi}from"./utils-vendor-BysuhMZA.js";function fe(...e){return Hi(_a(e))}function rr(e){return e instanceof Error?e.message:`${e}`}function Ug(e,t){let r=0,n=null;return function(...a){const o=Date.now(),l=t-(o-r);l<=0?(n&&(clearTimeout(n),n=null),r=o,e.apply(this,a)):n||(n=setTimeout(()=>{r=Date.now(),n=null,e.apply(this,a)},l))}}const En=e=>{const t=e;t.use={};for(const r of Object.keys(t.getState()))t.use[r]=()=>t(n=>n[r]);return t},Qr="",Wg="/webui/",Ne="ghost",Vi="#B2EBF2",qi="#000",Ui="#E2E2E2",Jr="#EEEEEE",Wi="#F57F17",Xi="#969696",Yi="#F57F17",Wn="#B2EBF2",It=50,Xn=100,ut=4,Zr=20,Ki=15,Yn="*",Xg={"text/plain":[".txt",".md",".rtf",".odt",".tex",".epub",".html",".htm",".csv",".json",".xml",".yaml",".yml",".log",".conf",".ini",".properties",".sql",".bat",".sh",".c",".cpp",".py",".java",".js",".ts",".swift",".go",".rb",".php",".css",".scss",".less"],"application/pdf":[".pdf"],"application/vnd.openxmlformats-officedocument.wordprocessingml.document":[".docx"],"application/vnd.openxmlformats-officedocument.presentationml.presentation":[".pptx"],"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":[".xlsx"]},Yg={name:"LightRAG",github:"https://github.com/HKUDS/LightRAG"},Qi="modulepreload",Ji=function(e){return"/webui/"+e},Kn={},Zi=function(t,r,n){let a=Promise.resolve();if(r&&r.length>0){document.getElementsByTagName("link");const l=document.querySelector("meta[property=csp-nonce]"),i=(l==null?void 0:l.nonce)||(l==null?void 0:l.getAttribute("nonce"));a=Promise.allSettled(r.map(s=>{if(s=Ji(s),s in Kn)return;Kn[s]=!0;const c=s.endsWith(".css"),u=c?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${s}"]${u}`))return;const d=document.createElement("link");if(d.rel=c?"stylesheet":Qi,c||(d.as="script"),d.crossOrigin="",d.href=s,i&&d.setAttribute("nonce",i),document.head.appendChild(d),c)return new Promise((h,f)=>{d.addEventListener("load",h),d.addEventListener("error",()=>f(new Error(`Unable to preload CSS for ${s}`)))})}))}function o(l){const i=new Event("vite:preloadError",{cancelable:!0});if(i.payload=l,window.dispatchEvent(i),!i.defaultPrevented)throw l}return a.then(l=>{for(const i of l||[])i.status==="rejected"&&o(i.reason);return t().catch(o)})};function Sa(e,t){let r;try{r=e()}catch{return}return{getItem:a=>{var o;const l=s=>s===null?null:JSON.parse(s,void 0),i=(o=r.getItem(a))!=null?o:null;return i instanceof Promise?i.then(l):l(i)},setItem:(a,o)=>r.setItem(a,JSON.stringify(o,void 0)),removeItem:a=>r.removeItem(a)}}const en=e=>t=>{try{const r=e(t);return r instanceof Promise?r:{then(n){return en(n)(r)},catch(n){return this}}}catch(r){return{then(n){return this},catch(n){return en(n)(r)}}}},el=(e,t)=>(r,n,a)=>{let o={storage:Sa(()=>localStorage),partialize:y=>y,version:0,merge:(y,C)=>({...C,...y}),...t},l=!1;const i=new Set,s=new Set;let c=o.storage;if(!c)return e((...y)=>{console.warn(`[zustand persist middleware] Unable to update item '${o.name}', the given storage is currently unavailable.`),r(...y)},n,a);const u=()=>{const y=o.partialize({...n()});return c.setItem(o.name,{state:y,version:o.version})},d=a.setState;a.setState=(y,C)=>{d(y,C),u()};const h=e((...y)=>{r(...y),u()},n,a);a.getInitialState=()=>h;let f;const b=()=>{var y,C;if(!c)return;l=!1,i.forEach(E=>{var j;return E((j=n())!=null?j:h)});const N=((C=o.onRehydrateStorage)==null?void 0:C.call(o,(y=n())!=null?y:h))||void 0;return en(c.getItem.bind(c))(o.name).then(E=>{if(E)if(typeof E.version=="number"&&E.version!==o.version){if(o.migrate){const j=o.migrate(E.state,E.version);return j instanceof Promise?j.then(A=>[!0,A]):[!0,j]}console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return[!1,E.state];return[!1,void 0]}).then(E=>{var j;const[A,I]=E;if(f=o.merge(I,(j=n())!=null?j:h),r(f,!0),A)return u()}).then(()=>{N==null||N(f,void 0),f=n(),l=!0,s.forEach(E=>E(f))}).catch(E=>{N==null||N(void 0,E)})};return a.persist={setOptions:y=>{o={...o,...y},y.storage&&(c=y.storage)},clearStorage:()=>{c==null||c.removeItem(o.name)},getOptions:()=>o,rehydrate:()=>b(),hasHydrated:()=>l,onHydrate:y=>(i.add(y),()=>{i.delete(y)}),onFinishHydration:y=>(s.add(y),()=>{s.delete(y)})},o.skipHydration||b(),f||h},tl=el,rl=tr()(tl(e=>({theme:"system",language:"en",showPropertyPanel:!0,showNodeSearchBar:!0,showLegend:!1,showNodeLabel:!0,enableNodeDrag:!0,showEdgeLabel:!1,enableHideUnselectedEdges:!0,enableEdgeEvents:!1,minEdgeSize:1,maxEdgeSize:1,graphQueryMaxDepth:3,graphMaxNodes:1e3,backendMaxGraphNodes:null,graphLayoutMaxIterations:15,queryLabel:Yn,enableHealthCheck:!0,apiKey:null,currentTab:"documents",showFileName:!1,documentsPageSize:10,retrievalHistory:[],querySettings:{mode:"global",response_type:"Multiple Paragraphs",top_k:40,chunk_top_k:20,max_entity_tokens:6e3,max_relation_tokens:8e3,max_total_tokens:3e4,only_need_context:!1,only_need_prompt:!1,stream:!0,history_turns:0,user_prompt:"",enable_rerank:!0},setTheme:t=>e({theme:t}),setLanguage:t=>{e({language:t}),Zi(async()=>{const{default:r}=await import("./utils-vendor-BysuhMZA.js").then(n=>n.d);return{default:r}},__vite__mapDeps([0,1])).then(({default:r})=>{r.language!==t&&r.changeLanguage(t)})},setGraphLayoutMaxIterations:t=>e({graphLayoutMaxIterations:t}),setQueryLabel:t=>e({queryLabel:t}),setGraphQueryMaxDepth:t=>e({graphQueryMaxDepth:t}),setGraphMaxNodes:(t,r=!1)=>{const n=Z.getState();if(n.graphMaxNodes!==t)if(r){const a=n.queryLabel;e({graphMaxNodes:t,queryLabel:""}),setTimeout(()=>{e({queryLabel:a})},300)}else e({graphMaxNodes:t})},setBackendMaxGraphNodes:t=>e({backendMaxGraphNodes:t}),setMinEdgeSize:t=>e({minEdgeSize:t}),setMaxEdgeSize:t=>e({maxEdgeSize:t}),setEnableHealthCheck:t=>e({enableHealthCheck:t}),setApiKey:t=>e({apiKey:t}),setCurrentTab:t=>e({currentTab:t}),setRetrievalHistory:t=>e({retrievalHistory:t}),updateQuerySettings:t=>{const r={...t};delete r.history_turns,e(n=>({querySettings:{...n.querySettings,...r,history_turns:0}}))},setShowFileName:t=>e({showFileName:t}),setShowLegend:t=>e({showLegend:t}),setDocumentsPageSize:t=>e({documentsPageSize:t})}),{name:"settings-storage",storage:Sa(()=>localStorage),version:17,migrate:(e,t)=>(t<2&&(e.showEdgeLabel=!1),t<3&&(e.queryLabel=Yn),t<4&&(e.showPropertyPanel=!0,e.showNodeSearchBar=!0,e.showNodeLabel=!0,e.enableHealthCheck=!0,e.apiKey=null),t<5&&(e.currentTab="documents"),t<6&&(e.querySettings={mode:"global",response_type:"Multiple Paragraphs",top_k:10,max_token_for_text_unit:4e3,max_token_for_global_context:4e3,max_token_for_local_context:4e3,only_need_context:!1,only_need_prompt:!1,stream:!0,history_turns:0,hl_keywords:[],ll_keywords:[]},e.retrievalHistory=[]),t<7&&(e.graphQueryMaxDepth=3,e.graphLayoutMaxIterations=15),t<8&&(e.graphMinDegree=0,e.language="en"),t<9&&(e.showFileName=!1),t<10&&(delete e.graphMinDegree,e.graphMaxNodes=1e3),t<11&&(e.minEdgeSize=1,e.maxEdgeSize=1),t<12&&(e.retrievalHistory=[]),t<13&&e.querySettings&&(e.querySettings.user_prompt=""),t<14&&(e.backendMaxGraphNodes=null),t<15&&(e.querySettings={...e.querySettings,mode:"mix",response_type:"Multiple Paragraphs",top_k:40,chunk_top_k:10,max_entity_tokens:1e4,max_relation_tokens:1e4,max_total_tokens:32e3,enable_rerank:!0,history_turns:0}),t<16&&(e.documentsPageSize=10),t<17&&e.querySettings&&(e.querySettings.history_turns=0),e)})),Z=En(rl);class nl{constructor(){Fe(this,"nodes",[]);Fe(this,"edges",[]);Fe(this,"nodeIdMap",{});Fe(this,"edgeIdMap",{});Fe(this,"edgeDynamicIdMap",{});Fe(this,"getNode",t=>{const r=this.nodeIdMap[t];if(r!==void 0)return this.nodes[r]});Fe(this,"getEdge",(t,r=!0)=>{const n=r?this.edgeDynamicIdMap[t]:this.edgeIdMap[t];if(n!==void 0)return this.edges[n]});Fe(this,"buildDynamicMap",()=>{this.edgeDynamicIdMap={};for(let t=0;t<this.edges.length;t++){const r=this.edges[t];this.edgeDynamicIdMap[r.dynamicId]=t}})}}const ol=tr()((e,t)=>({selectedNode:null,focusedNode:null,selectedEdge:null,focusedEdge:null,moveToSelectedNode:!1,isFetching:!1,graphIsEmpty:!1,lastSuccessfulQueryLabel:"",graphDataFetchAttempted:!1,labelsFetchAttempted:!1,rawGraph:null,sigmaGraph:null,sigmaInstance:null,allDatabaseLabels:["*"],typeColorMap:new Map,searchEngine:null,setGraphIsEmpty:r=>e({graphIsEmpty:r}),setLastSuccessfulQueryLabel:r=>e({lastSuccessfulQueryLabel:r}),setIsFetching:r=>e({isFetching:r}),setSelectedNode:(r,n)=>e({selectedNode:r,moveToSelectedNode:n}),setFocusedNode:r=>e({focusedNode:r}),setSelectedEdge:r=>e({selectedEdge:r}),setFocusedEdge:r=>e({focusedEdge:r}),clearSelection:()=>e({selectedNode:null,focusedNode:null,selectedEdge:null,focusedEdge:null}),reset:()=>{e({selectedNode:null,focusedNode:null,selectedEdge:null,focusedEdge:null,rawGraph:null,sigmaGraph:null,searchEngine:null,moveToSelectedNode:!1,graphIsEmpty:!1})},setRawGraph:r=>e({rawGraph:r}),setSigmaGraph:r=>{e({sigmaGraph:r})},setAllDatabaseLabels:r=>e({allDatabaseLabels:r}),fetchAllDatabaseLabels:async()=>{try{console.log("Fetching all database labels...");const r=await sl();e({allDatabaseLabels:["*",...r]});return}catch(r){throw console.error("Failed to fetch all database labels:",r),e({allDatabaseLabels:["*"]}),r}},setMoveToSelectedNode:r=>e({moveToSelectedNode:r}),setSigmaInstance:r=>e({sigmaInstance:r}),setTypeColorMap:r=>e({typeColorMap:r}),setSearchEngine:r=>e({searchEngine:r}),resetSearchEngine:()=>e({searchEngine:null}),setGraphDataFetchAttempted:r=>e({graphDataFetchAttempted:r}),setLabelsFetchAttempted:r=>e({labelsFetchAttempted:r}),nodeToExpand:null,nodeToPrune:null,triggerNodeExpand:r=>e({nodeToExpand:r}),triggerNodePrune:r=>e({nodeToPrune:r}),graphDataVersion:0,incrementGraphDataVersion:()=>e(r=>({graphDataVersion:r.graphDataVersion+1})),updateNodeAndSelect:async(r,n,a,o)=>{const l=t(),{sigmaGraph:i,rawGraph:s}=l;if(!(!i||!s||!i.hasNode(r)))try{const c=i.getNodeAttributes(r);if(console.log("updateNodeAndSelect",r,n,a,o),r===n&&a==="entity_id"){i.addNode(o,{...c,label:o});const u=[];i.forEachEdge(r,(h,f,b,y)=>{const C=b===r?y:b,N=b===r,E=h,j=s.edgeDynamicIdMap[E],A=i.addEdge(N?o:C,N?C:o,f);j!==void 0&&u.push({originalDynamicId:E,newEdgeId:A,edgeIndex:j}),i.dropEdge(h)}),i.dropNode(r);const d=s.nodeIdMap[r];d!==void 0&&(s.nodes[d].id=o,s.nodes[d].labels=[o],s.nodes[d].properties.entity_id=o,delete s.nodeIdMap[r],s.nodeIdMap[o]=d),u.forEach(({originalDynamicId:h,newEdgeId:f,edgeIndex:b})=>{s.edges[b]&&(s.edges[b].source===r&&(s.edges[b].source=o),s.edges[b].target===r&&(s.edges[b].target=o),s.edges[b].dynamicId=f,delete s.edgeDynamicIdMap[h],s.edgeDynamicIdMap[f]=b)}),e({selectedNode:o,moveToSelectedNode:!0})}else{const u=s.nodeIdMap[String(r)];u!==void 0&&(s.nodes[u].properties[a]=o,a==="entity_id"&&(s.nodes[u].labels=[o],i.setNodeAttribute(String(r),"label",o))),e(d=>({graphDataVersion:d.graphDataVersion+1}))}}catch(c){throw console.error("Error updating node in graph:",c),new Error("Failed to update node in graph")}},updateEdgeAndSelect:async(r,n,a,o,l,i)=>{const s=t(),{sigmaGraph:c,rawGraph:u}=s;if(!(!c||!u))try{const d=u.edgeIdMap[String(r)];d!==void 0&&u.edges[d]&&(u.edges[d].properties[l]=i,n!==void 0&&l==="keywords"&&c.setEdgeAttribute(n,"label",i)),e(h=>({graphDataVersion:h.graphDataVersion+1})),e({selectedEdge:n})}catch(d){throw console.error(`Error updating edge ${a}->${o} in graph:`,d),new Error("Failed to update edge in graph")}}})),te=En(ol);class al{constructor(){Fe(this,"navigate",null)}setNavigate(t){this.navigate=t}resetAllApplicationState(t=!1){console.log("Resetting all application state...");const r=te.getState(),n=r.sigmaInstance;r.reset(),r.setGraphDataFetchAttempted(!1),r.setLabelsFetchAttempted(!1),r.setSigmaInstance(null),r.setIsFetching(!1),kn.getState().clear(),t||Z.getState().setRetrievalHistory([]),sessionStorage.clear(),n&&(n.getGraph().clear(),n.kill(),te.getState().setSigmaInstance(null))}navigateToLogin(){if(!this.navigate){console.error("Navigation function not set");return}const t=Ut.getState().username;t&&localStorage.setItem("LIGHTRAG-PREVIOUS-USER",t),this.resetAllApplicationState(!0),Ut.getState().logout(),this.navigate("/login")}navigateToHome(){if(!this.navigate){console.error("Navigation function not set");return}this.navigate("/")}}const Ea=new al,Kg="Invalid API Key",Qg="API Key required",xe=Bi.create({baseURL:Qr,headers:{"Content-Type":"application/json"}});xe.interceptors.request.use(e=>{const t=Z.getState().apiKey,r=localStorage.getItem("LIGHTRAG-API-TOKEN");return r&&(e.headers.Authorization=`Bearer ${r}`),t&&(e.headers["X-API-Key"]=t),e});xe.interceptors.response.use(e=>e,e=>{var t,r,n,a;if(e.response){if(((t=e.response)==null?void 0:t.status)===401){if((n=(r=e.config)==null?void 0:r.url)!=null&&n.includes("/login"))throw e;return Ea.navigateToLogin(),Promise.reject(new Error("Authentication required"))}throw new Error(`${e.response.status} ${e.response.statusText}
${JSON.stringify(e.response.data)}
${(a=e.config)==null?void 0:a.url}`)}throw e});const ka=async(e,t,r)=>(await xe.get(`/graphs?label=${encodeURIComponent(e)}&max_depth=${t}&max_nodes=${r}`)).data,sl=async()=>(await xe.get("/graph/label/list")).data,il=async()=>{try{return(await xe.get("/health")).data}catch(e){return{status:"error",message:rr(e)}}},Jg=async()=>(await xe.post("/documents/scan")).data,Zg=async e=>(await xe.post("/query",e)).data,ep=async(e,t,r)=>{const n=Z.getState().apiKey,a=localStorage.getItem("LIGHTRAG-API-TOKEN"),o={"Content-Type":"application/json",Accept:"application/x-ndjson"};a&&(o.Authorization=`Bearer ${a}`),n&&(o["X-API-Key"]=n);try{const l=await fetch(`${Qr}/query/stream`,{method:"POST",headers:o,body:JSON.stringify(e)});if(!l.ok){if(l.status===401)throw Ea.navigateToLogin(),new Error("Authentication required");let u="Unknown error";try{u=await l.text()}catch{}const d=`${Qr}/query/stream`;throw new Error(`${l.status} ${l.statusText}
${JSON.stringify({error:u})}
${d}`)}if(!l.body)throw new Error("Response body is null");const i=l.body.getReader(),s=new TextDecoder;let c="";for(;;){const{done:u,value:d}=await i.read();if(u)break;c+=s.decode(d,{stream:!0});const h=c.split(`
`);c=h.pop()||"";for(const f of h)if(f.trim())try{const b=JSON.parse(f);b.response?t(b.response):b.error&&r&&r(b.error)}catch(b){console.error("Error parsing stream chunk:",f,b),r&&r(`Error parsing server response: ${f}`)}}if(c.trim())try{const u=JSON.parse(c);u.response?t(u.response):u.error&&r&&r(u.error)}catch(u){console.error("Error parsing final chunk:",c,u),r&&r(`Error parsing final server response: ${c}`)}}catch(l){const i=rr(l);if(i==="Authentication required"){console.error("Authentication required for stream request"),r&&r("Authentication required");return}const s=i.match(/^(\d{3})\s/);if(s){const c=parseInt(s[1],10);let u=i;switch(c){case 403:u="You do not have permission to access this resource (403 Forbidden)",console.error("Permission denied for stream request:",i);break;case 404:u="The requested resource does not exist (404 Not Found)",console.error("Resource not found for stream request:",i);break;case 429:u="Too many requests, please try again later (429 Too Many Requests)",console.error("Rate limited for stream request:",i);break;case 500:case 502:case 503:case 504:u=`Server error, please try again later (${c})`,console.error("Server error for stream request:",i);break;default:console.error("Stream request failed with status code:",c,i)}r&&r(u);return}if(i.includes("NetworkError")||i.includes("Failed to fetch")||i.includes("Network request failed")){console.error("Network error for stream request:",i),r&&r("Network connection error, please check your internet connection");return}if(i.includes("Error parsing")||i.includes("SyntaxError")){console.error("JSON parsing error in stream:",i),r&&r("Error processing response data");return}console.error("Unhandled stream error:",i),r?r(i):console.error("No error handler provided for stream error:",i)}},tp=async(e,t)=>{const r=new FormData;return r.append("file",e),(await xe.post("/documents/upload",r,{headers:{"Content-Type":"multipart/form-data"},onUploadProgress:t!==void 0?a=>{const o=Math.round(a.loaded*100/a.total);t(o)}:void 0})).data},rp=async()=>(await xe.delete("/documents")).data,np=async()=>(await xe.post("/documents/clear_cache",{})).data,op=async(e,t=!1)=>(await xe.delete("/documents/delete_document",{data:{doc_ids:e,delete_file:t}})).data,ap=async()=>{try{const e=await xe.get("/auth-status",{timeout:5e3,headers:{Accept:"application/json"}});if((e.headers["content-type"]||"").includes("text/html"))return console.warn("Received HTML response instead of JSON for auth-status endpoint"),{auth_configured:!0,auth_mode:"enabled"};if(e.data&&typeof e.data=="object"&&"auth_configured"in e.data&&typeof e.data.auth_configured=="boolean"){if(e.data.auth_configured)return e.data;if(e.data.access_token&&typeof e.data.access_token=="string")return e.data;console.warn("Auth not configured but no valid access token provided")}return console.warn("Received invalid auth status response:",e.data),{auth_configured:!0,auth_mode:"enabled"}}catch(e){return console.error("Failed to get auth status:",rr(e)),{auth_configured:!0,auth_mode:"enabled"}}},sp=async()=>(await xe.get("/documents/pipeline_status")).data,ip=async(e,t)=>{const r=new FormData;return r.append("username",e),r.append("password",t),(await xe.post("/login",r,{headers:{"Content-Type":"multipart/form-data"}})).data},ll=async(e,t,r=!1)=>(await xe.post("/graph/entity/edit",{entity_name:e,updated_data:t,allow_rename:r})).data,cl=async(e,t,r)=>(await xe.post("/graph/relation/edit",{source_id:e,target_id:t,updated_data:r})).data,ul=async e=>{try{return(await xe.get(`/graph/entity/exists?name=${encodeURIComponent(e)}`)).data.exists}catch(t){return console.error("Error checking entity name:",t),!1}},lp=async e=>(await xe.post("/documents/paginated",e)).data,dl=tr()((e,t)=>({health:!0,message:null,messageTitle:null,lastCheckTime:Date.now(),status:null,pipelineBusy:!1,healthCheckIntervalId:null,healthCheckFunction:null,healthCheckIntervalValue:Ki*1e3,check:async()=>{var n;const r=await il();if(r.status==="healthy"){if((r.core_version||r.api_version)&&Ut.getState().setVersion(r.core_version||null,r.api_version||null),("webui_title"in r||"webui_description"in r)&&Ut.getState().setCustomTitle("webui_title"in r?r.webui_title??null:null,"webui_description"in r?r.webui_description??null:null),(n=r.configuration)!=null&&n.max_graph_nodes){const a=parseInt(r.configuration.max_graph_nodes,10);!isNaN(a)&&a>0&&Z.getState().backendMaxGraphNodes!==a&&(Z.getState().setBackendMaxGraphNodes(a),Z.getState().graphMaxNodes>a&&Z.getState().setGraphMaxNodes(a,!0))}return e({health:!0,message:null,messageTitle:null,lastCheckTime:Date.now(),status:r,pipelineBusy:r.pipeline_busy}),!0}return e({health:!1,message:r.message,messageTitle:"Backend Health Check Error!",lastCheckTime:Date.now(),status:null}),!1},clear:()=>{e({health:!0,message:null,messageTitle:null})},setErrorMessage:(r,n)=>{e({health:!1,message:r,messageTitle:n})},setPipelineBusy:r=>{e({pipelineBusy:r})},setHealthCheckFunction:r=>{e({healthCheckFunction:r})},resetHealthCheckTimer:()=>{const{healthCheckIntervalId:r,healthCheckFunction:n,healthCheckIntervalValue:a}=t();if(r&&clearInterval(r),n){n();const o=setInterval(n,a);e({healthCheckIntervalId:o})}},resetHealthCheckTimerDelayed:r=>{setTimeout(()=>{t().resetHealthCheckTimer()},r)},clearHealthCheckTimer:()=>{const{healthCheckIntervalId:r}=t();r&&(clearInterval(r),e({healthCheckIntervalId:null}))}})),kn=En(dl),Ca=e=>{try{const t=e.split(".");return t.length!==3?{}:JSON.parse(atob(t[1]))}catch(t){return console.error("Error parsing token payload:",t),{}}},Ta=e=>Ca(e).sub||null,fl=e=>Ca(e).role==="guest",hl=()=>{const e=localStorage.getItem("LIGHTRAG-API-TOKEN"),t=localStorage.getItem("LIGHTRAG-CORE-VERSION"),r=localStorage.getItem("LIGHTRAG-API-VERSION"),n=localStorage.getItem("LIGHTRAG-WEBUI-TITLE"),a=localStorage.getItem("LIGHTRAG-WEBUI-DESCRIPTION"),o=e?Ta(e):null;return e?{isAuthenticated:!0,isGuestMode:fl(e),coreVersion:t,apiVersion:r,username:o,webuiTitle:n,webuiDescription:a}:{isAuthenticated:!1,isGuestMode:!1,coreVersion:t,apiVersion:r,username:null,webuiTitle:n,webuiDescription:a}},Ut=tr(e=>{const t=hl();return{isAuthenticated:t.isAuthenticated,isGuestMode:t.isGuestMode,coreVersion:t.coreVersion,apiVersion:t.apiVersion,username:t.username,webuiTitle:t.webuiTitle,webuiDescription:t.webuiDescription,login:(r,n=!1,a=null,o=null,l=null,i=null)=>{localStorage.setItem("LIGHTRAG-API-TOKEN",r),a&&localStorage.setItem("LIGHTRAG-CORE-VERSION",a),o&&localStorage.setItem("LIGHTRAG-API-VERSION",o),l?localStorage.setItem("LIGHTRAG-WEBUI-TITLE",l):localStorage.removeItem("LIGHTRAG-WEBUI-TITLE"),i?localStorage.setItem("LIGHTRAG-WEBUI-DESCRIPTION",i):localStorage.removeItem("LIGHTRAG-WEBUI-DESCRIPTION");const s=Ta(r);e({isAuthenticated:!0,isGuestMode:n,username:s,coreVersion:a,apiVersion:o,webuiTitle:l,webuiDescription:i})},logout:()=>{localStorage.removeItem("LIGHTRAG-API-TOKEN");const r=localStorage.getItem("LIGHTRAG-CORE-VERSION"),n=localStorage.getItem("LIGHTRAG-API-VERSION"),a=localStorage.getItem("LIGHTRAG-WEBUI-TITLE"),o=localStorage.getItem("LIGHTRAG-WEBUI-DESCRIPTION");e({isAuthenticated:!1,isGuestMode:!1,username:null,coreVersion:r,apiVersion:n,webuiTitle:a,webuiDescription:o})},setVersion:(r,n)=>{r&&localStorage.setItem("LIGHTRAG-CORE-VERSION",r),n&&localStorage.setItem("LIGHTRAG-API-VERSION",n),e({coreVersion:r,apiVersion:n})},setCustomTitle:(r,n)=>{r?localStorage.setItem("LIGHTRAG-WEBUI-TITLE",r):localStorage.removeItem("LIGHTRAG-WEBUI-TITLE"),n?localStorage.setItem("LIGHTRAG-WEBUI-DESCRIPTION",n):localStorage.removeItem("LIGHTRAG-WEBUI-DESCRIPTION"),e({webuiTitle:r,webuiDescription:n})}}});var gl=e=>{switch(e){case"success":return vl;case"info":return bl;case"warning":return yl;case"error":return wl;default:return null}},pl=Array(12).fill(0),ml=({visible:e,className:t})=>X.createElement("div",{className:["sonner-loading-wrapper",t].filter(Boolean).join(" "),"data-visible":e},X.createElement("div",{className:"sonner-spinner"},pl.map((r,n)=>X.createElement("div",{className:"sonner-loading-bar",key:`spinner-bar-${n}`})))),vl=X.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},X.createElement("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z",clipRule:"evenodd"})),yl=X.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",height:"20",width:"20"},X.createElement("path",{fillRule:"evenodd",d:"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z",clipRule:"evenodd"})),bl=X.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},X.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z",clipRule:"evenodd"})),wl=X.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},X.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"})),xl=X.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"},X.createElement("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),X.createElement("line",{x1:"6",y1:"6",x2:"18",y2:"18"})),_l=()=>{let[e,t]=X.useState(document.hidden);return X.useEffect(()=>{let r=()=>{t(document.hidden)};return document.addEventListener("visibilitychange",r),()=>window.removeEventListener("visibilitychange",r)},[]),e},tn=1,Sl=class{constructor(){this.subscribe=e=>(this.subscribers.push(e),()=>{let t=this.subscribers.indexOf(e);this.subscribers.splice(t,1)}),this.publish=e=>{this.subscribers.forEach(t=>t(e))},this.addToast=e=>{this.publish(e),this.toasts=[...this.toasts,e]},this.create=e=>{var t;let{message:r,...n}=e,a=typeof(e==null?void 0:e.id)=="number"||((t=e.id)==null?void 0:t.length)>0?e.id:tn++,o=this.toasts.find(i=>i.id===a),l=e.dismissible===void 0?!0:e.dismissible;return this.dismissedToasts.has(a)&&this.dismissedToasts.delete(a),o?this.toasts=this.toasts.map(i=>i.id===a?(this.publish({...i,...e,id:a,title:r}),{...i,...e,id:a,dismissible:l,title:r}):i):this.addToast({title:r,...n,dismissible:l,id:a}),a},this.dismiss=e=>(this.dismissedToasts.add(e),e||this.toasts.forEach(t=>{this.subscribers.forEach(r=>r({id:t.id,dismiss:!0}))}),this.subscribers.forEach(t=>t({id:e,dismiss:!0})),e),this.message=(e,t)=>this.create({...t,message:e}),this.error=(e,t)=>this.create({...t,message:e,type:"error"}),this.success=(e,t)=>this.create({...t,type:"success",message:e}),this.info=(e,t)=>this.create({...t,type:"info",message:e}),this.warning=(e,t)=>this.create({...t,type:"warning",message:e}),this.loading=(e,t)=>this.create({...t,type:"loading",message:e}),this.promise=(e,t)=>{if(!t)return;let r;t.loading!==void 0&&(r=this.create({...t,promise:e,type:"loading",message:t.loading,description:typeof t.description!="function"?t.description:void 0}));let n=e instanceof Promise?e:e(),a=r!==void 0,o,l=n.then(async s=>{if(o=["resolve",s],X.isValidElement(s))a=!1,this.create({id:r,type:"default",message:s});else if(kl(s)&&!s.ok){a=!1;let c=typeof t.error=="function"?await t.error(`HTTP error! status: ${s.status}`):t.error,u=typeof t.description=="function"?await t.description(`HTTP error! status: ${s.status}`):t.description;this.create({id:r,type:"error",message:c,description:u})}else if(t.success!==void 0){a=!1;let c=typeof t.success=="function"?await t.success(s):t.success,u=typeof t.description=="function"?await t.description(s):t.description;this.create({id:r,type:"success",message:c,description:u})}}).catch(async s=>{if(o=["reject",s],t.error!==void 0){a=!1;let c=typeof t.error=="function"?await t.error(s):t.error,u=typeof t.description=="function"?await t.description(s):t.description;this.create({id:r,type:"error",message:c,description:u})}}).finally(()=>{var s;a&&(this.dismiss(r),r=void 0),(s=t.finally)==null||s.call(t)}),i=()=>new Promise((s,c)=>l.then(()=>o[0]==="reject"?c(o[1]):s(o[1])).catch(c));return typeof r!="string"&&typeof r!="number"?{unwrap:i}:Object.assign(r,{unwrap:i})},this.custom=(e,t)=>{let r=(t==null?void 0:t.id)||tn++;return this.create({jsx:e(r),id:r,...t}),r},this.getActiveToasts=()=>this.toasts.filter(e=>!this.dismissedToasts.has(e.id)),this.subscribers=[],this.toasts=[],this.dismissedToasts=new Set}},Ae=new Sl,El=(e,t)=>{let r=(t==null?void 0:t.id)||tn++;return Ae.addToast({title:e,...t,id:r}),r},kl=e=>e&&typeof e=="object"&&"ok"in e&&typeof e.ok=="boolean"&&"status"in e&&typeof e.status=="number",Cl=El,Tl=()=>Ae.toasts,Rl=()=>Ae.getActiveToasts(),rt=Object.assign(Cl,{success:Ae.success,info:Ae.info,warning:Ae.warning,error:Ae.error,custom:Ae.custom,message:Ae.message,promise:Ae.promise,dismiss:Ae.dismiss,loading:Ae.loading},{getHistory:Tl,getToasts:Rl});function Al(e,{insertAt:t}={}){if(typeof document>"u")return;let r=document.head||document.getElementsByTagName("head")[0],n=document.createElement("style");n.type="text/css",t==="top"&&r.firstChild?r.insertBefore(n,r.firstChild):r.appendChild(n),n.styleSheet?n.styleSheet.cssText=e:n.appendChild(document.createTextNode(e))}Al(`:where(html[dir="ltr"]),:where([data-sonner-toaster][dir="ltr"]){--toast-icon-margin-start: -3px;--toast-icon-margin-end: 4px;--toast-svg-margin-start: -1px;--toast-svg-margin-end: 0px;--toast-button-margin-start: auto;--toast-button-margin-end: 0;--toast-close-button-start: 0;--toast-close-button-end: unset;--toast-close-button-transform: translate(-35%, -35%)}:where(html[dir="rtl"]),:where([data-sonner-toaster][dir="rtl"]){--toast-icon-margin-start: 4px;--toast-icon-margin-end: -3px;--toast-svg-margin-start: 0px;--toast-svg-margin-end: -1px;--toast-button-margin-start: 0;--toast-button-margin-end: auto;--toast-close-button-start: unset;--toast-close-button-end: 0;--toast-close-button-transform: translate(35%, -35%)}:where([data-sonner-toaster]){position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1: hsl(0, 0%, 99%);--gray2: hsl(0, 0%, 97.3%);--gray3: hsl(0, 0%, 95.1%);--gray4: hsl(0, 0%, 93%);--gray5: hsl(0, 0%, 90.9%);--gray6: hsl(0, 0%, 88.7%);--gray7: hsl(0, 0%, 85.8%);--gray8: hsl(0, 0%, 78%);--gray9: hsl(0, 0%, 56.1%);--gray10: hsl(0, 0%, 52.3%);--gray11: hsl(0, 0%, 43.5%);--gray12: hsl(0, 0%, 9%);--border-radius: 8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:none;z-index:999999999;transition:transform .4s ease}:where([data-sonner-toaster][data-lifted="true"]){transform:translateY(-10px)}@media (hover: none) and (pointer: coarse){:where([data-sonner-toaster][data-lifted="true"]){transform:none}}:where([data-sonner-toaster][data-x-position="right"]){right:var(--offset-right)}:where([data-sonner-toaster][data-x-position="left"]){left:var(--offset-left)}:where([data-sonner-toaster][data-x-position="center"]){left:50%;transform:translate(-50%)}:where([data-sonner-toaster][data-y-position="top"]){top:var(--offset-top)}:where([data-sonner-toaster][data-y-position="bottom"]){bottom:var(--offset-bottom)}:where([data-sonner-toast]){--y: translateY(100%);--lift-amount: calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);filter:blur(0);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:none;overflow-wrap:anywhere}:where([data-sonner-toast][data-styled="true"]){padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px #0000001a;width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}:where([data-sonner-toast]:focus-visible){box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast][data-y-position="top"]){top:0;--y: translateY(-100%);--lift: 1;--lift-amount: calc(1 * var(--gap))}:where([data-sonner-toast][data-y-position="bottom"]){bottom:0;--y: translateY(100%);--lift: -1;--lift-amount: calc(var(--lift) * var(--gap))}:where([data-sonner-toast]) :where([data-description]){font-weight:400;line-height:1.4;color:inherit}:where([data-sonner-toast]) :where([data-title]){font-weight:500;line-height:1.5;color:inherit}:where([data-sonner-toast]) :where([data-icon]){display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}:where([data-sonner-toast][data-promise="true"]) :where([data-icon])>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}:where([data-sonner-toast]) :where([data-icon])>*{flex-shrink:0}:where([data-sonner-toast]) :where([data-icon]) svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}:where([data-sonner-toast]) :where([data-content]){display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;cursor:pointer;outline:none;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}:where([data-sonner-toast]) :where([data-button]):focus-visible{box-shadow:0 0 0 2px #0006}:where([data-sonner-toast]) :where([data-button]):first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}:where([data-sonner-toast]) :where([data-cancel]){color:var(--normal-text);background:rgba(0,0,0,.08)}:where([data-sonner-toast][data-theme="dark"]) :where([data-cancel]){background:rgba(255,255,255,.3)}:where([data-sonner-toast]) :where([data-close-button]){position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast] [data-close-button]{background:var(--gray1)}:where([data-sonner-toast]) :where([data-close-button]):focus-visible{box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast]) :where([data-disabled="true"]){cursor:not-allowed}:where([data-sonner-toast]):hover :where([data-close-button]):hover{background:var(--gray2);border-color:var(--gray5)}:where([data-sonner-toast][data-swiping="true"]):before{content:"";position:absolute;left:-50%;right:-50%;height:100%;z-index:-1}:where([data-sonner-toast][data-y-position="top"][data-swiping="true"]):before{bottom:50%;transform:scaleY(3) translateY(50%)}:where([data-sonner-toast][data-y-position="bottom"][data-swiping="true"]):before{top:50%;transform:scaleY(3) translateY(-50%)}:where([data-sonner-toast][data-swiping="false"][data-removed="true"]):before{content:"";position:absolute;inset:0;transform:scaleY(2)}:where([data-sonner-toast]):after{content:"";position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}:where([data-sonner-toast][data-mounted="true"]){--y: translateY(0);opacity:1}:where([data-sonner-toast][data-expanded="false"][data-front="false"]){--scale: var(--toasts-before) * .05 + 1;--y: translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}:where([data-sonner-toast])>*{transition:opacity .4s}:where([data-sonner-toast][data-expanded="false"][data-front="false"][data-styled="true"])>*{opacity:0}:where([data-sonner-toast][data-visible="false"]){opacity:0;pointer-events:none}:where([data-sonner-toast][data-mounted="true"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}:where([data-sonner-toast][data-removed="true"][data-front="true"][data-swipe-out="false"]){--y: translateY(calc(var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="false"]){--y: translateY(40%);opacity:0;transition:transform .5s,opacity .2s}:where([data-sonner-toast][data-removed="true"][data-front="false"]):before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount-y, 0px)) translate(var(--swipe-amount-x, 0px));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation-duration:.2s;animation-timing-function:ease-out;animation-fill-mode:forwards}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=left]{animation-name:swipe-out-left}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=right]{animation-name:swipe-out-right}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=up]{animation-name:swipe-out-up}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=down]{animation-name:swipe-out-down}@keyframes swipe-out-left{0%{transform:var(--y) translate(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translate(calc(var(--swipe-amount-x) - 100%));opacity:0}}@keyframes swipe-out-right{0%{transform:var(--y) translate(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translate(calc(var(--swipe-amount-x) + 100%));opacity:0}}@keyframes swipe-out-up{0%{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) - 100%));opacity:0}}@keyframes swipe-out-down{0%{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) + 100%));opacity:0}}@media (max-width: 600px){[data-sonner-toaster]{position:fixed;right:var(--mobile-offset-right);left:var(--mobile-offset-left);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset-left) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset-left) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset-left)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--mobile-offset-bottom)}[data-sonner-toaster][data-y-position=top]{top:var(--mobile-offset-top)}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset-left);right:var(--mobile-offset-right);transform:none}}[data-sonner-toaster][data-theme=light]{--normal-bg: #fff;--normal-border: var(--gray4);--normal-text: var(--gray12);--success-bg: hsl(143, 85%, 96%);--success-border: hsl(145, 92%, 91%);--success-text: hsl(140, 100%, 27%);--info-bg: hsl(208, 100%, 97%);--info-border: hsl(221, 91%, 91%);--info-text: hsl(210, 92%, 45%);--warning-bg: hsl(49, 100%, 97%);--warning-border: hsl(49, 91%, 91%);--warning-text: hsl(31, 92%, 45%);--error-bg: hsl(359, 100%, 97%);--error-border: hsl(359, 100%, 94%);--error-text: hsl(360, 100%, 45%)}[data-sonner-toaster][data-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg: #fff;--normal-border: var(--gray3);--normal-text: var(--gray12)}[data-sonner-toaster][data-theme=dark]{--normal-bg: #000;--normal-bg-hover: hsl(0, 0%, 12%);--normal-border: hsl(0, 0%, 20%);--normal-border-hover: hsl(0, 0%, 25%);--normal-text: var(--gray1);--success-bg: hsl(150, 100%, 6%);--success-border: hsl(147, 100%, 12%);--success-text: hsl(150, 86%, 65%);--info-bg: hsl(215, 100%, 6%);--info-border: hsl(223, 100%, 12%);--info-text: hsl(216, 87%, 65%);--warning-bg: hsl(64, 100%, 6%);--warning-border: hsl(60, 100%, 12%);--warning-text: hsl(46, 87%, 65%);--error-bg: hsl(358, 76%, 10%);--error-border: hsl(357, 89%, 16%);--error-text: hsl(358, 100%, 81%)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast] [data-close-button]{background:var(--normal-bg);border-color:var(--normal-border);color:var(--normal-text)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast] [data-close-button]:hover{background:var(--normal-bg-hover);border-color:var(--normal-border-hover)}[data-rich-colors=true][data-sonner-toast][data-type=success],[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info],[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning],[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error],[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size: 16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:nth-child(1){animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}to{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}to{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}to{opacity:.15}}@media (prefers-reduced-motion){[data-sonner-toast],[data-sonner-toast]>*,.sonner-loading-bar{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}
`);function Nt(e){return e.label!==void 0}var jl=3,Il="32px",Nl="16px",Qn=4e3,Ll=356,zl=14,Pl=20,Dl=200;function Me(...e){return e.filter(Boolean).join(" ")}function Ol(e){let[t,r]=e.split("-"),n=[];return t&&n.push(t),r&&n.push(r),n}var Gl=e=>{var t,r,n,a,o,l,i,s,c,u,d;let{invert:h,toast:f,unstyled:b,interacting:y,setHeights:C,visibleToasts:N,heights:E,index:j,toasts:A,expanded:I,removeToast:P,defaultRichColors:m,closeButton:S,style:x,cancelButtonStyle:T,actionButtonStyle:R,className:O="",descriptionClassName:w="",duration:H,position:K,gap:D,loadingIcon:k,expandByDefault:_,classNames:B,icons:se,closeButtonAriaLabel:F="Close toast",pauseWhenPageIsHidden:v}=e,[z,V]=X.useState(null),[$,Q]=X.useState(null),[W,Y]=X.useState(!1),[ie,ne]=X.useState(!1),[ae,M]=X.useState(!1),[J,U]=X.useState(!1),[q,L]=X.useState(!1),[oe,ue]=X.useState(0),[re,ee]=X.useState(0),G=X.useRef(f.duration||H||Qn),ge=X.useRef(null),pe=X.useRef(null),ye=j===0,we=j+1<=N,de=f.type,me=f.dismissible!==!1,Ie=f.className||"",Ce=f.descriptionClassName||"",Te=X.useMemo(()=>E.findIndex(ce=>ce.toastId===f.id)||0,[E,f.id]),ze=X.useMemo(()=>{var ce;return(ce=f.closeButton)!=null?ce:S},[f.closeButton,S]),Ye=X.useMemo(()=>f.duration||H||Qn,[f.duration,H]),Ke=X.useRef(0),Re=X.useRef(0),st=X.useRef(0),Pe=X.useRef(null),[ii,li]=K.split("-"),Bn=X.useMemo(()=>E.reduce((ce,he,ve)=>ve>=Te?ce:ce+he.height,0),[E,Te]),Vn=_l(),ci=f.invert||h,gr=de==="loading";Re.current=X.useMemo(()=>Te*D+Bn,[Te,Bn]),X.useEffect(()=>{G.current=Ye},[Ye]),X.useEffect(()=>{Y(!0)},[]),X.useEffect(()=>{let ce=pe.current;if(ce){let he=ce.getBoundingClientRect().height;return ee(he),C(ve=>[{toastId:f.id,height:he,position:f.position},...ve]),()=>C(ve=>ve.filter(De=>De.toastId!==f.id))}},[C,f.id]),X.useLayoutEffect(()=>{if(!W)return;let ce=pe.current,he=ce.style.height;ce.style.height="auto";let ve=ce.getBoundingClientRect().height;ce.style.height=he,ee(ve),C(De=>De.find(Oe=>Oe.toastId===f.id)?De.map(Oe=>Oe.toastId===f.id?{...Oe,height:ve}:Oe):[{toastId:f.id,height:ve,position:f.position},...De])},[W,f.title,f.description,C,f.id]);let Qe=X.useCallback(()=>{ne(!0),ue(Re.current),C(ce=>ce.filter(he=>he.toastId!==f.id)),setTimeout(()=>{P(f)},Dl)},[f,P,C,Re]);X.useEffect(()=>{if(f.promise&&de==="loading"||f.duration===1/0||f.type==="loading")return;let ce;return I||y||v&&Vn?(()=>{if(st.current<Ke.current){let he=new Date().getTime()-Ke.current;G.current=G.current-he}st.current=new Date().getTime()})():G.current!==1/0&&(Ke.current=new Date().getTime(),ce=setTimeout(()=>{var he;(he=f.onAutoClose)==null||he.call(f,f),Qe()},G.current)),()=>clearTimeout(ce)},[I,y,f,de,v,Vn,Qe]),X.useEffect(()=>{f.delete&&Qe()},[Qe,f.delete]);function ui(){var ce,he,ve;return se!=null&&se.loading?X.createElement("div",{className:Me(B==null?void 0:B.loader,(ce=f==null?void 0:f.classNames)==null?void 0:ce.loader,"sonner-loader"),"data-visible":de==="loading"},se.loading):k?X.createElement("div",{className:Me(B==null?void 0:B.loader,(he=f==null?void 0:f.classNames)==null?void 0:he.loader,"sonner-loader"),"data-visible":de==="loading"},k):X.createElement(ml,{className:Me(B==null?void 0:B.loader,(ve=f==null?void 0:f.classNames)==null?void 0:ve.loader),visible:de==="loading"})}return X.createElement("li",{tabIndex:0,ref:pe,className:Me(O,Ie,B==null?void 0:B.toast,(t=f==null?void 0:f.classNames)==null?void 0:t.toast,B==null?void 0:B.default,B==null?void 0:B[de],(r=f==null?void 0:f.classNames)==null?void 0:r[de]),"data-sonner-toast":"","data-rich-colors":(n=f.richColors)!=null?n:m,"data-styled":!(f.jsx||f.unstyled||b),"data-mounted":W,"data-promise":!!f.promise,"data-swiped":q,"data-removed":ie,"data-visible":we,"data-y-position":ii,"data-x-position":li,"data-index":j,"data-front":ye,"data-swiping":ae,"data-dismissible":me,"data-type":de,"data-invert":ci,"data-swipe-out":J,"data-swipe-direction":$,"data-expanded":!!(I||_&&W),style:{"--index":j,"--toasts-before":j,"--z-index":A.length-j,"--offset":`${ie?oe:Re.current}px`,"--initial-height":_?"auto":`${re}px`,...x,...f.style},onDragEnd:()=>{M(!1),V(null),Pe.current=null},onPointerDown:ce=>{gr||!me||(ge.current=new Date,ue(Re.current),ce.target.setPointerCapture(ce.pointerId),ce.target.tagName!=="BUTTON"&&(M(!0),Pe.current={x:ce.clientX,y:ce.clientY}))},onPointerUp:()=>{var ce,he,ve,De;if(J||!me)return;Pe.current=null;let Oe=Number(((ce=pe.current)==null?void 0:ce.style.getPropertyValue("--swipe-amount-x").replace("px",""))||0),Je=Number(((he=pe.current)==null?void 0:he.style.getPropertyValue("--swipe-amount-y").replace("px",""))||0),it=new Date().getTime()-((ve=ge.current)==null?void 0:ve.getTime()),Ge=z==="x"?Oe:Je,Ze=Math.abs(Ge)/it;if(Math.abs(Ge)>=Pl||Ze>.11){ue(Re.current),(De=f.onDismiss)==null||De.call(f,f),Q(z==="x"?Oe>0?"right":"left":Je>0?"down":"up"),Qe(),U(!0),L(!1);return}M(!1),V(null)},onPointerMove:ce=>{var he,ve,De,Oe;if(!Pe.current||!me||((he=window.getSelection())==null?void 0:he.toString().length)>0)return;let Je=ce.clientY-Pe.current.y,it=ce.clientX-Pe.current.x,Ge=(ve=e.swipeDirections)!=null?ve:Ol(K);!z&&(Math.abs(it)>1||Math.abs(Je)>1)&&V(Math.abs(it)>Math.abs(Je)?"x":"y");let Ze={x:0,y:0};z==="y"?(Ge.includes("top")||Ge.includes("bottom"))&&(Ge.includes("top")&&Je<0||Ge.includes("bottom")&&Je>0)&&(Ze.y=Je):z==="x"&&(Ge.includes("left")||Ge.includes("right"))&&(Ge.includes("left")&&it<0||Ge.includes("right")&&it>0)&&(Ze.x=it),(Math.abs(Ze.x)>0||Math.abs(Ze.y)>0)&&L(!0),(De=pe.current)==null||De.style.setProperty("--swipe-amount-x",`${Ze.x}px`),(Oe=pe.current)==null||Oe.style.setProperty("--swipe-amount-y",`${Ze.y}px`)}},ze&&!f.jsx?X.createElement("button",{"aria-label":F,"data-disabled":gr,"data-close-button":!0,onClick:gr||!me?()=>{}:()=>{var ce;Qe(),(ce=f.onDismiss)==null||ce.call(f,f)},className:Me(B==null?void 0:B.closeButton,(a=f==null?void 0:f.classNames)==null?void 0:a.closeButton)},(o=se==null?void 0:se.close)!=null?o:xl):null,f.jsx||p.isValidElement(f.title)?f.jsx?f.jsx:typeof f.title=="function"?f.title():f.title:X.createElement(X.Fragment,null,de||f.icon||f.promise?X.createElement("div",{"data-icon":"",className:Me(B==null?void 0:B.icon,(l=f==null?void 0:f.classNames)==null?void 0:l.icon)},f.promise||f.type==="loading"&&!f.icon?f.icon||ui():null,f.type!=="loading"?f.icon||(se==null?void 0:se[de])||gl(de):null):null,X.createElement("div",{"data-content":"",className:Me(B==null?void 0:B.content,(i=f==null?void 0:f.classNames)==null?void 0:i.content)},X.createElement("div",{"data-title":"",className:Me(B==null?void 0:B.title,(s=f==null?void 0:f.classNames)==null?void 0:s.title)},typeof f.title=="function"?f.title():f.title),f.description?X.createElement("div",{"data-description":"",className:Me(w,Ce,B==null?void 0:B.description,(c=f==null?void 0:f.classNames)==null?void 0:c.description)},typeof f.description=="function"?f.description():f.description):null),p.isValidElement(f.cancel)?f.cancel:f.cancel&&Nt(f.cancel)?X.createElement("button",{"data-button":!0,"data-cancel":!0,style:f.cancelButtonStyle||T,onClick:ce=>{var he,ve;Nt(f.cancel)&&me&&((ve=(he=f.cancel).onClick)==null||ve.call(he,ce),Qe())},className:Me(B==null?void 0:B.cancelButton,(u=f==null?void 0:f.classNames)==null?void 0:u.cancelButton)},f.cancel.label):null,p.isValidElement(f.action)?f.action:f.action&&Nt(f.action)?X.createElement("button",{"data-button":!0,"data-action":!0,style:f.actionButtonStyle||R,onClick:ce=>{var he,ve;Nt(f.action)&&((ve=(he=f.action).onClick)==null||ve.call(he,ce),!ce.defaultPrevented&&Qe())},className:Me(B==null?void 0:B.actionButton,(d=f==null?void 0:f.classNames)==null?void 0:d.actionButton)},f.action.label):null))};function Jn(){if(typeof window>"u"||typeof document>"u")return"ltr";let e=document.documentElement.getAttribute("dir");return e==="auto"||!e?window.getComputedStyle(document.documentElement).direction:e}function Ml(e,t){let r={};return[e,t].forEach((n,a)=>{let o=a===1,l=o?"--mobile-offset":"--offset",i=o?Nl:Il;function s(c){["top","right","bottom","left"].forEach(u=>{r[`${l}-${u}`]=typeof c=="number"?`${c}px`:c})}typeof n=="number"||typeof n=="string"?s(n):typeof n=="object"?["top","right","bottom","left"].forEach(c=>{n[c]===void 0?r[`${l}-${c}`]=i:r[`${l}-${c}`]=typeof n[c]=="number"?`${n[c]}px`:n[c]}):s(i)}),r}var cp=p.forwardRef(function(e,t){let{invert:r,position:n="bottom-right",hotkey:a=["altKey","KeyT"],expand:o,closeButton:l,className:i,offset:s,mobileOffset:c,theme:u="light",richColors:d,duration:h,style:f,visibleToasts:b=jl,toastOptions:y,dir:C=Jn(),gap:N=zl,loadingIcon:E,icons:j,containerAriaLabel:A="Notifications",pauseWhenPageIsHidden:I}=e,[P,m]=X.useState([]),S=X.useMemo(()=>Array.from(new Set([n].concat(P.filter(v=>v.position).map(v=>v.position)))),[P,n]),[x,T]=X.useState([]),[R,O]=X.useState(!1),[w,H]=X.useState(!1),[K,D]=X.useState(u!=="system"?u:typeof window<"u"&&window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),k=X.useRef(null),_=a.join("+").replace(/Key/g,"").replace(/Digit/g,""),B=X.useRef(null),se=X.useRef(!1),F=X.useCallback(v=>{m(z=>{var V;return(V=z.find($=>$.id===v.id))!=null&&V.delete||Ae.dismiss(v.id),z.filter(({id:$})=>$!==v.id)})},[]);return X.useEffect(()=>Ae.subscribe(v=>{if(v.dismiss){m(z=>z.map(V=>V.id===v.id?{...V,delete:!0}:V));return}setTimeout(()=>{hi.flushSync(()=>{m(z=>{let V=z.findIndex($=>$.id===v.id);return V!==-1?[...z.slice(0,V),{...z[V],...v},...z.slice(V+1)]:[v,...z]})})})}),[]),X.useEffect(()=>{if(u!=="system"){D(u);return}if(u==="system"&&(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?D("dark"):D("light")),typeof window>"u")return;let v=window.matchMedia("(prefers-color-scheme: dark)");try{v.addEventListener("change",({matches:z})=>{D(z?"dark":"light")})}catch{v.addListener(({matches:V})=>{try{D(V?"dark":"light")}catch($){console.error($)}})}},[u]),X.useEffect(()=>{P.length<=1&&O(!1)},[P]),X.useEffect(()=>{let v=z=>{var V,$;a.every(Q=>z[Q]||z.code===Q)&&(O(!0),(V=k.current)==null||V.focus()),z.code==="Escape"&&(document.activeElement===k.current||($=k.current)!=null&&$.contains(document.activeElement))&&O(!1)};return document.addEventListener("keydown",v),()=>document.removeEventListener("keydown",v)},[a]),X.useEffect(()=>{if(k.current)return()=>{B.current&&(B.current.focus({preventScroll:!0}),B.current=null,se.current=!1)}},[k.current]),X.createElement("section",{ref:t,"aria-label":`${A} ${_}`,tabIndex:-1,"aria-live":"polite","aria-relevant":"additions text","aria-atomic":"false",suppressHydrationWarning:!0},S.map((v,z)=>{var V;let[$,Q]=v.split("-");return P.length?X.createElement("ol",{key:v,dir:C==="auto"?Jn():C,tabIndex:-1,ref:k,className:i,"data-sonner-toaster":!0,"data-theme":K,"data-y-position":$,"data-lifted":R&&P.length>1&&!o,"data-x-position":Q,style:{"--front-toast-height":`${((V=x[0])==null?void 0:V.height)||0}px`,"--width":`${Ll}px`,"--gap":`${N}px`,...f,...Ml(s,c)},onBlur:W=>{se.current&&!W.currentTarget.contains(W.relatedTarget)&&(se.current=!1,B.current&&(B.current.focus({preventScroll:!0}),B.current=null))},onFocus:W=>{W.target instanceof HTMLElement&&W.target.dataset.dismissible==="false"||se.current||(se.current=!0,B.current=W.relatedTarget)},onMouseEnter:()=>O(!0),onMouseMove:()=>O(!0),onMouseLeave:()=>{w||O(!1)},onDragEnd:()=>O(!1),onPointerDown:W=>{W.target instanceof HTMLElement&&W.target.dataset.dismissible==="false"||H(!0)},onPointerUp:()=>H(!1)},P.filter(W=>!W.position&&z===0||W.position===v).map((W,Y)=>{var ie,ne;return X.createElement(Gl,{key:W.id,icons:j,index:Y,toast:W,defaultRichColors:d,duration:(ie=y==null?void 0:y.duration)!=null?ie:h,className:y==null?void 0:y.className,descriptionClassName:y==null?void 0:y.descriptionClassName,invert:r,visibleToasts:b,closeButton:(ne=y==null?void 0:y.closeButton)!=null?ne:l,interacting:w,position:v,style:y==null?void 0:y.style,unstyled:y==null?void 0:y.unstyled,classNames:y==null?void 0:y.classNames,cancelButtonStyle:y==null?void 0:y.cancelButtonStyle,actionButtonStyle:y==null?void 0:y.actionButtonStyle,removeToast:F,toasts:P.filter(ae=>ae.position==W.position),heights:x.filter(ae=>ae.position==W.position),setHeights:T,expandByDefault:o,gap:N,loadingIcon:E,expanded:R,pauseWhenPageIsHidden:I,swipeDirections:e.swipeDirections})})):null}))});const Fl={theme:"system",setTheme:()=>null},Ra=p.createContext(Fl);function up({children:e,...t}){const r=Z.use.theme(),n=Z.use.setTheme();p.useEffect(()=>{const o=window.document.documentElement;if(o.classList.remove("light","dark"),r==="system"){const l=window.matchMedia("(prefers-color-scheme: dark)"),i=s=>{o.classList.remove("light","dark"),o.classList.add(s.matches?"dark":"light")};return o.classList.add(l.matches?"dark":"light"),l.addEventListener("change",i),()=>l.removeEventListener("change",i)}else o.classList.add(r)},[r]);const a={theme:r,setTheme:n};return g.jsx(Ra.Provider,{...t,value:a,children:e})}const $l=(e,t,r,n)=>{var o,l,i,s;const a=[r,{code:t,...n||{}}];if((l=(o=e==null?void 0:e.services)==null?void 0:o.logger)!=null&&l.forward)return e.services.logger.forward(a,"warn","react-i18next::",!0);ht(a[0])&&(a[0]=`react-i18next:: ${a[0]}`),(s=(i=e==null?void 0:e.services)==null?void 0:i.logger)!=null&&s.warn?e.services.logger.warn(...a):console!=null&&console.warn&&console.warn(...a)},Zn={},rn=(e,t,r,n)=>{ht(r)&&Zn[r]||(ht(r)&&(Zn[r]=new Date),$l(e,t,r,n))},Aa=(e,t)=>()=>{if(e.isInitialized)t();else{const r=()=>{setTimeout(()=>{e.off("initialized",r)},0),t()};e.on("initialized",r)}},nn=(e,t,r)=>{e.loadNamespaces(t,Aa(e,r))},eo=(e,t,r,n)=>{if(ht(r)&&(r=[r]),e.options.preload&&e.options.preload.indexOf(t)>-1)return nn(e,r,n);r.forEach(a=>{e.options.ns.indexOf(a)<0&&e.options.ns.push(a)}),e.loadLanguages(t,Aa(e,n))},Hl=(e,t,r={})=>!t.languages||!t.languages.length?(rn(t,"NO_LANGUAGES","i18n.languages were undefined or empty",{languages:t.languages}),!0):t.hasLoadedNamespace(e,{lng:r.lng,precheck:(n,a)=>{var o;if(((o=r.bindI18n)==null?void 0:o.indexOf("languageChanging"))>-1&&n.services.backendConnector.backend&&n.isLanguageChangingTo&&!a(n.isLanguageChangingTo,e))return!1}}),ht=e=>typeof e=="string",Bl=e=>typeof e=="object"&&e!==null,Vl=/&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g,ql={"&amp;":"&","&#38;":"&","&lt;":"<","&#60;":"<","&gt;":">","&#62;":">","&apos;":"'","&#39;":"'","&quot;":'"',"&#34;":'"',"&nbsp;":" ","&#160;":" ","&copy;":"©","&#169;":"©","&reg;":"®","&#174;":"®","&hellip;":"…","&#8230;":"…","&#x2F;":"/","&#47;":"/"},Ul=e=>ql[e],Wl=e=>e.replace(Vl,Ul);let on={bindI18n:"languageChanged",bindI18nStore:"",transEmptyNodeValue:"",transSupportBasicHtmlNodes:!0,transWrapTextNodes:"",transKeepBasicHtmlNodesFor:["br","strong","i","p"],useSuspense:!0,unescape:Wl};const Xl=(e={})=>{on={...on,...e}},Yl=()=>on;let ja;const Kl=e=>{ja=e},Ql=()=>ja,dp={type:"3rdParty",init(e){Xl(e.options.react),Kl(e)}},Jl=p.createContext();class Zl{constructor(){this.usedNamespaces={}}addUsedNamespaces(t){t.forEach(r=>{this.usedNamespaces[r]||(this.usedNamespaces[r]=!0)})}getUsedNamespaces(){return Object.keys(this.usedNamespaces)}}const ec=(e,t)=>{const r=p.useRef();return p.useEffect(()=>{r.current=e},[e,t]),r.current},Ia=(e,t,r,n)=>e.getFixedT(t,r,n),tc=(e,t,r,n)=>p.useCallback(Ia(e,t,r,n),[e,t,r,n]),_e=(e,t={})=>{var A,I,P,m;const{i18n:r}=t,{i18n:n,defaultNS:a}=p.useContext(Jl)||{},o=r||n||Ql();if(o&&!o.reportNamespaces&&(o.reportNamespaces=new Zl),!o){rn(o,"NO_I18NEXT_INSTANCE","useTranslation: You will need to pass in an i18next instance by using initReactI18next");const S=(T,R)=>ht(R)?R:Bl(R)&&ht(R.defaultValue)?R.defaultValue:Array.isArray(T)?T[T.length-1]:T,x=[S,{},!1];return x.t=S,x.i18n={},x.ready=!1,x}(A=o.options.react)!=null&&A.wait&&rn(o,"DEPRECATED_OPTION","useTranslation: It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.");const l={...Yl(),...o.options.react,...t},{useSuspense:i,keyPrefix:s}=l;let c=a||((I=o.options)==null?void 0:I.defaultNS);c=ht(c)?[c]:c||["translation"],(m=(P=o.reportNamespaces).addUsedNamespaces)==null||m.call(P,c);const u=(o.isInitialized||o.initializedStoreOnce)&&c.every(S=>Hl(S,o,l)),d=tc(o,t.lng||null,l.nsMode==="fallback"?c:c[0],s),h=()=>d,f=()=>Ia(o,t.lng||null,l.nsMode==="fallback"?c:c[0],s),[b,y]=p.useState(h);let C=c.join();t.lng&&(C=`${t.lng}${C}`);const N=ec(C),E=p.useRef(!0);p.useEffect(()=>{const{bindI18n:S,bindI18nStore:x}=l;E.current=!0,!u&&!i&&(t.lng?eo(o,t.lng,c,()=>{E.current&&y(f)}):nn(o,c,()=>{E.current&&y(f)})),u&&N&&N!==C&&E.current&&y(f);const T=()=>{E.current&&y(f)};return S&&(o==null||o.on(S,T)),x&&(o==null||o.store.on(x,T)),()=>{E.current=!1,o&&(S==null||S.split(" ").forEach(R=>o.off(R,T))),x&&o&&x.split(" ").forEach(R=>o.store.off(R,T))}},[o,C]),p.useEffect(()=>{E.current&&u&&y(h)},[o,s,u]);const j=[b,o,u];if(j.t=b,j.i18n=o,j.ready=u,u||!u&&!i)return j;throw new Promise(S=>{t.lng?eo(o,t.lng,c,()=>S()):nn(o,c,()=>S())})},to=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,ro=_a,rc=(e,t)=>r=>{var n;if((t==null?void 0:t.variants)==null)return ro(e,r==null?void 0:r.class,r==null?void 0:r.className);const{variants:a,defaultVariants:o}=t,l=Object.keys(a).map(c=>{const u=r==null?void 0:r[c],d=o==null?void 0:o[c];if(u===null)return null;const h=to(u)||to(d);return a[c][h]}),i=r&&Object.entries(r).reduce((c,u)=>{let[d,h]=u;return h===void 0||(c[d]=h),c},{}),s=t==null||(n=t.compoundVariants)===null||n===void 0?void 0:n.reduce((c,u)=>{let{class:d,className:h,...f}=u;return Object.entries(f).every(b=>{let[y,C]=b;return Array.isArray(C)?C.includes({...o,...i}[y]):{...o,...i}[y]===C})?[...c,d,h]:c},[]);return ro(e,l,s,r==null?void 0:r.class,r==null?void 0:r.className)};var[nr,fp]=xn("Tooltip",[pa]),or=pa(),Na="TooltipProvider",nc=700,an="tooltip.open",[oc,Cn]=nr(Na),La=e=>{const{__scopeTooltip:t,delayDuration:r=nc,skipDelayDuration:n=300,disableHoverableContent:a=!1,children:o}=e,[l,i]=p.useState(!0),s=p.useRef(!1),c=p.useRef(0);return p.useEffect(()=>{const u=c.current;return()=>window.clearTimeout(u)},[]),g.jsx(oc,{scope:t,isOpenDelayed:l,delayDuration:r,onOpen:p.useCallback(()=>{window.clearTimeout(c.current),i(!1)},[]),onClose:p.useCallback(()=>{window.clearTimeout(c.current),c.current=window.setTimeout(()=>i(!0),n)},[n]),isPointerInTransitRef:s,onPointerInTransitChange:p.useCallback(u=>{s.current=u},[]),disableHoverableContent:a,children:o})};La.displayName=Na;var ar="Tooltip",[ac,sr]=nr(ar),za=e=>{const{__scopeTooltip:t,children:r,open:n,defaultOpen:a=!1,onOpenChange:o,disableHoverableContent:l,delayDuration:i}=e,s=Cn(ar,e.__scopeTooltip),c=or(t),[u,d]=p.useState(null),h=ft(),f=p.useRef(0),b=l??s.disableHoverableContent,y=i??s.delayDuration,C=p.useRef(!1),[N=!1,E]=ma({prop:n,defaultProp:a,onChange:m=>{m?(s.onOpen(),document.dispatchEvent(new CustomEvent(an))):s.onClose(),o==null||o(m)}}),j=p.useMemo(()=>N?C.current?"delayed-open":"instant-open":"closed",[N]),A=p.useCallback(()=>{window.clearTimeout(f.current),f.current=0,C.current=!1,E(!0)},[E]),I=p.useCallback(()=>{window.clearTimeout(f.current),f.current=0,E(!1)},[E]),P=p.useCallback(()=>{window.clearTimeout(f.current),f.current=window.setTimeout(()=>{C.current=!0,E(!0),f.current=0},y)},[y,E]);return p.useEffect(()=>()=>{f.current&&(window.clearTimeout(f.current),f.current=0)},[]),g.jsx(Ri,{...c,children:g.jsx(ac,{scope:t,contentId:h,open:N,stateAttribute:j,trigger:u,onTriggerChange:d,onTriggerEnter:p.useCallback(()=>{s.isOpenDelayed?P():A()},[s.isOpenDelayed,P,A]),onTriggerLeave:p.useCallback(()=>{b?I():(window.clearTimeout(f.current),f.current=0)},[I,b]),onOpen:A,onClose:I,disableHoverableContent:b,children:r})})};za.displayName=ar;var sn="TooltipTrigger",Pa=p.forwardRef((e,t)=>{const{__scopeTooltip:r,...n}=e,a=sr(sn,r),o=Cn(sn,r),l=or(r),i=p.useRef(null),s=Xe(t,i,a.onTriggerChange),c=p.useRef(!1),u=p.useRef(!1),d=p.useCallback(()=>c.current=!1,[]);return p.useEffect(()=>()=>document.removeEventListener("pointerup",d),[d]),g.jsx(Ai,{asChild:!0,...l,children:g.jsx(Ee.button,{"aria-describedby":a.open?a.contentId:void 0,"data-state":a.stateAttribute,...n,ref:s,onPointerMove:ke(e.onPointerMove,h=>{h.pointerType!=="touch"&&!u.current&&!o.isPointerInTransitRef.current&&(a.onTriggerEnter(),u.current=!0)}),onPointerLeave:ke(e.onPointerLeave,()=>{a.onTriggerLeave(),u.current=!1}),onPointerDown:ke(e.onPointerDown,()=>{c.current=!0,document.addEventListener("pointerup",d,{once:!0})}),onFocus:ke(e.onFocus,()=>{c.current||a.onOpen()}),onBlur:ke(e.onBlur,a.onClose),onClick:ke(e.onClick,a.onClose)})})});Pa.displayName=sn;var sc="TooltipPortal",[hp,ic]=nr(sc,{forceMount:void 0}),wt="TooltipContent",Da=p.forwardRef((e,t)=>{const r=ic(wt,e.__scopeTooltip),{forceMount:n=r.forceMount,side:a="top",...o}=e,l=sr(wt,e.__scopeTooltip);return g.jsx(_t,{present:n||l.open,children:l.disableHoverableContent?g.jsx(Oa,{side:a,...o,ref:t}):g.jsx(lc,{side:a,...o,ref:t})})}),lc=p.forwardRef((e,t)=>{const r=sr(wt,e.__scopeTooltip),n=Cn(wt,e.__scopeTooltip),a=p.useRef(null),o=Xe(t,a),[l,i]=p.useState(null),{trigger:s,onClose:c}=r,u=a.current,{onPointerInTransitChange:d}=n,h=p.useCallback(()=>{i(null),d(!1)},[d]),f=p.useCallback((b,y)=>{const C=b.currentTarget,N={x:b.clientX,y:b.clientY},E=fc(N,C.getBoundingClientRect()),j=hc(N,E),A=gc(y.getBoundingClientRect()),I=mc([...j,...A]);i(I),d(!0)},[d]);return p.useEffect(()=>()=>h(),[h]),p.useEffect(()=>{if(s&&u){const b=C=>f(C,u),y=C=>f(C,s);return s.addEventListener("pointerleave",b),u.addEventListener("pointerleave",y),()=>{s.removeEventListener("pointerleave",b),u.removeEventListener("pointerleave",y)}}},[s,u,f,h]),p.useEffect(()=>{if(l){const b=y=>{const C=y.target,N={x:y.clientX,y:y.clientY},E=(s==null?void 0:s.contains(C))||(u==null?void 0:u.contains(C)),j=!pc(N,l);E?h():j&&(h(),c())};return document.addEventListener("pointermove",b),()=>document.removeEventListener("pointermove",b)}},[s,u,l,c,h]),g.jsx(Oa,{...e,ref:o})}),[cc,uc]=nr(ar,{isInside:!1}),Oa=p.forwardRef((e,t)=>{const{__scopeTooltip:r,children:n,"aria-label":a,onEscapeKeyDown:o,onPointerDownOutside:l,...i}=e,s=sr(wt,r),c=or(r),{onClose:u}=s;return p.useEffect(()=>(document.addEventListener(an,u),()=>document.removeEventListener(an,u)),[u]),p.useEffect(()=>{if(s.trigger){const d=h=>{const f=h.target;f!=null&&f.contains(s.trigger)&&u()};return window.addEventListener("scroll",d,{capture:!0}),()=>window.removeEventListener("scroll",d,{capture:!0})}},[s.trigger,u]),g.jsx(Ei,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:o,onPointerDownOutside:l,onFocusOutside:d=>d.preventDefault(),onDismiss:u,children:g.jsxs(ki,{"data-state":s.stateAttribute,...c,...i,ref:t,style:{...i.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[g.jsx(Ci,{children:n}),g.jsx(cc,{scope:r,isInside:!0,children:g.jsx(Ti,{id:s.contentId,role:"tooltip",children:a||n})})]})})});Da.displayName=wt;var Ga="TooltipArrow",dc=p.forwardRef((e,t)=>{const{__scopeTooltip:r,...n}=e,a=or(r);return uc(Ga,r).isInside?null:g.jsx(ji,{...a,...n,ref:t})});dc.displayName=Ga;function fc(e,t){const r=Math.abs(t.top-e.y),n=Math.abs(t.bottom-e.y),a=Math.abs(t.right-e.x),o=Math.abs(t.left-e.x);switch(Math.min(r,n,a,o)){case o:return"left";case a:return"right";case r:return"top";case n:return"bottom";default:throw new Error("unreachable")}}function hc(e,t,r=5){const n=[];switch(t){case"top":n.push({x:e.x-r,y:e.y+r},{x:e.x+r,y:e.y+r});break;case"bottom":n.push({x:e.x-r,y:e.y-r},{x:e.x+r,y:e.y-r});break;case"left":n.push({x:e.x+r,y:e.y-r},{x:e.x+r,y:e.y+r});break;case"right":n.push({x:e.x-r,y:e.y-r},{x:e.x-r,y:e.y+r});break}return n}function gc(e){const{top:t,right:r,bottom:n,left:a}=e;return[{x:a,y:t},{x:r,y:t},{x:r,y:n},{x:a,y:n}]}function pc(e,t){const{x:r,y:n}=e;let a=!1;for(let o=0,l=t.length-1;o<t.length;l=o++){const i=t[o].x,s=t[o].y,c=t[l].x,u=t[l].y;s>n!=u>n&&r<(c-i)*(n-s)/(u-s)+i&&(a=!a)}return a}function mc(e){const t=e.slice();return t.sort((r,n)=>r.x<n.x?-1:r.x>n.x?1:r.y<n.y?-1:r.y>n.y?1:0),vc(t)}function vc(e){if(e.length<=1)return e.slice();const t=[];for(let n=0;n<e.length;n++){const a=e[n];for(;t.length>=2;){const o=t[t.length-1],l=t[t.length-2];if((o.x-l.x)*(a.y-l.y)>=(o.y-l.y)*(a.x-l.x))t.pop();else break}t.push(a)}t.pop();const r=[];for(let n=e.length-1;n>=0;n--){const a=e[n];for(;r.length>=2;){const o=r[r.length-1],l=r[r.length-2];if((o.x-l.x)*(a.y-l.y)>=(o.y-l.y)*(a.x-l.x))r.pop();else break}r.push(a)}return r.pop(),t.length===1&&r.length===1&&t[0].x===r[0].x&&t[0].y===r[0].y?t:t.concat(r)}var yc=La,bc=za,wc=Pa,Ma=Da;const Fa=yc,$a=bc,Ha=wc,xc=e=>typeof e!="string"?e:g.jsx("div",{className:"relative top-0 pt-1 whitespace-pre-wrap break-words",children:e}),Tn=p.forwardRef(({className:e,side:t="left",align:r="start",children:n,...a},o)=>{const l=p.useRef(null);return p.useEffect(()=>{l.current&&(l.current.scrollTop=0)},[n]),g.jsx(Ma,{ref:o,side:t,align:r,className:fe("bg-popover text-popover-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 max-h-[60vh] overflow-y-auto whitespace-pre-wrap break-words rounded-md border px-3 py-2 text-sm shadow-md z-60",e),...a,children:typeof n=="string"?xc(n):n})});Tn.displayName=Ma.displayName;const no=rc("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"size-8"}},defaultVariants:{variant:"default",size:"default"}}),be=p.forwardRef(({className:e,variant:t,tooltip:r,size:n,side:a="right",asChild:o=!1,...l},i)=>{const s=o?Ii:"button";return r?g.jsx(Fa,{children:g.jsxs($a,{children:[g.jsx(Ha,{asChild:!0,children:g.jsx(s,{className:fe(no({variant:t,size:n,className:e}),"cursor-pointer"),ref:i,...l})}),g.jsx(Tn,{side:a,children:r})]})}):g.jsx(s,{className:fe(no({variant:t,size:n,className:e}),"cursor-pointer"),ref:i,...l})});be.displayName="Button";const Wt=p.forwardRef(({className:e,type:t,...r},n)=>g.jsx("input",{type:t,className:fe("border-input file:text-foreground placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 rounded-md border bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:ring-1 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm [&::-webkit-inner-spin-button]:opacity-50 [&::-webkit-outer-spin-button]:opacity-50",e),ref:n,...r}));Wt.displayName="Input";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _c=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Ba=(...e)=>e.filter((t,r,n)=>!!t&&t.trim()!==""&&n.indexOf(t)===r).join(" ").trim();/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Sc={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ec=p.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:n,className:a="",children:o,iconNode:l,...i},s)=>p.createElement("svg",{ref:s,...Sc,width:t,height:t,stroke:e,strokeWidth:n?Number(r)*24/Number(t):r,className:Ba("lucide",a),...i},[...l.map(([c,u])=>p.createElement(c,u)),...Array.isArray(o)?o:[o]]));/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const le=(e,t)=>{const r=p.forwardRef(({className:n,...a},o)=>p.createElement(Ec,{ref:o,iconNode:t,className:Ba(`lucide-${_c(e)}`,n),...a}));return r.displayName=`${e}`,r};/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kc=[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]],gp=le("Activity",kc);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cc=[["path",{d:"M17 12H7",key:"16if0g"}],["path",{d:"M19 18H5",key:"18s9l3"}],["path",{d:"M21 6H3",key:"1jwq7v"}]],pp=le("AlignCenter",Cc);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tc=[["path",{d:"M15 12H3",key:"6jk70r"}],["path",{d:"M17 18H3",key:"1amg6g"}],["path",{d:"M21 6H3",key:"1jwq7v"}]],mp=le("AlignLeft",Tc);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rc=[["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M21 18H7",key:"1ygte8"}],["path",{d:"M21 6H3",key:"1jwq7v"}]],vp=le("AlignRight",Rc);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ac=[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]],yp=le("ArrowDown",Ac);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jc=[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]],bp=le("ArrowUp",jc);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ic=[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]],Nc=le("BookOpen",Ic);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Lc=[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]],Va=le("Check",Lc);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zc=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],wp=le("ChevronDown",zc);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pc=[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]],xp=le("ChevronLeft",Pc);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dc=[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]],_p=le("ChevronRight",Dc);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Oc=[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]],Sp=le("ChevronUp",Oc);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gc=[["path",{d:"m11 17-5-5 5-5",key:"13zhaf"}],["path",{d:"m18 17-5-5 5-5",key:"h8a8et"}]],Ep=le("ChevronsLeft",Gc);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Mc=[["path",{d:"m6 17 5-5-5-5",key:"xnjwq"}],["path",{d:"m13 17 5-5-5-5",key:"17xmmf"}]],kp=le("ChevronsRight",Mc);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fc=[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]],$c=le("ChevronsUpDown",Fc);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hc=[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]],Cp=le("Copy",Hc);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bc=[["path",{d:"m7 21-4.3-4.3c-1-1-1-2.5 0-3.4l9.6-9.6c1-1 2.5-1 3.4 0l5.6 5.6c1 1 1 2.5 0 3.4L13 21",key:"182aya"}],["path",{d:"M22 21H7",key:"t4ddhn"}],["path",{d:"m5 11 9 9",key:"1mo9qw"}]],Tp=le("Eraser",Bc);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vc=[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]],Rp=le("FileText",Vc);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qc=[["path",{d:"M20 7h-3a2 2 0 0 1-2-2V2",key:"x099mo"}],["path",{d:"M9 18a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h7l4 4v10a2 2 0 0 1-2 2Z",key:"18t6ie"}],["path",{d:"M3 7.6v12.8A1.6 1.6 0 0 0 4.6 22h9.8",key:"1nja0z"}]],Ap=le("Files",qc);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Uc=[["path",{d:"M3 7V5a2 2 0 0 1 2-2h2",key:"aa7l1z"}],["path",{d:"M17 3h2a2 2 0 0 1 2 2v2",key:"4qcy5o"}],["path",{d:"M21 17v2a2 2 0 0 1-2 2h-2",key:"6vwrx8"}],["path",{d:"M7 21H5a2 2 0 0 1-2-2v-2",key:"ioqczr"}],["rect",{width:"10",height:"8",x:"7",y:"8",rx:"1",key:"vys8me"}]],Wc=le("Fullscreen",Uc);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xc=[["path",{d:"M6 3v12",key:"qpgusn"}],["path",{d:"M18 9a3 3 0 1 0 0-6 3 3 0 0 0 0 6z",key:"1d02ji"}],["path",{d:"M6 21a3 3 0 1 0 0-6 3 3 0 0 0 0 6z",key:"chk6ph"}],["path",{d:"M15 6a9 9 0 0 0-9 9",key:"or332x"}],["path",{d:"M18 15v6",key:"9wciyi"}],["path",{d:"M21 18h-6",key:"139f0c"}]],Yc=le("GitBranchPlus",Xc);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Kc=[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]],jp=le("Github",Kc);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qc=[["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"19",cy:"5",r:"1",key:"w8mnmm"}],["circle",{cx:"5",cy:"5",r:"1",key:"lttvr7"}],["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}],["circle",{cx:"19",cy:"19",r:"1",key:"shf9b7"}],["circle",{cx:"5",cy:"19",r:"1",key:"bfqh0e"}]],Jc=le("Grip",Qc);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zc=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]],Ip=le("Info",Zc);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const eu=[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]],qa=le("LoaderCircle",eu);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tu=[["path",{d:"M12 2v4",key:"3427ic"}],["path",{d:"m16.2 7.8 2.9-2.9",key:"r700ao"}],["path",{d:"M18 12h4",key:"wj9ykh"}],["path",{d:"m16.2 16.2 2.9 2.9",key:"1bxg5t"}],["path",{d:"M12 18v4",key:"jadmvz"}],["path",{d:"m4.9 19.1 2.9-2.9",key:"bwix9q"}],["path",{d:"M2 12h4",key:"j09sii"}],["path",{d:"m4.9 4.9 2.9 2.9",key:"giyufr"}]],Np=le("Loader",tu);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ru=[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]],Lp=le("LogOut",ru);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nu=[["path",{d:"M8 3H5a2 2 0 0 0-2 2v3",key:"1dcmit"}],["path",{d:"M21 8V5a2 2 0 0 0-2-2h-3",key:"1e4gt3"}],["path",{d:"M3 16v3a2 2 0 0 0 2 2h3",key:"wsl5sc"}],["path",{d:"M16 21h3a2 2 0 0 0 2-2v-3",key:"18trek"}]],ou=le("Maximize",nu);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const au=[["path",{d:"M8 3v3a2 2 0 0 1-2 2H3",key:"hohbtr"}],["path",{d:"M21 8h-3a2 2 0 0 1-2-2V3",key:"5jw1f3"}],["path",{d:"M3 16h3a2 2 0 0 1 2 2v3",key:"198tvr"}],["path",{d:"M16 21v-3a2 2 0 0 1 2-2h3",key:"ph8mxp"}]],su=le("Minimize",au);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const iu=[["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["path",{d:"M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z",key:"12rzf8"}]],zp=le("Palette",iu);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lu=[["rect",{x:"14",y:"4",width:"4",height:"16",rx:"1",key:"zuxfzm"}],["rect",{x:"6",y:"4",width:"4",height:"16",rx:"1",key:"1okwgv"}]],cu=le("Pause",lu);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const uu=[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}],["path",{d:"m15 5 4 4",key:"1mk7zo"}]],du=le("Pencil",uu);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fu=[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]],hu=le("Play",fu);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gu=[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]],pu=le("RefreshCw",gu);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mu=[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]],vu=le("RotateCcw",mu);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yu=[["path",{d:"M21 12a9 9 0 1 1-9-9c2.52 0 4.93 1 6.74 2.74L21 8",key:"1p45f6"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}]],bu=le("RotateCw",yu);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wu=[["circle",{cx:"6",cy:"6",r:"3",key:"1lh9wr"}],["path",{d:"M8.12 8.12 12 12",key:"1alkpv"}],["path",{d:"M20 4 8.12 15.88",key:"xgtan2"}],["circle",{cx:"6",cy:"18",r:"3",key:"fqmcym"}],["path",{d:"M14.8 14.8 20 20",key:"ptml3r"}]],xu=le("Scissors",wu);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _u=[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]],Su=le("Search",_u);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Eu=[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]],Pp=le("Send",Eu);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ku=[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],Cu=le("Settings",ku);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tu=[["path",{d:"M21 10.5V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h12.5",key:"1uzm8b"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]],Dp=le("SquareCheckBig",Tu);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ru=[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}]],Op=le("Trash",Ru);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Au=[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]],Gp=le("TriangleAlert",Au);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ju=[["path",{d:"M9 14 4 9l5-5",key:"102s5s"}],["path",{d:"M4 9h10.5a5.5 5.5 0 0 1 5.5 5.5a5.5 5.5 0 0 1-5.5 5.5H11",key:"f3b9sd"}]],Ua=le("Undo2",ju);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Iu=[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]],Mp=le("Upload",Iu);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Nu=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],Lu=le("X",Nu);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zu=[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]],Fp=le("Zap",zu);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pu=[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"11",x2:"11",y1:"8",y2:"14",key:"1vmskp"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]],Du=le("ZoomIn",Pu);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ou=[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]],Gu=le("ZoomOut",Ou),Mu=wa,$p=Li,Fu=va,Wa=p.forwardRef(({className:e,...t},r)=>g.jsx(_n,{ref:r,className:fe("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/30",e),...t}));Wa.displayName=_n.displayName;const Xa=p.forwardRef(({className:e,children:t,...r},n)=>g.jsxs(Fu,{children:[g.jsx(Wa,{}),g.jsxs(Sn,{ref:n,className:fe("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-top-[48%] fixed top-[50%] left-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border p-6 shadow-lg duration-200 sm:rounded-lg",e),...r,children:[t,g.jsxs(Ni,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-sm opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-none disabled:pointer-events-none",children:[g.jsx(Lu,{className:"h-4 w-4"}),g.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));Xa.displayName=Sn.displayName;const Ya=({className:e,...t})=>g.jsx("div",{className:fe("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});Ya.displayName="DialogHeader";const Ka=({className:e,...t})=>g.jsx("div",{className:fe("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});Ka.displayName="DialogFooter";const Qa=p.forwardRef(({className:e,...t},r)=>g.jsx(ya,{ref:r,className:fe("text-lg leading-none font-semibold tracking-tight",e),...t}));Qa.displayName=ya.displayName;const Ja=p.forwardRef(({className:e,...t},r)=>g.jsx(ba,{ref:r,className:fe("text-muted-foreground text-sm",e),...t}));Ja.displayName=ba.displayName;const Rn=Pi,An=Di,ir=p.forwardRef(({className:e,align:t="center",sideOffset:r=4,collisionPadding:n,sticky:a,avoidCollisions:o=!1,...l},i)=>g.jsx(zi,{children:g.jsx(xa,{ref:i,align:t,sideOffset:r,collisionPadding:n,sticky:a,avoidCollisions:o,className:fe("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 rounded-md border p-4 shadow-md outline-none",e),...l})}));ir.displayName=xa.displayName;var $u=`
precision mediump float;

varying vec4 v_color;
varying float v_border;

const float radius = 0.5;
const vec4 transparent = vec4(0.0, 0.0, 0.0, 0.0);

void main(void) {
  vec2 m = gl_PointCoord - vec2(0.5, 0.5);
  float dist = radius - length(m);

  // No antialiasing for picking mode:
  #ifdef PICKING_MODE
  if (dist > v_border)
    gl_FragColor = v_color;
  else
    gl_FragColor = transparent;

  #else
  float t = 0.0;
  if (dist > v_border)
    t = 1.0;
  else if (dist > 0.0)
    t = dist / v_border;

  gl_FragColor = mix(transparent, v_color, t);
  #endif
}
`,Hu=$u,Bu=`
attribute vec4 a_id;
attribute vec4 a_color;
attribute vec2 a_position;
attribute float a_size;

uniform float u_sizeRatio;
uniform float u_pixelRatio;
uniform mat3 u_matrix;

varying vec4 v_color;
varying float v_border;

const float bias = 255.0 / 254.0;

void main() {
  gl_Position = vec4(
    (u_matrix * vec3(a_position, 1)).xy,
    0,
    1
  );

  // Multiply the point size twice:
  //  - x SCALING_RATIO to correct the canvas scaling
  //  - x 2 to correct the formulae
  gl_PointSize = a_size / u_sizeRatio * u_pixelRatio * 2.0;

  v_border = (0.5 / a_size) * u_sizeRatio;

  #ifdef PICKING_MODE
  // For picking mode, we use the ID as the color:
  v_color = a_id;
  #else
  // For normal mode, we use the color:
  v_color = a_color;
  #endif

  v_color.a *= bias;
}
`,Vu=Bu,Za=WebGLRenderingContext,oo=Za.UNSIGNED_BYTE,ao=Za.FLOAT,qu=["u_sizeRatio","u_pixelRatio","u_matrix"],Uu=function(e){function t(){return la(this,t),ca(this,t,arguments)}return aa(t,e),sa(t,[{key:"getDefinition",value:function(){return{VERTICES:1,VERTEX_SHADER_SOURCE:Vu,FRAGMENT_SHADER_SOURCE:Hu,METHOD:WebGLRenderingContext.POINTS,UNIFORMS:qu,ATTRIBUTES:[{name:"a_position",size:2,type:ao},{name:"a_size",size:1,type:ao},{name:"a_color",size:4,type:oo,normalized:!0},{name:"a_id",size:4,type:oo,normalized:!0}]}}},{key:"processVisibleItem",value:function(n,a,o){var l=this.array;l[a++]=o.x,l[a++]=o.y,l[a++]=o.size,l[a++]=er(o.color),l[a++]=n}},{key:"setUniforms",value:function(n,a){var o=n.sizeRatio,l=n.pixelRatio,i=n.matrix,s=a.gl,c=a.uniformLocations,u=c.u_sizeRatio,d=c.u_pixelRatio,h=c.u_matrix;s.uniform1f(d,l),s.uniform1f(u,o),s.uniformMatrix3fv(h,!1,i)}}])}(ia),Wu=`
attribute vec4 a_id;
attribute vec4 a_color;
attribute vec2 a_normal;
attribute float a_normalCoef;
attribute vec2 a_positionStart;
attribute vec2 a_positionEnd;
attribute float a_positionCoef;
attribute float a_sourceRadius;
attribute float a_targetRadius;
attribute float a_sourceRadiusCoef;
attribute float a_targetRadiusCoef;

uniform mat3 u_matrix;
uniform float u_zoomRatio;
uniform float u_sizeRatio;
uniform float u_pixelRatio;
uniform float u_correctionRatio;
uniform float u_minEdgeThickness;
uniform float u_lengthToThicknessRatio;
uniform float u_feather;

varying vec4 v_color;
varying vec2 v_normal;
varying float v_thickness;
varying float v_feather;

const float bias = 255.0 / 254.0;

void main() {
  float minThickness = u_minEdgeThickness;

  vec2 normal = a_normal * a_normalCoef;
  vec2 position = a_positionStart * (1.0 - a_positionCoef) + a_positionEnd * a_positionCoef;

  float normalLength = length(normal);
  vec2 unitNormal = normal / normalLength;

  // These first computations are taken from edge.vert.glsl. Please read it to
  // get better comments on what's happening:
  float pixelsThickness = max(normalLength, minThickness * u_sizeRatio);
  float webGLThickness = pixelsThickness * u_correctionRatio / u_sizeRatio;

  // Here, we move the point to leave space for the arrow heads:
  // Source arrow head
  float sourceRadius = a_sourceRadius * a_sourceRadiusCoef;
  float sourceDirection = sign(sourceRadius);
  float webGLSourceRadius = sourceDirection * sourceRadius * 2.0 * u_correctionRatio / u_sizeRatio;
  float webGLSourceArrowHeadLength = webGLThickness * u_lengthToThicknessRatio * 2.0;
  vec2 sourceCompensationVector =
    vec2(-sourceDirection * unitNormal.y, sourceDirection * unitNormal.x)
    * (webGLSourceRadius + webGLSourceArrowHeadLength);
    
  // Target arrow head
  float targetRadius = a_targetRadius * a_targetRadiusCoef;
  float targetDirection = sign(targetRadius);
  float webGLTargetRadius = targetDirection * targetRadius * 2.0 * u_correctionRatio / u_sizeRatio;
  float webGLTargetArrowHeadLength = webGLThickness * u_lengthToThicknessRatio * 2.0;
  vec2 targetCompensationVector =
  vec2(-targetDirection * unitNormal.y, targetDirection * unitNormal.x)
    * (webGLTargetRadius + webGLTargetArrowHeadLength);

  // Here is the proper position of the vertex
  gl_Position = vec4((u_matrix * vec3(position + unitNormal * webGLThickness + sourceCompensationVector + targetCompensationVector, 1)).xy, 0, 1);

  v_thickness = webGLThickness / u_zoomRatio;

  v_normal = unitNormal;

  v_feather = u_feather * u_correctionRatio / u_zoomRatio / u_pixelRatio * 2.0;

  #ifdef PICKING_MODE
  // For picking mode, we use the ID as the color:
  v_color = a_id;
  #else
  // For normal mode, we use the color:
  v_color = a_color;
  #endif

  v_color.a *= bias;
}
`,Xu=Wu,es=WebGLRenderingContext,so=es.UNSIGNED_BYTE,qe=es.FLOAT,Yu=["u_matrix","u_zoomRatio","u_sizeRatio","u_correctionRatio","u_pixelRatio","u_feather","u_minEdgeThickness","u_lengthToThicknessRatio"],Ku={lengthToThicknessRatio:wn.lengthToThicknessRatio};function ts(e){var t=qt(qt({},Ku),{});return function(r){function n(){return la(this,n),ca(this,n,arguments)}return aa(n,r),sa(n,[{key:"getDefinition",value:function(){return{VERTICES:6,VERTEX_SHADER_SOURCE:Xu,FRAGMENT_SHADER_SOURCE:mi,METHOD:WebGLRenderingContext.TRIANGLES,UNIFORMS:Yu,ATTRIBUTES:[{name:"a_positionStart",size:2,type:qe},{name:"a_positionEnd",size:2,type:qe},{name:"a_normal",size:2,type:qe},{name:"a_color",size:4,type:so,normalized:!0},{name:"a_id",size:4,type:so,normalized:!0},{name:"a_sourceRadius",size:1,type:qe},{name:"a_targetRadius",size:1,type:qe}],CONSTANT_ATTRIBUTES:[{name:"a_positionCoef",size:1,type:qe},{name:"a_normalCoef",size:1,type:qe},{name:"a_sourceRadiusCoef",size:1,type:qe},{name:"a_targetRadiusCoef",size:1,type:qe}],CONSTANT_DATA:[[0,1,-1,0],[0,-1,1,0],[1,1,0,1],[1,1,0,1],[0,-1,1,0],[1,-1,0,-1]]}}},{key:"processVisibleItem",value:function(o,l,i,s,c){var u=c.size||1,d=i.x,h=i.y,f=s.x,b=s.y,y=er(c.color),C=f-d,N=b-h,E=i.size||1,j=s.size||1,A=C*C+N*N,I=0,P=0;A&&(A=1/Math.sqrt(A),I=-N*A*u,P=C*A*u);var m=this.array;m[l++]=d,m[l++]=h,m[l++]=f,m[l++]=b,m[l++]=I,m[l++]=P,m[l++]=y,m[l++]=o,m[l++]=E,m[l++]=j}},{key:"setUniforms",value:function(o,l){var i=l.gl,s=l.uniformLocations,c=s.u_matrix,u=s.u_zoomRatio,d=s.u_feather,h=s.u_pixelRatio,f=s.u_correctionRatio,b=s.u_sizeRatio,y=s.u_minEdgeThickness,C=s.u_lengthToThicknessRatio;i.uniformMatrix3fv(c,!1,o.matrix),i.uniform1f(u,o.zoomRatio),i.uniform1f(b,o.sizeRatio),i.uniform1f(f,o.correctionRatio),i.uniform1f(h,o.pixelRatio),i.uniform1f(d,o.antiAliasingFeather),i.uniform1f(y,o.minEdgeThickness),i.uniform1f(C,t.lengthToThicknessRatio)}}])}(ua)}ts();function Qu(e){return vi([ts(),qn(e),qn(qt(qt({},e),{},{extremity:"source"}))])}Qu();function Ju(e){if(Array.isArray(e))return e}function Zu(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,a,o,l,i=[],s=!0,c=!1;try{if(o=(r=r.call(e)).next,t!==0)for(;!(s=(n=o.call(r)).done)&&(i.push(n.value),i.length!==t);s=!0);}catch(u){c=!0,a=u}finally{try{if(!s&&r.return!=null&&(l=r.return(),Object(l)!==l))return}finally{if(c)throw a}}return i}}function ln(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function rs(e,t){if(e){if(typeof e=="string")return ln(e,t);var r={}.toString.call(e).slice(8,-1);return r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set"?Array.from(e):r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?ln(e,t):void 0}}function ed(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function td(e,t){return Ju(e)||Zu(e,t)||rs(e,t)||ed()}function rd(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function nd(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function ns(e){var t=nd(e,"string");return typeof t=="symbol"?t:t+""}function od(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,ns(n.key),n)}}function ad(e,t,r){return t&&od(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function Xt(e){return Xt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Xt(e)}function os(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(os=function(){return!!e})()}function cn(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function sd(e,t){if(t&&(typeof t=="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return cn(e)}function id(e,t,r){return t=Xt(t),sd(e,os()?Reflect.construct(t,r||[],Xt(e).constructor):t.apply(e,r))}function un(e,t){return un=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,n){return r.__proto__=n,r},un(e,t)}function ld(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&un(e,t)}function vt(e,t,r){return(t=ns(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function cd(e){if(Array.isArray(e))return ln(e)}function ud(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function dd(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function pr(e){return cd(e)||ud(e)||rs(e)||dd()}function io(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),r.push.apply(r,n)}return r}function lo(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?io(Object(r),!0).forEach(function(n){vt(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):io(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}var fd="relative",hd={drawLabel:void 0,drawHover:void 0,borders:[{size:{value:.1},color:{attribute:"borderColor"}},{size:{fill:!0},color:{attribute:"color"}}]},gd="#000000";function pd(e){var t=e.borders,r=Un(t.filter(function(a){var o=a.size;return"fill"in o}).length),n=`
precision highp float;

varying vec2 v_diffVector;
varying float v_radius;

#ifdef PICKING_MODE
varying vec4 v_color;
#else
// For normal mode, we use the border colors defined in the program:
`.concat(t.flatMap(function(a,o){var l=a.size;return"attribute"in l?["varying float v_borderSize_".concat(o+1,";")]:[]}).join(`
`),`
`).concat(t.flatMap(function(a,o){var l=a.color;return"attribute"in l?["varying vec4 v_borderColor_".concat(o+1,";")]:"value"in l?["uniform vec4 u_borderColor_".concat(o+1,";")]:[]}).join(`
`),`
#endif

uniform float u_correctionRatio;

const float bias = 255.0 / 254.0;
const vec4 transparent = vec4(0.0, 0.0, 0.0, 0.0);

void main(void) {
  float dist = length(v_diffVector);
  float aaBorder = 2.0 * u_correctionRatio;
  float v_borderSize_0 = v_radius;
  vec4 v_borderColor_0 = transparent;

  // No antialiasing for picking mode:
  #ifdef PICKING_MODE
  if (dist > v_radius)
    gl_FragColor = transparent;
  else {
    gl_FragColor = v_color;
    gl_FragColor.a *= bias;
  }
  #else
  // Sizes:
`).concat(t.flatMap(function(a,o){var l=a.size;if("fill"in l)return[];l=l;var i="attribute"in l?"v_borderSize_".concat(o+1):Un(l.value),s=(l.mode||fd)==="pixels"?"u_correctionRatio":"v_radius";return["  float borderSize_".concat(o+1," = ").concat(s," * ").concat(i,";")]}).join(`
`),`
  // Now, let's split the remaining space between "fill" borders:
  float fillBorderSize = (v_radius - (`).concat(t.flatMap(function(a,o){var l=a.size;return"fill"in l?[]:["borderSize_".concat(o+1)]}).join(" + "),") ) / ").concat(r,`;
`).concat(t.flatMap(function(a,o){var l=a.size;return"fill"in l?["  float borderSize_".concat(o+1," = fillBorderSize;")]:[]}).join(`
`),`

  // Finally, normalize all border sizes, to start from the full size and to end with the smallest:
  float adjustedBorderSize_0 = v_radius;
`).concat(t.map(function(a,o){return"  float adjustedBorderSize_".concat(o+1," = adjustedBorderSize_").concat(o," - borderSize_").concat(o+1,";")}).join(`
`),`

  // Colors:
  vec4 borderColor_0 = transparent;
`).concat(t.map(function(a,o){var l=a.color,i=[];return"attribute"in l?i.push("  vec4 borderColor_".concat(o+1," = v_borderColor_").concat(o+1,";")):"transparent"in l?i.push("  vec4 borderColor_".concat(o+1," = vec4(0.0, 0.0, 0.0, 0.0);")):i.push("  vec4 borderColor_".concat(o+1," = u_borderColor_").concat(o+1,";")),i.push("  borderColor_".concat(o+1,".a *= bias;")),i.push("  if (borderSize_".concat(o+1," <= 1.0 * u_correctionRatio) { borderColor_").concat(o+1," = borderColor_").concat(o,"; }")),i.join(`
`)}).join(`
`),`
  if (dist > adjustedBorderSize_0) {
    gl_FragColor = borderColor_0;
  } else `).concat(t.map(function(a,o){return"if (dist > adjustedBorderSize_".concat(o,` - aaBorder) {
    gl_FragColor = mix(borderColor_`).concat(o+1,", borderColor_").concat(o,", (dist - adjustedBorderSize_").concat(o,` + aaBorder) / aaBorder);
  } else if (dist > adjustedBorderSize_`).concat(o+1,`) {
    gl_FragColor = borderColor_`).concat(o+1,`;
  } else `)}).join(""),` { /* Nothing to add here */ }
  #endif
}
`);return n}function md(e){var t=e.borders,r=`
attribute vec2 a_position;
attribute float a_size;
attribute float a_angle;

uniform mat3 u_matrix;
uniform float u_sizeRatio;
uniform float u_correctionRatio;

varying vec2 v_diffVector;
varying float v_radius;

#ifdef PICKING_MODE
attribute vec4 a_id;
varying vec4 v_color;
#else
`.concat(t.flatMap(function(n,a){var o=n.size;return"attribute"in o?["attribute float a_borderSize_".concat(a+1,";"),"varying float v_borderSize_".concat(a+1,";")]:[]}).join(`
`),`
`).concat(t.flatMap(function(n,a){var o=n.color;return"attribute"in o?["attribute vec4 a_borderColor_".concat(a+1,";"),"varying vec4 v_borderColor_".concat(a+1,";")]:[]}).join(`
`),`
#endif

const float bias = 255.0 / 254.0;
const vec4 transparent = vec4(0.0, 0.0, 0.0, 0.0);

void main() {
  float size = a_size * u_correctionRatio / u_sizeRatio * 4.0;
  vec2 diffVector = size * vec2(cos(a_angle), sin(a_angle));
  vec2 position = a_position + diffVector;
  gl_Position = vec4(
    (u_matrix * vec3(position, 1)).xy,
    0,
    1
  );

  v_radius = size / 2.0;
  v_diffVector = diffVector;

  #ifdef PICKING_MODE
  v_color = a_id;
  #else
`).concat(t.flatMap(function(n,a){var o=n.size;return"attribute"in o?["  v_borderSize_".concat(a+1," = a_borderSize_").concat(a+1,";")]:[]}).join(`
`),`
`).concat(t.flatMap(function(n,a){var o=n.color;return"attribute"in o?["  v_borderColor_".concat(a+1," = a_borderColor_").concat(a+1,";")]:[]}).join(`
`),`
  #endif
}
`);return r}var as=WebGLRenderingContext,co=as.UNSIGNED_BYTE,Lt=as.FLOAT;function vd(e){var t,r=lo(lo({},hd),{}),n=r.borders,a=r.drawLabel,o=r.drawHover,l=["u_sizeRatio","u_correctionRatio","u_matrix"].concat(pr(n.flatMap(function(i,s){var c=i.color;return"value"in c?["u_borderColor_".concat(s+1)]:[]})));return t=function(i){ld(s,i);function s(){var c;rd(this,s);for(var u=arguments.length,d=new Array(u),h=0;h<u;h++)d[h]=arguments[h];return c=id(this,s,[].concat(d)),vt(cn(c),"drawLabel",a),vt(cn(c),"drawHover",o),c}return ad(s,[{key:"getDefinition",value:function(){return{VERTICES:3,VERTEX_SHADER_SOURCE:md(r),FRAGMENT_SHADER_SOURCE:pd(r),METHOD:WebGLRenderingContext.TRIANGLES,UNIFORMS:l,ATTRIBUTES:[{name:"a_position",size:2,type:Lt},{name:"a_id",size:4,type:co,normalized:!0},{name:"a_size",size:1,type:Lt}].concat(pr(n.flatMap(function(u,d){var h=u.color;return"attribute"in h?[{name:"a_borderColor_".concat(d+1),size:4,type:co,normalized:!0}]:[]})),pr(n.flatMap(function(u,d){var h=u.size;return"attribute"in h?[{name:"a_borderSize_".concat(d+1),size:1,type:Lt}]:[]}))),CONSTANT_ATTRIBUTES:[{name:"a_angle",size:1,type:Lt}],CONSTANT_DATA:[[s.ANGLE_1],[s.ANGLE_2],[s.ANGLE_3]]}}},{key:"processVisibleItem",value:function(u,d,h){var f=this.array;f[d++]=h.x,f[d++]=h.y,f[d++]=u,f[d++]=h.size,n.forEach(function(b){var y=b.color;"attribute"in y&&(f[d++]=er(h[y.attribute]||y.defaultValue||gd))}),n.forEach(function(b){var y=b.size;"attribute"in y&&(f[d++]=h[y.attribute]||y.defaultValue)})}},{key:"setUniforms",value:function(u,d){var h=d.gl,f=d.uniformLocations,b=f.u_sizeRatio,y=f.u_correctionRatio,C=f.u_matrix;h.uniform1f(y,u.correctionRatio),h.uniform1f(b,u.sizeRatio),h.uniformMatrix3fv(C,!1,u.matrix),n.forEach(function(N,E){var j=N.color;if("value"in j){var A=f["u_borderColor_".concat(E+1)],I=yi(j.value),P=td(I,4),m=P[0],S=P[1],x=P[2],T=P[3];h.uniform4f(A,m/255,S/255,x/255,T/255)}})}}]),s}(ia),vt(t,"ANGLE_1",0),vt(t,"ANGLE_2",2*Math.PI/3),vt(t,"ANGLE_3",4*Math.PI/3),t}var yd=vd();function bd(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function ss(e){var t=bd(e,"string");return typeof t=="symbol"?t:t+""}function is(e,t,r){return(t=ss(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function uo(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),r.push.apply(r,n)}return r}function Yt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?uo(Object(r),!0).forEach(function(n){is(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):uo(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function wd(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function xd(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,ss(n.key),n)}}function _d(e,t,r){return t&&xd(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function Kt(e){return Kt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Kt(e)}function ls(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(ls=function(){return!!e})()}function cs(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Sd(e,t){if(t&&(typeof t=="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return cs(e)}function Ed(e,t,r){return t=Kt(t),Sd(e,ls()?Reflect.construct(t,r||[],Kt(e).constructor):t.apply(e,r))}function dn(e,t){return dn=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,n){return r.__proto__=n,r},dn(e,t)}function kd(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&dn(e,t)}function fn(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function Cd(e){if(Array.isArray(e))return fn(e)}function Td(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function Rd(e,t){if(e){if(typeof e=="string")return fn(e,t);var r={}.toString.call(e).slice(8,-1);return r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set"?Array.from(e):r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?fn(e,t):void 0}}function Ad(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function mr(e){return Cd(e)||Td(e)||Rd(e)||Ad()}function us(e,t,r,n){var a=Math.pow(1-e,2)*t.x+2*(1-e)*e*r.x+Math.pow(e,2)*n.x,o=Math.pow(1-e,2)*t.y+2*(1-e)*e*r.y+Math.pow(e,2)*n.y;return{x:a,y:o}}function jd(e,t,r){for(var n=20,a=0,o=e,l=0;l<n;l++){var i=us((l+1)/n,e,t,r);a+=Math.sqrt(Math.pow(o.x-i.x,2)+Math.pow(o.y-i.y,2)),o=i}return a}function Id(e){var t=e.curvatureAttribute,r=e.defaultCurvature,n=e.keepLabelUpright,a=n===void 0?!0:n;return function(o,l,i,s,c){var u=c.edgeLabelSize,d=l[t]||r,h=c.edgeLabelFont,f=c.edgeLabelWeight,b=c.edgeLabelColor.attribute?l[c.edgeLabelColor.attribute]||c.edgeLabelColor.color||"#000":c.edgeLabelColor.color,y=l.label;if(y){o.fillStyle=b,o.font="".concat(f," ").concat(u,"px ").concat(h);var C=!a||i.x<s.x,N=C?i.x:s.x,E=C?i.y:s.y,j=C?s.x:i.x,A=C?s.y:i.y,I=(N+j)/2,P=(E+A)/2,m=j-N,S=A-E,x=Math.sqrt(Math.pow(m,2)+Math.pow(S,2)),T=C?1:-1,R=I+S*d*T,O=P-m*d*T,w=l.size*.7+5,H={x:O-E,y:-(R-N)},K=Math.sqrt(Math.pow(H.x,2)+Math.pow(H.y,2)),D={x:A-O,y:-(j-R)},k=Math.sqrt(Math.pow(D.x,2)+Math.pow(D.y,2));N+=w*H.x/K,E+=w*H.y/K,j+=w*D.x/k,A+=w*D.y/k,R+=w*S/x,O-=w*m/x;var _={x:R,y:O},B={x:N,y:E},se={x:j,y:A},F=jd(B,_,se);if(!(F<i.size+s.size)){var v=o.measureText(y).width,z=F-i.size-s.size;if(v>z){var V="…";for(y=y+V,v=o.measureText(y).width;v>z&&y.length>1;)y=y.slice(0,-2)+V,v=o.measureText(y).width;if(y.length<4)return}for(var $={},Q=0,W=y.length;Q<W;Q++){var Y=y[Q];$[Y]||($[Y]=o.measureText(Y).width*(1+d*.35))}for(var ie=.5-v/F/2,ne=0,ae=y.length;ne<ae;ne++){var M=y[ne],J=us(ie,B,_,se),U=2*(1-ie)*(R-N)+2*ie*(j-R),q=2*(1-ie)*(O-E)+2*ie*(A-O),L=Math.atan2(q,U);o.save(),o.translate(J.x,J.y),o.rotate(L),o.fillText(M,0,0),o.restore(),ie+=$[M]/F}}}}}function Nd(e){var t=e.arrowHead,r=(t==null?void 0:t.extremity)==="target"||(t==null?void 0:t.extremity)==="both",n=(t==null?void 0:t.extremity)==="source"||(t==null?void 0:t.extremity)==="both",a=`
precision highp float;

varying vec4 v_color;
varying float v_thickness;
varying float v_feather;
varying vec2 v_cpA;
varying vec2 v_cpB;
varying vec2 v_cpC;
`.concat(r?`
varying float v_targetSize;
varying vec2 v_targetPoint;`:"",`
`).concat(n?`
varying float v_sourceSize;
varying vec2 v_sourcePoint;`:"",`
`).concat(t?`
uniform float u_lengthToThicknessRatio;
uniform float u_widenessToThicknessRatio;`:"",`

float det(vec2 a, vec2 b) {
  return a.x * b.y - b.x * a.y;
}

vec2 getDistanceVector(vec2 b0, vec2 b1, vec2 b2) {
  float a = det(b0, b2), b = 2.0 * det(b1, b0), d = 2.0 * det(b2, b1);
  float f = b * d - a * a;
  vec2 d21 = b2 - b1, d10 = b1 - b0, d20 = b2 - b0;
  vec2 gf = 2.0 * (b * d21 + d * d10 + a * d20);
  gf = vec2(gf.y, -gf.x);
  vec2 pp = -f * gf / dot(gf, gf);
  vec2 d0p = b0 - pp;
  float ap = det(d0p, d20), bp = 2.0 * det(d10, d0p);
  float t = clamp((ap + bp) / (2.0 * a + b + d), 0.0, 1.0);
  return mix(mix(b0, b1, t), mix(b1, b2, t), t);
}

float distToQuadraticBezierCurve(vec2 p, vec2 b0, vec2 b1, vec2 b2) {
  return length(getDistanceVector(b0 - p, b1 - p, b2 - p));
}

const vec4 transparent = vec4(0.0, 0.0, 0.0, 0.0);

void main(void) {
  float dist = distToQuadraticBezierCurve(gl_FragCoord.xy, v_cpA, v_cpB, v_cpC);
  float thickness = v_thickness;
`).concat(r?`
  float distToTarget = length(gl_FragCoord.xy - v_targetPoint);
  float targetArrowLength = v_targetSize + thickness * u_lengthToThicknessRatio;
  if (distToTarget < targetArrowLength) {
    thickness = (distToTarget - v_targetSize) / (targetArrowLength - v_targetSize) * u_widenessToThicknessRatio * thickness;
  }`:"",`
`).concat(n?`
  float distToSource = length(gl_FragCoord.xy - v_sourcePoint);
  float sourceArrowLength = v_sourceSize + thickness * u_lengthToThicknessRatio;
  if (distToSource < sourceArrowLength) {
    thickness = (distToSource - v_sourceSize) / (sourceArrowLength - v_sourceSize) * u_widenessToThicknessRatio * thickness;
  }`:"",`

  float halfThickness = thickness / 2.0;
  if (dist < halfThickness) {
    #ifdef PICKING_MODE
    gl_FragColor = v_color;
    #else
    float t = smoothstep(
      halfThickness - v_feather,
      halfThickness,
      dist
    );

    gl_FragColor = mix(v_color, transparent, t);
    #endif
  } else {
    gl_FragColor = transparent;
  }
}
`);return a}function Ld(e){var t=e.arrowHead,r=(t==null?void 0:t.extremity)==="target"||(t==null?void 0:t.extremity)==="both",n=(t==null?void 0:t.extremity)==="source"||(t==null?void 0:t.extremity)==="both",a=`
attribute vec4 a_id;
attribute vec4 a_color;
attribute float a_direction;
attribute float a_thickness;
attribute vec2 a_source;
attribute vec2 a_target;
attribute float a_current;
attribute float a_curvature;
`.concat(r?`attribute float a_targetSize;
`:"",`
`).concat(n?`attribute float a_sourceSize;
`:"",`

uniform mat3 u_matrix;
uniform float u_sizeRatio;
uniform float u_pixelRatio;
uniform vec2 u_dimensions;
uniform float u_minEdgeThickness;
uniform float u_feather;

varying vec4 v_color;
varying float v_thickness;
varying float v_feather;
varying vec2 v_cpA;
varying vec2 v_cpB;
varying vec2 v_cpC;
`).concat(r?`
varying float v_targetSize;
varying vec2 v_targetPoint;`:"",`
`).concat(n?`
varying float v_sourceSize;
varying vec2 v_sourcePoint;`:"",`
`).concat(t?`
uniform float u_widenessToThicknessRatio;`:"",`

const float bias = 255.0 / 254.0;
const float epsilon = 0.7;

vec2 clipspaceToViewport(vec2 pos, vec2 dimensions) {
  return vec2(
    (pos.x + 1.0) * dimensions.x / 2.0,
    (pos.y + 1.0) * dimensions.y / 2.0
  );
}

vec2 viewportToClipspace(vec2 pos, vec2 dimensions) {
  return vec2(
    pos.x / dimensions.x * 2.0 - 1.0,
    pos.y / dimensions.y * 2.0 - 1.0
  );
}

void main() {
  float minThickness = u_minEdgeThickness;

  // Selecting the correct position
  // Branchless "position = a_source if a_current == 1.0 else a_target"
  vec2 position = a_source * max(0.0, a_current) + a_target * max(0.0, 1.0 - a_current);
  position = (u_matrix * vec3(position, 1)).xy;

  vec2 source = (u_matrix * vec3(a_source, 1)).xy;
  vec2 target = (u_matrix * vec3(a_target, 1)).xy;

  vec2 viewportPosition = clipspaceToViewport(position, u_dimensions);
  vec2 viewportSource = clipspaceToViewport(source, u_dimensions);
  vec2 viewportTarget = clipspaceToViewport(target, u_dimensions);

  vec2 delta = viewportTarget.xy - viewportSource.xy;
  float len = length(delta);
  vec2 normal = vec2(-delta.y, delta.x) * a_direction;
  vec2 unitNormal = normal / len;
  float boundingBoxThickness = len * a_curvature;

  float curveThickness = max(minThickness, a_thickness / u_sizeRatio);
  v_thickness = curveThickness * u_pixelRatio;
  v_feather = u_feather;

  v_cpA = viewportSource;
  v_cpB = 0.5 * (viewportSource + viewportTarget) + unitNormal * a_direction * boundingBoxThickness;
  v_cpC = viewportTarget;

  vec2 viewportOffsetPosition = (
    viewportPosition +
    unitNormal * (boundingBoxThickness / 2.0 + sign(boundingBoxThickness) * (`).concat(t?"curveThickness * u_widenessToThicknessRatio":"curveThickness",` + epsilon)) *
    max(0.0, a_direction) // NOTE: cutting the bounding box in half to avoid overdraw
  );

  position = viewportToClipspace(viewportOffsetPosition, u_dimensions);
  gl_Position = vec4(position, 0, 1);
    
`).concat(r?`
  v_targetSize = a_targetSize * u_pixelRatio / u_sizeRatio;
  v_targetPoint = viewportTarget;
`:"",`
`).concat(n?`
  v_sourceSize = a_sourceSize * u_pixelRatio / u_sizeRatio;
  v_sourcePoint = viewportSource;
`:"",`

  #ifdef PICKING_MODE
  // For picking mode, we use the ID as the color:
  v_color = a_id;
  #else
  // For normal mode, we use the color:
  v_color = a_color;
  #endif

  v_color.a *= bias;
}
`);return a}var ds=.25,zd={arrowHead:null,curvatureAttribute:"curvature",defaultCurvature:ds},fs=WebGLRenderingContext,fo=fs.UNSIGNED_BYTE,et=fs.FLOAT;function lr(e){var t=Yt(Yt({},zd),e||{}),r=t,n=r.arrowHead,a=r.curvatureAttribute,o=r.drawLabel,l=(n==null?void 0:n.extremity)==="target"||(n==null?void 0:n.extremity)==="both",i=(n==null?void 0:n.extremity)==="source"||(n==null?void 0:n.extremity)==="both",s=["u_matrix","u_sizeRatio","u_dimensions","u_pixelRatio","u_feather","u_minEdgeThickness"].concat(mr(n?["u_lengthToThicknessRatio","u_widenessToThicknessRatio"]:[]));return function(c){kd(u,c);function u(){var d;wd(this,u);for(var h=arguments.length,f=new Array(h),b=0;b<h;b++)f[b]=arguments[b];return d=Ed(this,u,[].concat(f)),is(cs(d),"drawLabel",o||Id(t)),d}return _d(u,[{key:"getDefinition",value:function(){return{VERTICES:6,VERTEX_SHADER_SOURCE:Ld(t),FRAGMENT_SHADER_SOURCE:Nd(t),METHOD:WebGLRenderingContext.TRIANGLES,UNIFORMS:s,ATTRIBUTES:[{name:"a_source",size:2,type:et},{name:"a_target",size:2,type:et}].concat(mr(l?[{name:"a_targetSize",size:1,type:et}]:[]),mr(i?[{name:"a_sourceSize",size:1,type:et}]:[]),[{name:"a_thickness",size:1,type:et},{name:"a_curvature",size:1,type:et},{name:"a_color",size:4,type:fo,normalized:!0},{name:"a_id",size:4,type:fo,normalized:!0}]),CONSTANT_ATTRIBUTES:[{name:"a_current",size:1,type:et},{name:"a_direction",size:1,type:et}],CONSTANT_DATA:[[0,1],[0,-1],[1,1],[0,-1],[1,1],[1,-1]]}}},{key:"processVisibleItem",value:function(h,f,b,y,C){var N,E=C.size||1,j=b.x,A=b.y,I=y.x,P=y.y,m=er(C.color),S=(N=C[a])!==null&&N!==void 0?N:ds,x=this.array;x[f++]=j,x[f++]=A,x[f++]=I,x[f++]=P,l&&(x[f++]=y.size),i&&(x[f++]=b.size),x[f++]=E,x[f++]=S,x[f++]=m,x[f++]=h}},{key:"setUniforms",value:function(h,f){var b=f.gl,y=f.uniformLocations,C=y.u_matrix,N=y.u_pixelRatio,E=y.u_feather,j=y.u_sizeRatio,A=y.u_dimensions,I=y.u_minEdgeThickness;if(b.uniformMatrix3fv(C,!1,h.matrix),b.uniform1f(N,h.pixelRatio),b.uniform1f(j,h.sizeRatio),b.uniform1f(E,h.antiAliasingFeather),b.uniform2f(A,h.width*h.pixelRatio,h.height*h.pixelRatio),b.uniform1f(I,h.minEdgeThickness),n){var P=y.u_lengthToThicknessRatio,m=y.u_widenessToThicknessRatio;b.uniform1f(P,n.lengthToThicknessRatio),b.uniform1f(m,n.widenessToThicknessRatio)}}}]),u}(ua)}lr();var Pd=lr({arrowHead:wn});lr({arrowHead:Yt(Yt({},wn),{},{extremity:"both"})});const Dd=({node:e,move:t})=>{const r=Be(),{gotoNode:n}=da();return p.useEffect(()=>{const a=r.getGraph();if(t){if(e&&a.hasNode(e))try{a.setNodeAttribute(e,"highlighted",!0),n(e)}catch(o){console.error("Error focusing on node:",o)}else r.setCustomBBox(null),r.getCamera().animate({x:.5,y:.5,ratio:1},{duration:0});te.getState().setMoveToSelectedNode(!1)}else if(e&&a.hasNode(e))try{a.setNodeAttribute(e,"highlighted",!0)}catch(o){console.error("Error highlighting node:",o)}return()=>{if(e&&a.hasNode(e))try{a.setNodeAttribute(e,"highlighted",!1)}catch(o){console.error("Error cleaning up node highlight:",o)}}},[e,t,r,n]),null};function St(e,t){const r=Be(),n=p.useRef(t);return fa(n.current,t)||(n.current=t),{positions:p.useCallback(()=>n.current?e(r.getGraph(),n.current):{},[r,n,e]),assign:p.useCallback(()=>{n.current&&e.assign(r.getGraph(),n.current)},[r,n,e])}}function jn(e,t){const r=Be(),[n,a]=p.useState(!1),[o,l]=p.useState(null),i=p.useRef(t);return fa(i.current,t)||(i.current=t),p.useEffect(()=>{a(!1);let s=null;return i.current&&(s=new e(r.getGraph(),i.current)),l(s),()=>{s!==null&&s.kill()}},[r,i,l,a,e]),{stop:p.useCallback(()=>{o&&(o.stop(),a(!1))},[o,a]),start:p.useCallback(()=>{o&&(o.start(),a(!0))},[o,a]),kill:p.useCallback(()=>{o&&o.kill(),a(!1)},[o,a]),isRunning:n}}var vr,ho;function At(){if(ho)return vr;ho=1;function e(r){return!r||typeof r!="object"||typeof r=="function"||Array.isArray(r)||r instanceof Set||r instanceof Map||r instanceof RegExp||r instanceof Date}function t(r,n){r=r||{};var a={};for(var o in n){var l=r[o],i=n[o];if(!e(i)){a[o]=t(l,i);continue}l===void 0?a[o]=i:a[o]=l}return a}return vr=t,vr}var yr,go;function Od(){if(go)return yr;go=1;function e(r){return function(n,a){return n+Math.floor(r()*(a-n+1))}}var t=e(Math.random);return t.createRandom=e,yr=t,yr}var br,po;function Gd(){if(po)return br;po=1;var e=Od().createRandom;function t(n){var a=e(n);return function(o){for(var l=o.length,i=l-1,s=-1;++s<l;){var c=a(s,i),u=o[c];o[c]=o[s],o[s]=u}}}var r=t(Math.random);return r.createShuffleInPlace=t,br=r,br}var wr,mo;function Md(){if(mo)return wr;mo=1;var e=At(),t=We(),r=Gd(),n={attributes:{x:"x",y:"y"},center:0,hierarchyAttributes:[],rng:Math.random,scale:1};function a(m,S,x,T,R){this.wrappedCircle=R||null,this.children={},this.countChildren=0,this.id=m||null,this.next=null,this.previous=null,this.x=S||null,this.y=x||null,R?this.r=1010101:this.r=T||999}a.prototype.hasChildren=function(){return this.countChildren>0},a.prototype.addChild=function(m,S){this.children[m]=S,++this.countChildren},a.prototype.getChild=function(m){if(!this.children.hasOwnProperty(m)){var S=new a;this.children[m]=S,++this.countChildren}return this.children[m]},a.prototype.applyPositionToChildren=function(){if(this.hasChildren()){var m=this;for(var S in m.children){var x=m.children[S];x.x+=m.x,x.y+=m.y,x.applyPositionToChildren()}}};function o(m,S,x){for(var T in S.children){var R=S.children[T];R.hasChildren()?o(m,R,x):x[R.id]={x:R.x,y:R.y}}}function l(m,S){var x=m.r-S.r,T=S.x-m.x,R=S.y-m.y;return x<0||x*x<T*T+R*R}function i(m,S){var x=m.r-S.r+1e-6,T=S.x-m.x,R=S.y-m.y;return x>0&&x*x>T*T+R*R}function s(m,S){for(var x=0;x<S.length;++x)if(!i(m,S[x]))return!1;return!0}function c(m){return new a(null,m.x,m.y,m.r)}function u(m,S){var x=m.x,T=m.y,R=m.r,O=S.x,w=S.y,H=S.r,K=O-x,D=w-T,k=H-R,_=Math.sqrt(K*K+D*D);return new a(null,(x+O+K/_*k)/2,(T+w+D/_*k)/2,(_+R+H)/2)}function d(m,S,x){var T=m.x,R=m.y,O=m.r,w=S.x,H=S.y,K=S.r,D=x.x,k=x.y,_=x.r,B=T-w,se=T-D,F=R-H,v=R-k,z=K-O,V=_-O,$=T*T+R*R-O*O,Q=$-w*w-H*H+K*K,W=$-D*D-k*k+_*_,Y=se*F-B*v,ie=(F*W-v*Q)/(Y*2)-T,ne=(v*z-F*V)/Y,ae=(se*Q-B*W)/(Y*2)-R,M=(B*V-se*z)/Y,J=ne*ne+M*M-1,U=2*(O+ie*ne+ae*M),q=ie*ie+ae*ae-O*O,L=-(J?(U+Math.sqrt(U*U-4*J*q))/(2*J):q/U);return new a(null,T+ie+ne*L,R+ae+M*L,L)}function h(m){switch(m.length){case 1:return c(m[0]);case 2:return u(m[0],m[1]);case 3:return d(m[0],m[1],m[2]);default:throw new Error("graphology-layout/circlepack: Invalid basis length "+m.length)}}function f(m,S){var x,T;if(s(S,m))return[S];for(x=0;x<m.length;++x)if(l(S,m[x])&&s(u(m[x],S),m))return[m[x],S];for(x=0;x<m.length-1;++x)for(T=x+1;T<m.length;++T)if(l(u(m[x],m[T]),S)&&l(u(m[x],S),m[T])&&l(u(m[T],S),m[x])&&s(d(m[x],m[T],S),m))return[m[x],m[T],S];throw new Error("graphology-layout/circlepack: extendBasis failure !")}function b(m){var S=m.wrappedCircle,x=m.next.wrappedCircle,T=S.r+x.r,R=(S.x*x.r+x.x*S.r)/T,O=(S.y*x.r+x.y*S.r)/T;return R*R+O*O}function y(m,S){var x=0,T=m.slice(),R=m.length,O=[],w,H;for(S(T);x<R;)w=T[x],H&&i(H,w)?++x:(O=f(O,w),H=h(O),x=0);return H}function C(m,S,x){var T=m.x-S.x,R,O,w=m.y-S.y,H,K,D=T*T+w*w;D?(O=S.r+x.r,O*=O,K=m.r+x.r,K*=K,O>K?(R=(D+K-O)/(2*D),H=Math.sqrt(Math.max(0,K/D-R*R)),x.x=m.x-R*T-H*w,x.y=m.y-R*w+H*T):(R=(D+O-K)/(2*D),H=Math.sqrt(Math.max(0,O/D-R*R)),x.x=S.x+R*T-H*w,x.y=S.y+R*w+H*T)):(x.x=S.x+x.r,x.y=S.y)}function N(m,S){var x=m.r+S.r-1e-6,T=S.x-m.x,R=S.y-m.y;return x>0&&x*x>T*T+R*R}function E(m,S){var x=m.length;if(x===0)return 0;var T,R,O,w,H,K,D,k,_,B;if(T=m[0],T.x=0,T.y=0,x<=1)return T.r;if(R=m[1],T.x=-R.r,R.x=T.r,R.y=0,x<=2)return T.r+R.r;O=m[2],C(R,T,O),T=new a(null,null,null,null,T),R=new a(null,null,null,null,R),O=new a(null,null,null,null,O),T.next=O.previous=R,R.next=T.previous=O,O.next=R.previous=T;e:for(K=3;K<x;++K){O=m[K],C(T.wrappedCircle,R.wrappedCircle,O),O=new a(null,null,null,null,O),D=R.next,k=T.previous,_=R.wrappedCircle.r,B=T.wrappedCircle.r;do if(_<=B){if(N(D.wrappedCircle,O.wrappedCircle)){R=D,T.next=R,R.previous=T,--K;continue e}_+=D.wrappedCircle.r,D=D.next}else{if(N(k.wrappedCircle,O.wrappedCircle)){T=k,T.next=R,R.previous=T,--K;continue e}B+=k.wrappedCircle.r,k=k.previous}while(D!==k.next);for(O.previous=T,O.next=R,T.next=R.previous=R=O,w=b(T);(O=O.next)!==R;)(H=b(O))<w&&(T=O,w=H);R=T.next}T=[R.wrappedCircle],O=R;for(var se=1e4;(O=O.next)!==R&&--se!==0;)T.push(O.wrappedCircle);for(O=y(T,S),K=0;K<x;++K)T=m[K],T.x-=O.x,T.y-=O.y;return O.r}function j(m,S){var x=0;if(m.hasChildren()){for(var T in m.children){var R=m.children[T];R.hasChildren()&&(R.r=j(R,S))}x=E(Object.values(m.children),S)}return x}function A(m,S){j(m,S);for(var x in m.children){var T=m.children[x];T.applyPositionToChildren()}}function I(m,S,x){if(!t(S))throw new Error("graphology-layout/circlepack: the given graph is not a valid graphology instance.");x=e(x,n);var T={},R={},O=S.nodes(),w=x.center,H=x.hierarchyAttributes,K=r.createShuffleInPlace(x.rng),D=x.scale,k=new a;S.forEachNode(function(z,V){var $=V.size?V.size:1,Q=new a(z,null,null,$),W=k;H.forEach(function(Y){var ie=V[Y];W=W.getChild(ie)}),W.addChild(z,Q)}),A(k,K),o(S,k,T);var _=O.length,B,se,F;for(F=0;F<_;F++){var v=O[F];B=w+D*T[v].x,se=w+D*T[v].y,R[v]={x:B,y:se},m&&(S.setNodeAttribute(v,x.attributes.x,B),S.setNodeAttribute(v,x.attributes.y,se))}return R}var P=I.bind(null,!1);return P.assign=I.bind(null,!0),wr=P,wr}var Fd=Md();const $d=He(Fd);function Hd(e={}){return St($d,e)}var xr,vo;function Bd(){if(vo)return xr;vo=1;var e=At(),t=We(),r={dimensions:["x","y"],center:.5,scale:1};function n(o,l,i){if(!t(l))throw new Error("graphology-layout/random: the given graph is not a valid graphology instance.");i=e(i,r);var s=i.dimensions;if(!Array.isArray(s)||s.length!==2)throw new Error("graphology-layout/random: given dimensions are invalid.");var c=i.center,u=i.scale,d=Math.PI*2,h=(c-.5)*u,f=l.order,b=s[0],y=s[1];function C(j,A){return A[b]=u*Math.cos(j*d/f)+h,A[y]=u*Math.sin(j*d/f)+h,A}var N=0;if(!o){var E={};return l.forEachNode(function(j){E[j]=C(N++,{})}),E}l.updateEachNodeAttributes(function(j,A){return C(N++,A),A},{attributes:s})}var a=n.bind(null,!1);return a.assign=n.bind(null,!0),xr=a,xr}var Vd=Bd();const qd=He(Vd);function Ud(e={}){return St(qd,e)}var Ct={},yo;function In(){if(yo)return Ct;yo=1;function e(n){return typeof n!="number"||isNaN(n)?1:n}function t(n,a){var o={},l=function(c){return typeof c>"u"?a:c};typeof a=="function"&&(l=a);var i=function(c){return l(c[n])},s=function(){return l(void 0)};return typeof n=="string"?(o.fromAttributes=i,o.fromGraph=function(c,u){return i(c.getNodeAttributes(u))},o.fromEntry=function(c,u){return i(u)}):typeof n=="function"?(o.fromAttributes=function(){throw new Error("graphology-utils/getters/createNodeValueGetter: irrelevant usage.")},o.fromGraph=function(c,u){return l(n(u,c.getNodeAttributes(u)))},o.fromEntry=function(c,u){return l(n(c,u))}):(o.fromAttributes=s,o.fromGraph=s,o.fromEntry=s),o}function r(n,a){var o={},l=function(c){return typeof c>"u"?a:c};typeof a=="function"&&(l=a);var i=function(c){return l(c[n])},s=function(){return l(void 0)};return typeof n=="string"?(o.fromAttributes=i,o.fromGraph=function(c,u){return i(c.getEdgeAttributes(u))},o.fromEntry=function(c,u){return i(u)},o.fromPartialEntry=o.fromEntry,o.fromMinimalEntry=o.fromEntry):typeof n=="function"?(o.fromAttributes=function(){throw new Error("graphology-utils/getters/createEdgeValueGetter: irrelevant usage.")},o.fromGraph=function(c,u){var d=c.extremities(u);return l(n(u,c.getEdgeAttributes(u),d[0],d[1],c.getNodeAttributes(d[0]),c.getNodeAttributes(d[1]),c.isUndirected(u)))},o.fromEntry=function(c,u,d,h,f,b,y){return l(n(c,u,d,h,f,b,y))},o.fromPartialEntry=function(c,u,d,h){return l(n(c,u,d,h))},o.fromMinimalEntry=function(c,u){return l(n(c,u))}):(o.fromAttributes=s,o.fromGraph=s,o.fromEntry=s,o.fromMinimalEntry=s),o}return Ct.createNodeValueGetter=t,Ct.createEdgeValueGetter=r,Ct.createEdgeWeightGetter=function(n){return r(n,e)},Ct}var _r,bo;function hs(){if(bo)return _r;bo=1;const{createNodeValueGetter:e,createEdgeValueGetter:t}=In();return _r=function(n,a,o){const{nodeXAttribute:l,nodeYAttribute:i}=o,{attraction:s,repulsion:c,gravity:u,inertia:d,maxMove:h}=o.settings;let{shouldSkipNode:f,shouldSkipEdge:b,isNodeFixed:y}=o;y=e(y),f=e(f,!1),b=t(b,!1);const C=n.filterNodes((j,A)=>!f.fromEntry(j,A)),N=C.length;for(let j=0;j<N;j++){const A=C[j],I=n.getNodeAttributes(A),P=a[A];P?a[A]={dx:P.dx*d,dy:P.dy*d,x:I[l]||0,y:I[i]||0}:a[A]={dx:0,dy:0,x:I[l]||0,y:I[i]||0}}if(c)for(let j=0;j<N;j++){const A=C[j],I=a[A];for(let P=j+1;P<N;P++){const m=C[P],S=a[m],x=S.x-I.x,T=S.y-I.y,R=Math.sqrt(x*x+T*T)||1,O=c/R*x,w=c/R*T;I.dx-=O,I.dy-=w,S.dx+=O,S.dy+=w}}if(s&&n.forEachEdge((j,A,I,P,m,S,x)=>{if(I===P||f.fromEntry(I,m)||f.fromEntry(P,S)||b.fromEntry(j,A,I,P,m,S,x))return;const T=a[I],R=a[P],O=R.x-T.x,w=R.y-T.y,H=Math.sqrt(O*O+w*w)||1,K=s*H*O,D=s*H*w;T.dx+=K,T.dy+=D,R.dx-=K,R.dy-=D}),u)for(let j=0;j<N;j++){const A=C[j],I=a[A],{x:P,y:m}=I,S=Math.sqrt(P*P+m*m)||1;a[A].dx-=P*u*S,a[A].dy-=m*u*S}const E=!1;for(let j=0;j<N;j++){const A=C[j],I=a[A],P=Math.sqrt(I.dx*I.dx+I.dy*I.dy);P>h&&(I.dx*=h/P,I.dy*=h/P),y.fromGraph(n,A)?I.fixed=!0:(I.x+=I.dx,I.y+=I.dy,I.fixed=!1)}return{converged:E}},_r}var zt={},wo;function gs(){return wo||(wo=1,zt.assignLayoutChanges=function(e,t,r){const{nodeXAttribute:n,nodeYAttribute:a}=r;e.updateEachNodeAttributes((o,l)=>{const i=t[o];return!i||i.fixed||(l[n]=i.x,l[a]=i.y),l},{attributes:["x","y"]})},zt.collectLayoutChanges=function(e){const t={};for(const r in e){const n=e[r];t[r]={x:n.x,y:n.y}}return t}),zt}var Sr,xo;function ps(){return xo||(xo=1,Sr={nodeXAttribute:"x",nodeYAttribute:"y",isNodeFixed:"fixed",shouldSkipNode:null,shouldSkipEdge:null,settings:{attraction:5e-4,repulsion:.1,gravity:1e-4,inertia:.6,maxMove:200}}),Sr}var Er,_o;function Wd(){if(_o)return Er;_o=1;const e=We(),t=At(),r=hs(),n=gs(),a=ps();function o(i,s,c){if(!e(s))throw new Error("graphology-layout-force: the given graph is not a valid graphology instance.");typeof c=="number"?c={maxIterations:c}:c=c||{};const u=c.maxIterations;if(c=t(c,a),typeof u!="number"||u<=0)throw new Error("graphology-layout-force: you should provide a positive number of maximum iterations.");const d={};let h=null,f;for(f=0;f<u&&(h=r(s,d,c),!h.converged);f++);if(i){n.assignLayoutChanges(s,d,c);return}return n.collectLayoutChanges(d)}const l=o.bind(null,!1);return l.assign=o.bind(null,!0),Er=l,Er}var Xd=Wd();const Yd=He(Xd);var kr,So;function Kd(){if(So)return kr;So=1;const e=We(),t=At(),r=hs(),n=gs(),a=ps();function o(l,i){if(!e(l))throw new Error("graphology-layout-force/worker: the given graph is not a valid graphology instance.");i=t(i,a),this.callbacks={},i.onConverged&&(this.callbacks.onConverged=i.onConverged),this.graph=l,this.params=i,this.nodeStates={},this.frameID=null,this.running=!1,this.killed=!1}return o.prototype.isRunning=function(){return this.running},o.prototype.runFrame=function(){let{converged:l}=r(this.graph,this.nodeStates,this.params);n.assignLayoutChanges(this.graph,this.nodeStates,this.params),l=!1,l?(this.callbacks.onConverged&&this.callbacks.onConverged(),this.stop()):this.frameID=window.requestAnimationFrame(()=>this.runFrame())},o.prototype.stop=function(){return this.running=!1,this.frameID!==null&&(window.cancelAnimationFrame(this.frameID),this.frameID=null),this},o.prototype.start=function(){if(this.killed)throw new Error("graphology-layout-force/worker.start: layout was killed.");this.running||(this.running=!0,this.runFrame())},o.prototype.kill=function(){this.stop(),delete this.nodeStates,this.killed=!0},kr=o,kr}var Qd=Kd();const Jd=He(Qd);function Zd(e={maxIterations:100}){return St(Yd,e)}function ef(e={}){return jn(Jd,e)}var Cr,Eo;function tf(){if(Eo)return Cr;Eo=1;var e=0,t=1,r=2,n=3,a=4,o=5,l=6,i=7,s=8,c=9,u=0,d=1,h=2,f=0,b=1,y=2,C=3,N=4,E=5,j=6,A=7,I=8,P=3,m=10,S=3,x=9,T=10;return Cr=function(O,w,H){var K,D,k,_,B,se,F,v,z,V,$=w.length,Q=H.length,W=O.adjustSizes,Y=O.barnesHutTheta*O.barnesHutTheta,ie,ne,ae,M,J,U,q,L=[];for(k=0;k<$;k+=m)w[k+a]=w[k+r],w[k+o]=w[k+n],w[k+r]=0,w[k+n]=0;if(O.outboundAttractionDistribution){for(ie=0,k=0;k<$;k+=m)ie+=w[k+l];ie/=$/m}if(O.barnesHutOptimize){var oe=1/0,ue=-1/0,re=1/0,ee=-1/0,G,ge,pe;for(k=0;k<$;k+=m)oe=Math.min(oe,w[k+e]),ue=Math.max(ue,w[k+e]),re=Math.min(re,w[k+t]),ee=Math.max(ee,w[k+t]);var ye=ue-oe,we=ee-re;for(ye>we?(re-=(ye-we)/2,ee=re+ye):(oe-=(we-ye)/2,ue=oe+we),L[0+f]=-1,L[0+b]=(oe+ue)/2,L[0+y]=(re+ee)/2,L[0+C]=Math.max(ue-oe,ee-re),L[0+N]=-1,L[0+E]=-1,L[0+j]=0,L[0+A]=0,L[0+I]=0,K=1,k=0;k<$;k+=m)for(D=0,pe=P;;)if(L[D+E]>=0){w[k+e]<L[D+b]?w[k+t]<L[D+y]?G=L[D+E]:G=L[D+E]+x:w[k+t]<L[D+y]?G=L[D+E]+x*2:G=L[D+E]+x*3,L[D+A]=(L[D+A]*L[D+j]+w[k+e]*w[k+l])/(L[D+j]+w[k+l]),L[D+I]=(L[D+I]*L[D+j]+w[k+t]*w[k+l])/(L[D+j]+w[k+l]),L[D+j]+=w[k+l],D=G;continue}else if(L[D+f]<0){L[D+f]=k;break}else{if(L[D+E]=K*x,v=L[D+C]/2,z=L[D+E],L[z+f]=-1,L[z+b]=L[D+b]-v,L[z+y]=L[D+y]-v,L[z+C]=v,L[z+N]=z+x,L[z+E]=-1,L[z+j]=0,L[z+A]=0,L[z+I]=0,z+=x,L[z+f]=-1,L[z+b]=L[D+b]-v,L[z+y]=L[D+y]+v,L[z+C]=v,L[z+N]=z+x,L[z+E]=-1,L[z+j]=0,L[z+A]=0,L[z+I]=0,z+=x,L[z+f]=-1,L[z+b]=L[D+b]+v,L[z+y]=L[D+y]-v,L[z+C]=v,L[z+N]=z+x,L[z+E]=-1,L[z+j]=0,L[z+A]=0,L[z+I]=0,z+=x,L[z+f]=-1,L[z+b]=L[D+b]+v,L[z+y]=L[D+y]+v,L[z+C]=v,L[z+N]=L[D+N],L[z+E]=-1,L[z+j]=0,L[z+A]=0,L[z+I]=0,K+=4,w[L[D+f]+e]<L[D+b]?w[L[D+f]+t]<L[D+y]?G=L[D+E]:G=L[D+E]+x:w[L[D+f]+t]<L[D+y]?G=L[D+E]+x*2:G=L[D+E]+x*3,L[D+j]=w[L[D+f]+l],L[D+A]=w[L[D+f]+e],L[D+I]=w[L[D+f]+t],L[G+f]=L[D+f],L[D+f]=-1,w[k+e]<L[D+b]?w[k+t]<L[D+y]?ge=L[D+E]:ge=L[D+E]+x:w[k+t]<L[D+y]?ge=L[D+E]+x*2:ge=L[D+E]+x*3,G===ge)if(pe--){D=G;continue}else{pe=P;break}L[ge+f]=k;break}}if(O.barnesHutOptimize)for(ne=O.scalingRatio,k=0;k<$;k+=m)for(D=0;;)if(L[D+E]>=0)if(U=Math.pow(w[k+e]-L[D+A],2)+Math.pow(w[k+t]-L[D+I],2),V=L[D+C],4*V*V/U<Y){if(ae=w[k+e]-L[D+A],M=w[k+t]-L[D+I],W===!0?U>0?(q=ne*w[k+l]*L[D+j]/U,w[k+r]+=ae*q,w[k+n]+=M*q):U<0&&(q=-ne*w[k+l]*L[D+j]/Math.sqrt(U),w[k+r]+=ae*q,w[k+n]+=M*q):U>0&&(q=ne*w[k+l]*L[D+j]/U,w[k+r]+=ae*q,w[k+n]+=M*q),D=L[D+N],D<0)break;continue}else{D=L[D+E];continue}else{if(se=L[D+f],se>=0&&se!==k&&(ae=w[k+e]-w[se+e],M=w[k+t]-w[se+t],U=ae*ae+M*M,W===!0?U>0?(q=ne*w[k+l]*w[se+l]/U,w[k+r]+=ae*q,w[k+n]+=M*q):U<0&&(q=-ne*w[k+l]*w[se+l]/Math.sqrt(U),w[k+r]+=ae*q,w[k+n]+=M*q):U>0&&(q=ne*w[k+l]*w[se+l]/U,w[k+r]+=ae*q,w[k+n]+=M*q)),D=L[D+N],D<0)break;continue}else for(ne=O.scalingRatio,_=0;_<$;_+=m)for(B=0;B<_;B+=m)ae=w[_+e]-w[B+e],M=w[_+t]-w[B+t],W===!0?(U=Math.sqrt(ae*ae+M*M)-w[_+s]-w[B+s],U>0?(q=ne*w[_+l]*w[B+l]/U/U,w[_+r]+=ae*q,w[_+n]+=M*q,w[B+r]-=ae*q,w[B+n]-=M*q):U<0&&(q=100*ne*w[_+l]*w[B+l],w[_+r]+=ae*q,w[_+n]+=M*q,w[B+r]-=ae*q,w[B+n]-=M*q)):(U=Math.sqrt(ae*ae+M*M),U>0&&(q=ne*w[_+l]*w[B+l]/U/U,w[_+r]+=ae*q,w[_+n]+=M*q,w[B+r]-=ae*q,w[B+n]-=M*q));for(z=O.gravity/O.scalingRatio,ne=O.scalingRatio,k=0;k<$;k+=m)q=0,ae=w[k+e],M=w[k+t],U=Math.sqrt(Math.pow(ae,2)+Math.pow(M,2)),O.strongGravityMode?U>0&&(q=ne*w[k+l]*z):U>0&&(q=ne*w[k+l]*z/U),w[k+r]-=ae*q,w[k+n]-=M*q;for(ne=1*(O.outboundAttractionDistribution?ie:1),F=0;F<Q;F+=S)_=H[F+u],B=H[F+d],v=H[F+h],J=Math.pow(v,O.edgeWeightInfluence),ae=w[_+e]-w[B+e],M=w[_+t]-w[B+t],W===!0?(U=Math.sqrt(ae*ae+M*M)-w[_+s]-w[B+s],O.linLogMode?O.outboundAttractionDistribution?U>0&&(q=-ne*J*Math.log(1+U)/U/w[_+l]):U>0&&(q=-ne*J*Math.log(1+U)/U):O.outboundAttractionDistribution?U>0&&(q=-ne*J/w[_+l]):U>0&&(q=-ne*J)):(U=Math.sqrt(Math.pow(ae,2)+Math.pow(M,2)),O.linLogMode?O.outboundAttractionDistribution?U>0&&(q=-ne*J*Math.log(1+U)/U/w[_+l]):U>0&&(q=-ne*J*Math.log(1+U)/U):O.outboundAttractionDistribution?(U=1,q=-ne*J/w[_+l]):(U=1,q=-ne*J)),U>0&&(w[_+r]+=ae*q,w[_+n]+=M*q,w[B+r]-=ae*q,w[B+n]-=M*q);var de,me,Ie,Ce,Te,ze;if(W===!0)for(k=0;k<$;k+=m)w[k+c]!==1&&(de=Math.sqrt(Math.pow(w[k+r],2)+Math.pow(w[k+n],2)),de>T&&(w[k+r]=w[k+r]*T/de,w[k+n]=w[k+n]*T/de),me=w[k+l]*Math.sqrt((w[k+a]-w[k+r])*(w[k+a]-w[k+r])+(w[k+o]-w[k+n])*(w[k+o]-w[k+n])),Ie=Math.sqrt((w[k+a]+w[k+r])*(w[k+a]+w[k+r])+(w[k+o]+w[k+n])*(w[k+o]+w[k+n]))/2,Ce=.1*Math.log(1+Ie)/(1+Math.sqrt(me)),Te=w[k+e]+w[k+r]*(Ce/O.slowDown),w[k+e]=Te,ze=w[k+t]+w[k+n]*(Ce/O.slowDown),w[k+t]=ze);else for(k=0;k<$;k+=m)w[k+c]!==1&&(me=w[k+l]*Math.sqrt((w[k+a]-w[k+r])*(w[k+a]-w[k+r])+(w[k+o]-w[k+n])*(w[k+o]-w[k+n])),Ie=Math.sqrt((w[k+a]+w[k+r])*(w[k+a]+w[k+r])+(w[k+o]+w[k+n])*(w[k+o]+w[k+n]))/2,Ce=w[k+i]*Math.log(1+Ie)/(1+Math.sqrt(me)),w[k+i]=Math.min(1,Math.sqrt(Ce*(Math.pow(w[k+r],2)+Math.pow(w[k+n],2))/(1+Math.sqrt(me)))),Te=w[k+e]+w[k+r]*(Ce/O.slowDown),w[k+e]=Te,ze=w[k+t]+w[k+n]*(Ce/O.slowDown),w[k+t]=ze);return{}},Cr}var Ue={},ko;function ms(){if(ko)return Ue;ko=1;var e=10,t=3;return Ue.assign=function(r){r=r||{};var n=Array.prototype.slice.call(arguments).slice(1),a,o,l;for(a=0,l=n.length;a<l;a++)if(n[a])for(o in n[a])r[o]=n[a][o];return r},Ue.validateSettings=function(r){return"linLogMode"in r&&typeof r.linLogMode!="boolean"?{message:"the `linLogMode` setting should be a boolean."}:"outboundAttractionDistribution"in r&&typeof r.outboundAttractionDistribution!="boolean"?{message:"the `outboundAttractionDistribution` setting should be a boolean."}:"adjustSizes"in r&&typeof r.adjustSizes!="boolean"?{message:"the `adjustSizes` setting should be a boolean."}:"edgeWeightInfluence"in r&&typeof r.edgeWeightInfluence!="number"?{message:"the `edgeWeightInfluence` setting should be a number."}:"scalingRatio"in r&&!(typeof r.scalingRatio=="number"&&r.scalingRatio>=0)?{message:"the `scalingRatio` setting should be a number >= 0."}:"strongGravityMode"in r&&typeof r.strongGravityMode!="boolean"?{message:"the `strongGravityMode` setting should be a boolean."}:"gravity"in r&&!(typeof r.gravity=="number"&&r.gravity>=0)?{message:"the `gravity` setting should be a number >= 0."}:"slowDown"in r&&!(typeof r.slowDown=="number"||r.slowDown>=0)?{message:"the `slowDown` setting should be a number >= 0."}:"barnesHutOptimize"in r&&typeof r.barnesHutOptimize!="boolean"?{message:"the `barnesHutOptimize` setting should be a boolean."}:"barnesHutTheta"in r&&!(typeof r.barnesHutTheta=="number"&&r.barnesHutTheta>=0)?{message:"the `barnesHutTheta` setting should be a number >= 0."}:null},Ue.graphToByteArrays=function(r,n){var a=r.order,o=r.size,l={},i,s=new Float32Array(a*e),c=new Float32Array(o*t);return i=0,r.forEachNode(function(u,d){l[u]=i,s[i]=d.x,s[i+1]=d.y,s[i+2]=0,s[i+3]=0,s[i+4]=0,s[i+5]=0,s[i+6]=1,s[i+7]=1,s[i+8]=d.size||1,s[i+9]=d.fixed?1:0,i+=e}),i=0,r.forEachEdge(function(u,d,h,f,b,y,C){var N=l[h],E=l[f],j=n(u,d,h,f,b,y,C);s[N+6]+=j,s[E+6]+=j,c[i]=N,c[i+1]=E,c[i+2]=j,i+=t}),{nodes:s,edges:c}},Ue.assignLayoutChanges=function(r,n,a){var o=0;r.updateEachNodeAttributes(function(l,i){return i.x=n[o],i.y=n[o+1],o+=e,a?a(l,i):i})},Ue.readGraphPositions=function(r,n){var a=0;r.forEachNode(function(o,l){n[a]=l.x,n[a+1]=l.y,a+=e})},Ue.collectLayoutChanges=function(r,n,a){for(var o=r.nodes(),l={},i=0,s=0,c=n.length;i<c;i+=e){if(a){var u=Object.assign({},r.getNodeAttributes(o[s]));u.x=n[i],u.y=n[i+1],u=a(o[s],u),l[o[s]]={x:u.x,y:u.y}}else l[o[s]]={x:n[i],y:n[i+1]};s++}return l},Ue.createWorker=function(n){var a=window.URL||window.webkitURL,o=n.toString(),l=a.createObjectURL(new Blob(["("+o+").call(this);"],{type:"text/javascript"})),i=new Worker(l);return a.revokeObjectURL(l),i},Ue}var Tr,Co;function vs(){return Co||(Co=1,Tr={linLogMode:!1,outboundAttractionDistribution:!1,adjustSizes:!1,edgeWeightInfluence:1,scalingRatio:1,strongGravityMode:!1,gravity:1,slowDown:1,barnesHutOptimize:!1,barnesHutTheta:.5}),Tr}var Rr,To;function rf(){if(To)return Rr;To=1;var e=We(),t=In().createEdgeWeightGetter,r=tf(),n=ms(),a=vs();function o(s,c,u){if(!e(c))throw new Error("graphology-layout-forceatlas2: the given graph is not a valid graphology instance.");typeof u=="number"&&(u={iterations:u});var d=u.iterations;if(typeof d!="number")throw new Error("graphology-layout-forceatlas2: invalid number of iterations.");if(d<=0)throw new Error("graphology-layout-forceatlas2: you should provide a positive number of iterations.");var h=t("getEdgeWeight"in u?u.getEdgeWeight:"weight").fromEntry,f=typeof u.outputReducer=="function"?u.outputReducer:null,b=n.assign({},a,u.settings),y=n.validateSettings(b);if(y)throw new Error("graphology-layout-forceatlas2: "+y.message);var C=n.graphToByteArrays(c,h),N;for(N=0;N<d;N++)r(b,C.nodes,C.edges);if(s){n.assignLayoutChanges(c,C.nodes,f);return}return n.collectLayoutChanges(c,C.nodes)}function l(s){var c=typeof s=="number"?s:s.order;return{barnesHutOptimize:c>2e3,strongGravityMode:!0,gravity:.05,scalingRatio:10,slowDown:1+Math.log(c)}}var i=o.bind(null,!1);return i.assign=o.bind(null,!0),i.inferSettings=l,Rr=i,Rr}var nf=rf();const of=He(nf);var Ar,Ro;function af(){return Ro||(Ro=1,Ar=function(){var t,r,n={};(function(){var o=0,l=1,i=2,s=3,c=4,u=5,d=6,h=7,f=8,b=9,y=0,C=1,N=2,E=0,j=1,A=2,I=3,P=4,m=5,S=6,x=7,T=8,R=3,O=10,w=3,H=9,K=10;n.exports=function(k,_,B){var se,F,v,z,V,$,Q,W,Y,ie,ne=_.length,ae=B.length,M=k.adjustSizes,J=k.barnesHutTheta*k.barnesHutTheta,U,q,L,oe,ue,re,ee,G=[];for(v=0;v<ne;v+=O)_[v+c]=_[v+i],_[v+u]=_[v+s],_[v+i]=0,_[v+s]=0;if(k.outboundAttractionDistribution){for(U=0,v=0;v<ne;v+=O)U+=_[v+d];U/=ne/O}if(k.barnesHutOptimize){var ge=1/0,pe=-1/0,ye=1/0,we=-1/0,de,me,Ie;for(v=0;v<ne;v+=O)ge=Math.min(ge,_[v+o]),pe=Math.max(pe,_[v+o]),ye=Math.min(ye,_[v+l]),we=Math.max(we,_[v+l]);var Ce=pe-ge,Te=we-ye;for(Ce>Te?(ye-=(Ce-Te)/2,we=ye+Ce):(ge-=(Te-Ce)/2,pe=ge+Te),G[0+E]=-1,G[0+j]=(ge+pe)/2,G[0+A]=(ye+we)/2,G[0+I]=Math.max(pe-ge,we-ye),G[0+P]=-1,G[0+m]=-1,G[0+S]=0,G[0+x]=0,G[0+T]=0,se=1,v=0;v<ne;v+=O)for(F=0,Ie=R;;)if(G[F+m]>=0){_[v+o]<G[F+j]?_[v+l]<G[F+A]?de=G[F+m]:de=G[F+m]+H:_[v+l]<G[F+A]?de=G[F+m]+H*2:de=G[F+m]+H*3,G[F+x]=(G[F+x]*G[F+S]+_[v+o]*_[v+d])/(G[F+S]+_[v+d]),G[F+T]=(G[F+T]*G[F+S]+_[v+l]*_[v+d])/(G[F+S]+_[v+d]),G[F+S]+=_[v+d],F=de;continue}else if(G[F+E]<0){G[F+E]=v;break}else{if(G[F+m]=se*H,W=G[F+I]/2,Y=G[F+m],G[Y+E]=-1,G[Y+j]=G[F+j]-W,G[Y+A]=G[F+A]-W,G[Y+I]=W,G[Y+P]=Y+H,G[Y+m]=-1,G[Y+S]=0,G[Y+x]=0,G[Y+T]=0,Y+=H,G[Y+E]=-1,G[Y+j]=G[F+j]-W,G[Y+A]=G[F+A]+W,G[Y+I]=W,G[Y+P]=Y+H,G[Y+m]=-1,G[Y+S]=0,G[Y+x]=0,G[Y+T]=0,Y+=H,G[Y+E]=-1,G[Y+j]=G[F+j]+W,G[Y+A]=G[F+A]-W,G[Y+I]=W,G[Y+P]=Y+H,G[Y+m]=-1,G[Y+S]=0,G[Y+x]=0,G[Y+T]=0,Y+=H,G[Y+E]=-1,G[Y+j]=G[F+j]+W,G[Y+A]=G[F+A]+W,G[Y+I]=W,G[Y+P]=G[F+P],G[Y+m]=-1,G[Y+S]=0,G[Y+x]=0,G[Y+T]=0,se+=4,_[G[F+E]+o]<G[F+j]?_[G[F+E]+l]<G[F+A]?de=G[F+m]:de=G[F+m]+H:_[G[F+E]+l]<G[F+A]?de=G[F+m]+H*2:de=G[F+m]+H*3,G[F+S]=_[G[F+E]+d],G[F+x]=_[G[F+E]+o],G[F+T]=_[G[F+E]+l],G[de+E]=G[F+E],G[F+E]=-1,_[v+o]<G[F+j]?_[v+l]<G[F+A]?me=G[F+m]:me=G[F+m]+H:_[v+l]<G[F+A]?me=G[F+m]+H*2:me=G[F+m]+H*3,de===me)if(Ie--){F=de;continue}else{Ie=R;break}G[me+E]=v;break}}if(k.barnesHutOptimize)for(q=k.scalingRatio,v=0;v<ne;v+=O)for(F=0;;)if(G[F+m]>=0)if(re=Math.pow(_[v+o]-G[F+x],2)+Math.pow(_[v+l]-G[F+T],2),ie=G[F+I],4*ie*ie/re<J){if(L=_[v+o]-G[F+x],oe=_[v+l]-G[F+T],M===!0?re>0?(ee=q*_[v+d]*G[F+S]/re,_[v+i]+=L*ee,_[v+s]+=oe*ee):re<0&&(ee=-q*_[v+d]*G[F+S]/Math.sqrt(re),_[v+i]+=L*ee,_[v+s]+=oe*ee):re>0&&(ee=q*_[v+d]*G[F+S]/re,_[v+i]+=L*ee,_[v+s]+=oe*ee),F=G[F+P],F<0)break;continue}else{F=G[F+m];continue}else{if($=G[F+E],$>=0&&$!==v&&(L=_[v+o]-_[$+o],oe=_[v+l]-_[$+l],re=L*L+oe*oe,M===!0?re>0?(ee=q*_[v+d]*_[$+d]/re,_[v+i]+=L*ee,_[v+s]+=oe*ee):re<0&&(ee=-q*_[v+d]*_[$+d]/Math.sqrt(re),_[v+i]+=L*ee,_[v+s]+=oe*ee):re>0&&(ee=q*_[v+d]*_[$+d]/re,_[v+i]+=L*ee,_[v+s]+=oe*ee)),F=G[F+P],F<0)break;continue}else for(q=k.scalingRatio,z=0;z<ne;z+=O)for(V=0;V<z;V+=O)L=_[z+o]-_[V+o],oe=_[z+l]-_[V+l],M===!0?(re=Math.sqrt(L*L+oe*oe)-_[z+f]-_[V+f],re>0?(ee=q*_[z+d]*_[V+d]/re/re,_[z+i]+=L*ee,_[z+s]+=oe*ee,_[V+i]-=L*ee,_[V+s]-=oe*ee):re<0&&(ee=100*q*_[z+d]*_[V+d],_[z+i]+=L*ee,_[z+s]+=oe*ee,_[V+i]-=L*ee,_[V+s]-=oe*ee)):(re=Math.sqrt(L*L+oe*oe),re>0&&(ee=q*_[z+d]*_[V+d]/re/re,_[z+i]+=L*ee,_[z+s]+=oe*ee,_[V+i]-=L*ee,_[V+s]-=oe*ee));for(Y=k.gravity/k.scalingRatio,q=k.scalingRatio,v=0;v<ne;v+=O)ee=0,L=_[v+o],oe=_[v+l],re=Math.sqrt(Math.pow(L,2)+Math.pow(oe,2)),k.strongGravityMode?re>0&&(ee=q*_[v+d]*Y):re>0&&(ee=q*_[v+d]*Y/re),_[v+i]-=L*ee,_[v+s]-=oe*ee;for(q=1*(k.outboundAttractionDistribution?U:1),Q=0;Q<ae;Q+=w)z=B[Q+y],V=B[Q+C],W=B[Q+N],ue=Math.pow(W,k.edgeWeightInfluence),L=_[z+o]-_[V+o],oe=_[z+l]-_[V+l],M===!0?(re=Math.sqrt(L*L+oe*oe)-_[z+f]-_[V+f],k.linLogMode?k.outboundAttractionDistribution?re>0&&(ee=-q*ue*Math.log(1+re)/re/_[z+d]):re>0&&(ee=-q*ue*Math.log(1+re)/re):k.outboundAttractionDistribution?re>0&&(ee=-q*ue/_[z+d]):re>0&&(ee=-q*ue)):(re=Math.sqrt(Math.pow(L,2)+Math.pow(oe,2)),k.linLogMode?k.outboundAttractionDistribution?re>0&&(ee=-q*ue*Math.log(1+re)/re/_[z+d]):re>0&&(ee=-q*ue*Math.log(1+re)/re):k.outboundAttractionDistribution?(re=1,ee=-q*ue/_[z+d]):(re=1,ee=-q*ue)),re>0&&(_[z+i]+=L*ee,_[z+s]+=oe*ee,_[V+i]-=L*ee,_[V+s]-=oe*ee);var ze,Ye,Ke,Re,st,Pe;if(M===!0)for(v=0;v<ne;v+=O)_[v+b]!==1&&(ze=Math.sqrt(Math.pow(_[v+i],2)+Math.pow(_[v+s],2)),ze>K&&(_[v+i]=_[v+i]*K/ze,_[v+s]=_[v+s]*K/ze),Ye=_[v+d]*Math.sqrt((_[v+c]-_[v+i])*(_[v+c]-_[v+i])+(_[v+u]-_[v+s])*(_[v+u]-_[v+s])),Ke=Math.sqrt((_[v+c]+_[v+i])*(_[v+c]+_[v+i])+(_[v+u]+_[v+s])*(_[v+u]+_[v+s]))/2,Re=.1*Math.log(1+Ke)/(1+Math.sqrt(Ye)),st=_[v+o]+_[v+i]*(Re/k.slowDown),_[v+o]=st,Pe=_[v+l]+_[v+s]*(Re/k.slowDown),_[v+l]=Pe);else for(v=0;v<ne;v+=O)_[v+b]!==1&&(Ye=_[v+d]*Math.sqrt((_[v+c]-_[v+i])*(_[v+c]-_[v+i])+(_[v+u]-_[v+s])*(_[v+u]-_[v+s])),Ke=Math.sqrt((_[v+c]+_[v+i])*(_[v+c]+_[v+i])+(_[v+u]+_[v+s])*(_[v+u]+_[v+s]))/2,Re=_[v+h]*Math.log(1+Ke)/(1+Math.sqrt(Ye)),_[v+h]=Math.min(1,Math.sqrt(Re*(Math.pow(_[v+i],2)+Math.pow(_[v+s],2))/(1+Math.sqrt(Ye)))),st=_[v+o]+_[v+i]*(Re/k.slowDown),_[v+o]=st,Pe=_[v+l]+_[v+s]*(Re/k.slowDown),_[v+l]=Pe);return{}}})();var a=n.exports;self.addEventListener("message",function(o){var l=o.data;t=new Float32Array(l.nodes),l.edges&&(r=new Float32Array(l.edges)),a(l.settings,t,r),self.postMessage({nodes:t.buffer},[t.buffer])})}),Ar}var jr,Ao;function sf(){if(Ao)return jr;Ao=1;var e=af(),t=We(),r=In().createEdgeWeightGetter,n=ms(),a=vs();function o(l,i){if(i=i||{},!t(l))throw new Error("graphology-layout-forceatlas2/worker: the given graph is not a valid graphology instance.");var s=r("getEdgeWeight"in i?i.getEdgeWeight:"weight").fromEntry,c=n.assign({},a,i.settings),u=n.validateSettings(c);if(u)throw new Error("graphology-layout-forceatlas2/worker: "+u.message);this.worker=null,this.graph=l,this.settings=c,this.getEdgeWeight=s,this.matrices=null,this.running=!1,this.killed=!1,this.outputReducer=typeof i.outputReducer=="function"?i.outputReducer:null,this.handleMessage=this.handleMessage.bind(this);var d=void 0,h=this;this.handleGraphUpdate=function(){h.worker&&h.worker.terminate(),d&&clearTimeout(d),d=setTimeout(function(){d=void 0,h.spawnWorker()},0)},l.on("nodeAdded",this.handleGraphUpdate),l.on("edgeAdded",this.handleGraphUpdate),l.on("nodeDropped",this.handleGraphUpdate),l.on("edgeDropped",this.handleGraphUpdate),this.spawnWorker()}return o.prototype.isRunning=function(){return this.running},o.prototype.spawnWorker=function(){this.worker&&this.worker.terminate(),this.worker=n.createWorker(e),this.worker.addEventListener("message",this.handleMessage),this.running&&(this.running=!1,this.start())},o.prototype.handleMessage=function(l){if(this.running){var i=new Float32Array(l.data.nodes);n.assignLayoutChanges(this.graph,i,this.outputReducer),this.outputReducer&&n.readGraphPositions(this.graph,i),this.matrices.nodes=i,this.askForIterations()}},o.prototype.askForIterations=function(l){var i=this.matrices,s={settings:this.settings,nodes:i.nodes.buffer},c=[i.nodes.buffer];return l&&(s.edges=i.edges.buffer,c.push(i.edges.buffer)),this.worker.postMessage(s,c),this},o.prototype.start=function(){if(this.killed)throw new Error("graphology-layout-forceatlas2/worker.start: layout was killed.");return this.running?this:(this.matrices=n.graphToByteArrays(this.graph,this.getEdgeWeight),this.running=!0,this.askForIterations(!0),this)},o.prototype.stop=function(){return this.running=!1,this},o.prototype.kill=function(){if(this.killed)return this;this.running=!1,this.killed=!0,this.matrices=null,this.worker.terminate(),this.graph.removeListener("nodeAdded",this.handleGraphUpdate),this.graph.removeListener("edgeAdded",this.handleGraphUpdate),this.graph.removeListener("nodeDropped",this.handleGraphUpdate),this.graph.removeListener("edgeDropped",this.handleGraphUpdate)},jr=o,jr}var lf=sf();const cf=He(lf);function ys(e={iterations:100}){return St(of,e)}function uf(e={}){return jn(cf,e)}var Ir,jo;function df(){if(jo)return Ir;jo=1;var e=0,t=1,r=2,n=3;function a(l,i){return l+"§"+i}function o(){return .01*(.5-Math.random())}return Ir=function(i,s){var c=i.margin,u=i.ratio,d=i.expansion,h=i.gridSize,f=i.speed,b,y,C,N,E,j,A=!0,I=s.length,P=I/n|0,m=new Float32Array(P),S=new Float32Array(P),x=1/0,T=1/0,R=-1/0,O=-1/0;for(b=0;b<I;b+=n)C=s[b+e],N=s[b+t],j=s[b+r]*u+c,x=Math.min(x,C-j),R=Math.max(R,C+j),T=Math.min(T,N-j),O=Math.max(O,N+j);var w=R-x,H=O-T,K=(x+R)/2,D=(T+O)/2;x=K-d*w/2,R=K+d*w/2,T=D-d*H/2,O=D+d*H/2;var k=new Array(h*h),_=k.length,B;for(B=0;B<_;B++)k[B]=[];var se,F,v,z,V,$,Q,W,Y,ie;for(b=0;b<I;b+=n)for(C=s[b+e],N=s[b+t],j=s[b+r]*u+c,se=C-j,F=C+j,v=N-j,z=N+j,V=Math.floor(h*(se-x)/(R-x)),$=Math.floor(h*(F-x)/(R-x)),Q=Math.floor(h*(v-T)/(O-T)),W=Math.floor(h*(z-T)/(O-T)),Y=V;Y<=$;Y++)for(ie=Q;ie<=W;ie++)k[Y*h+ie].push(b);var ne,ae=new Set,M,J,U,q,L,oe,ue,re,ee,G,ge,pe,ye;for(B=0;B<_;B++)for(ne=k[B],b=0,E=ne.length;b<E;b++)for(M=ne[b],U=s[M+e],L=s[M+t],ue=s[M+r],y=b+1;y<E;y++)J=ne[y],ee=a(M,J),!(_>1&&ae.has(ee))&&(_>1&&ae.add(ee),q=s[J+e],oe=s[J+t],re=s[J+r],G=q-U,ge=oe-L,pe=Math.sqrt(G*G+ge*ge),ye=pe<ue*u+c+(re*u+c),ye&&(A=!1,J=J/n|0,pe>0?(m[J]+=G/pe*(1+ue),S[J]+=ge/pe*(1+ue)):(m[J]+=w*o(),S[J]+=H*o())));for(b=0,y=0;b<I;b+=n,y++)s[b+e]+=m[y]*.1*f,s[b+t]+=S[y]*.1*f;return{converged:A}},Ir}var lt={},Io;function bs(){if(Io)return lt;Io=1;var e=3;return lt.validateSettings=function(t){return"gridSize"in t&&typeof t.gridSize!="number"||t.gridSize<=0?{message:"the `gridSize` setting should be a positive number."}:"margin"in t&&typeof t.margin!="number"||t.margin<0?{message:"the `margin` setting should be 0 or a positive number."}:"expansion"in t&&typeof t.expansion!="number"||t.expansion<=0?{message:"the `expansion` setting should be a positive number."}:"ratio"in t&&typeof t.ratio!="number"||t.ratio<=0?{message:"the `ratio` setting should be a positive number."}:"speed"in t&&typeof t.speed!="number"||t.speed<=0?{message:"the `speed` setting should be a positive number."}:null},lt.graphToByteArray=function(t,r){var n=t.order,a=new Float32Array(n*e),o=0;return t.forEachNode(function(l,i){typeof r=="function"&&(i=r(l,i)),a[o]=i.x,a[o+1]=i.y,a[o+2]=i.size||1,o+=e}),a},lt.assignLayoutChanges=function(t,r,n){var a=0;t.forEachNode(function(o){var l={x:r[a],y:r[a+1]};typeof n=="function"&&(l=n(o,l)),t.mergeNodeAttributes(o,l),a+=e})},lt.collectLayoutChanges=function(t,r,n){var a={},o=0;return t.forEachNode(function(l){var i={x:r[o],y:r[o+1]};typeof n=="function"&&(i=n(l,i)),a[l]=i,o+=e}),a},lt.createWorker=function(r){var n=window.URL||window.webkitURL,a=r.toString(),o=n.createObjectURL(new Blob(["("+a+").call(this);"],{type:"text/javascript"})),l=new Worker(o);return n.revokeObjectURL(o),l},lt}var Nr,No;function ws(){return No||(No=1,Nr={gridSize:20,margin:5,expansion:1.1,ratio:1,speed:3}),Nr}var Lr,Lo;function ff(){if(Lo)return Lr;Lo=1;var e=We(),t=df(),r=bs(),n=ws(),a=500;function o(i,s,c){if(!e(s))throw new Error("graphology-layout-noverlap: the given graph is not a valid graphology instance.");typeof c=="number"?c={maxIterations:c}:c=c||{};var u=c.maxIterations||a;if(typeof u!="number"||u<=0)throw new Error("graphology-layout-force: you should provide a positive number of maximum iterations.");var d=Object.assign({},n,c.settings),h=r.validateSettings(d);if(h)throw new Error("graphology-layout-noverlap: "+h.message);var f=r.graphToByteArray(s,c.inputReducer),b=!1,y;for(y=0;y<u&&!b;y++)b=t(d,f).converged;if(i){r.assignLayoutChanges(s,f,c.outputReducer);return}return r.collectLayoutChanges(s,f,c.outputReducer)}var l=o.bind(null,!1);return l.assign=o.bind(null,!0),Lr=l,Lr}var hf=ff();const gf=He(hf);var zr,zo;function pf(){return zo||(zo=1,zr=function(){var t,r={};(function(){var a=0,o=1,l=2,i=3;function s(u,d){return u+"§"+d}function c(){return .01*(.5-Math.random())}r.exports=function(d,h){var f=d.margin,b=d.ratio,y=d.expansion,C=d.gridSize,N=d.speed,E,j,A,I,P,m,S=!0,x=h.length,T=x/i|0,R=new Float32Array(T),O=new Float32Array(T),w=1/0,H=1/0,K=-1/0,D=-1/0;for(E=0;E<x;E+=i)A=h[E+a],I=h[E+o],m=h[E+l]*b+f,w=Math.min(w,A-m),K=Math.max(K,A+m),H=Math.min(H,I-m),D=Math.max(D,I+m);var k=K-w,_=D-H,B=(w+K)/2,se=(H+D)/2;w=B-y*k/2,K=B+y*k/2,H=se-y*_/2,D=se+y*_/2;var F=new Array(C*C),v=F.length,z;for(z=0;z<v;z++)F[z]=[];var V,$,Q,W,Y,ie,ne,ae,M,J;for(E=0;E<x;E+=i)for(A=h[E+a],I=h[E+o],m=h[E+l]*b+f,V=A-m,$=A+m,Q=I-m,W=I+m,Y=Math.floor(C*(V-w)/(K-w)),ie=Math.floor(C*($-w)/(K-w)),ne=Math.floor(C*(Q-H)/(D-H)),ae=Math.floor(C*(W-H)/(D-H)),M=Y;M<=ie;M++)for(J=ne;J<=ae;J++)F[M*C+J].push(E);var U,q=new Set,L,oe,ue,re,ee,G,ge,pe,ye,we,de,me,Ie;for(z=0;z<v;z++)for(U=F[z],E=0,P=U.length;E<P;E++)for(L=U[E],ue=h[L+a],ee=h[L+o],ge=h[L+l],j=E+1;j<P;j++)oe=U[j],ye=s(L,oe),!(v>1&&q.has(ye))&&(v>1&&q.add(ye),re=h[oe+a],G=h[oe+o],pe=h[oe+l],we=re-ue,de=G-ee,me=Math.sqrt(we*we+de*de),Ie=me<ge*b+f+(pe*b+f),Ie&&(S=!1,oe=oe/i|0,me>0?(R[oe]+=we/me*(1+ge),O[oe]+=de/me*(1+ge)):(R[oe]+=k*c(),O[oe]+=_*c())));for(E=0,j=0;E<x;E+=i,j++)h[E+a]+=R[j]*.1*N,h[E+o]+=O[j]*.1*N;return{converged:S}}})();var n=r.exports;self.addEventListener("message",function(a){var o=a.data;t=new Float32Array(o.nodes);var l=n(o.settings,t);self.postMessage({result:l,nodes:t.buffer},[t.buffer])})}),zr}var Pr,Po;function mf(){if(Po)return Pr;Po=1;var e=pf(),t=We(),r=bs(),n=ws();function a(o,l){if(l=l||{},!t(o))throw new Error("graphology-layout-noverlap/worker: the given graph is not a valid graphology instance.");var i=Object.assign({},n,l.settings),s=r.validateSettings(i);if(s)throw new Error("graphology-layout-noverlap/worker: "+s.message);this.worker=null,this.graph=o,this.settings=i,this.matrices=null,this.running=!1,this.killed=!1,this.inputReducer=l.inputReducer,this.outputReducer=l.outputReducer,this.callbacks={onConverged:typeof l.onConverged=="function"?l.onConverged:null},this.handleMessage=this.handleMessage.bind(this);var c=!1,u=this;this.handleAddition=function(){c||(c=!0,u.spawnWorker(),setTimeout(function(){c=!1},0))},o.on("nodeAdded",this.handleAddition),o.on("edgeAdded",this.handleAddition),this.spawnWorker()}return a.prototype.isRunning=function(){return this.running},a.prototype.spawnWorker=function(){this.worker&&this.worker.terminate(),this.worker=r.createWorker(e),this.worker.addEventListener("message",this.handleMessage),this.running&&(this.running=!1,this.start())},a.prototype.handleMessage=function(o){if(this.running){var l=new Float32Array(o.data.nodes);if(r.assignLayoutChanges(this.graph,l,this.outputReducer),this.matrices.nodes=l,o.data.result.converged){this.callbacks.onConverged&&this.callbacks.onConverged(),this.stop();return}this.askForIterations()}},a.prototype.askForIterations=function(){var o=this.matrices,l={settings:this.settings,nodes:o.nodes.buffer},i=[o.nodes.buffer];return this.worker.postMessage(l,i),this},a.prototype.start=function(){if(this.killed)throw new Error("graphology-layout-noverlap/worker.start: layout was killed.");return this.running?this:(this.matrices={nodes:r.graphToByteArray(this.graph,this.inputReducer)},this.running=!0,this.askForIterations(),this)},a.prototype.stop=function(){return this.running=!1,this},a.prototype.kill=function(){if(this.killed)return this;this.running=!1,this.killed=!0,this.matrices=null,this.worker.terminate(),this.graph.removeListener("nodeAdded",this.handleAddition),this.graph.removeListener("edgeAdded",this.handleAddition)},Pr=a,Pr}var vf=mf();const yf=He(vf);function bf(e={}){return St(gf,e)}function wf(e={}){return jn(yf,e)}var Dr,Do;function xf(){if(Do)return Dr;Do=1;var e=At(),t=We(),r={dimensions:["x","y"],center:.5,rng:Math.random,scale:1};function n(o,l,i){if(!t(l))throw new Error("graphology-layout/random: the given graph is not a valid graphology instance.");i=e(i,r);var s=i.dimensions;if(!Array.isArray(s)||s.length<1)throw new Error("graphology-layout/random: given dimensions are invalid.");var c=s.length,u=i.center,d=i.rng,h=i.scale,f=(u-.5)*h;function b(C){for(var N=0;N<c;N++)C[s[N]]=d()*h+f;return C}if(!o){var y={};return l.forEachNode(function(C){y[C]=b({})}),y}l.updateEachNodeAttributes(function(C,N){return b(N),N},{attributes:s})}var a=n.bind(null,!1);return a.assign=n.bind(null,!0),Dr=a,Dr}var _f=xf();const Sf=He(_f);function Ef(e={}){return St(Sf,e)}var Oo=1,kf=.9,Cf=.8,Tf=.17,Or=.1,Gr=.999,Rf=.9999,Af=.99,jf=/[\\\/_+.#"@\[\(\{&]/,If=/[\\\/_+.#"@\[\(\{&]/g,Nf=/[\s-]/,xs=/[\s-]/g;function hn(e,t,r,n,a,o,l){if(o===t.length)return a===e.length?Oo:Af;var i=`${a},${o}`;if(l[i]!==void 0)return l[i];for(var s=n.charAt(o),c=r.indexOf(s,a),u=0,d,h,f,b;c>=0;)d=hn(e,t,r,n,c+1,o+1,l),d>u&&(c===a?d*=Oo:jf.test(e.charAt(c-1))?(d*=Cf,f=e.slice(a,c-1).match(If),f&&a>0&&(d*=Math.pow(Gr,f.length))):Nf.test(e.charAt(c-1))?(d*=kf,b=e.slice(a,c-1).match(xs),b&&a>0&&(d*=Math.pow(Gr,b.length))):(d*=Tf,a>0&&(d*=Math.pow(Gr,c-a))),e.charAt(c)!==t.charAt(o)&&(d*=Rf)),(d<Or&&r.charAt(c-1)===n.charAt(o+1)||n.charAt(o+1)===n.charAt(o)&&r.charAt(c-1)!==n.charAt(o))&&(h=hn(e,t,r,n,c+1,o+2,l),h*Or>d&&(d=h*Or)),d>u&&(u=d),c=r.indexOf(s,c+1);return l[i]=u,u}function Go(e){return e.toLowerCase().replace(xs," ")}function Lf(e,t,r){return e=r&&r.length>0?`${e+" "+r.join(" ")}`:e,hn(e,t,Go(e),Go(t),0,0,{})}var Mr={exports:{}},Fr={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Mo;function zf(){if(Mo)return Fr;Mo=1;var e=gi();function t(d,h){return d===h&&(d!==0||1/d===1/h)||d!==d&&h!==h}var r=typeof Object.is=="function"?Object.is:t,n=e.useState,a=e.useEffect,o=e.useLayoutEffect,l=e.useDebugValue;function i(d,h){var f=h(),b=n({inst:{value:f,getSnapshot:h}}),y=b[0].inst,C=b[1];return o(function(){y.value=f,y.getSnapshot=h,s(y)&&C({inst:y})},[d,f,h]),a(function(){return s(y)&&C({inst:y}),d(function(){s(y)&&C({inst:y})})},[d]),l(f),f}function s(d){var h=d.getSnapshot;d=d.value;try{var f=h();return!r(d,f)}catch{return!0}}function c(d,h){return h()}var u=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?c:i;return Fr.useSyncExternalStore=e.useSyncExternalStore!==void 0?e.useSyncExternalStore:u,Fr}var Fo;function Pf(){return Fo||(Fo=1,Mr.exports=zf()),Mr.exports}var Df=Pf(),Tt='[cmdk-group=""]',$r='[cmdk-group-items=""]',Of='[cmdk-group-heading=""]',Nn='[cmdk-item=""]',$o=`${Nn}:not([aria-disabled="true"])`,gn="cmdk-item-select",dt="data-value",Gf=(e,t,r)=>Lf(e,t,r),_s=p.createContext(void 0),jt=()=>p.useContext(_s),Ss=p.createContext(void 0),Ln=()=>p.useContext(Ss),Es=p.createContext(void 0),ks=p.forwardRef((e,t)=>{let r=yt(()=>{var v,z;return{search:"",value:(z=(v=e.value)!=null?v:e.defaultValue)!=null?z:"",filtered:{count:0,items:new Map,groups:new Set}}}),n=yt(()=>new Set),a=yt(()=>new Map),o=yt(()=>new Map),l=yt(()=>new Set),i=Cs(e),{label:s,children:c,value:u,onValueChange:d,filter:h,shouldFilter:f,loop:b,disablePointerSelection:y=!1,vimBindings:C=!0,...N}=e,E=ft(),j=ft(),A=ft(),I=p.useRef(null),P=Yf();gt(()=>{if(u!==void 0){let v=u.trim();r.current.value=v,m.emit()}},[u]),gt(()=>{P(6,w)},[]);let m=p.useMemo(()=>({subscribe:v=>(l.current.add(v),()=>l.current.delete(v)),snapshot:()=>r.current,setState:(v,z,V)=>{var $,Q,W;if(!Object.is(r.current[v],z)){if(r.current[v]=z,v==="search")O(),T(),P(1,R);else if(v==="value"&&(V||P(5,w),(($=i.current)==null?void 0:$.value)!==void 0)){let Y=z??"";(W=(Q=i.current).onValueChange)==null||W.call(Q,Y);return}m.emit()}},emit:()=>{l.current.forEach(v=>v())}}),[]),S=p.useMemo(()=>({value:(v,z,V)=>{var $;z!==(($=o.current.get(v))==null?void 0:$.value)&&(o.current.set(v,{value:z,keywords:V}),r.current.filtered.items.set(v,x(z,V)),P(2,()=>{T(),m.emit()}))},item:(v,z)=>(n.current.add(v),z&&(a.current.has(z)?a.current.get(z).add(v):a.current.set(z,new Set([v]))),P(3,()=>{O(),T(),r.current.value||R(),m.emit()}),()=>{o.current.delete(v),n.current.delete(v),r.current.filtered.items.delete(v);let V=H();P(4,()=>{O(),(V==null?void 0:V.getAttribute("id"))===v&&R(),m.emit()})}),group:v=>(a.current.has(v)||a.current.set(v,new Set),()=>{o.current.delete(v),a.current.delete(v)}),filter:()=>i.current.shouldFilter,label:s||e["aria-label"],getDisablePointerSelection:()=>i.current.disablePointerSelection,listId:E,inputId:A,labelId:j,listInnerRef:I}),[]);function x(v,z){var V,$;let Q=($=(V=i.current)==null?void 0:V.filter)!=null?$:Gf;return v?Q(v,r.current.search,z):0}function T(){if(!r.current.search||i.current.shouldFilter===!1)return;let v=r.current.filtered.items,z=[];r.current.filtered.groups.forEach($=>{let Q=a.current.get($),W=0;Q.forEach(Y=>{let ie=v.get(Y);W=Math.max(ie,W)}),z.push([$,W])});let V=I.current;K().sort(($,Q)=>{var W,Y;let ie=$.getAttribute("id"),ne=Q.getAttribute("id");return((W=v.get(ne))!=null?W:0)-((Y=v.get(ie))!=null?Y:0)}).forEach($=>{let Q=$.closest($r);Q?Q.appendChild($.parentElement===Q?$:$.closest(`${$r} > *`)):V.appendChild($.parentElement===V?$:$.closest(`${$r} > *`))}),z.sort(($,Q)=>Q[1]-$[1]).forEach($=>{var Q;let W=(Q=I.current)==null?void 0:Q.querySelector(`${Tt}[${dt}="${encodeURIComponent($[0])}"]`);W==null||W.parentElement.appendChild(W)})}function R(){let v=K().find(V=>V.getAttribute("aria-disabled")!=="true"),z=v==null?void 0:v.getAttribute(dt);m.setState("value",z||void 0)}function O(){var v,z,V,$;if(!r.current.search||i.current.shouldFilter===!1){r.current.filtered.count=n.current.size;return}r.current.filtered.groups=new Set;let Q=0;for(let W of n.current){let Y=(z=(v=o.current.get(W))==null?void 0:v.value)!=null?z:"",ie=($=(V=o.current.get(W))==null?void 0:V.keywords)!=null?$:[],ne=x(Y,ie);r.current.filtered.items.set(W,ne),ne>0&&Q++}for(let[W,Y]of a.current)for(let ie of Y)if(r.current.filtered.items.get(ie)>0){r.current.filtered.groups.add(W);break}r.current.filtered.count=Q}function w(){var v,z,V;let $=H();$&&(((v=$.parentElement)==null?void 0:v.firstChild)===$&&((V=(z=$.closest(Tt))==null?void 0:z.querySelector(Of))==null||V.scrollIntoView({block:"nearest"})),$.scrollIntoView({block:"nearest"}))}function H(){var v;return(v=I.current)==null?void 0:v.querySelector(`${Nn}[aria-selected="true"]`)}function K(){var v;return Array.from(((v=I.current)==null?void 0:v.querySelectorAll($o))||[])}function D(v){let z=K()[v];z&&m.setState("value",z.getAttribute(dt))}function k(v){var z;let V=H(),$=K(),Q=$.findIndex(Y=>Y===V),W=$[Q+v];(z=i.current)!=null&&z.loop&&(W=Q+v<0?$[$.length-1]:Q+v===$.length?$[0]:$[Q+v]),W&&m.setState("value",W.getAttribute(dt))}function _(v){let z=H(),V=z==null?void 0:z.closest(Tt),$;for(;V&&!$;)V=v>0?Wf(V,Tt):Xf(V,Tt),$=V==null?void 0:V.querySelector($o);$?m.setState("value",$.getAttribute(dt)):k(v)}let B=()=>D(K().length-1),se=v=>{v.preventDefault(),v.metaKey?B():v.altKey?_(1):k(1)},F=v=>{v.preventDefault(),v.metaKey?D(0):v.altKey?_(-1):k(-1)};return p.createElement(Ee.div,{ref:t,tabIndex:-1,...N,"cmdk-root":"",onKeyDown:v=>{var z;if((z=N.onKeyDown)==null||z.call(N,v),!v.defaultPrevented)switch(v.key){case"n":case"j":{C&&v.ctrlKey&&se(v);break}case"ArrowDown":{se(v);break}case"p":case"k":{C&&v.ctrlKey&&F(v);break}case"ArrowUp":{F(v);break}case"Home":{v.preventDefault(),D(0);break}case"End":{v.preventDefault(),B();break}case"Enter":if(!v.nativeEvent.isComposing&&v.keyCode!==229){v.preventDefault();let V=H();if(V){let $=new Event(gn);V.dispatchEvent($)}}}}},p.createElement("label",{"cmdk-label":"",htmlFor:S.inputId,id:S.labelId,style:Qf},s),cr(e,v=>p.createElement(Ss.Provider,{value:m},p.createElement(_s.Provider,{value:S},v))))}),Mf=p.forwardRef((e,t)=>{var r,n;let a=ft(),o=p.useRef(null),l=p.useContext(Es),i=jt(),s=Cs(e),c=(n=(r=s.current)==null?void 0:r.forceMount)!=null?n:l==null?void 0:l.forceMount;gt(()=>{if(!c)return i.item(a,l==null?void 0:l.id)},[c]);let u=Ts(a,o,[e.value,e.children,o],e.keywords),d=Ln(),h=pt(P=>P.value&&P.value===u.current),f=pt(P=>c||i.filter()===!1?!0:P.search?P.filtered.items.get(a)>0:!0);p.useEffect(()=>{let P=o.current;if(!(!P||e.disabled))return P.addEventListener(gn,b),()=>P.removeEventListener(gn,b)},[f,e.onSelect,e.disabled]);function b(){var P,m;y(),(m=(P=s.current).onSelect)==null||m.call(P,u.current)}function y(){d.setState("value",u.current,!0)}if(!f)return null;let{disabled:C,value:N,onSelect:E,forceMount:j,keywords:A,...I}=e;return p.createElement(Ee.div,{ref:Rt([o,t]),...I,id:a,"cmdk-item":"",role:"option","aria-disabled":!!C,"aria-selected":!!h,"data-disabled":!!C,"data-selected":!!h,onPointerMove:C||i.getDisablePointerSelection()?void 0:y,onClick:C?void 0:b},e.children)}),Ff=p.forwardRef((e,t)=>{let{heading:r,children:n,forceMount:a,...o}=e,l=ft(),i=p.useRef(null),s=p.useRef(null),c=ft(),u=jt(),d=pt(f=>a||u.filter()===!1?!0:f.search?f.filtered.groups.has(l):!0);gt(()=>u.group(l),[]),Ts(l,i,[e.value,e.heading,s]);let h=p.useMemo(()=>({id:l,forceMount:a}),[a]);return p.createElement(Ee.div,{ref:Rt([i,t]),...o,"cmdk-group":"",role:"presentation",hidden:d?void 0:!0},r&&p.createElement("div",{ref:s,"cmdk-group-heading":"","aria-hidden":!0,id:c},r),cr(e,f=>p.createElement("div",{"cmdk-group-items":"",role:"group","aria-labelledby":r?c:void 0},p.createElement(Es.Provider,{value:h},f))))}),$f=p.forwardRef((e,t)=>{let{alwaysRender:r,...n}=e,a=p.useRef(null),o=pt(l=>!l.search);return!r&&!o?null:p.createElement(Ee.div,{ref:Rt([a,t]),...n,"cmdk-separator":"",role:"separator"})}),Hf=p.forwardRef((e,t)=>{let{onValueChange:r,...n}=e,a=e.value!=null,o=Ln(),l=pt(u=>u.search),i=pt(u=>u.value),s=jt(),c=p.useMemo(()=>{var u;let d=(u=s.listInnerRef.current)==null?void 0:u.querySelector(`${Nn}[${dt}="${encodeURIComponent(i)}"]`);return d==null?void 0:d.getAttribute("id")},[]);return p.useEffect(()=>{e.value!=null&&o.setState("search",e.value)},[e.value]),p.createElement(Ee.input,{ref:t,...n,"cmdk-input":"",autoComplete:"off",autoCorrect:"off",spellCheck:!1,"aria-autocomplete":"list",role:"combobox","aria-expanded":!0,"aria-controls":s.listId,"aria-labelledby":s.labelId,"aria-activedescendant":c,id:s.inputId,type:"text",value:a?e.value:l,onChange:u=>{a||o.setState("search",u.target.value),r==null||r(u.target.value)}})}),Bf=p.forwardRef((e,t)=>{let{children:r,label:n="Suggestions",...a}=e,o=p.useRef(null),l=p.useRef(null),i=jt();return p.useEffect(()=>{if(l.current&&o.current){let s=l.current,c=o.current,u,d=new ResizeObserver(()=>{u=requestAnimationFrame(()=>{let h=s.offsetHeight;c.style.setProperty("--cmdk-list-height",h.toFixed(1)+"px")})});return d.observe(s),()=>{cancelAnimationFrame(u),d.unobserve(s)}}},[]),p.createElement(Ee.div,{ref:Rt([o,t]),...a,"cmdk-list":"",role:"listbox","aria-label":n,id:i.listId},cr(e,s=>p.createElement("div",{ref:Rt([l,i.listInnerRef]),"cmdk-list-sizer":""},s)))}),Vf=p.forwardRef((e,t)=>{let{open:r,onOpenChange:n,overlayClassName:a,contentClassName:o,container:l,...i}=e;return p.createElement(wa,{open:r,onOpenChange:n},p.createElement(va,{container:l},p.createElement(_n,{"cmdk-overlay":"",className:a}),p.createElement(Sn,{"aria-label":e.label,"cmdk-dialog":"",className:o},p.createElement(ks,{ref:t,...i}))))}),qf=p.forwardRef((e,t)=>pt(r=>r.filtered.count===0)?p.createElement(Ee.div,{ref:t,...e,"cmdk-empty":"",role:"presentation"}):null),Uf=p.forwardRef((e,t)=>{let{progress:r,children:n,label:a="Loading...",...o}=e;return p.createElement(Ee.div,{ref:t,...o,"cmdk-loading":"",role:"progressbar","aria-valuenow":r,"aria-valuemin":0,"aria-valuemax":100,"aria-label":a},cr(e,l=>p.createElement("div",{"aria-hidden":!0},l)))}),je=Object.assign(ks,{List:Bf,Item:Mf,Input:Hf,Group:Ff,Separator:$f,Dialog:Vf,Empty:qf,Loading:Uf});function Wf(e,t){let r=e.nextElementSibling;for(;r;){if(r.matches(t))return r;r=r.nextElementSibling}}function Xf(e,t){let r=e.previousElementSibling;for(;r;){if(r.matches(t))return r;r=r.previousElementSibling}}function Cs(e){let t=p.useRef(e);return gt(()=>{t.current=e}),t}var gt=typeof window>"u"?p.useEffect:p.useLayoutEffect;function yt(e){let t=p.useRef();return t.current===void 0&&(t.current=e()),t}function Rt(e){return t=>{e.forEach(r=>{typeof r=="function"?r(t):r!=null&&(r.current=t)})}}function pt(e){let t=Ln(),r=()=>e(t.snapshot());return Df.useSyncExternalStore(t.subscribe,r,r)}function Ts(e,t,r,n=[]){let a=p.useRef(),o=jt();return gt(()=>{var l;let i=(()=>{var c;for(let u of r){if(typeof u=="string")return u.trim();if(typeof u=="object"&&"current"in u)return u.current?(c=u.current.textContent)==null?void 0:c.trim():a.current}})(),s=n.map(c=>c.trim());o.value(e,i,s),(l=t.current)==null||l.setAttribute(dt,i),a.current=i}),a}var Yf=()=>{let[e,t]=p.useState(),r=yt(()=>new Map);return gt(()=>{r.current.forEach(n=>n()),r.current=new Map},[e]),(n,a)=>{r.current.set(n,a),t({})}};function Kf(e){let t=e.type;return typeof t=="function"?t(e.props):"render"in t?t.render(e.props):e}function cr({asChild:e,children:t},r){return e&&p.isValidElement(t)?p.cloneElement(Kf(t),{ref:t.ref},r(t.props.children)):r(t)}var Qf={position:"absolute",width:"1px",height:"1px",padding:"0",margin:"-1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0"};const ur=p.forwardRef(({className:e,...t},r)=>g.jsx(je,{ref:r,className:fe("bg-popover text-popover-foreground flex h-full w-full flex-col overflow-hidden rounded-md",e),...t}));ur.displayName=je.displayName;const zn=p.forwardRef(({className:e,...t},r)=>g.jsxs("div",{className:"flex items-center border-b px-3","cmdk-input-wrapper":"",children:[g.jsx(Su,{className:"mr-2 h-4 w-4 shrink-0 opacity-50"}),g.jsx(je.Input,{ref:r,className:fe("placeholder:text-muted-foreground flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none disabled:cursor-not-allowed disabled:opacity-50",e),...t})]}));zn.displayName=je.Input.displayName;const dr=p.forwardRef(({className:e,...t},r)=>g.jsx(je.List,{ref:r,className:fe("max-h-[300px] overflow-x-hidden overflow-y-auto",e),...t}));dr.displayName=je.List.displayName;const Pn=p.forwardRef((e,t)=>g.jsx(je.Empty,{ref:t,className:"py-6 text-center text-sm",...e}));Pn.displayName=je.Empty.displayName;const Et=p.forwardRef(({className:e,...t},r)=>g.jsx(je.Group,{ref:r,className:fe("text-foreground [&_[cmdk-group-heading]]:text-muted-foreground overflow-hidden p-1 [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium",e),...t}));Et.displayName=je.Group.displayName;const Jf=p.forwardRef(({className:e,...t},r)=>g.jsx(je.Separator,{ref:r,className:fe("bg-border -mx-1 h-px",e),...t}));Jf.displayName=je.Separator.displayName;const kt=p.forwardRef(({className:e,...t},r)=>g.jsx(je.Item,{ref:r,className:fe("data-[selected='true']:bg-accent data-[selected=true]:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none select-none data-[disabled=true]:pointer-events-none data-[disabled=true]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",e),...t}));kt.displayName=je.Item.displayName;const Zf=({layout:e,autoRunFor:t,mainLayout:r})=>{const n=Be(),[a,o]=p.useState(!1),l=p.useRef(null),{t:i}=_e(),s=p.useCallback(()=>{if(n)try{const u=n.getGraph();if(!u||u.order===0)return;const d=r.positions();ha(u,d,{duration:300})}catch(u){console.error("Error updating positions:",u),l.current&&(window.clearInterval(l.current),l.current=null,o(!1))}},[n,r]),c=p.useCallback(()=>{if(a){console.log("Stopping layout animation"),l.current&&(window.clearInterval(l.current),l.current=null);try{typeof e.kill=="function"?(e.kill(),console.log("Layout algorithm killed")):typeof e.stop=="function"&&(e.stop(),console.log("Layout algorithm stopped"))}catch(u){console.error("Error stopping layout algorithm:",u)}o(!1)}else console.log("Starting layout animation"),s(),l.current=window.setInterval(()=>{s()},200),o(!0),setTimeout(()=>{if(l.current){console.log("Auto-stopping layout animation after 3 seconds"),window.clearInterval(l.current),l.current=null,o(!1);try{typeof e.kill=="function"?e.kill():typeof e.stop=="function"&&e.stop()}catch(u){console.error("Error stopping layout algorithm:",u)}}},3e3)},[a,e,s]);return p.useEffect(()=>{if(!n){console.log("No sigma instance available");return}let u=null;return t!==void 0&&t>-1&&n.getGraph().order>0&&(console.log("Auto-starting layout animation"),s(),l.current=window.setInterval(()=>{s()},200),o(!0),t>0&&(u=window.setTimeout(()=>{console.log("Auto-stopping layout animation after timeout"),l.current&&(window.clearInterval(l.current),l.current=null),o(!1)},t))),()=>{l.current&&(window.clearInterval(l.current),l.current=null),u&&window.clearTimeout(u),o(!1)}},[t,n,s]),g.jsx(be,{size:"icon",onClick:c,tooltip:i(a?"graphPanel.sideBar.layoutsControl.stopAnimation":"graphPanel.sideBar.layoutsControl.startAnimation"),variant:Ne,children:a?g.jsx(cu,{}):g.jsx(hu,{})})},eh=()=>{const e=Be(),{t}=_e(),[r,n]=p.useState("Circular"),[a,o]=p.useState(!1),l=Z.use.graphLayoutMaxIterations(),i=Ud(),s=Hd(),c=Ef(),u=bf({maxIterations:l,settings:{margin:5,expansion:1.1,gridSize:1,ratio:1,speed:3}}),d=Zd({maxIterations:l,settings:{attraction:3e-4,repulsion:.02,gravity:.02,inertia:.4,maxMove:100}}),h=ys({iterations:l}),f=wf(),b=ef(),y=uf(),C=p.useMemo(()=>({Circular:{layout:i},Circlepack:{layout:s},Random:{layout:c},Noverlaps:{layout:u,worker:f},"Force Directed":{layout:d,worker:b},"Force Atlas":{layout:h,worker:y}}),[s,i,d,h,u,c,b,f,y]),N=p.useCallback(E=>{console.debug("Running layout:",E);const{positions:j}=C[E].layout;try{const A=e.getGraph();if(!A){console.error("No graph available");return}const I=j();console.log("Positions calculated, animating nodes"),ha(A,I,{duration:400}),n(E)}catch(A){console.error("Error running layout:",A)}},[C,e]);return g.jsxs("div",{children:[g.jsx("div",{children:C[r]&&"worker"in C[r]&&g.jsx(Zf,{layout:C[r].worker,mainLayout:C[r].layout})}),g.jsx("div",{children:g.jsxs(Rn,{open:a,onOpenChange:o,children:[g.jsx(An,{asChild:!0,children:g.jsx(be,{size:"icon",variant:Ne,onClick:()=>o(E=>!E),tooltip:t("graphPanel.sideBar.layoutsControl.layoutGraph"),children:g.jsx(Jc,{})})}),g.jsx(ir,{side:"right",align:"start",sideOffset:8,collisionPadding:5,sticky:"always",className:"p-1 min-w-auto",children:g.jsx(ur,{children:g.jsx(dr,{children:g.jsx(Et,{children:Object.keys(C).map(E=>g.jsx(kt,{onSelect:()=>{N(E)},className:"cursor-pointer text-xs",children:t(`graphPanel.sideBar.layoutsControl.layouts.${E}`)},E))})})})})]})})]})},th=()=>{const e=p.useContext(Ra);if(e===void 0)throw new Error("useTheme must be used within a ThemeProvider");return e},Pt=e=>!!(e.type.startsWith("mouse")&&e.buttons!==0),rh=({disableHoverEffect:e})=>{const t=Be(),r=ga(),n=bi(),a=Z.use.graphLayoutMaxIterations(),{assign:o}=ys({iterations:a}),{theme:l}=th(),i=Z.use.enableHideUnselectedEdges(),s=Z.use.enableEdgeEvents(),c=Z.use.showEdgeLabel(),u=Z.use.showNodeLabel(),d=Z.use.minEdgeSize(),h=Z.use.maxEdgeSize(),f=te.use.selectedNode(),b=te.use.focusedNode(),y=te.use.selectedEdge(),C=te.use.focusedEdge(),N=te.use.sigmaGraph();return p.useEffect(()=>{if(N&&t){try{typeof t.setGraph=="function"?(t.setGraph(N),console.log("Binding graph to sigma instance")):(t.graph=N,console.warn("Simgma missing setGraph function, set graph property directly"))}catch(E){console.error("Error setting graph on sigma instance:",E)}o(),console.log("Initial layout applied to graph")}},[t,N,o,a]),p.useEffect(()=>{t&&(te.getState().sigmaInstance||(console.log("Setting sigma instance from GraphControl"),te.getState().setSigmaInstance(t)))},[t]),p.useEffect(()=>{const{setFocusedNode:E,setSelectedNode:j,setFocusedEdge:A,setSelectedEdge:I,clearSelection:P}=te.getState(),m={enterNode:S=>{Pt(S.event.original)||t.getGraph().hasNode(S.node)&&E(S.node)},leaveNode:S=>{Pt(S.event.original)||E(null)},clickNode:S=>{t.getGraph().hasNode(S.node)&&(j(S.node),I(null))},clickStage:()=>P()};s&&(m.clickEdge=S=>{I(S.edge),j(null)},m.enterEdge=S=>{Pt(S.event.original)||A(S.edge)},m.leaveEdge=S=>{Pt(S.event.original)||A(null)}),r(m)},[r,s,t]),p.useEffect(()=>{if(t&&N){const E=t.getGraph();let j=Number.MAX_SAFE_INTEGER,A=0;E.forEachEdge(P=>{const m=E.getEdgeAttribute(P,"originalWeight")||1;typeof m=="number"&&(j=Math.min(j,m),A=Math.max(A,m))});const I=A-j;if(I>0){const P=h-d;E.forEachEdge(m=>{const S=E.getEdgeAttribute(m,"originalWeight")||1;if(typeof S=="number"){const x=d+P*Math.pow((S-j)/I,.5);E.setEdgeAttribute(m,"size",x)}})}else E.forEachEdge(P=>{E.setEdgeAttribute(P,"size",d)});t.refresh()}},[t,N,d,h]),p.useEffect(()=>{const E=l==="dark",j=E?Vi:void 0,A=E?Xi:void 0;n({enableEdgeEvents:s,renderEdgeLabels:c,renderLabels:u,nodeReducer:(I,P)=>{const m=t.getGraph(),S={...P,highlighted:P.highlighted||!1,labelColor:j};if(!e){S.highlighted=!1;const x=b||f,T=C||y;if(x&&m.hasNode(x))try{(I===x||m.neighbors(x).includes(I))&&(S.highlighted=!0,I===f&&(S.borderColor=Wi))}catch(R){console.error("Error in nodeReducer:",R)}else if(T&&m.hasEdge(T))m.extremities(T).includes(I)&&(S.highlighted=!0,S.size=3);else return S;S.highlighted?E&&(S.labelColor=qi):S.color=Ui}return S},edgeReducer:(I,P)=>{const m=t.getGraph(),S={...P,hidden:!1,labelColor:j,color:A};if(!e){const x=b||f;if(x&&m.hasNode(x))try{i?m.extremities(I).includes(x)||(S.hidden=!0):m.extremities(I).includes(x)&&(S.color=Wn)}catch(T){console.error("Error in edgeReducer:",T)}else{const T=y&&m.hasEdge(y)?y:null,R=C&&m.hasEdge(C)?C:null;(T||R)&&(I===T?S.color=Yi:I===R?S.color=Wn:i&&(S.hidden=!0))}}return S}})},[f,b,y,C,n,t,e,l,i,s,c,u]),null},nh=()=>{const{zoomIn:e,zoomOut:t,reset:r}=da({duration:200,factor:1.5}),n=Be(),{t:a}=_e(),o=p.useCallback(()=>e(),[e]),l=p.useCallback(()=>t(),[t]),i=p.useCallback(()=>{if(n)try{n.setCustomBBox(null),n.refresh();const u=n.getGraph();if(!(u!=null&&u.order)||u.nodes().length===0){r();return}n.getCamera().animate({x:.5,y:.5,ratio:1.1},{duration:1e3})}catch(u){console.error("Error resetting zoom:",u),r()}},[n,r]),s=p.useCallback(()=>{if(!n)return;const u=n.getCamera(),h=u.angle+Math.PI/8;u.animate({angle:h},{duration:200})},[n]),c=p.useCallback(()=>{if(!n)return;const u=n.getCamera(),h=u.angle-Math.PI/8;u.animate({angle:h},{duration:200})},[n]);return g.jsxs(g.Fragment,{children:[g.jsx(be,{variant:Ne,onClick:s,tooltip:a("graphPanel.sideBar.zoomControl.rotateCamera"),size:"icon",children:g.jsx(bu,{})}),g.jsx(be,{variant:Ne,onClick:c,tooltip:a("graphPanel.sideBar.zoomControl.rotateCameraCounterClockwise"),size:"icon",children:g.jsx(vu,{})}),g.jsx(be,{variant:Ne,onClick:i,tooltip:a("graphPanel.sideBar.zoomControl.resetZoom"),size:"icon",children:g.jsx(Wc,{})}),g.jsx(be,{variant:Ne,onClick:o,tooltip:a("graphPanel.sideBar.zoomControl.zoomIn"),size:"icon",children:g.jsx(Du,{})}),g.jsx(be,{variant:Ne,onClick:l,tooltip:a("graphPanel.sideBar.zoomControl.zoomOut"),size:"icon",children:g.jsx(Gu,{})})]})},oh=()=>{const{isFullScreen:e,toggle:t}=wi(),{t:r}=_e();return g.jsx(g.Fragment,{children:e?g.jsx(be,{variant:Ne,onClick:t,tooltip:r("graphPanel.sideBar.fullScreenControl.windowed"),size:"icon",children:g.jsx(su,{})}):g.jsx(be,{variant:Ne,onClick:t,tooltip:r("graphPanel.sideBar.fullScreenControl.fullScreen"),size:"icon",children:g.jsx(ou,{})})})};var Dn="Checkbox",[ah,Hp]=xn(Dn),[sh,ih]=ah(Dn),Rs=p.forwardRef((e,t)=>{const{__scopeCheckbox:r,name:n,checked:a,defaultChecked:o,required:l,disabled:i,value:s="on",onCheckedChange:c,form:u,...d}=e,[h,f]=p.useState(null),b=Xe(t,A=>f(A)),y=p.useRef(!1),C=h?u||!!h.closest("form"):!0,[N=!1,E]=ma({prop:a,defaultProp:o,onChange:c}),j=p.useRef(N);return p.useEffect(()=>{const A=h==null?void 0:h.form;if(A){const I=()=>E(j.current);return A.addEventListener("reset",I),()=>A.removeEventListener("reset",I)}},[h,E]),g.jsxs(sh,{scope:r,state:N,disabled:i,children:[g.jsx(Ee.button,{type:"button",role:"checkbox","aria-checked":ot(N)?"mixed":N,"aria-required":l,"data-state":Is(N),"data-disabled":i?"":void 0,disabled:i,value:s,...d,ref:b,onKeyDown:ke(e.onKeyDown,A=>{A.key==="Enter"&&A.preventDefault()}),onClick:ke(e.onClick,A=>{E(I=>ot(I)?!0:!I),C&&(y.current=A.isPropagationStopped(),y.current||A.stopPropagation())})}),C&&g.jsx(lh,{control:h,bubbles:!y.current,name:n,value:s,checked:N,required:l,disabled:i,form:u,style:{transform:"translateX(-100%)"},defaultChecked:ot(o)?!1:o})]})});Rs.displayName=Dn;var As="CheckboxIndicator",js=p.forwardRef((e,t)=>{const{__scopeCheckbox:r,forceMount:n,...a}=e,o=ih(As,r);return g.jsx(_t,{present:n||ot(o.state)||o.state===!0,children:g.jsx(Ee.span,{"data-state":Is(o.state),"data-disabled":o.disabled?"":void 0,...a,ref:t,style:{pointerEvents:"none",...e.style}})})});js.displayName=As;var lh=e=>{const{control:t,checked:r,bubbles:n=!0,defaultChecked:a,...o}=e,l=p.useRef(null),i=Oi(r),s=Gi(t);p.useEffect(()=>{const u=l.current,d=window.HTMLInputElement.prototype,f=Object.getOwnPropertyDescriptor(d,"checked").set;if(i!==r&&f){const b=new Event("click",{bubbles:n});u.indeterminate=ot(r),f.call(u,ot(r)?!1:r),u.dispatchEvent(b)}},[i,r,n]);const c=p.useRef(ot(r)?!1:r);return g.jsx("input",{type:"checkbox","aria-hidden":!0,defaultChecked:a??c.current,...o,tabIndex:-1,ref:l,style:{...e.style,...s,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function ot(e){return e==="indeterminate"}function Is(e){return ot(e)?"indeterminate":e?"checked":"unchecked"}var Ns=Rs,ch=js;const Ls=p.forwardRef(({className:e,...t},r)=>g.jsx(Ns,{ref:r,className:fe("peer border-primary ring-offset-background focus-visible:ring-ring data-[state=checked]:bg-muted data-[state=checked]:text-muted-foreground h-4 w-4 shrink-0 rounded-sm border focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:g.jsx(ch,{className:fe("flex items-center justify-center text-current"),children:g.jsx(Va,{className:"h-4 w-4"})})}));Ls.displayName=Ns.displayName;var uh="Separator",Ho="horizontal",dh=["horizontal","vertical"],zs=p.forwardRef((e,t)=>{const{decorative:r,orientation:n=Ho,...a}=e,o=fh(n)?n:Ho,i=r?{role:"none"}:{"aria-orientation":o==="vertical"?o:void 0,role:"separator"};return g.jsx(Ee.div,{"data-orientation":o,...i,...a,ref:t})});zs.displayName=uh;function fh(e){return dh.includes(e)}var Ps=zs;const bt=p.forwardRef(({className:e,orientation:t="horizontal",decorative:r=!0,...n},a)=>g.jsx(Ps,{ref:a,decorative:r,orientation:t,className:fe("bg-border shrink-0",t==="horizontal"?"h-[1px] w-full":"h-full w-[1px]",e),...n}));bt.displayName=Ps.displayName;const tt=({checked:e,onCheckedChange:t,label:r})=>{const n=`checkbox-${r.toLowerCase().replace(/\s+/g,"-")}`;return g.jsxs("div",{className:"flex items-center gap-2",children:[g.jsx(Ls,{id:n,checked:e,onCheckedChange:t}),g.jsx("label",{htmlFor:n,className:"text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:r})]})},Hr=({value:e,onEditFinished:t,label:r,min:n,max:a,defaultValue:o})=>{const{t:l}=_e(),[i,s]=p.useState(e),c=`input-${r.toLowerCase().replace(/\s+/g,"-")}`;p.useEffect(()=>{s(e)},[e]);const u=p.useCallback(f=>{const b=f.target.value.trim();if(b.length===0){s(null);return}const y=Number.parseInt(b);if(!isNaN(y)&&y!==i){if(n!==void 0&&y<n||a!==void 0&&y>a)return;s(y)}},[i,n,a]),d=p.useCallback(()=>{i!==null&&e!==i&&t(i)},[e,i,t]),h=p.useCallback(()=>{o!==void 0&&e!==o&&(s(o),t(o))},[o,e,t]);return g.jsxs("div",{className:"flex flex-col gap-2",children:[g.jsx("label",{htmlFor:c,className:"text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:r}),g.jsxs("div",{className:"flex items-center gap-1",children:[g.jsx(Wt,{id:c,type:"number",value:i===null?"":i,onChange:u,className:"h-6 w-full min-w-0 pr-1",min:n,max:a,onBlur:d,onKeyDown:f=>{f.key==="Enter"&&d()}}),o!==void 0&&g.jsx(be,{variant:"ghost",size:"icon",className:"h-6 w-6 flex-shrink-0 hover:bg-muted text-muted-foreground hover:text-foreground",onClick:h,type:"button",title:l("graphPanel.sideBar.settings.resetToDefault"),children:g.jsx(Ua,{className:"h-3.5 w-3.5"})})]})]})};function hh(){const[e,t]=p.useState(!1),r=Z.use.showPropertyPanel(),n=Z.use.showNodeSearchBar(),a=Z.use.showNodeLabel(),o=Z.use.enableEdgeEvents(),l=Z.use.enableNodeDrag(),i=Z.use.enableHideUnselectedEdges(),s=Z.use.showEdgeLabel(),c=Z.use.minEdgeSize(),u=Z.use.maxEdgeSize(),d=Z.use.graphQueryMaxDepth(),h=Z.use.graphMaxNodes(),f=Z.use.backendMaxGraphNodes(),b=Z.use.graphLayoutMaxIterations(),y=Z.use.enableHealthCheck(),C=p.useCallback(()=>Z.setState(w=>({enableNodeDrag:!w.enableNodeDrag})),[]),N=p.useCallback(()=>Z.setState(w=>({enableEdgeEvents:!w.enableEdgeEvents})),[]),E=p.useCallback(()=>Z.setState(w=>({enableHideUnselectedEdges:!w.enableHideUnselectedEdges})),[]),j=p.useCallback(()=>Z.setState(w=>({showEdgeLabel:!w.showEdgeLabel})),[]),A=p.useCallback(()=>Z.setState(w=>({showPropertyPanel:!w.showPropertyPanel})),[]),I=p.useCallback(()=>Z.setState(w=>({showNodeSearchBar:!w.showNodeSearchBar})),[]),P=p.useCallback(()=>Z.setState(w=>({showNodeLabel:!w.showNodeLabel})),[]),m=p.useCallback(()=>Z.setState(w=>({enableHealthCheck:!w.enableHealthCheck})),[]),S=p.useCallback(w=>{if(w<1)return;Z.setState({graphQueryMaxDepth:w});const H=Z.getState().queryLabel;Z.getState().setQueryLabel(""),setTimeout(()=>{Z.getState().setQueryLabel(H)},300)},[]),x=p.useCallback(w=>{const H=f||1e3;w<1||w>H||Z.getState().setGraphMaxNodes(w,!0)},[f]),T=p.useCallback(w=>{w<1||Z.setState({graphLayoutMaxIterations:w})},[]),{t:R}=_e(),O=()=>t(!1);return g.jsx(g.Fragment,{children:g.jsxs(Rn,{open:e,onOpenChange:t,children:[g.jsx(An,{asChild:!0,children:g.jsx(be,{variant:Ne,tooltip:R("graphPanel.sideBar.settings.settings"),size:"icon",children:g.jsx(Cu,{})})}),g.jsx(ir,{side:"right",align:"end",sideOffset:8,collisionPadding:5,className:"p-2 max-w-[200px]",onCloseAutoFocus:w=>w.preventDefault(),children:g.jsxs("div",{className:"flex flex-col gap-2",children:[g.jsx(tt,{checked:y,onCheckedChange:m,label:R("graphPanel.sideBar.settings.healthCheck")}),g.jsx(bt,{}),g.jsx(tt,{checked:r,onCheckedChange:A,label:R("graphPanel.sideBar.settings.showPropertyPanel")}),g.jsx(tt,{checked:n,onCheckedChange:I,label:R("graphPanel.sideBar.settings.showSearchBar")}),g.jsx(bt,{}),g.jsx(tt,{checked:a,onCheckedChange:P,label:R("graphPanel.sideBar.settings.showNodeLabel")}),g.jsx(tt,{checked:l,onCheckedChange:C,label:R("graphPanel.sideBar.settings.nodeDraggable")}),g.jsx(bt,{}),g.jsx(tt,{checked:s,onCheckedChange:j,label:R("graphPanel.sideBar.settings.showEdgeLabel")}),g.jsx(tt,{checked:i,onCheckedChange:E,label:R("graphPanel.sideBar.settings.hideUnselectedEdges")}),g.jsx(tt,{checked:o,onCheckedChange:N,label:R("graphPanel.sideBar.settings.edgeEvents")}),g.jsxs("div",{className:"flex flex-col gap-2",children:[g.jsx("label",{htmlFor:"edge-size-min",className:"text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:R("graphPanel.sideBar.settings.edgeSizeRange")}),g.jsxs("div",{className:"flex items-center gap-2",children:[g.jsx(Wt,{id:"edge-size-min",type:"number",value:c,onChange:w=>{const H=Number(w.target.value);!isNaN(H)&&H>=1&&H<=u&&Z.setState({minEdgeSize:H})},className:"h-6 w-16 min-w-0 pr-1",min:1,max:Math.min(u,10)}),g.jsx("span",{children:"-"}),g.jsxs("div",{className:"flex items-center gap-1",children:[g.jsx(Wt,{id:"edge-size-max",type:"number",value:u,onChange:w=>{const H=Number(w.target.value);!isNaN(H)&&H>=c&&H>=1&&H<=10&&Z.setState({maxEdgeSize:H})},className:"h-6 w-16 min-w-0 pr-1",min:c,max:10}),g.jsx(be,{variant:"ghost",size:"icon",className:"h-6 w-6 flex-shrink-0 hover:bg-muted text-muted-foreground hover:text-foreground",onClick:()=>Z.setState({minEdgeSize:1,maxEdgeSize:5}),type:"button",title:R("graphPanel.sideBar.settings.resetToDefault"),children:g.jsx(Ua,{className:"h-3.5 w-3.5"})})]})]})]}),g.jsx(bt,{}),g.jsx(Hr,{label:R("graphPanel.sideBar.settings.maxQueryDepth"),min:1,value:d,defaultValue:3,onEditFinished:S}),g.jsx(Hr,{label:`${R("graphPanel.sideBar.settings.maxNodes")} (≤ ${f||1e3})`,min:1,max:f||1e3,value:h,defaultValue:f||1e3,onEditFinished:x}),g.jsx(Hr,{label:R("graphPanel.sideBar.settings.maxLayoutIterations"),min:1,max:30,value:b,defaultValue:15,onEditFinished:T}),g.jsx(bt,{}),g.jsx(be,{onClick:O,variant:"outline",size:"sm",className:"ml-auto px-4",children:R("graphPanel.sideBar.settings.save")})]})})]})})}const gh="ENTRIES",Ds="KEYS",Os="VALUES",Se="";class Br{constructor(t,r){const n=t._tree,a=Array.from(n.keys());this.set=t,this._type=r,this._path=a.length>0?[{node:n,keys:a}]:[]}next(){const t=this.dive();return this.backtrack(),t}dive(){if(this._path.length===0)return{done:!0,value:void 0};const{node:t,keys:r}=mt(this._path);if(mt(r)===Se)return{done:!1,value:this.result()};const n=t.get(mt(r));return this._path.push({node:n,keys:Array.from(n.keys())}),this.dive()}backtrack(){if(this._path.length===0)return;const t=mt(this._path).keys;t.pop(),!(t.length>0)&&(this._path.pop(),this.backtrack())}key(){return this.set._prefix+this._path.map(({keys:t})=>mt(t)).filter(t=>t!==Se).join("")}value(){return mt(this._path).node.get(Se)}result(){switch(this._type){case Os:return this.value();case Ds:return this.key();default:return[this.key(),this.value()]}}[Symbol.iterator](){return this}}const mt=e=>e[e.length-1],ph=(e,t,r)=>{const n=new Map;if(t===void 0)return n;const a=t.length+1,o=a+r,l=new Uint8Array(o*a).fill(r+1);for(let i=0;i<a;++i)l[i]=i;for(let i=1;i<o;++i)l[i*a]=i;return Gs(e,t,r,n,l,1,a,""),n},Gs=(e,t,r,n,a,o,l,i)=>{const s=o*l;e:for(const c of e.keys())if(c===Se){const u=a[s-1];u<=r&&n.set(i,[e.get(c),u])}else{let u=o;for(let d=0;d<c.length;++d,++u){const h=c[d],f=l*u,b=f-l;let y=a[f];const C=Math.max(0,u-r-1),N=Math.min(l-1,u+r);for(let E=C;E<N;++E){const j=h!==t[E],A=a[b+E]+ +j,I=a[b+E+1]+1,P=a[f+E]+1,m=a[f+E+1]=Math.min(A,I,P);m<y&&(y=m)}if(y>r)continue e}Gs(e.get(c),t,r,n,a,u,l,i+c)}};class nt{constructor(t=new Map,r=""){this._size=void 0,this._tree=t,this._prefix=r}atPrefix(t){if(!t.startsWith(this._prefix))throw new Error("Mismatched prefix");const[r,n]=Qt(this._tree,t.slice(this._prefix.length));if(r===void 0){const[a,o]=On(n);for(const l of a.keys())if(l!==Se&&l.startsWith(o)){const i=new Map;return i.set(l.slice(o.length),a.get(l)),new nt(i,t)}}return new nt(r,t)}clear(){this._size=void 0,this._tree.clear()}delete(t){return this._size=void 0,mh(this._tree,t)}entries(){return new Br(this,gh)}forEach(t){for(const[r,n]of this)t(r,n,this)}fuzzyGet(t,r){return ph(this._tree,t,r)}get(t){const r=pn(this._tree,t);return r!==void 0?r.get(Se):void 0}has(t){const r=pn(this._tree,t);return r!==void 0&&r.has(Se)}keys(){return new Br(this,Ds)}set(t,r){if(typeof t!="string")throw new Error("key must be a string");return this._size=void 0,Vr(this._tree,t).set(Se,r),this}get size(){if(this._size)return this._size;this._size=0;const t=this.entries();for(;!t.next().done;)this._size+=1;return this._size}update(t,r){if(typeof t!="string")throw new Error("key must be a string");this._size=void 0;const n=Vr(this._tree,t);return n.set(Se,r(n.get(Se))),this}fetch(t,r){if(typeof t!="string")throw new Error("key must be a string");this._size=void 0;const n=Vr(this._tree,t);let a=n.get(Se);return a===void 0&&n.set(Se,a=r()),a}values(){return new Br(this,Os)}[Symbol.iterator](){return this.entries()}static from(t){const r=new nt;for(const[n,a]of t)r.set(n,a);return r}static fromObject(t){return nt.from(Object.entries(t))}}const Qt=(e,t,r=[])=>{if(t.length===0||e==null)return[e,r];for(const n of e.keys())if(n!==Se&&t.startsWith(n))return r.push([e,n]),Qt(e.get(n),t.slice(n.length),r);return r.push([e,t]),Qt(void 0,"",r)},pn=(e,t)=>{if(t.length===0||e==null)return e;for(const r of e.keys())if(r!==Se&&t.startsWith(r))return pn(e.get(r),t.slice(r.length))},Vr=(e,t)=>{const r=t.length;e:for(let n=0;e&&n<r;){for(const o of e.keys())if(o!==Se&&t[n]===o[0]){const l=Math.min(r-n,o.length);let i=1;for(;i<l&&t[n+i]===o[i];)++i;const s=e.get(o);if(i===o.length)e=s;else{const c=new Map;c.set(o.slice(i),s),e.set(t.slice(n,n+i),c),e.delete(o),e=c}n+=i;continue e}const a=new Map;return e.set(t.slice(n),a),a}return e},mh=(e,t)=>{const[r,n]=Qt(e,t);if(r!==void 0){if(r.delete(Se),r.size===0)Ms(n);else if(r.size===1){const[a,o]=r.entries().next().value;Fs(n,a,o)}}},Ms=e=>{if(e.length===0)return;const[t,r]=On(e);if(t.delete(r),t.size===0)Ms(e.slice(0,-1));else if(t.size===1){const[n,a]=t.entries().next().value;n!==Se&&Fs(e.slice(0,-1),n,a)}},Fs=(e,t,r)=>{if(e.length===0)return;const[n,a]=On(e);n.set(a+t,r),n.delete(a)},On=e=>e[e.length-1],Gn="or",$s="and",vh="and_not";class at{constructor(t){if((t==null?void 0:t.fields)==null)throw new Error('MiniSearch: option "fields" must be provided');const r=t.autoVacuum==null||t.autoVacuum===!0?Wr:t.autoVacuum;this._options={...Ur,...t,autoVacuum:r,searchOptions:{...Bo,...t.searchOptions||{}},autoSuggestOptions:{..._h,...t.autoSuggestOptions||{}}},this._index=new nt,this._documentCount=0,this._documentIds=new Map,this._idToShortId=new Map,this._fieldIds={},this._fieldLength=new Map,this._avgFieldLength=[],this._nextId=0,this._storedFields=new Map,this._dirtCount=0,this._currentVacuum=null,this._enqueuedVacuum=null,this._enqueuedVacuumConditions=vn,this.addFields(this._options.fields)}add(t){const{extractField:r,tokenize:n,processTerm:a,fields:o,idField:l}=this._options,i=r(t,l);if(i==null)throw new Error(`MiniSearch: document does not have ID field "${l}"`);if(this._idToShortId.has(i))throw new Error(`MiniSearch: duplicate ID ${i}`);const s=this.addDocumentId(i);this.saveStoredFields(s,t);for(const c of o){const u=r(t,c);if(u==null)continue;const d=n(u.toString(),c),h=this._fieldIds[c],f=new Set(d).size;this.addFieldLength(s,h,this._documentCount-1,f);for(const b of d){const y=a(b,c);if(Array.isArray(y))for(const C of y)this.addTerm(h,s,C);else y&&this.addTerm(h,s,y)}}}addAll(t){for(const r of t)this.add(r)}addAllAsync(t,r={}){const{chunkSize:n=10}=r,a={chunk:[],promise:Promise.resolve()},{chunk:o,promise:l}=t.reduce(({chunk:i,promise:s},c,u)=>(i.push(c),(u+1)%n===0?{chunk:[],promise:s.then(()=>new Promise(d=>setTimeout(d,0))).then(()=>this.addAll(i))}:{chunk:i,promise:s}),a);return l.then(()=>this.addAll(o))}remove(t){const{tokenize:r,processTerm:n,extractField:a,fields:o,idField:l}=this._options,i=a(t,l);if(i==null)throw new Error(`MiniSearch: document does not have ID field "${l}"`);const s=this._idToShortId.get(i);if(s==null)throw new Error(`MiniSearch: cannot remove document with ID ${i}: it is not in the index`);for(const c of o){const u=a(t,c);if(u==null)continue;const d=r(u.toString(),c),h=this._fieldIds[c],f=new Set(d).size;this.removeFieldLength(s,h,this._documentCount,f);for(const b of d){const y=n(b,c);if(Array.isArray(y))for(const C of y)this.removeTerm(h,s,C);else y&&this.removeTerm(h,s,y)}}this._storedFields.delete(s),this._documentIds.delete(s),this._idToShortId.delete(i),this._fieldLength.delete(s),this._documentCount-=1}removeAll(t){if(t)for(const r of t)this.remove(r);else{if(arguments.length>0)throw new Error("Expected documents to be present. Omit the argument to remove all documents.");this._index=new nt,this._documentCount=0,this._documentIds=new Map,this._idToShortId=new Map,this._fieldLength=new Map,this._avgFieldLength=[],this._storedFields=new Map,this._nextId=0}}discard(t){const r=this._idToShortId.get(t);if(r==null)throw new Error(`MiniSearch: cannot discard document with ID ${t}: it is not in the index`);this._idToShortId.delete(t),this._documentIds.delete(r),this._storedFields.delete(r),(this._fieldLength.get(r)||[]).forEach((n,a)=>{this.removeFieldLength(r,a,this._documentCount,n)}),this._fieldLength.delete(r),this._documentCount-=1,this._dirtCount+=1,this.maybeAutoVacuum()}maybeAutoVacuum(){if(this._options.autoVacuum===!1)return;const{minDirtFactor:t,minDirtCount:r,batchSize:n,batchWait:a}=this._options.autoVacuum;this.conditionalVacuum({batchSize:n,batchWait:a},{minDirtCount:r,minDirtFactor:t})}discardAll(t){const r=this._options.autoVacuum;try{this._options.autoVacuum=!1;for(const n of t)this.discard(n)}finally{this._options.autoVacuum=r}this.maybeAutoVacuum()}replace(t){const{idField:r,extractField:n}=this._options,a=n(t,r);this.discard(a),this.add(t)}vacuum(t={}){return this.conditionalVacuum(t)}conditionalVacuum(t,r){return this._currentVacuum?(this._enqueuedVacuumConditions=this._enqueuedVacuumConditions&&r,this._enqueuedVacuum!=null?this._enqueuedVacuum:(this._enqueuedVacuum=this._currentVacuum.then(()=>{const n=this._enqueuedVacuumConditions;return this._enqueuedVacuumConditions=vn,this.performVacuuming(t,n)}),this._enqueuedVacuum)):this.vacuumConditionsMet(r)===!1?Promise.resolve():(this._currentVacuum=this.performVacuuming(t),this._currentVacuum)}async performVacuuming(t,r){const n=this._dirtCount;if(this.vacuumConditionsMet(r)){const a=t.batchSize||mn.batchSize,o=t.batchWait||mn.batchWait;let l=1;for(const[i,s]of this._index){for(const[c,u]of s)for(const[d]of u)this._documentIds.has(d)||(u.size<=1?s.delete(c):u.delete(d));this._index.get(i).size===0&&this._index.delete(i),l%a===0&&await new Promise(c=>setTimeout(c,o)),l+=1}this._dirtCount-=n}await null,this._currentVacuum=this._enqueuedVacuum,this._enqueuedVacuum=null}vacuumConditionsMet(t){if(t==null)return!0;let{minDirtCount:r,minDirtFactor:n}=t;return r=r||Wr.minDirtCount,n=n||Wr.minDirtFactor,this.dirtCount>=r&&this.dirtFactor>=n}get isVacuuming(){return this._currentVacuum!=null}get dirtCount(){return this._dirtCount}get dirtFactor(){return this._dirtCount/(1+this._documentCount+this._dirtCount)}has(t){return this._idToShortId.has(t)}getStoredFields(t){const r=this._idToShortId.get(t);if(r!=null)return this._storedFields.get(r)}search(t,r={}){const{searchOptions:n}=this._options,a={...n,...r},o=this.executeQuery(t,r),l=[];for(const[i,{score:s,terms:c,match:u}]of o){const d=c.length||1,h={id:this._documentIds.get(i),score:s*d,terms:Object.keys(u),queryTerms:c,match:u};Object.assign(h,this._storedFields.get(i)),(a.filter==null||a.filter(h))&&l.push(h)}return t===at.wildcard&&a.boostDocument==null||l.sort(qo),l}autoSuggest(t,r={}){r={...this._options.autoSuggestOptions,...r};const n=new Map;for(const{score:o,terms:l}of this.search(t,r)){const i=l.join(" "),s=n.get(i);s!=null?(s.score+=o,s.count+=1):n.set(i,{score:o,terms:l,count:1})}const a=[];for(const[o,{score:l,terms:i,count:s}]of n)a.push({suggestion:o,terms:i,score:l/s});return a.sort(qo),a}get documentCount(){return this._documentCount}get termCount(){return this._index.size}static loadJSON(t,r){if(r==null)throw new Error("MiniSearch: loadJSON should be given the same options used when serializing the index");return this.loadJS(JSON.parse(t),r)}static async loadJSONAsync(t,r){if(r==null)throw new Error("MiniSearch: loadJSON should be given the same options used when serializing the index");return this.loadJSAsync(JSON.parse(t),r)}static getDefault(t){if(Ur.hasOwnProperty(t))return qr(Ur,t);throw new Error(`MiniSearch: unknown option "${t}"`)}static loadJS(t,r){const{index:n,documentIds:a,fieldLength:o,storedFields:l,serializationVersion:i}=t,s=this.instantiateMiniSearch(t,r);s._documentIds=Dt(a),s._fieldLength=Dt(o),s._storedFields=Dt(l);for(const[c,u]of s._documentIds)s._idToShortId.set(u,c);for(const[c,u]of n){const d=new Map;for(const h of Object.keys(u)){let f=u[h];i===1&&(f=f.ds),d.set(parseInt(h,10),Dt(f))}s._index.set(c,d)}return s}static async loadJSAsync(t,r){const{index:n,documentIds:a,fieldLength:o,storedFields:l,serializationVersion:i}=t,s=this.instantiateMiniSearch(t,r);s._documentIds=await Ot(a),s._fieldLength=await Ot(o),s._storedFields=await Ot(l);for(const[u,d]of s._documentIds)s._idToShortId.set(d,u);let c=0;for(const[u,d]of n){const h=new Map;for(const f of Object.keys(d)){let b=d[f];i===1&&(b=b.ds),h.set(parseInt(f,10),await Ot(b))}++c%1e3===0&&await Hs(0),s._index.set(u,h)}return s}static instantiateMiniSearch(t,r){const{documentCount:n,nextId:a,fieldIds:o,averageFieldLength:l,dirtCount:i,serializationVersion:s}=t;if(s!==1&&s!==2)throw new Error("MiniSearch: cannot deserialize an index created with an incompatible version");const c=new at(r);return c._documentCount=n,c._nextId=a,c._idToShortId=new Map,c._fieldIds=o,c._avgFieldLength=l,c._dirtCount=i||0,c._index=new nt,c}executeQuery(t,r={}){if(t===at.wildcard)return this.executeWildcardQuery(r);if(typeof t!="string"){const h={...r,...t,queries:void 0},f=t.queries.map(b=>this.executeQuery(b,h));return this.combineResults(f,h.combineWith)}const{tokenize:n,processTerm:a,searchOptions:o}=this._options,l={tokenize:n,processTerm:a,...o,...r},{tokenize:i,processTerm:s}=l,d=i(t).flatMap(h=>s(h)).filter(h=>!!h).map(xh(l)).map(h=>this.executeQuerySpec(h,l));return this.combineResults(d,l.combineWith)}executeQuerySpec(t,r){const n={...this._options.searchOptions,...r},a=(n.fields||this._options.fields).reduce((y,C)=>({...y,[C]:qr(n.boost,C)||1}),{}),{boostDocument:o,weights:l,maxFuzzy:i,bm25:s}=n,{fuzzy:c,prefix:u}={...Bo.weights,...l},d=this._index.get(t.term),h=this.termResults(t.term,t.term,1,t.termBoost,d,a,o,s);let f,b;if(t.prefix&&(f=this._index.atPrefix(t.term)),t.fuzzy){const y=t.fuzzy===!0?.2:t.fuzzy,C=y<1?Math.min(i,Math.round(t.term.length*y)):y;C&&(b=this._index.fuzzyGet(t.term,C))}if(f)for(const[y,C]of f){const N=y.length-t.term.length;if(!N)continue;b==null||b.delete(y);const E=u*y.length/(y.length+.3*N);this.termResults(t.term,y,E,t.termBoost,C,a,o,s,h)}if(b)for(const y of b.keys()){const[C,N]=b.get(y);if(!N)continue;const E=c*y.length/(y.length+N);this.termResults(t.term,y,E,t.termBoost,C,a,o,s,h)}return h}executeWildcardQuery(t){const r=new Map,n={...this._options.searchOptions,...t};for(const[a,o]of this._documentIds){const l=n.boostDocument?n.boostDocument(o,"",this._storedFields.get(a)):1;r.set(a,{score:l,terms:[],match:{}})}return r}combineResults(t,r=Gn){if(t.length===0)return new Map;const n=r.toLowerCase(),a=yh[n];if(!a)throw new Error(`Invalid combination operator: ${r}`);return t.reduce(a)||new Map}toJSON(){const t=[];for(const[r,n]of this._index){const a={};for(const[o,l]of n)a[o]=Object.fromEntries(l);t.push([r,a])}return{documentCount:this._documentCount,nextId:this._nextId,documentIds:Object.fromEntries(this._documentIds),fieldIds:this._fieldIds,fieldLength:Object.fromEntries(this._fieldLength),averageFieldLength:this._avgFieldLength,storedFields:Object.fromEntries(this._storedFields),dirtCount:this._dirtCount,index:t,serializationVersion:2}}termResults(t,r,n,a,o,l,i,s,c=new Map){if(o==null)return c;for(const u of Object.keys(l)){const d=l[u],h=this._fieldIds[u],f=o.get(h);if(f==null)continue;let b=f.size;const y=this._avgFieldLength[h];for(const C of f.keys()){if(!this._documentIds.has(C)){this.removeTerm(h,C,r),b-=1;continue}const N=i?i(this._documentIds.get(C),r,this._storedFields.get(C)):1;if(!N)continue;const E=f.get(C),j=this._fieldLength.get(C)[h],A=wh(E,b,this._documentCount,j,y,s),I=n*a*d*N*A,P=c.get(C);if(P){P.score+=I,Sh(P.terms,t);const m=qr(P.match,r);m?m.push(u):P.match[r]=[u]}else c.set(C,{score:I,terms:[t],match:{[r]:[u]}})}}return c}addTerm(t,r,n){const a=this._index.fetch(n,Uo);let o=a.get(t);if(o==null)o=new Map,o.set(r,1),a.set(t,o);else{const l=o.get(r);o.set(r,(l||0)+1)}}removeTerm(t,r,n){if(!this._index.has(n)){this.warnDocumentChanged(r,t,n);return}const a=this._index.fetch(n,Uo),o=a.get(t);o==null||o.get(r)==null?this.warnDocumentChanged(r,t,n):o.get(r)<=1?o.size<=1?a.delete(t):o.delete(r):o.set(r,o.get(r)-1),this._index.get(n).size===0&&this._index.delete(n)}warnDocumentChanged(t,r,n){for(const a of Object.keys(this._fieldIds))if(this._fieldIds[a]===r){this._options.logger("warn",`MiniSearch: document with ID ${this._documentIds.get(t)} has changed before removal: term "${n}" was not present in field "${a}". Removing a document after it has changed can corrupt the index!`,"version_conflict");return}}addDocumentId(t){const r=this._nextId;return this._idToShortId.set(t,r),this._documentIds.set(r,t),this._documentCount+=1,this._nextId+=1,r}addFields(t){for(let r=0;r<t.length;r++)this._fieldIds[t[r]]=r}addFieldLength(t,r,n,a){let o=this._fieldLength.get(t);o==null&&this._fieldLength.set(t,o=[]),o[r]=a;const i=(this._avgFieldLength[r]||0)*n+a;this._avgFieldLength[r]=i/(n+1)}removeFieldLength(t,r,n,a){if(n===1){this._avgFieldLength[r]=0;return}const o=this._avgFieldLength[r]*n-a;this._avgFieldLength[r]=o/(n-1)}saveStoredFields(t,r){const{storeFields:n,extractField:a}=this._options;if(n==null||n.length===0)return;let o=this._storedFields.get(t);o==null&&this._storedFields.set(t,o={});for(const l of n){const i=a(r,l);i!==void 0&&(o[l]=i)}}}at.wildcard=Symbol("*");const qr=(e,t)=>Object.prototype.hasOwnProperty.call(e,t)?e[t]:void 0,yh={[Gn]:(e,t)=>{for(const r of t.keys()){const n=e.get(r);if(n==null)e.set(r,t.get(r));else{const{score:a,terms:o,match:l}=t.get(r);n.score=n.score+a,n.match=Object.assign(n.match,l),Vo(n.terms,o)}}return e},[$s]:(e,t)=>{const r=new Map;for(const n of t.keys()){const a=e.get(n);if(a==null)continue;const{score:o,terms:l,match:i}=t.get(n);Vo(a.terms,l),r.set(n,{score:a.score+o,terms:a.terms,match:Object.assign(a.match,i)})}return r},[vh]:(e,t)=>{for(const r of t.keys())e.delete(r);return e}},bh={k:1.2,b:.7,d:.5},wh=(e,t,r,n,a,o)=>{const{k:l,b:i,d:s}=o;return Math.log(1+(r-t+.5)/(t+.5))*(s+e*(l+1)/(e+l*(1-i+i*n/a)))},xh=e=>(t,r,n)=>{const a=typeof e.fuzzy=="function"?e.fuzzy(t,r,n):e.fuzzy||!1,o=typeof e.prefix=="function"?e.prefix(t,r,n):e.prefix===!0,l=typeof e.boostTerm=="function"?e.boostTerm(t,r,n):1;return{term:t,fuzzy:a,prefix:o,termBoost:l}},Ur={idField:"id",extractField:(e,t)=>e[t],tokenize:e=>e.split(Eh),processTerm:e=>e.toLowerCase(),fields:void 0,searchOptions:void 0,storeFields:[],logger:(e,t)=>{typeof(console==null?void 0:console[e])=="function"&&console[e](t)},autoVacuum:!0},Bo={combineWith:Gn,prefix:!1,fuzzy:!1,maxFuzzy:6,boost:{},weights:{fuzzy:.45,prefix:.375},bm25:bh},_h={combineWith:$s,prefix:(e,t,r)=>t===r.length-1},mn={batchSize:1e3,batchWait:10},vn={minDirtFactor:.1,minDirtCount:20},Wr={...mn,...vn},Sh=(e,t)=>{e.includes(t)||e.push(t)},Vo=(e,t)=>{for(const r of t)e.includes(r)||e.push(r)},qo=({score:e},{score:t})=>t-e,Uo=()=>new Map,Dt=e=>{const t=new Map;for(const r of Object.keys(e))t.set(parseInt(r,10),e[r]);return t},Ot=async e=>{const t=new Map;let r=0;for(const n of Object.keys(e))t.set(parseInt(n,10),e[n]),++r%1e3===0&&await Hs(0);return t},Hs=e=>new Promise(t=>setTimeout(t,e)),Eh=/[\n\r\p{Z}\p{P}]+/u,kh={index:new at({fields:[]})};p.createContext(kh);const yn=({label:e,color:t,hidden:r,labels:n={}})=>X.createElement("div",{className:"node"},X.createElement("span",{className:"render "+(r?"circle":"disc"),style:{backgroundColor:t||"#000"}}),X.createElement("span",{className:`label ${r?"text-muted":""} ${e?"":"text-italic"}`},e||n.no_label||"No label")),Ch=({id:e,labels:t})=>{const r=Be(),n=p.useMemo(()=>{const a=r.getGraph().getNodeAttributes(e),o=r.getSetting("nodeReducer");return Object.assign(Object.assign({color:r.getSetting("defaultNodeColor")},a),o?o(e,a):{})},[r,e]);return X.createElement(yn,Object.assign({},n,{labels:t}))},Th=({label:e,color:t,source:r,target:n,hidden:a,directed:o,labels:l={}})=>X.createElement("div",{className:"edge"},X.createElement(yn,Object.assign({},r,{labels:l})),X.createElement("div",{className:"body"},X.createElement("div",{className:"render"},X.createElement("span",{className:a?"dotted":"dash",style:{borderColor:t||"#000"}})," ",o&&X.createElement("span",{className:"arrow",style:{borderTopColor:t||"#000"}})),X.createElement("span",{className:`label ${a?"text-muted":""} ${e?"":"fst-italic"}`},e||l.no_label||"No label")),X.createElement(yn,Object.assign({},n,{labels:l}))),Rh=({id:e,labels:t})=>{const r=Be(),n=p.useMemo(()=>{const a=r.getGraph().getEdgeAttributes(e),o=r.getSetting("nodeReducer"),l=r.getSetting("edgeReducer"),i=r.getGraph().getNodeAttributes(r.getGraph().source(e)),s=r.getGraph().getNodeAttributes(r.getGraph().target(e));return Object.assign(Object.assign(Object.assign({color:r.getSetting("defaultEdgeColor"),directed:r.getGraph().isDirected(e)},a),l?l(e,a):{}),{source:Object.assign(Object.assign({color:r.getSetting("defaultNodeColor")},i),o?o(e,i):{}),target:Object.assign(Object.assign({color:r.getSetting("defaultNodeColor")},s),o?o(e,s):{})})},[r,e]);return X.createElement(Th,Object.assign({},n,{labels:t}))};function Bs(e,t){const[r,n]=p.useState(e);return p.useEffect(()=>{const a=setTimeout(()=>{n(e)},t);return()=>{clearTimeout(a)}},[e,t]),r}function Ah({fetcher:e,preload:t,filterFn:r,renderOption:n,getOptionValue:a,notFound:o,loadingSkeleton:l,label:i,placeholder:s="Select...",value:c,onChange:u,onFocus:d,disabled:h=!1,className:f,noResultsMessage:b}){const[y,C]=p.useState(!1),[N,E]=p.useState(!1),[j,A]=p.useState([]),[I,P]=p.useState(!1),[m,S]=p.useState(null),[x,T]=p.useState(""),R=Bs(x,t?0:150),O=p.useRef(null);p.useEffect(()=>{C(!0)},[]),p.useEffect(()=>{const k=_=>{O.current&&!O.current.contains(_.target)&&N&&E(!1)};return document.addEventListener("mousedown",k),()=>{document.removeEventListener("mousedown",k)}},[N]);const w=p.useCallback(async k=>{try{P(!0),S(null);const _=await e(k);A(_)}catch(_){S(_ instanceof Error?_.message:"Failed to fetch options")}finally{P(!1)}},[e]);p.useEffect(()=>{y&&(t?R&&A(k=>k.filter(_=>r?r(_,R):!0)):w(R))},[y,R,t,r,w]),p.useEffect(()=>{!y||!c||w(c)},[y,c,w]);const H=p.useCallback(k=>{u(k),requestAnimationFrame(()=>{const _=document.activeElement;_==null||_.blur(),E(!1)})},[u]),K=p.useCallback(()=>{E(!0),w(x)},[x,w]),D=p.useCallback(k=>{k.target.closest(".cmd-item")&&k.preventDefault()},[]);return g.jsx("div",{ref:O,className:fe(h&&"cursor-not-allowed opacity-50",f),onMouseDown:D,children:g.jsxs(ur,{shouldFilter:!1,className:"bg-transparent",children:[g.jsxs("div",{children:[g.jsx(zn,{placeholder:s,value:x,className:"max-h-8",onFocus:K,onValueChange:k=>{T(k),N||E(!0)}}),I&&g.jsx("div",{className:"absolute top-1/2 right-2 flex -translate-y-1/2 transform items-center",children:g.jsx(qa,{className:"h-4 w-4 animate-spin"})})]}),g.jsxs(dr,{hidden:!N,children:[m&&g.jsx("div",{className:"text-destructive p-4 text-center",children:m}),I&&j.length===0&&(l||g.jsx(jh,{})),!I&&!m&&j.length===0&&(o||g.jsx(Pn,{children:b??`No ${i.toLowerCase()} found.`})),g.jsx(Et,{children:j.map((k,_)=>g.jsxs(X.Fragment,{children:[g.jsx(kt,{value:a(k),onSelect:H,onMouseMove:()=>d(a(k)),className:"truncate cmd-item",children:n(k)},a(k)+`${_}`),_!==j.length-1&&g.jsx("div",{className:"bg-foreground/10 h-[1px]"},`divider-${_}`)]},a(k)+`-fragment-${_}`))})]})]})})}function jh(){return g.jsx(Et,{children:g.jsx(kt,{disabled:!0,children:g.jsxs("div",{className:"flex w-full items-center gap-2",children:[g.jsx("div",{className:"bg-muted h-6 w-6 animate-pulse rounded-full"}),g.jsxs("div",{className:"flex flex-1 flex-col gap-1",children:[g.jsx("div",{className:"bg-muted h-4 w-24 animate-pulse rounded"}),g.jsx("div",{className:"bg-muted h-3 w-16 animate-pulse rounded"})]})]})})})}const Xr="__message_item",Ih=({id:e})=>{const t=te.use.sigmaGraph();return t!=null&&t.hasNode(e)?g.jsx(Ch,{id:e}):null};function Nh(e){return g.jsxs("div",{children:[e.type==="nodes"&&g.jsx(Ih,{id:e.id}),e.type==="edges"&&g.jsx(Rh,{id:e.id}),e.type==="message"&&g.jsx("div",{children:e.message})]})}const Lh=({onChange:e,onFocus:t,value:r})=>{const{t:n}=_e(),a=te.use.sigmaGraph(),o=te.use.searchEngine();p.useEffect(()=>{a&&te.getState().resetSearchEngine()},[a]),p.useEffect(()=>{if(!a||a.nodes().length===0||o)return;const i=new at({idField:"id",fields:["label"],searchOptions:{prefix:!0,fuzzy:.2,boost:{label:2}}}),s=a.nodes().map(c=>({id:c,label:a.getNodeAttribute(c,"label")}));i.addAll(s),te.getState().setSearchEngine(i)},[a,o]);const l=p.useCallback(async i=>{if(t&&t(null),!a||!o)return[];if(a.nodes().length===0)return[];if(!i)return a.nodes().filter(u=>a.hasNode(u)).slice(0,It).map(u=>({id:u,type:"nodes"}));let s=o.search(i).filter(c=>a.hasNode(c.id)).map(c=>({id:c.id,type:"nodes"}));if(s.length<5){const c=new Set(s.map(d=>d.id)),u=a.nodes().filter(d=>{if(c.has(d))return!1;const h=a.getNodeAttribute(d,"label");return h&&typeof h=="string"&&!h.toLowerCase().startsWith(i.toLowerCase())&&h.toLowerCase().includes(i.toLowerCase())}).map(d=>({id:d,type:"nodes"}));s=[...s,...u]}return s.length<=It?s:[...s.slice(0,It),{type:"message",id:Xr,message:n("graphPanel.search.message",{count:s.length-It})}]},[a,o,t,n]);return g.jsx(Ah,{className:"bg-background/60 w-24 rounded-xl border-1 opacity-60 backdrop-blur-lg transition-all hover:w-fit hover:opacity-100",fetcher:l,renderOption:Nh,getOptionValue:i=>i.id,value:r&&r.type!=="message"?r.id:null,onChange:i=>{i!==Xr&&e(i?{id:i,type:"nodes"}:null)},onFocus:i=>{i!==Xr&&t&&t(i?{id:i,type:"nodes"}:null)},label:"item",placeholder:n("graphPanel.search.placeholder")})},zh=({...e})=>g.jsx(Lh,{...e});function Ph({fetcher:e,preload:t,filterFn:r,renderOption:n,getOptionValue:a,getDisplayValue:o,notFound:l,loadingSkeleton:i,label:s,placeholder:c="Select...",value:u,onChange:d,disabled:h=!1,className:f,triggerClassName:b,searchInputClassName:y,noResultsMessage:C,triggerTooltip:N,clearable:E=!0}){const[j,A]=p.useState(!1),[I,P]=p.useState(!1),[m,S]=p.useState([]),[x,T]=p.useState(!1),[R,O]=p.useState(null),[w,H]=p.useState(u),[K,D]=p.useState(null),[k,_]=p.useState(""),B=Bs(k,t?0:150),[se,F]=p.useState([]),[v,z]=p.useState(null);p.useEffect(()=>{A(!0),H(u)},[u]),p.useEffect(()=>{u&&(!m.length||!K)?z(g.jsx("div",{children:u})):K&&z(null)},[u,m.length,K]),p.useEffect(()=>{if(u&&m.length>0){const $=m.find(Q=>a(Q)===u);$&&D($)}},[u,m,a]),p.useEffect(()=>{j||(async()=>{try{T(!0),O(null);const Q=await e(u);F(Q),S(Q)}catch(Q){O(Q instanceof Error?Q.message:"Failed to fetch options")}finally{T(!1)}})()},[j,e,u]),p.useEffect(()=>{const $=async()=>{try{T(!0),O(null);const Q=await e(B);F(Q),S(Q)}catch(Q){O(Q instanceof Error?Q.message:"Failed to fetch options")}finally{T(!1)}};j&&t?t&&S(B?se.filter(Q=>r?r(Q,B):!0):se):$()},[e,B,j,t,r]);const V=p.useCallback($=>{const Q=E&&$===w?"":$;H(Q),D(m.find(W=>a(W)===Q)||null),d(Q),P(!1)},[w,d,E,m,a]);return g.jsxs(Rn,{open:I,onOpenChange:P,children:[g.jsx(An,{asChild:!0,children:g.jsxs(be,{variant:"outline",role:"combobox","aria-expanded":I,className:fe("justify-between",h&&"cursor-not-allowed opacity-50",b),disabled:h,tooltip:N,side:"bottom",children:[u==="*"?g.jsx("div",{children:"*"}):K?o(K):v||c,g.jsx($c,{className:"opacity-50",size:10})]})}),g.jsx(ir,{className:fe("p-0",f),onCloseAutoFocus:$=>$.preventDefault(),align:"start",sideOffset:8,collisionPadding:5,children:g.jsxs(ur,{shouldFilter:!1,children:[g.jsxs("div",{className:"relative w-full border-b",children:[g.jsx(zn,{placeholder:`Search ${s.toLowerCase()}...`,value:k,onValueChange:$=>{_($)},className:y}),x&&m.length>0&&g.jsx("div",{className:"absolute top-1/2 right-2 flex -translate-y-1/2 transform items-center",children:g.jsx(qa,{className:"h-4 w-4 animate-spin"})})]}),g.jsxs(dr,{children:[R&&g.jsx("div",{className:"text-destructive p-4 text-center",children:R}),x&&m.length===0&&(i||g.jsx(Dh,{})),!x&&!R&&m.length===0&&(l||g.jsx(Pn,{children:C??`No ${s.toLowerCase()} found.`})),g.jsx(Et,{children:m.map(($,Q)=>{const W=a($),Y=`option-${Q}-${W.length}`;return g.jsxs(kt,{value:Y,onSelect:ie=>{const ne=parseInt(ie.split("-")[1]),ae=a(m[ne]);console.log(`CommandItem onSelect: safeValue='${ie}', originalValue='${ae}' (length: ${ae.length})`),V(ae)},className:"truncate",children:[n($),g.jsx(Va,{className:fe("ml-auto h-3 w-3",w===W?"opacity-100":"opacity-0")})]},W)})})]})]})})]})}function Dh(){return g.jsx(Et,{children:g.jsx(kt,{disabled:!0,children:g.jsxs("div",{className:"flex w-full items-center gap-2",children:[g.jsx("div",{className:"bg-muted h-6 w-6 animate-pulse rounded-full"}),g.jsxs("div",{className:"flex flex-1 flex-col gap-1",children:[g.jsx("div",{className:"bg-muted h-4 w-24 animate-pulse rounded"}),g.jsx("div",{className:"bg-muted h-3 w-16 animate-pulse rounded"})]})]})})})}const Oh=()=>{const{t:e}=_e(),t=Z.use.queryLabel(),r=te.use.allDatabaseLabels(),n=te.use.labelsFetchAttempted(),a=p.useCallback(()=>{const i=new at({idField:"id",fields:["value"],searchOptions:{prefix:!0,fuzzy:.2,boost:{label:2}}}),s=r.map((c,u)=>({id:u,value:c}));return i.addAll(s),{labels:r,searchEngine:i}},[r]),o=p.useCallback(async i=>{const{labels:s,searchEngine:c}=a();let u=s;if(i&&(u=c.search(i).map(d=>s[d.id]),u.length<15)){const d=new Set(u),h=s.filter(f=>d.has(f)?!1:f&&typeof f=="string"&&!f.toLowerCase().startsWith(i.toLowerCase())&&f.toLowerCase().includes(i.toLowerCase()));u=[...u,...h]}return u.length<=Xn?u:[...u.slice(0,Xn),"..."]},[a]);p.useEffect(()=>{n&&(r.length>1?t&&t!=="*"&&!r.includes(t)?(console.log(`Label "${t}" not in available labels, setting to "*"`),Z.getState().setQueryLabel("*")):console.log(`Label "${t}" is valid`):t&&r.length<=1&&t&&t!=="*"&&(console.log("Available labels list is empty, setting label to empty"),Z.getState().setQueryLabel("")),te.getState().setLabelsFetchAttempted(!1))},[r,t,n]);const l=p.useCallback(()=>{te.getState().setLabelsFetchAttempted(!1),te.getState().setGraphDataFetchAttempted(!1),te.getState().setLastSuccessfulQueryLabel("");const i=Z.getState().queryLabel;i?(Z.getState().setQueryLabel(""),setTimeout(()=>{Z.getState().setQueryLabel(i)},0)):Z.getState().setQueryLabel("*")},[]);return g.jsxs("div",{className:"flex items-center",children:[g.jsx(be,{size:"icon",variant:Ne,onClick:l,tooltip:e("graphPanel.graphLabels.refreshTooltip"),className:"mr-2",children:g.jsx(pu,{className:"h-4 w-4"})}),g.jsx(Ph,{className:"min-w-[300px]",triggerClassName:"max-h-8",searchInputClassName:"max-h-8",triggerTooltip:e("graphPanel.graphLabels.selectTooltip"),fetcher:o,renderOption:i=>g.jsx("div",{style:{whiteSpace:"pre"},children:i}),getOptionValue:i=>i,getDisplayValue:i=>g.jsx("div",{style:{whiteSpace:"pre"},children:i}),notFound:g.jsx("div",{className:"py-6 text-center text-sm",children:"No labels found"}),label:e("graphPanel.graphLabels.label"),placeholder:e("graphPanel.graphLabels.placeholder"),value:t!==null?t:"*",onChange:i=>{const s=Z.getState().queryLabel;i==="..."&&(i="*"),i===s&&i!=="*"&&(i="*"),te.getState().setGraphDataFetchAttempted(!1),Z.getState().setQueryLabel(i)},clearable:!1})]})},Vs=({text:e,className:t,tooltipClassName:r,tooltip:n,side:a,onClick:o})=>n?g.jsx(Fa,{delayDuration:200,children:g.jsxs($a,{children:[g.jsx(Ha,{asChild:!0,children:g.jsx("label",{className:fe(t,o!==void 0?"cursor-pointer":void 0),onClick:o,children:e})}),g.jsx(Tn,{side:a,className:r,children:n})]})}):g.jsx("label",{className:fe(t,o!==void 0?"cursor-pointer":void 0),onClick:o,children:e});var Gt={exports:{}},Gh=Gt.exports,Wo;function Mh(){return Wo||(Wo=1,function(e){(function(t,r,n){function a(s){var c=this,u=i();c.next=function(){var d=2091639*c.s0+c.c*23283064365386963e-26;return c.s0=c.s1,c.s1=c.s2,c.s2=d-(c.c=d|0)},c.c=1,c.s0=u(" "),c.s1=u(" "),c.s2=u(" "),c.s0-=u(s),c.s0<0&&(c.s0+=1),c.s1-=u(s),c.s1<0&&(c.s1+=1),c.s2-=u(s),c.s2<0&&(c.s2+=1),u=null}function o(s,c){return c.c=s.c,c.s0=s.s0,c.s1=s.s1,c.s2=s.s2,c}function l(s,c){var u=new a(s),d=c&&c.state,h=u.next;return h.int32=function(){return u.next()*4294967296|0},h.double=function(){return h()+(h()*2097152|0)*11102230246251565e-32},h.quick=h,d&&(typeof d=="object"&&o(d,u),h.state=function(){return o(u,{})}),h}function i(){var s=4022871197,c=function(u){u=String(u);for(var d=0;d<u.length;d++){s+=u.charCodeAt(d);var h=.02519603282416938*s;s=h>>>0,h-=s,h*=s,s=h>>>0,h-=s,s+=h*4294967296}return(s>>>0)*23283064365386963e-26};return c}r&&r.exports?r.exports=l:this.alea=l})(Gh,e)}(Gt)),Gt.exports}var Mt={exports:{}},Fh=Mt.exports,Xo;function $h(){return Xo||(Xo=1,function(e){(function(t,r,n){function a(i){var s=this,c="";s.x=0,s.y=0,s.z=0,s.w=0,s.next=function(){var d=s.x^s.x<<11;return s.x=s.y,s.y=s.z,s.z=s.w,s.w^=s.w>>>19^d^d>>>8},i===(i|0)?s.x=i:c+=i;for(var u=0;u<c.length+64;u++)s.x^=c.charCodeAt(u)|0,s.next()}function o(i,s){return s.x=i.x,s.y=i.y,s.z=i.z,s.w=i.w,s}function l(i,s){var c=new a(i),u=s&&s.state,d=function(){return(c.next()>>>0)/4294967296};return d.double=function(){do var h=c.next()>>>11,f=(c.next()>>>0)/4294967296,b=(h+f)/(1<<21);while(b===0);return b},d.int32=c.next,d.quick=d,u&&(typeof u=="object"&&o(u,c),d.state=function(){return o(c,{})}),d}r&&r.exports?r.exports=l:this.xor128=l})(Fh,e)}(Mt)),Mt.exports}var Ft={exports:{}},Hh=Ft.exports,Yo;function Bh(){return Yo||(Yo=1,function(e){(function(t,r,n){function a(i){var s=this,c="";s.next=function(){var d=s.x^s.x>>>2;return s.x=s.y,s.y=s.z,s.z=s.w,s.w=s.v,(s.d=s.d+362437|0)+(s.v=s.v^s.v<<4^(d^d<<1))|0},s.x=0,s.y=0,s.z=0,s.w=0,s.v=0,i===(i|0)?s.x=i:c+=i;for(var u=0;u<c.length+64;u++)s.x^=c.charCodeAt(u)|0,u==c.length&&(s.d=s.x<<10^s.x>>>4),s.next()}function o(i,s){return s.x=i.x,s.y=i.y,s.z=i.z,s.w=i.w,s.v=i.v,s.d=i.d,s}function l(i,s){var c=new a(i),u=s&&s.state,d=function(){return(c.next()>>>0)/4294967296};return d.double=function(){do var h=c.next()>>>11,f=(c.next()>>>0)/4294967296,b=(h+f)/(1<<21);while(b===0);return b},d.int32=c.next,d.quick=d,u&&(typeof u=="object"&&o(u,c),d.state=function(){return o(c,{})}),d}r&&r.exports?r.exports=l:this.xorwow=l})(Hh,e)}(Ft)),Ft.exports}var $t={exports:{}},Vh=$t.exports,Ko;function qh(){return Ko||(Ko=1,function(e){(function(t,r,n){function a(i){var s=this;s.next=function(){var u=s.x,d=s.i,h,f;return h=u[d],h^=h>>>7,f=h^h<<24,h=u[d+1&7],f^=h^h>>>10,h=u[d+3&7],f^=h^h>>>3,h=u[d+4&7],f^=h^h<<7,h=u[d+7&7],h=h^h<<13,f^=h^h<<9,u[d]=f,s.i=d+1&7,f};function c(u,d){var h,f=[];if(d===(d|0))f[0]=d;else for(d=""+d,h=0;h<d.length;++h)f[h&7]=f[h&7]<<15^d.charCodeAt(h)+f[h+1&7]<<13;for(;f.length<8;)f.push(0);for(h=0;h<8&&f[h]===0;++h);for(h==8?f[7]=-1:f[h],u.x=f,u.i=0,h=256;h>0;--h)u.next()}c(s,i)}function o(i,s){return s.x=i.x.slice(),s.i=i.i,s}function l(i,s){i==null&&(i=+new Date);var c=new a(i),u=s&&s.state,d=function(){return(c.next()>>>0)/4294967296};return d.double=function(){do var h=c.next()>>>11,f=(c.next()>>>0)/4294967296,b=(h+f)/(1<<21);while(b===0);return b},d.int32=c.next,d.quick=d,u&&(u.x&&o(u,c),d.state=function(){return o(c,{})}),d}r&&r.exports?r.exports=l:this.xorshift7=l})(Vh,e)}($t)),$t.exports}var Ht={exports:{}},Uh=Ht.exports,Qo;function Wh(){return Qo||(Qo=1,function(e){(function(t,r,n){function a(i){var s=this;s.next=function(){var u=s.w,d=s.X,h=s.i,f,b;return s.w=u=u+1640531527|0,b=d[h+34&127],f=d[h=h+1&127],b^=b<<13,f^=f<<17,b^=b>>>15,f^=f>>>12,b=d[h]=b^f,s.i=h,b+(u^u>>>16)|0};function c(u,d){var h,f,b,y,C,N=[],E=128;for(d===(d|0)?(f=d,d=null):(d=d+"\0",f=0,E=Math.max(E,d.length)),b=0,y=-32;y<E;++y)d&&(f^=d.charCodeAt((y+32)%d.length)),y===0&&(C=f),f^=f<<10,f^=f>>>15,f^=f<<4,f^=f>>>13,y>=0&&(C=C+1640531527|0,h=N[y&127]^=f+C,b=h==0?b+1:0);for(b>=128&&(N[(d&&d.length||0)&127]=-1),b=127,y=4*128;y>0;--y)f=N[b+34&127],h=N[b=b+1&127],f^=f<<13,h^=h<<17,f^=f>>>15,h^=h>>>12,N[b]=f^h;u.w=C,u.X=N,u.i=b}c(s,i)}function o(i,s){return s.i=i.i,s.w=i.w,s.X=i.X.slice(),s}function l(i,s){i==null&&(i=+new Date);var c=new a(i),u=s&&s.state,d=function(){return(c.next()>>>0)/4294967296};return d.double=function(){do var h=c.next()>>>11,f=(c.next()>>>0)/4294967296,b=(h+f)/(1<<21);while(b===0);return b},d.int32=c.next,d.quick=d,u&&(u.X&&o(u,c),d.state=function(){return o(c,{})}),d}r&&r.exports?r.exports=l:this.xor4096=l})(Uh,e)}(Ht)),Ht.exports}var Bt={exports:{}},Xh=Bt.exports,Jo;function Yh(){return Jo||(Jo=1,function(e){(function(t,r,n){function a(i){var s=this,c="";s.next=function(){var d=s.b,h=s.c,f=s.d,b=s.a;return d=d<<25^d>>>7^h,h=h-f|0,f=f<<24^f>>>8^b,b=b-d|0,s.b=d=d<<20^d>>>12^h,s.c=h=h-f|0,s.d=f<<16^h>>>16^b,s.a=b-d|0},s.a=0,s.b=0,s.c=-1640531527,s.d=1367130551,i===Math.floor(i)?(s.a=i/4294967296|0,s.b=i|0):c+=i;for(var u=0;u<c.length+20;u++)s.b^=c.charCodeAt(u)|0,s.next()}function o(i,s){return s.a=i.a,s.b=i.b,s.c=i.c,s.d=i.d,s}function l(i,s){var c=new a(i),u=s&&s.state,d=function(){return(c.next()>>>0)/4294967296};return d.double=function(){do var h=c.next()>>>11,f=(c.next()>>>0)/4294967296,b=(h+f)/(1<<21);while(b===0);return b},d.int32=c.next,d.quick=d,u&&(typeof u=="object"&&o(u,c),d.state=function(){return o(c,{})}),d}r&&r.exports?r.exports=l:this.tychei=l})(Xh,e)}(Bt)),Bt.exports}var Vt={exports:{}};const Kh={},Qh=Object.freeze(Object.defineProperty({__proto__:null,default:Kh},Symbol.toStringTag,{value:"Module"})),Jh=pi(Qh);var Zh=Vt.exports,Zo;function eg(){return Zo||(Zo=1,function(e){(function(t,r,n){var a=256,o=6,l=52,i="random",s=n.pow(a,o),c=n.pow(2,l),u=c*2,d=a-1,h;function f(A,I,P){var m=[];I=I==!0?{entropy:!0}:I||{};var S=N(C(I.entropy?[A,j(r)]:A??E(),3),m),x=new b(m),T=function(){for(var R=x.g(o),O=s,w=0;R<c;)R=(R+w)*a,O*=a,w=x.g(1);for(;R>=u;)R/=2,O/=2,w>>>=1;return(R+w)/O};return T.int32=function(){return x.g(4)|0},T.quick=function(){return x.g(4)/4294967296},T.double=T,N(j(x.S),r),(I.pass||P||function(R,O,w,H){return H&&(H.S&&y(H,x),R.state=function(){return y(x,{})}),w?(n[i]=R,O):R})(T,S,"global"in I?I.global:this==n,I.state)}function b(A){var I,P=A.length,m=this,S=0,x=m.i=m.j=0,T=m.S=[];for(P||(A=[P++]);S<a;)T[S]=S++;for(S=0;S<a;S++)T[S]=T[x=d&x+A[S%P]+(I=T[S])],T[x]=I;(m.g=function(R){for(var O,w=0,H=m.i,K=m.j,D=m.S;R--;)O=D[H=d&H+1],w=w*a+D[d&(D[H]=D[K=d&K+O])+(D[K]=O)];return m.i=H,m.j=K,w})(a)}function y(A,I){return I.i=A.i,I.j=A.j,I.S=A.S.slice(),I}function C(A,I){var P=[],m=typeof A,S;if(I&&m=="object")for(S in A)try{P.push(C(A[S],I-1))}catch{}return P.length?P:m=="string"?A:A+"\0"}function N(A,I){for(var P=A+"",m,S=0;S<P.length;)I[d&S]=d&(m^=I[d&S]*19)+P.charCodeAt(S++);return j(I)}function E(){try{var A;return h&&(A=h.randomBytes)?A=A(a):(A=new Uint8Array(a),(t.crypto||t.msCrypto).getRandomValues(A)),j(A)}catch{var I=t.navigator,P=I&&I.plugins;return[+new Date,t,P,t.screen,j(r)]}}function j(A){return String.fromCharCode.apply(0,A)}if(N(n.random(),r),e.exports){e.exports=f;try{h=Jh}catch{}}else n["seed"+i]=f})(typeof self<"u"?self:Zh,[],Math)}(Vt)),Vt.exports}var Yr,ea;function tg(){if(ea)return Yr;ea=1;var e=Mh(),t=$h(),r=Bh(),n=qh(),a=Wh(),o=Yh(),l=eg();return l.alea=e,l.xor128=t,l.xorwow=r,l.xorshift7=n,l.xor4096=a,l.tychei=o,Yr=l,Yr}var rg=tg();const bn=He(rg),ng={unknown:"unknown",未知:"unknown",other:"unknown",category:"category",类别:"category",type:"category",分类:"category",organization:"organization",组织:"organization",org:"organization",company:"organization",公司:"organization",机构:"organization",event:"event",事件:"event",activity:"event",活动:"event",person:"person",人物:"person",people:"person",human:"person",人:"person",animal:"animal",动物:"animal",creature:"animal",生物:"animal",geo:"geo",地理:"geo",geography:"geo",地域:"geo",location:"location",地点:"location",place:"location",address:"location",位置:"location",地址:"location",technology:"technology",技术:"technology",tech:"technology",科技:"technology",equipment:"equipment",设备:"equipment",device:"equipment",装备:"equipment",weapon:"weapon",武器:"weapon",arms:"weapon",军火:"weapon",object:"object",物品:"object",stuff:"object",物体:"object",group:"group",群组:"group",community:"group",社区:"group"},ta={unknown:"#f4d371",category:"#e3493b",organization:"#0f705d",event:"#00bfa0",person:"#4169E1",animal:"#84a3e1",geo:"#ff99cc",location:"#cf6d17",technology:"#b300b3",equipment:"#2F4F4F",weapon:"#4421af",object:"#00cc00",group:"#0f558a"},og=["#5a2c6d","#0000ff","#cd071e","#00CED1","#9b3a31","#b2e061","#bd7ebe","#6ef7b3","#003366","#DEB887"],ra=e=>{const t="#5D6D7E",r=e?e.toLowerCase():"unknown",n=te.getState().typeColorMap;if(n.has(r))return n.get(r)||t;const a=ng[r];if(a){const c=ta[a],u=new Map(n);return u.set(r,c),te.setState({typeColorMap:u}),c}const o=new Set(Array.from(n.entries()).filter(([,c])=>!Object.values(ta).includes(c)).map(([,c])=>c)),i=og.find(c=>!o.has(c))||t,s=new Map(n);return s.set(r,i),te.setState({typeColorMap:s}),i},ag=e=>{if(!e)return console.log("Graph validation failed: graph is null"),!1;if(!Array.isArray(e.nodes)||!Array.isArray(e.edges))return console.log("Graph validation failed: nodes or edges is not an array"),!1;if(e.nodes.length===0)return console.log("Graph validation failed: nodes array is empty"),!1;for(const t of e.nodes)if(!t.id||!t.labels||!t.properties)return console.log("Graph validation failed: invalid node structure"),!1;for(const t of e.edges)if(!t.id||!t.source||!t.target)return console.log("Graph validation failed: invalid edge structure"),!1;for(const t of e.edges){const r=e.getNode(t.source),n=e.getNode(t.target);if(r==null||n==null)return console.log("Graph validation failed: edge references non-existent node"),!1}return console.log("Graph validation passed"),!0},sg=async(e,t,r)=>{let n=null;if(!te.getState().lastSuccessfulQueryLabel){console.log("Last successful queryLabel is empty");try{await te.getState().fetchAllDatabaseLabels()}catch(i){console.error("Failed to fetch all database labels:",i)}}te.getState().setLabelsFetchAttempted(!0);const o=e||"*";try{console.log(`Fetching graph label: ${o}, depth: ${t}, nodes: ${r}`),n=await ka(o,t,r)}catch(i){return kn.getState().setErrorMessage(rr(i),"Query Graphs Error!"),null}let l=null;if(n){const i={},s={};for(let h=0;h<n.nodes.length;h++){const f=n.nodes[h];i[f.id]=h,f.x=Math.random(),f.y=Math.random(),f.degree=0,f.size=10}for(let h=0;h<n.edges.length;h++){const f=n.edges[h];s[f.id]=h;const b=i[f.source],y=i[f.target];if(b!==void 0&&b!==void 0){const C=n.nodes[b],N=n.nodes[y];if(!C){console.error(`Source node ${f.source} is undefined`);continue}if(!N){console.error(`Target node ${f.target} is undefined`);continue}C.degree+=1,N.degree+=1}}let c=Number.MAX_SAFE_INTEGER,u=0;for(const h of n.nodes)c=Math.min(c,h.degree),u=Math.max(u,h.degree);const d=u-c;if(d>0){const h=Zr-ut;for(const f of n.nodes)f.size=Math.round(ut+h*Math.pow((f.degree-c)/d,.5))}l=new nl,l.nodes=n.nodes,l.edges=n.edges,l.nodeIdMap=i,l.edgeIdMap=s,ag(l)||(l=null,console.warn("Invalid graph data")),console.log("Graph data loaded")}return{rawGraph:l,is_truncated:n.is_truncated}},ig=e=>{var i,s;const t=Z.getState().minEdgeSize,r=Z.getState().maxEdgeSize;if(!e||!e.nodes.length)return console.log("No graph data available, skipping sigma graph creation"),null;const n=new Kr;for(const c of(e==null?void 0:e.nodes)??[]){bn(c.id+Date.now().toString(),{global:!0});const u=Math.random(),d=Math.random();n.addNode(c.id,{label:c.labels.join(", "),color:c.color,x:u,y:d,size:c.size,borderColor:Jr,borderSize:.2})}for(const c of(e==null?void 0:e.edges)??[]){const u=((i=c.properties)==null?void 0:i.weight)!==void 0?Number(c.properties.weight):1;c.dynamicId=n.addEdge(c.source,c.target,{label:((s=c.properties)==null?void 0:s.keywords)||void 0,size:u,originalWeight:u,type:"curvedNoArrow"})}let a=Number.MAX_SAFE_INTEGER,o=0;n.forEachEdge(c=>{const u=n.getEdgeAttribute(c,"originalWeight")||1;a=Math.min(a,u),o=Math.max(o,u)});const l=o-a;if(l>0){const c=r-t;n.forEachEdge(u=>{const d=n.getEdgeAttribute(u,"originalWeight")||1,h=t+c*Math.pow((d-a)/l,.5);n.setEdgeAttribute(u,"size",h)})}else n.forEachEdge(c=>{n.setEdgeAttribute(c,"size",t)});return n},lg=()=>{const{t:e}=_e(),t=Z.use.queryLabel(),r=te.use.rawGraph(),n=te.use.sigmaGraph(),a=Z.use.graphQueryMaxDepth(),o=Z.use.graphMaxNodes(),l=te.use.isFetching(),i=te.use.nodeToExpand(),s=te.use.nodeToPrune(),c=p.useRef(!1),u=p.useRef(!1),d=p.useRef(!1),h=p.useCallback(N=>(r==null?void 0:r.getNode(N))||null,[r]),f=p.useCallback((N,E=!0)=>(r==null?void 0:r.getEdge(N,E))||null,[r]),b=p.useRef(!1);p.useEffect(()=>{if(!t&&(r!==null||n!==null)){const N=te.getState();N.reset(),N.setGraphDataFetchAttempted(!1),N.setLabelsFetchAttempted(!1),c.current=!1,u.current=!1}},[t,r,n]),p.useEffect(()=>{if(!b.current&&!(!t&&d.current)&&!l&&!te.getState().graphDataFetchAttempted){b.current=!0,te.getState().setGraphDataFetchAttempted(!0);const N=te.getState();N.setIsFetching(!0),N.clearSelection(),N.sigmaGraph&&N.sigmaGraph.forEachNode(P=>{var m;(m=N.sigmaGraph)==null||m.setNodeAttribute(P,"highlighted",!1)}),console.log("Preparing graph data...");const E=t,j=a,A=o;let I;E?I=sg(E,j,A):(console.log("Query label is empty, show empty graph"),I=Promise.resolve({rawGraph:null,is_truncated:!1})),I.then(P=>{const m=te.getState(),S=P==null?void 0:P.rawGraph;if(S&&S.nodes&&S.nodes.forEach(x=>{var R;const T=(R=x.properties)==null?void 0:R.entity_type;x.color=ra(T)}),P!=null&&P.is_truncated&&rt.info(e("graphPanel.dataIsTruncated","Graph data is truncated to Max Nodes")),m.reset(),!S||!S.nodes||S.nodes.length===0){const x=new Kr;x.addNode("empty-graph-node",{label:e("graphPanel.emptyGraph"),color:"#5D6D7E",x:.5,y:.5,size:15,borderColor:Jr,borderSize:.2}),m.setSigmaGraph(x),m.setRawGraph(null),m.setGraphIsEmpty(!0);const T=kn.getState().message,R=T&&T.includes("Authentication required");!R&&E&&Z.getState().setQueryLabel(""),R?console.log("Keep queryLabel for post-login reload"):m.setLastSuccessfulQueryLabel(""),console.log(`Graph data is empty, created graph with empty graph node. Auth error: ${R}`)}else{const x=ig(S);S.buildDynamicMap(),m.setSigmaGraph(x),m.setRawGraph(S),m.setGraphIsEmpty(!1),m.setLastSuccessfulQueryLabel(E),m.setMoveToSelectedNode(!0)}c.current=!0,u.current=!0,b.current=!1,m.setIsFetching(!1),(!S||!S.nodes||S.nodes.length===0)&&!E&&(d.current=!0)}).catch(P=>{console.error("Error fetching graph data:",P);const m=te.getState();m.setIsFetching(!1),c.current=!1,b.current=!1,m.setGraphDataFetchAttempted(!1),m.setLastSuccessfulQueryLabel("")})}},[t,a,o,l,e]),p.useEffect(()=>{i&&((async E=>{var j,A,I,P,m,S;if(!(!E||!n||!r))try{const x=r.getNode(E);if(!x){console.error("Node not found:",E);return}const T=x.labels[0];if(!T){console.error("Node has no label:",E);return}const R=await ka(T,2,1e3);if(!R||!R.nodes||!R.edges){console.error("Failed to fetch extended graph");return}const O=[];for(const M of R.nodes){bn(M.id,{global:!0});const J=(j=M.properties)==null?void 0:j.entity_type,U=ra(J);O.push({id:M.id,labels:M.labels,properties:M.properties,size:10,x:Math.random(),y:Math.random(),color:U,degree:0})}const w=[];for(const M of R.edges)w.push({id:M.id,source:M.source,target:M.target,type:M.type,properties:M.properties,dynamicId:""});const H={};n.forEachNode(M=>{H[M]={x:n.getNodeAttribute(M,"x"),y:n.getNodeAttribute(M,"y")}});const K=new Set(n.nodes()),D=new Set,k=new Set,_=1;let B=0,se=Number.MAX_SAFE_INTEGER,F=0;n.forEachNode(M=>{const J=n.degree(M);B=Math.max(B,J)}),n.forEachEdge(M=>{const J=n.getEdgeAttribute(M,"originalWeight")||1;se=Math.min(se,J),F=Math.max(F,J)});for(const M of O){if(K.has(M.id))continue;w.some(U=>U.source===E&&U.target===M.id||U.target===E&&U.source===M.id)&&D.add(M.id)}const v=new Map,z=new Map,V=new Set;for(const M of w){const J=K.has(M.source)||D.has(M.source),U=K.has(M.target)||D.has(M.target);J&&U?(k.add(M.id),D.has(M.source)?v.set(M.source,(v.get(M.source)||0)+1):K.has(M.source)&&z.set(M.source,(z.get(M.source)||0)+1),D.has(M.target)?v.set(M.target,(v.get(M.target)||0)+1):K.has(M.target)&&z.set(M.target,(z.get(M.target)||0)+1)):(n.hasNode(M.source)?V.add(M.source):D.has(M.source)&&(V.add(M.source),v.set(M.source,(v.get(M.source)||0)+1)),n.hasNode(M.target)?V.add(M.target):D.has(M.target)&&(V.add(M.target),v.set(M.target,(v.get(M.target)||0)+1)))}const $=(M,J,U,q)=>{const L=q-U||1,oe=Zr-ut;for(const ue of J)if(M.hasNode(ue)){let re=M.degree(ue);re+=1;const ee=Math.min(re,q+1),G=Math.round(ut+oe*Math.pow((ee-U)/L,.5));M.setNodeAttribute(ue,"size",G)}},Q=(M,J,U)=>{const q=Z.getState().minEdgeSize,L=Z.getState().maxEdgeSize,oe=U-J||1,ue=L-q;M.forEachEdge(re=>{const ee=M.getEdgeAttribute(re,"originalWeight")||1,G=q+ue*Math.pow((ee-J)/oe,.5);M.setEdgeAttribute(re,"size",G)})};if(D.size===0){$(n,V,_,B),rt.info(e("graphPanel.propertiesView.node.noNewNodes"));return}for(const[,M]of v.entries())B=Math.max(B,M);for(const[M,J]of z.entries()){const q=n.degree(M)+J;B=Math.max(B,q)}const W=B-_||1,Y=Zr-ut,ie=((A=te.getState().sigmaInstance)==null?void 0:A.getCamera().ratio)||1,ne=Math.max(Math.sqrt(x.size)*4,Math.sqrt(D.size)*3)/ie;bn(Date.now().toString(),{global:!0});const ae=Math.random()*2*Math.PI;console.log("nodeSize:",x.size,"nodesToAdd:",D.size),console.log("cameraRatio:",Math.round(ie*100)/100,"spreadFactor:",Math.round(ne*100)/100);for(const M of D){const J=O.find(ee=>ee.id===M),U=v.get(M)||0,q=Math.min(U,B+1),L=Math.round(ut+Y*Math.pow((q-_)/W,.5)),oe=2*Math.PI*(Array.from(D).indexOf(M)/D.size),ue=((I=H[M])==null?void 0:I.x)||H[x.id].x+Math.cos(ae+oe)*ne,re=((P=H[M])==null?void 0:P.y)||H[x.id].y+Math.sin(ae+oe)*ne;n.addNode(M,{label:J.labels.join(", "),color:J.color,x:ue,y:re,size:L,borderColor:Jr,borderSize:.2}),r.getNode(M)||(J.size=L,J.x=ue,J.y=re,J.degree=U,r.nodes.push(J),r.nodeIdMap[M]=r.nodes.length-1)}for(const M of k){const J=w.find(q=>q.id===M);if(n.hasEdge(J.source,J.target))continue;const U=((m=J.properties)==null?void 0:m.weight)!==void 0?Number(J.properties.weight):1;se=Math.min(se,U),F=Math.max(F,U),J.dynamicId=n.addEdge(J.source,J.target,{label:((S=J.properties)==null?void 0:S.keywords)||void 0,size:U,originalWeight:U,type:"curvedNoArrow"}),r.getEdge(J.id,!1)?console.error("Edge already exists in rawGraph:",J.id):(r.edges.push(J),r.edgeIdMap[J.id]=r.edges.length-1,r.edgeDynamicIdMap[J.dynamicId]=r.edges.length-1)}if(r.buildDynamicMap(),te.getState().resetSearchEngine(),$(n,V,_,B),Q(n,se,F),n.hasNode(E)){const M=n.degree(E),J=Math.min(M,B+1),U=Math.round(ut+Y*Math.pow((J-_)/W,.5));n.setNodeAttribute(E,"size",U),x.size=U,x.degree=M}}catch(x){console.error("Error expanding node:",x)}})(i),window.setTimeout(()=>{te.getState().triggerNodeExpand(null)},0))},[i,n,r,e]);const y=p.useCallback((N,E)=>{const j=new Set([N]);return E.forEachNode(A=>{if(A===N)return;const I=E.neighbors(A);I.length===1&&I[0]===N&&j.add(A)}),j},[]);return p.useEffect(()=>{s&&((E=>{if(!(!E||!n||!r))try{const j=te.getState();if(!n.hasNode(E)){console.error("Node not found:",E);return}const A=y(E,n);if(A.size===n.nodes().length){rt.error(e("graphPanel.propertiesView.node.deleteAllNodesError"));return}j.clearSelection();for(const I of A){n.dropNode(I);const P=r.nodeIdMap[I];if(P!==void 0){const m=r.edges.filter(S=>S.source===I||S.target===I);for(const S of m){const x=r.edgeIdMap[S.id];if(x!==void 0){r.edges.splice(x,1);for(const[T,R]of Object.entries(r.edgeIdMap))R>x&&(r.edgeIdMap[T]=R-1);delete r.edgeIdMap[S.id],delete r.edgeDynamicIdMap[S.dynamicId]}}r.nodes.splice(P,1);for(const[S,x]of Object.entries(r.nodeIdMap))x>P&&(r.nodeIdMap[S]=x-1);delete r.nodeIdMap[I]}}r.buildDynamicMap(),te.getState().resetSearchEngine(),A.size>1&&rt.info(e("graphPanel.propertiesView.node.nodesRemoved",{count:A.size}))}catch(j){console.error("Error pruning node:",j)}})(s),window.setTimeout(()=>{te.getState().triggerNodePrune(null)},0))},[s,n,r,y,e]),{lightrageGraph:p.useCallback(()=>{if(n)return n;console.log("Creating new Sigma graph instance");const N=new Kr;return te.getState().setSigmaGraph(N),N},[n]),getNode:h,getEdge:f}},cg=({name:e})=>{const{t}=_e(),r=n=>{const a=`graphPanel.propertiesView.node.propertyNames.${n}`,o=t(a);return o===a?n:o};return g.jsx("span",{className:"text-primary/60 tracking-wide whitespace-nowrap",children:r(e)})},ug=({onClick:e})=>g.jsx("div",{children:g.jsx(du,{className:"h-3 w-3 text-gray-500 hover:text-gray-700 cursor-pointer",onClick:e})}),dg=({value:e,onClick:t,tooltip:r})=>g.jsx("div",{className:"flex items-center gap-1 overflow-hidden",children:g.jsx(Vs,{className:"hover:bg-primary/20 rounded p-1 overflow-hidden text-ellipsis whitespace-nowrap",tooltipClassName:"max-w-80 -translate-x-15",text:e,tooltip:r||(typeof e=="string"?e:JSON.stringify(e,null,2)),side:"left",onClick:t})}),fg=({isOpen:e,onClose:t,onSave:r,propertyName:n,initialValue:a,isSubmitting:o=!1})=>{const{t:l}=_e(),[i,s]=p.useState(""),[c,u]=p.useState(null);p.useEffect(()=>{e&&s(a)},[e,a]);const d=b=>{const y=`graphPanel.propertiesView.node.propertyNames.${b}`,C=l(y);return C===y?b:C},h=b=>{switch(b){case"description":return{className:"max-h-[50vh] min-h-[10em] resize-y",style:{height:"70vh",minHeight:"20em",resize:"vertical"}};case"entity_id":return{rows:2,className:"",style:{}};case"keywords":return{rows:4,className:"",style:{}};default:return{rows:5,className:"",style:{}}}},f=async()=>{if(i.trim()!==""){u(null);try{await r(i),t()}catch(b){console.error("Save error:",b),u(typeof b=="object"&&b!==null&&b.message||l("common.saveFailed"))}}};return g.jsx(Mu,{open:e,onOpenChange:b=>!b&&t(),children:g.jsxs(Xa,{className:"sm:max-w-md",children:[g.jsxs(Ya,{children:[g.jsx(Qa,{children:l("graphPanel.propertiesView.editProperty",{property:d(n)})}),g.jsx(Ja,{children:l("graphPanel.propertiesView.editPropertyDescription")})]}),c&&g.jsx("div",{className:"bg-destructive/15 text-destructive px-4 py-2 rounded-md text-sm mt-2",children:c}),g.jsx("div",{className:"grid gap-4 py-4",children:(()=>{const b=h(n);return n==="description"?g.jsx("textarea",{value:i,onChange:y=>s(y.target.value),className:`border-input focus-visible:ring-ring flex w-full rounded-md border bg-transparent px-3 py-2 text-sm shadow-sm transition-colors focus-visible:ring-1 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50 ${b.className}`,style:b.style,disabled:o}):g.jsx("textarea",{value:i,onChange:y=>s(y.target.value),rows:b.rows,className:`border-input focus-visible:ring-ring flex w-full rounded-md border bg-transparent px-3 py-2 text-sm shadow-sm transition-colors focus-visible:ring-1 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50 ${b.className}`,disabled:o})})()}),g.jsxs(Ka,{children:[g.jsx(be,{type:"button",variant:"outline",onClick:t,disabled:o,children:l("common.cancel")}),g.jsx(be,{type:"button",onClick:f,disabled:o,children:o?g.jsxs(g.Fragment,{children:[g.jsx("span",{className:"mr-2",children:g.jsxs("svg",{className:"animate-spin h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[g.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),g.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]})}),l("common.saving")]}):l("common.save")})]})]})})},hg=({name:e,value:t,onClick:r,nodeId:n,edgeId:a,entityId:o,dynamicId:l,entityType:i,sourceId:s,targetId:c,onValueChange:u,isEditable:d=!1,tooltip:h})=>{const{t:f}=_e(),[b,y]=p.useState(!1),[C,N]=p.useState(!1),[E,j]=p.useState(t);p.useEffect(()=>{j(t)},[t]);const A=()=>{d&&!b&&y(!0)},I=()=>{y(!1)},P=async m=>{if(C||m===String(E)){y(!1);return}N(!0);try{if(i==="node"&&o&&n){let S={[e]:m};if(e==="entity_id"){if(await ul(m)){rt.error(f("graphPanel.propertiesView.errors.duplicateName"));return}S={entity_name:m}}await ll(o,S,!0);try{await te.getState().updateNodeAndSelect(n,o,e,m)}catch(x){throw console.error("Error updating node in graph:",x),new Error("Failed to update node in graph")}rt.success(f("graphPanel.propertiesView.success.entityUpdated"))}else if(i==="edge"&&s&&c&&a&&l){const S={[e]:m};await cl(s,c,S);try{await te.getState().updateEdgeAndSelect(a,l,s,c,e,m)}catch(x){throw console.error(`Error updating edge ${s}->${c} in graph:`,x),new Error("Failed to update edge in graph")}rt.success(f("graphPanel.propertiesView.success.relationUpdated"))}y(!1),j(m),u==null||u(m)}catch(S){console.error("Error updating property:",S),rt.error(f("graphPanel.propertiesView.errors.updateFailed"))}finally{N(!1)}};return g.jsxs("div",{className:"flex items-center gap-1 overflow-hidden",children:[g.jsx(cg,{name:e}),g.jsx(ug,{onClick:A}),":",g.jsx(dg,{value:E,onClick:r,tooltip:h||(typeof E=="string"?E:JSON.stringify(E,null,2))}),g.jsx(fg,{isOpen:b,onClose:I,onSave:P,propertyName:e,initialValue:String(E),isSubmitting:C})]})},gg=()=>{const{getNode:e,getEdge:t}=lg(),r=te.use.selectedNode(),n=te.use.focusedNode(),a=te.use.selectedEdge(),o=te.use.focusedEdge(),l=te.use.graphDataVersion(),[i,s]=p.useState(null),[c,u]=p.useState(null);return p.useEffect(()=>{let d=null,h=null;n?(d="node",h=e(n)):r?(d="node",h=e(r)):o?(d="edge",h=t(o,!0)):a&&(d="edge",h=t(a,!0)),h?(d=="node"?s(pg(h)):s(mg(h)),u(d)):(s(null),u(null))},[n,r,o,a,l,s,u,e,t]),i?g.jsx("div",{className:"bg-background/80 max-w-xs rounded-lg border-2 p-2 text-xs backdrop-blur-lg",children:c=="node"?g.jsx(vg,{node:i}):g.jsx(yg,{edge:i})}):g.jsx(g.Fragment,{})},pg=e=>{const t=te.getState(),r=[];if(t.sigmaGraph&&t.rawGraph)try{if(!t.sigmaGraph.hasNode(e.id))return console.warn("Node not found in sigmaGraph:",e.id),{...e,relationships:[]};const n=t.sigmaGraph.edges(e.id);for(const a of n){if(!t.sigmaGraph.hasEdge(a))continue;const o=t.rawGraph.getEdge(a,!0);if(o){const i=e.id===o.source?o.target:o.source;if(!t.sigmaGraph.hasNode(i))continue;const s=t.rawGraph.getNode(i);s&&r.push({type:"Neighbour",id:i,label:s.properties.entity_id?s.properties.entity_id:s.labels.join(", ")})}}}catch(n){console.error("Error refining node properties:",n)}return{...e,relationships:r}},mg=e=>{const t=te.getState();let r,n;if(t.sigmaGraph&&t.rawGraph)try{if(!t.sigmaGraph.hasEdge(e.dynamicId))return console.warn("Edge not found in sigmaGraph:",e.id,"dynamicId:",e.dynamicId),{...e,sourceNode:void 0,targetNode:void 0};t.sigmaGraph.hasNode(e.source)&&(r=t.rawGraph.getNode(e.source)),t.sigmaGraph.hasNode(e.target)&&(n=t.rawGraph.getNode(e.target))}catch(a){console.error("Error refining edge properties:",a)}return{...e,sourceNode:r,targetNode:n}},$e=({name:e,value:t,onClick:r,tooltip:n,nodeId:a,edgeId:o,dynamicId:l,entityId:i,entityType:s,sourceId:c,targetId:u,isEditable:d=!1})=>{const{t:h}=_e(),f=b=>{const y=`graphPanel.propertiesView.node.propertyNames.${b}`,C=h(y);return C===y?b:C};return d&&(e==="description"||e==="entity_id"||e==="keywords")?g.jsx(hg,{name:e,value:t,onClick:r,nodeId:a,entityId:i,edgeId:o,dynamicId:l,entityType:s,sourceId:c,targetId:u,isEditable:!0,tooltip:n||(typeof t=="string"?t:JSON.stringify(t,null,2))}):g.jsxs("div",{className:"flex items-center gap-2",children:[g.jsx("span",{className:"text-primary/60 tracking-wide whitespace-nowrap",children:f(e)}),":",g.jsx(Vs,{className:"hover:bg-primary/20 rounded p-1 overflow-hidden text-ellipsis",tooltipClassName:"max-w-80 -translate-x-13",text:t,tooltip:n||(typeof t=="string"?t:JSON.stringify(t,null,2)),side:"left",onClick:r})]})},vg=({node:e})=>{const{t}=_e(),r=()=>{te.getState().triggerNodeExpand(e.id)},n=()=>{te.getState().triggerNodePrune(e.id)};return g.jsxs("div",{className:"flex flex-col gap-2",children:[g.jsxs("div",{className:"flex justify-between items-center",children:[g.jsx("h3",{className:"text-md pl-1 font-bold tracking-wide text-blue-700",children:t("graphPanel.propertiesView.node.title")}),g.jsxs("div",{className:"flex gap-3",children:[g.jsx(be,{size:"icon",variant:"ghost",className:"h-7 w-7 border border-gray-400 hover:bg-gray-200 dark:border-gray-600 dark:hover:bg-gray-700",onClick:r,tooltip:t("graphPanel.propertiesView.node.expandNode"),children:g.jsx(Yc,{className:"h-4 w-4 text-gray-700 dark:text-gray-300"})}),g.jsx(be,{size:"icon",variant:"ghost",className:"h-7 w-7 border border-gray-400 hover:bg-gray-200 dark:border-gray-600 dark:hover:bg-gray-700",onClick:n,tooltip:t("graphPanel.propertiesView.node.pruneNode"),children:g.jsx(xu,{className:"h-4 w-4 text-gray-900 dark:text-gray-300"})})]})]}),g.jsxs("div",{className:"bg-primary/5 max-h-96 overflow-auto rounded p-1",children:[g.jsx($e,{name:t("graphPanel.propertiesView.node.id"),value:String(e.id)}),g.jsx($e,{name:t("graphPanel.propertiesView.node.labels"),value:e.labels.join(", "),onClick:()=>{te.getState().setSelectedNode(e.id,!0)}}),g.jsx($e,{name:t("graphPanel.propertiesView.node.degree"),value:e.degree})]}),g.jsx("h3",{className:"text-md pl-1 font-bold tracking-wide text-amber-700",children:t("graphPanel.propertiesView.node.properties")}),g.jsx("div",{className:"bg-primary/5 max-h-96 overflow-auto rounded p-1",children:Object.keys(e.properties).sort().map(a=>a==="created_at"?null:g.jsx($e,{name:a,value:e.properties[a],nodeId:String(e.id),entityId:e.properties.entity_id,entityType:"node",isEditable:a==="description"||a==="entity_id"},a))}),e.relationships.length>0&&g.jsxs(g.Fragment,{children:[g.jsx("h3",{className:"text-md pl-1 font-bold tracking-wide text-emerald-700",children:t("graphPanel.propertiesView.node.relationships")}),g.jsx("div",{className:"bg-primary/5 max-h-96 overflow-auto rounded p-1",children:e.relationships.map(({type:a,id:o,label:l})=>g.jsx($e,{name:a,value:l,onClick:()=>{te.getState().setSelectedNode(o,!0)}},o))})]})]})},yg=({edge:e})=>{const{t}=_e();return g.jsxs("div",{className:"flex flex-col gap-2",children:[g.jsx("h3",{className:"text-md pl-1 font-bold tracking-wide text-violet-700",children:t("graphPanel.propertiesView.edge.title")}),g.jsxs("div",{className:"bg-primary/5 max-h-96 overflow-auto rounded p-1",children:[g.jsx($e,{name:t("graphPanel.propertiesView.edge.id"),value:e.id}),e.type&&g.jsx($e,{name:t("graphPanel.propertiesView.edge.type"),value:e.type}),g.jsx($e,{name:t("graphPanel.propertiesView.edge.source"),value:e.sourceNode?e.sourceNode.labels.join(", "):e.source,onClick:()=>{te.getState().setSelectedNode(e.source,!0)}}),g.jsx($e,{name:t("graphPanel.propertiesView.edge.target"),value:e.targetNode?e.targetNode.labels.join(", "):e.target,onClick:()=>{te.getState().setSelectedNode(e.target,!0)}})]}),g.jsx("h3",{className:"text-md pl-1 font-bold tracking-wide text-amber-700",children:t("graphPanel.propertiesView.edge.properties")}),g.jsx("div",{className:"bg-primary/5 max-h-96 overflow-auto rounded p-1",children:Object.keys(e.properties).sort().map(r=>{var n,a;return r==="created_at"?null:g.jsx($e,{name:r,value:e.properties[r],edgeId:String(e.id),dynamicId:String(e.dynamicId),entityType:"edge",sourceId:((n=e.sourceNode)==null?void 0:n.properties.entity_id)||e.source,targetId:((a=e.targetNode)==null?void 0:a.properties.entity_id)||e.target,isEditable:r==="description"||r==="keywords"},r)})})]})},bg=()=>{const{t:e}=_e(),t=Z.use.graphQueryMaxDepth(),r=Z.use.graphMaxNodes();return g.jsxs("div",{className:"absolute bottom-4 left-[calc(1rem+2.5rem)] flex items-center gap-2 text-xs text-gray-400",children:[g.jsxs("div",{children:[e("graphPanel.sideBar.settings.depth"),": ",t]}),g.jsxs("div",{children:[e("graphPanel.sideBar.settings.max"),": ",r]})]})},qs=p.forwardRef(({className:e,...t},r)=>g.jsx("div",{ref:r,className:fe("bg-card text-card-foreground rounded-xl border shadow",e),...t}));qs.displayName="Card";const wg=p.forwardRef(({className:e,...t},r)=>g.jsx("div",{ref:r,className:fe("flex flex-col space-y-1.5 p-6",e),...t}));wg.displayName="CardHeader";const xg=p.forwardRef(({className:e,...t},r)=>g.jsx("div",{ref:r,className:fe("leading-none font-semibold tracking-tight",e),...t}));xg.displayName="CardTitle";const _g=p.forwardRef(({className:e,...t},r)=>g.jsx("div",{ref:r,className:fe("text-muted-foreground text-sm",e),...t}));_g.displayName="CardDescription";const Sg=p.forwardRef(({className:e,...t},r)=>g.jsx("div",{ref:r,className:fe("p-6 pt-0",e),...t}));Sg.displayName="CardContent";const Eg=p.forwardRef(({className:e,...t},r)=>g.jsx("div",{ref:r,className:fe("flex items-center p-6 pt-0",e),...t}));Eg.displayName="CardFooter";function kg(e,t){return p.useReducer((r,n)=>t[r][n]??r,e)}var Mn="ScrollArea",[Us,Bp]=xn(Mn),[Cg,Le]=Us(Mn),Ws=p.forwardRef((e,t)=>{const{__scopeScrollArea:r,type:n="hover",dir:a,scrollHideDelay:o=600,...l}=e,[i,s]=p.useState(null),[c,u]=p.useState(null),[d,h]=p.useState(null),[f,b]=p.useState(null),[y,C]=p.useState(null),[N,E]=p.useState(0),[j,A]=p.useState(0),[I,P]=p.useState(!1),[m,S]=p.useState(!1),x=Xe(t,R=>s(R)),T=Mi(a);return g.jsx(Cg,{scope:r,type:n,dir:T,scrollHideDelay:o,scrollArea:i,viewport:c,onViewportChange:u,content:d,onContentChange:h,scrollbarX:f,onScrollbarXChange:b,scrollbarXEnabled:I,onScrollbarXEnabledChange:P,scrollbarY:y,onScrollbarYChange:C,scrollbarYEnabled:m,onScrollbarYEnabledChange:S,onCornerWidthChange:E,onCornerHeightChange:A,children:g.jsx(Ee.div,{dir:T,...l,ref:x,style:{position:"relative","--radix-scroll-area-corner-width":N+"px","--radix-scroll-area-corner-height":j+"px",...e.style}})})});Ws.displayName=Mn;var Xs="ScrollAreaViewport",Ys=p.forwardRef((e,t)=>{const{__scopeScrollArea:r,children:n,nonce:a,...o}=e,l=Le(Xs,r),i=p.useRef(null),s=Xe(t,i,l.onViewportChange);return g.jsxs(g.Fragment,{children:[g.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:a}),g.jsx(Ee.div,{"data-radix-scroll-area-viewport":"",...o,ref:s,style:{overflowX:l.scrollbarXEnabled?"scroll":"hidden",overflowY:l.scrollbarYEnabled?"scroll":"hidden",...e.style},children:g.jsx("div",{ref:l.onContentChange,style:{minWidth:"100%",display:"table"},children:n})})]})});Ys.displayName=Xs;var Ve="ScrollAreaScrollbar",Fn=p.forwardRef((e,t)=>{const{forceMount:r,...n}=e,a=Le(Ve,e.__scopeScrollArea),{onScrollbarXEnabledChange:o,onScrollbarYEnabledChange:l}=a,i=e.orientation==="horizontal";return p.useEffect(()=>(i?o(!0):l(!0),()=>{i?o(!1):l(!1)}),[i,o,l]),a.type==="hover"?g.jsx(Tg,{...n,ref:t,forceMount:r}):a.type==="scroll"?g.jsx(Rg,{...n,ref:t,forceMount:r}):a.type==="auto"?g.jsx(Ks,{...n,ref:t,forceMount:r}):a.type==="always"?g.jsx($n,{...n,ref:t}):null});Fn.displayName=Ve;var Tg=p.forwardRef((e,t)=>{const{forceMount:r,...n}=e,a=Le(Ve,e.__scopeScrollArea),[o,l]=p.useState(!1);return p.useEffect(()=>{const i=a.scrollArea;let s=0;if(i){const c=()=>{window.clearTimeout(s),l(!0)},u=()=>{s=window.setTimeout(()=>l(!1),a.scrollHideDelay)};return i.addEventListener("pointerenter",c),i.addEventListener("pointerleave",u),()=>{window.clearTimeout(s),i.removeEventListener("pointerenter",c),i.removeEventListener("pointerleave",u)}}},[a.scrollArea,a.scrollHideDelay]),g.jsx(_t,{present:r||o,children:g.jsx(Ks,{"data-state":o?"visible":"hidden",...n,ref:t})})}),Rg=p.forwardRef((e,t)=>{const{forceMount:r,...n}=e,a=Le(Ve,e.__scopeScrollArea),o=e.orientation==="horizontal",l=hr(()=>s("SCROLL_END"),100),[i,s]=kg("hidden",{hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}});return p.useEffect(()=>{if(i==="idle"){const c=window.setTimeout(()=>s("HIDE"),a.scrollHideDelay);return()=>window.clearTimeout(c)}},[i,a.scrollHideDelay,s]),p.useEffect(()=>{const c=a.viewport,u=o?"scrollLeft":"scrollTop";if(c){let d=c[u];const h=()=>{const f=c[u];d!==f&&(s("SCROLL"),l()),d=f};return c.addEventListener("scroll",h),()=>c.removeEventListener("scroll",h)}},[a.viewport,o,s,l]),g.jsx(_t,{present:r||i!=="hidden",children:g.jsx($n,{"data-state":i==="hidden"?"hidden":"visible",...n,ref:t,onPointerEnter:ke(e.onPointerEnter,()=>s("POINTER_ENTER")),onPointerLeave:ke(e.onPointerLeave,()=>s("POINTER_LEAVE"))})})}),Ks=p.forwardRef((e,t)=>{const r=Le(Ve,e.__scopeScrollArea),{forceMount:n,...a}=e,[o,l]=p.useState(!1),i=e.orientation==="horizontal",s=hr(()=>{if(r.viewport){const c=r.viewport.offsetWidth<r.viewport.scrollWidth,u=r.viewport.offsetHeight<r.viewport.scrollHeight;l(i?c:u)}},10);return xt(r.viewport,s),xt(r.content,s),g.jsx(_t,{present:n||o,children:g.jsx($n,{"data-state":o?"visible":"hidden",...a,ref:t})})}),$n=p.forwardRef((e,t)=>{const{orientation:r="vertical",...n}=e,a=Le(Ve,e.__scopeScrollArea),o=p.useRef(null),l=p.useRef(0),[i,s]=p.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),c=ti(i.viewport,i.content),u={...n,sizes:i,onSizesChange:s,hasThumb:c>0&&c<1,onThumbChange:h=>o.current=h,onThumbPointerUp:()=>l.current=0,onThumbPointerDown:h=>l.current=h};function d(h,f){return zg(h,l.current,i,f)}return r==="horizontal"?g.jsx(Ag,{...u,ref:t,onThumbPositionChange:()=>{if(a.viewport&&o.current){const h=a.viewport.scrollLeft,f=na(h,i,a.dir);o.current.style.transform=`translate3d(${f}px, 0, 0)`}},onWheelScroll:h=>{a.viewport&&(a.viewport.scrollLeft=h)},onDragScroll:h=>{a.viewport&&(a.viewport.scrollLeft=d(h,a.dir))}}):r==="vertical"?g.jsx(jg,{...u,ref:t,onThumbPositionChange:()=>{if(a.viewport&&o.current){const h=a.viewport.scrollTop,f=na(h,i);o.current.style.transform=`translate3d(0, ${f}px, 0)`}},onWheelScroll:h=>{a.viewport&&(a.viewport.scrollTop=h)},onDragScroll:h=>{a.viewport&&(a.viewport.scrollTop=d(h))}}):null}),Ag=p.forwardRef((e,t)=>{const{sizes:r,onSizesChange:n,...a}=e,o=Le(Ve,e.__scopeScrollArea),[l,i]=p.useState(),s=p.useRef(null),c=Xe(t,s,o.onScrollbarXChange);return p.useEffect(()=>{s.current&&i(getComputedStyle(s.current))},[s]),g.jsx(Js,{"data-orientation":"horizontal",...a,ref:c,sizes:r,style:{bottom:0,left:o.dir==="rtl"?"var(--radix-scroll-area-corner-width)":0,right:o.dir==="ltr"?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":fr(r)+"px",...e.style},onThumbPointerDown:u=>e.onThumbPointerDown(u.x),onDragScroll:u=>e.onDragScroll(u.x),onWheelScroll:(u,d)=>{if(o.viewport){const h=o.viewport.scrollLeft+u.deltaX;e.onWheelScroll(h),ni(h,d)&&u.preventDefault()}},onResize:()=>{s.current&&o.viewport&&l&&n({content:o.viewport.scrollWidth,viewport:o.viewport.offsetWidth,scrollbar:{size:s.current.clientWidth,paddingStart:Zt(l.paddingLeft),paddingEnd:Zt(l.paddingRight)}})}})}),jg=p.forwardRef((e,t)=>{const{sizes:r,onSizesChange:n,...a}=e,o=Le(Ve,e.__scopeScrollArea),[l,i]=p.useState(),s=p.useRef(null),c=Xe(t,s,o.onScrollbarYChange);return p.useEffect(()=>{s.current&&i(getComputedStyle(s.current))},[s]),g.jsx(Js,{"data-orientation":"vertical",...a,ref:c,sizes:r,style:{top:0,right:o.dir==="ltr"?0:void 0,left:o.dir==="rtl"?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":fr(r)+"px",...e.style},onThumbPointerDown:u=>e.onThumbPointerDown(u.y),onDragScroll:u=>e.onDragScroll(u.y),onWheelScroll:(u,d)=>{if(o.viewport){const h=o.viewport.scrollTop+u.deltaY;e.onWheelScroll(h),ni(h,d)&&u.preventDefault()}},onResize:()=>{s.current&&o.viewport&&l&&n({content:o.viewport.scrollHeight,viewport:o.viewport.offsetHeight,scrollbar:{size:s.current.clientHeight,paddingStart:Zt(l.paddingTop),paddingEnd:Zt(l.paddingBottom)}})}})}),[Ig,Qs]=Us(Ve),Js=p.forwardRef((e,t)=>{const{__scopeScrollArea:r,sizes:n,hasThumb:a,onThumbChange:o,onThumbPointerUp:l,onThumbPointerDown:i,onThumbPositionChange:s,onDragScroll:c,onWheelScroll:u,onResize:d,...h}=e,f=Le(Ve,r),[b,y]=p.useState(null),C=Xe(t,x=>y(x)),N=p.useRef(null),E=p.useRef(""),j=f.viewport,A=n.content-n.viewport,I=ct(u),P=ct(s),m=hr(d,10);function S(x){if(N.current){const T=x.clientX-N.current.left,R=x.clientY-N.current.top;c({x:T,y:R})}}return p.useEffect(()=>{const x=T=>{const R=T.target;(b==null?void 0:b.contains(R))&&I(T,A)};return document.addEventListener("wheel",x,{passive:!1}),()=>document.removeEventListener("wheel",x,{passive:!1})},[j,b,A,I]),p.useEffect(P,[n,P]),xt(b,m),xt(f.content,m),g.jsx(Ig,{scope:r,scrollbar:b,hasThumb:a,onThumbChange:ct(o),onThumbPointerUp:ct(l),onThumbPositionChange:P,onThumbPointerDown:ct(i),children:g.jsx(Ee.div,{...h,ref:C,style:{position:"absolute",...h.style},onPointerDown:ke(e.onPointerDown,x=>{x.button===0&&(x.target.setPointerCapture(x.pointerId),N.current=b.getBoundingClientRect(),E.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",f.viewport&&(f.viewport.style.scrollBehavior="auto"),S(x))}),onPointerMove:ke(e.onPointerMove,S),onPointerUp:ke(e.onPointerUp,x=>{const T=x.target;T.hasPointerCapture(x.pointerId)&&T.releasePointerCapture(x.pointerId),document.body.style.webkitUserSelect=E.current,f.viewport&&(f.viewport.style.scrollBehavior=""),N.current=null})})})}),Jt="ScrollAreaThumb",Zs=p.forwardRef((e,t)=>{const{forceMount:r,...n}=e,a=Qs(Jt,e.__scopeScrollArea);return g.jsx(_t,{present:r||a.hasThumb,children:g.jsx(Ng,{ref:t,...n})})}),Ng=p.forwardRef((e,t)=>{const{__scopeScrollArea:r,style:n,...a}=e,o=Le(Jt,r),l=Qs(Jt,r),{onThumbPositionChange:i}=l,s=Xe(t,d=>l.onThumbChange(d)),c=p.useRef(void 0),u=hr(()=>{c.current&&(c.current(),c.current=void 0)},100);return p.useEffect(()=>{const d=o.viewport;if(d){const h=()=>{if(u(),!c.current){const f=Pg(d,i);c.current=f,i()}};return i(),d.addEventListener("scroll",h),()=>d.removeEventListener("scroll",h)}},[o.viewport,u,i]),g.jsx(Ee.div,{"data-state":l.hasThumb?"visible":"hidden",...a,ref:s,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...n},onPointerDownCapture:ke(e.onPointerDownCapture,d=>{const f=d.target.getBoundingClientRect(),b=d.clientX-f.left,y=d.clientY-f.top;l.onThumbPointerDown({x:b,y})}),onPointerUp:ke(e.onPointerUp,l.onThumbPointerUp)})});Zs.displayName=Jt;var Hn="ScrollAreaCorner",ei=p.forwardRef((e,t)=>{const r=Le(Hn,e.__scopeScrollArea),n=!!(r.scrollbarX&&r.scrollbarY);return r.type!=="scroll"&&n?g.jsx(Lg,{...e,ref:t}):null});ei.displayName=Hn;var Lg=p.forwardRef((e,t)=>{const{__scopeScrollArea:r,...n}=e,a=Le(Hn,r),[o,l]=p.useState(0),[i,s]=p.useState(0),c=!!(o&&i);return xt(a.scrollbarX,()=>{var d;const u=((d=a.scrollbarX)==null?void 0:d.offsetHeight)||0;a.onCornerHeightChange(u),s(u)}),xt(a.scrollbarY,()=>{var d;const u=((d=a.scrollbarY)==null?void 0:d.offsetWidth)||0;a.onCornerWidthChange(u),l(u)}),c?g.jsx(Ee.div,{...n,ref:t,style:{width:o,height:i,position:"absolute",right:a.dir==="ltr"?0:void 0,left:a.dir==="rtl"?0:void 0,bottom:0,...e.style}}):null});function Zt(e){return e?parseInt(e,10):0}function ti(e,t){const r=e/t;return isNaN(r)?0:r}function fr(e){const t=ti(e.viewport,e.content),r=e.scrollbar.paddingStart+e.scrollbar.paddingEnd,n=(e.scrollbar.size-r)*t;return Math.max(n,18)}function zg(e,t,r,n="ltr"){const a=fr(r),o=a/2,l=t||o,i=a-l,s=r.scrollbar.paddingStart+l,c=r.scrollbar.size-r.scrollbar.paddingEnd-i,u=r.content-r.viewport,d=n==="ltr"?[0,u]:[u*-1,0];return ri([s,c],d)(e)}function na(e,t,r="ltr"){const n=fr(t),a=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,o=t.scrollbar.size-a,l=t.content-t.viewport,i=o-n,s=r==="ltr"?[0,l]:[l*-1,0],c=$i(e,s);return ri([0,l],[0,i])(c)}function ri(e,t){return r=>{if(e[0]===e[1]||t[0]===t[1])return t[0];const n=(t[1]-t[0])/(e[1]-e[0]);return t[0]+n*(r-e[0])}}function ni(e,t){return e>0&&e<t}var Pg=(e,t=()=>{})=>{let r={left:e.scrollLeft,top:e.scrollTop},n=0;return function a(){const o={left:e.scrollLeft,top:e.scrollTop},l=r.left!==o.left,i=r.top!==o.top;(l||i)&&t(),r=o,n=window.requestAnimationFrame(a)}(),()=>window.cancelAnimationFrame(n)};function hr(e,t){const r=ct(e),n=p.useRef(0);return p.useEffect(()=>()=>window.clearTimeout(n.current),[]),p.useCallback(()=>{window.clearTimeout(n.current),n.current=window.setTimeout(r,t)},[r,t])}function xt(e,t){const r=ct(t);Fi(()=>{let n=0;if(e){const a=new ResizeObserver(()=>{cancelAnimationFrame(n),n=window.requestAnimationFrame(r)});return a.observe(e),()=>{window.cancelAnimationFrame(n),a.unobserve(e)}}},[e,r])}var oi=Ws,Dg=Ys,Og=ei;const ai=p.forwardRef(({className:e,children:t,...r},n)=>g.jsxs(oi,{ref:n,className:fe("relative overflow-hidden",e),...r,children:[g.jsx(Dg,{className:"h-full w-full rounded-[inherit]",children:t}),g.jsx(si,{}),g.jsx(Og,{})]}));ai.displayName=oi.displayName;const si=p.forwardRef(({className:e,orientation:t="vertical",...r},n)=>g.jsx(Fn,{ref:n,orientation:t,className:fe("flex touch-none transition-colors select-none",t==="vertical"&&"h-full w-2.5 border-l border-l-transparent p-[1px]",t==="horizontal"&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",e),...r,children:g.jsx(Zs,{className:"bg-border relative flex-1 rounded-full"})}));si.displayName=Fn.displayName;const Gg=({className:e})=>{const{t}=_e(),r=te.use.typeColorMap();return!r||r.size===0?null:g.jsxs(qs,{className:`p-2 max-w-xs ${e}`,children:[g.jsx("h3",{className:"text-sm font-medium mb-2",children:t("graphPanel.legend")}),g.jsx(ai,{className:"max-h-80",children:g.jsx("div",{className:"flex flex-col gap-1",children:Array.from(r.entries()).map(([n,a])=>g.jsxs("div",{className:"flex items-center gap-2",children:[g.jsx("div",{className:"w-4 h-4 rounded-full",style:{backgroundColor:a}}),g.jsx("span",{className:"text-xs truncate",title:n,children:t(`graphPanel.nodeTypes.${n.toLowerCase()}`,n)})]},n))})})]})},Mg=()=>{const{t:e}=_e(),t=Z.use.showLegend(),r=Z.use.setShowLegend(),n=p.useCallback(()=>{r(!t)},[t,r]);return g.jsx(be,{variant:Ne,onClick:n,tooltip:e("graphPanel.sideBar.legendControl.toggleLegend"),size:"icon",children:g.jsx(Nc,{})})},oa={allowInvalidContainer:!0,defaultNodeType:"default",defaultEdgeType:"curvedNoArrow",renderEdgeLabels:!1,edgeProgramClasses:{arrow:_i,curvedArrow:Pd,curvedNoArrow:lr()},nodeProgramClasses:{default:yd,circel:xi,point:Uu},labelGridCellSize:60,labelRenderedSizeThreshold:12,enableEdgeEvents:!0,labelColor:{color:"#000",attribute:"labelColor"},edgeLabelColor:{color:"#000",attribute:"labelColor"},edgeLabelSize:8,labelSize:12},Fg=()=>{const e=ga(),t=Be(),[r,n]=p.useState(null);return p.useEffect(()=>{e({downNode:a=>{n(a.node),t.getGraph().setNodeAttribute(a.node,"highlighted",!0)},mousemovebody:a=>{if(!r)return;const o=t.viewportToGraph(a);t.getGraph().setNodeAttribute(r,"x",o.x),t.getGraph().setNodeAttribute(r,"y",o.y),a.preventSigmaDefault(),a.original.preventDefault(),a.original.stopPropagation()},mouseup:()=>{r&&(n(null),t.getGraph().removeNodeAttribute(r,"highlighted"))},mousedown:a=>{a.original.buttons!==0&&!t.getCustomBBox()&&t.setCustomBBox(t.getBBox())}})},[e,t,r]),null},Vp=()=>{const[e,t]=p.useState(oa),r=p.useRef(null),n=te.use.selectedNode(),a=te.use.focusedNode(),o=te.use.moveToSelectedNode(),l=te.use.isFetching(),i=Z.use.showPropertyPanel(),s=Z.use.showNodeSearchBar(),c=Z.use.enableNodeDrag(),u=Z.use.showLegend();p.useEffect(()=>{t(oa),console.log("Initialized sigma settings")},[]),p.useEffect(()=>()=>{const y=te.getState().sigmaInstance;if(y)try{y.kill(),te.getState().setSigmaInstance(null),console.log("Cleared sigma instance on Graphviewer unmount")}catch(C){console.error("Error cleaning up sigma instance:",C)}},[]);const d=p.useCallback(y=>{y===null?te.getState().setFocusedNode(null):y.type==="nodes"&&te.getState().setFocusedNode(y.id)},[]),h=p.useCallback(y=>{y===null?te.getState().setSelectedNode(null):y.type==="nodes"&&te.getState().setSelectedNode(y.id,!0)},[]),f=p.useMemo(()=>a??n,[a,n]),b=p.useMemo(()=>n?{type:"nodes",id:n}:null,[n]);return g.jsxs("div",{className:"relative h-full w-full overflow-hidden",children:[g.jsxs(Si,{settings:e,className:"!bg-background !size-full overflow-hidden",ref:r,children:[g.jsx(rh,{}),c&&g.jsx(Fg,{}),g.jsx(Dd,{node:f,move:o}),g.jsxs("div",{className:"absolute top-2 left-2 flex items-start gap-2",children:[g.jsx(Oh,{}),s&&g.jsx(zh,{value:b,onFocus:d,onChange:h})]}),g.jsxs("div",{className:"bg-background/60 absolute bottom-2 left-2 flex flex-col rounded-xl border-2 backdrop-blur-lg",children:[g.jsx(eh,{}),g.jsx(nh,{}),g.jsx(oh,{}),g.jsx(Mg,{}),g.jsx(hh,{})]}),i&&g.jsx("div",{className:"absolute top-2 right-2",children:g.jsx(gg,{})}),u&&g.jsx("div",{className:"absolute bottom-10 right-2",children:g.jsx(Gg,{className:"bg-background/60 backdrop-blur-lg"})}),g.jsx(bg,{})]}),l&&g.jsx("div",{className:"absolute inset-0 flex items-center justify-center bg-background/80 z-10",children:g.jsxs("div",{className:"text-center",children:[g.jsx("div",{className:"mb-2 h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"}),g.jsx("p",{children:"Loading Graph Data..."})]})})]})};export{yp as $,kp as A,be as B,wp as C,Mu as D,Tp as E,Ap as F,mp as G,pp as H,Wt as I,vp as J,sp as K,qa as L,kn as M,Z as N,Dp as O,lp as P,Jg as Q,wg as R,ai as S,Gp as T,Mp as U,Sg as V,pu as W,Lu as X,gp as Y,vu as Z,bp as _,Sp as a,Ip as a0,Fa as a1,$a as a2,Ha as a3,Tn as a4,th as a5,Np as a6,Cp as a7,Zi as a8,ep as a9,Zg as aa,Ug as ab,Bs as ac,Pp as ad,no as ae,Kg as af,Qg as ag,Rn as ah,An as ai,zp as aj,ir as ak,Ut as al,Wg as am,Fp as an,Yg as ao,jp as ap,Lp as aq,Ea as ar,Qr as as,up as at,Vp as au,ap as av,ip as aw,cp as ax,dp as ay,Va as b,fe as c,qs as d,xg as e,_g as f,rt as g,Rp as h,tp as i,rr as j,$p as k,Xa as l,Ya as m,Qa as n,Ja as o,rp as p,np as q,Ls as r,Xg as s,Ka as t,_e as u,op as v,Op as w,xp as x,_p as y,Ep as z};
