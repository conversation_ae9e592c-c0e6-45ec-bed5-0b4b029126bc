name: Build Test Docker Image manually

on:
  workflow_dispatch:

permissions:
  contents: read
  packages: write

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Fetch all history for tags

      - name: Get latest tag
        id: get_tag
        run: |
          # Get the latest tag, fallback to commit SHA if no tags exist
          LATEST_TAG=$(git describe --tags --abbrev=0 2>/dev/null || echo "")
          if [ -z "$LATEST_TAG" ]; then
            LATEST_TAG="sha-$(git rev-parse --short HEAD)"
            echo "No tags found, using commit SHA: $LATEST_TAG"
          else
            echo "Latest tag found: $LATEST_TAG"
          fi
          echo "tag=$LATEST_TAG" >> $GITHUB_OUTPUT
          echo "image_tag=$LATEST_TAG" >> $GITHUB_OUTPUT

      - name: Update version in __init__.py
        run: |
          sed -i "s/__version__ = \".*\"/__version__ = \"${{ steps.get_tag.outputs.tag }}\"/" lightrag/__init__.py
          echo "Updated __init__.py with version ${{ steps.get_tag.outputs.tag }}"
          cat lightrag/__init__.py | grep __version__

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata for Docker
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ghcr.io/${{ github.repository }}
          tags: |
            type=raw,value=${{ steps.get_tag.outputs.tag }}

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          platforms: linux/amd64,linux/arm64
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Output image details
        run: |
          echo "Docker image built and pushed successfully!"
          echo "Image tags:"
          echo "  - ghcr.io/${{ github.repository }}:${{ steps.get_tag.outputs.tag }}"
          echo "Latest Git tag used: ${{ steps.get_tag.outputs.tag }}"
