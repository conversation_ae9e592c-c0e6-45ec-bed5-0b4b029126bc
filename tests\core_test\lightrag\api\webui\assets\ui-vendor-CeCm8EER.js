import{r as c,a as zr,b as mt,c as Gr,R as be}from"./react-vendor-DEwriMA6.js";var Tt={exports:{}},Ke={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var bn;function Yr(){if(bn)return Ke;bn=1;var e=Symbol.for("react.transitional.element"),t=Symbol.for("react.fragment");function n(o,r,i){var s=null;if(i!==void 0&&(s=""+i),r.key!==void 0&&(s=""+r.key),"key"in r){i={};for(var a in r)a!=="key"&&(i[a]=r[a])}else i=r;return r=i.ref,{$$typeof:e,type:o,key:s,ref:r!==void 0?r:null,props:i}}return Ke.Fragment=t,Ke.jsx=n,Ke.jsxs=n,Ke}var Cn;function Xr(){return Cn||(Cn=1,Tt.exports=Yr()),Tt.exports}var v=Xr();function qr(e,t){const n=c.createContext(t),o=i=>{const{children:s,...a}=i,u=c.useMemo(()=>a,Object.values(a));return v.jsx(n.Provider,{value:u,children:s})};o.displayName=e+"Provider";function r(i){const s=c.useContext(n);if(s)return s;if(t!==void 0)return t;throw new Error(`\`${i}\` must be used within \`${e}\``)}return[o,r]}function Pe(e,t=[]){let n=[];function o(i,s){const a=c.createContext(s),u=n.length;n=[...n,s];const l=p=>{var y;const{scope:m,children:h,...w}=p,f=((y=m==null?void 0:m[e])==null?void 0:y[u])||a,g=c.useMemo(()=>w,Object.values(w));return v.jsx(f.Provider,{value:g,children:h})};l.displayName=i+"Provider";function d(p,m){var f;const h=((f=m==null?void 0:m[e])==null?void 0:f[u])||a,w=c.useContext(h);if(w)return w;if(s!==void 0)return s;throw new Error(`\`${p}\` must be used within \`${i}\``)}return[l,d]}const r=()=>{const i=n.map(s=>c.createContext(s));return function(a){const u=(a==null?void 0:a[e])||i;return c.useMemo(()=>({[`__scope${e}`]:{...a,[e]:u}}),[a,u])}};return r.scopeName=e,[o,Zr(r,...t)]}function Zr(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const o=e.map(r=>({useScope:r(),scopeName:r.scopeName}));return function(i){const s=o.reduce((a,{useScope:u,scopeName:l})=>{const p=u(i)[`__scope${l}`];return{...a,...p}},{});return c.useMemo(()=>({[`__scope${t.scopeName}`]:s}),[s])}};return n.scopeName=t.scopeName,n}function Sn(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function Kn(...e){return t=>{let n=!1;const o=e.map(r=>{const i=Sn(r,t);return!n&&typeof i=="function"&&(n=!0),i});if(n)return()=>{for(let r=0;r<o.length;r++){const i=o[r];typeof i=="function"?i():Sn(e[r],null)}}}}function K(...e){return c.useCallback(Kn(...e),e)}function I(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e==null||e(r),n===!1||!r.defaultPrevented)return t==null?void 0:t(r)}}var G=globalThis!=null&&globalThis.document?c.useLayoutEffect:()=>{},Jr=zr.useId||(()=>{}),Qr=0;function le(e){const[t,n]=c.useState(Jr());return G(()=>{n(o=>o??String(Qr++))},[e]),t?`radix-${t}`:""}function ie(e){const t=c.useRef(e);return c.useEffect(()=>{t.current=e}),c.useMemo(()=>(...n)=>{var o;return(o=t.current)==null?void 0:o.call(t,...n)},[])}function De({prop:e,defaultProp:t,onChange:n=()=>{}}){const[o,r]=ei({defaultProp:t,onChange:n}),i=e!==void 0,s=i?e:o,a=ie(n),u=c.useCallback(l=>{if(i){const p=typeof l=="function"?l(e):l;p!==e&&a(p)}else r(l)},[i,e,r,a]);return[s,u]}function ei({defaultProp:e,onChange:t}){const n=c.useState(e),[o]=n,r=c.useRef(o),i=ie(t);return c.useEffect(()=>{r.current!==o&&(i(o),r.current=o)},[o,r,i]),n}var Ce=c.forwardRef((e,t)=>{const{children:n,...o}=e,r=c.Children.toArray(n),i=r.find(ni);if(i){const s=i.props.children,a=r.map(u=>u===i?c.Children.count(s)>1?c.Children.only(null):c.isValidElement(s)?s.props.children:null:u);return v.jsx(Wt,{...o,ref:t,children:c.isValidElement(s)?c.cloneElement(s,void 0,a):null})}return v.jsx(Wt,{...o,ref:t,children:n})});Ce.displayName="Slot";var Wt=c.forwardRef((e,t)=>{const{children:n,...o}=e;if(c.isValidElement(n)){const r=ri(n),i=oi(o,n.props);return n.type!==c.Fragment&&(i.ref=t?Kn(t,r):r),c.cloneElement(n,i)}return c.Children.count(n)>1?c.Children.only(null):null});Wt.displayName="SlotClone";var ti=({children:e})=>v.jsx(v.Fragment,{children:e});function ni(e){return c.isValidElement(e)&&e.type===ti}function oi(e,t){const n={...t};for(const o in t){const r=e[o],i=t[o];/^on[A-Z]/.test(o)?r&&i?n[o]=(...a)=>{i(...a),r(...a)}:r&&(n[o]=r):o==="style"?n[o]={...r,...i}:o==="className"&&(n[o]=[r,i].filter(Boolean).join(" "))}return{...e,...n}}function ri(e){var o,r;let t=(o=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(r=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var ii=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],N=ii.reduce((e,t)=>{const n=c.forwardRef((o,r)=>{const{asChild:i,...s}=o,a=i?Ce:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),v.jsx(a,{...s,ref:r})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function si(e,t){e&&mt.flushSync(()=>e.dispatchEvent(t))}function ai(e,t=globalThis==null?void 0:globalThis.document){const n=ie(e);c.useEffect(()=>{const o=r=>{r.key==="Escape"&&n(r)};return t.addEventListener("keydown",o,{capture:!0}),()=>t.removeEventListener("keydown",o,{capture:!0})},[n,t])}var ci="DismissableLayer",Bt="dismissableLayer.update",li="dismissableLayer.pointerDownOutside",ui="dismissableLayer.focusOutside",En,zn=c.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),ht=c.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:o,onPointerDownOutside:r,onFocusOutside:i,onInteractOutside:s,onDismiss:a,...u}=e,l=c.useContext(zn),[d,p]=c.useState(null),m=(d==null?void 0:d.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,h]=c.useState({}),w=K(t,E=>p(E)),f=Array.from(l.layers),[g]=[...l.layersWithOutsidePointerEventsDisabled].slice(-1),y=f.indexOf(g),x=d?f.indexOf(d):-1,b=l.layersWithOutsidePointerEventsDisabled.size>0,C=x>=y,S=pi(E=>{const R=E.target,D=[...l.branches].some(_=>_.contains(R));!C||D||(r==null||r(E),s==null||s(E),E.defaultPrevented||a==null||a())},m),P=mi(E=>{const R=E.target;[...l.branches].some(_=>_.contains(R))||(i==null||i(E),s==null||s(E),E.defaultPrevented||a==null||a())},m);return ai(E=>{x===l.layers.size-1&&(o==null||o(E),!E.defaultPrevented&&a&&(E.preventDefault(),a()))},m),c.useEffect(()=>{if(d)return n&&(l.layersWithOutsidePointerEventsDisabled.size===0&&(En=m.body.style.pointerEvents,m.body.style.pointerEvents="none"),l.layersWithOutsidePointerEventsDisabled.add(d)),l.layers.add(d),Rn(),()=>{n&&l.layersWithOutsidePointerEventsDisabled.size===1&&(m.body.style.pointerEvents=En)}},[d,m,n,l]),c.useEffect(()=>()=>{d&&(l.layers.delete(d),l.layersWithOutsidePointerEventsDisabled.delete(d),Rn())},[d,l]),c.useEffect(()=>{const E=()=>h({});return document.addEventListener(Bt,E),()=>document.removeEventListener(Bt,E)},[]),v.jsx(N.div,{...u,ref:w,style:{pointerEvents:b?C?"auto":"none":void 0,...e.style},onFocusCapture:I(e.onFocusCapture,P.onFocusCapture),onBlurCapture:I(e.onBlurCapture,P.onBlurCapture),onPointerDownCapture:I(e.onPointerDownCapture,S.onPointerDownCapture)})});ht.displayName=ci;var fi="DismissableLayerBranch",di=c.forwardRef((e,t)=>{const n=c.useContext(zn),o=c.useRef(null),r=K(t,o);return c.useEffect(()=>{const i=o.current;if(i)return n.branches.add(i),()=>{n.branches.delete(i)}},[n.branches]),v.jsx(N.div,{...e,ref:r})});di.displayName=fi;function pi(e,t=globalThis==null?void 0:globalThis.document){const n=ie(e),o=c.useRef(!1),r=c.useRef(()=>{});return c.useEffect(()=>{const i=a=>{if(a.target&&!o.current){let u=function(){Gn(li,n,l,{discrete:!0})};const l={originalEvent:a};a.pointerType==="touch"?(t.removeEventListener("click",r.current),r.current=u,t.addEventListener("click",r.current,{once:!0})):u()}else t.removeEventListener("click",r.current);o.current=!1},s=window.setTimeout(()=>{t.addEventListener("pointerdown",i)},0);return()=>{window.clearTimeout(s),t.removeEventListener("pointerdown",i),t.removeEventListener("click",r.current)}},[t,n]),{onPointerDownCapture:()=>o.current=!0}}function mi(e,t=globalThis==null?void 0:globalThis.document){const n=ie(e),o=c.useRef(!1);return c.useEffect(()=>{const r=i=>{i.target&&!o.current&&Gn(ui,n,{originalEvent:i},{discrete:!1})};return t.addEventListener("focusin",r),()=>t.removeEventListener("focusin",r)},[t,n]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}function Rn(){const e=new CustomEvent(Bt);document.dispatchEvent(e)}function Gn(e,t,n,{discrete:o}){const r=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&r.addEventListener(e,t,{once:!0}),o?si(r,i):r.dispatchEvent(i)}var Ot="focusScope.autoFocusOnMount",It="focusScope.autoFocusOnUnmount",Pn={bubbles:!1,cancelable:!0},hi="FocusScope",gt=c.forwardRef((e,t)=>{const{loop:n=!1,trapped:o=!1,onMountAutoFocus:r,onUnmountAutoFocus:i,...s}=e,[a,u]=c.useState(null),l=ie(r),d=ie(i),p=c.useRef(null),m=K(t,f=>u(f)),h=c.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;c.useEffect(()=>{if(o){let f=function(b){if(h.paused||!a)return;const C=b.target;a.contains(C)?p.current=C:pe(p.current,{select:!0})},g=function(b){if(h.paused||!a)return;const C=b.relatedTarget;C!==null&&(a.contains(C)||pe(p.current,{select:!0}))},y=function(b){if(document.activeElement===document.body)for(const S of b)S.removedNodes.length>0&&pe(a)};document.addEventListener("focusin",f),document.addEventListener("focusout",g);const x=new MutationObserver(y);return a&&x.observe(a,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",f),document.removeEventListener("focusout",g),x.disconnect()}}},[o,a,h.paused]),c.useEffect(()=>{if(a){Tn.add(h);const f=document.activeElement;if(!a.contains(f)){const y=new CustomEvent(Ot,Pn);a.addEventListener(Ot,l),a.dispatchEvent(y),y.defaultPrevented||(gi(bi(Yn(a)),{select:!0}),document.activeElement===f&&pe(a))}return()=>{a.removeEventListener(Ot,l),setTimeout(()=>{const y=new CustomEvent(It,Pn);a.addEventListener(It,d),a.dispatchEvent(y),y.defaultPrevented||pe(f??document.body,{select:!0}),a.removeEventListener(It,d),Tn.remove(h)},0)}}},[a,l,d,h]);const w=c.useCallback(f=>{if(!n&&!o||h.paused)return;const g=f.key==="Tab"&&!f.altKey&&!f.ctrlKey&&!f.metaKey,y=document.activeElement;if(g&&y){const x=f.currentTarget,[b,C]=vi(x);b&&C?!f.shiftKey&&y===C?(f.preventDefault(),n&&pe(b,{select:!0})):f.shiftKey&&y===b&&(f.preventDefault(),n&&pe(C,{select:!0})):y===x&&f.preventDefault()}},[n,o,h.paused]);return v.jsx(N.div,{tabIndex:-1,...s,ref:m,onKeyDown:w})});gt.displayName=hi;function gi(e,{select:t=!1}={}){const n=document.activeElement;for(const o of e)if(pe(o,{select:t}),document.activeElement!==n)return}function vi(e){const t=Yn(e),n=An(t,e),o=An(t.reverse(),e);return[n,o]}function Yn(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:o=>{const r=o.tagName==="INPUT"&&o.type==="hidden";return o.disabled||o.hidden||r?NodeFilter.FILTER_SKIP:o.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function An(e,t){for(const n of e)if(!wi(n,{upTo:t}))return n}function wi(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function yi(e){return e instanceof HTMLInputElement&&"select"in e}function pe(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&yi(e)&&t&&e.select()}}var Tn=xi();function xi(){let e=[];return{add(t){const n=e[0];t!==n&&(n==null||n.pause()),e=On(e,t),e.unshift(t)},remove(t){var n;e=On(e,t),(n=e[0])==null||n.resume()}}}function On(e,t){const n=[...e],o=n.indexOf(t);return o!==-1&&n.splice(o,1),n}function bi(e){return e.filter(t=>t.tagName!=="A")}var Ci="Portal",vt=c.forwardRef((e,t)=>{var a;const{container:n,...o}=e,[r,i]=c.useState(!1);G(()=>i(!0),[]);const s=n||r&&((a=globalThis==null?void 0:globalThis.document)==null?void 0:a.body);return s?Gr.createPortal(v.jsx(N.div,{...o,ref:t}),s):null});vt.displayName=Ci;function Si(e,t){return c.useReducer((n,o)=>t[n][o]??n,e)}var Ae=e=>{const{present:t,children:n}=e,o=Ei(t),r=typeof n=="function"?n({present:o.isPresent}):c.Children.only(n),i=K(o.ref,Ri(r));return typeof n=="function"||o.isPresent?c.cloneElement(r,{ref:i}):null};Ae.displayName="Presence";function Ei(e){const[t,n]=c.useState(),o=c.useRef({}),r=c.useRef(e),i=c.useRef("none"),s=e?"mounted":"unmounted",[a,u]=Si(s,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return c.useEffect(()=>{const l=Qe(o.current);i.current=a==="mounted"?l:"none"},[a]),G(()=>{const l=o.current,d=r.current;if(d!==e){const m=i.current,h=Qe(l);e?u("MOUNT"):h==="none"||(l==null?void 0:l.display)==="none"?u("UNMOUNT"):u(d&&m!==h?"ANIMATION_OUT":"UNMOUNT"),r.current=e}},[e,u]),G(()=>{if(t){let l;const d=t.ownerDocument.defaultView??window,p=h=>{const f=Qe(o.current).includes(h.animationName);if(h.target===t&&f&&(u("ANIMATION_END"),!r.current)){const g=t.style.animationFillMode;t.style.animationFillMode="forwards",l=d.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=g)})}},m=h=>{h.target===t&&(i.current=Qe(o.current))};return t.addEventListener("animationstart",m),t.addEventListener("animationcancel",p),t.addEventListener("animationend",p),()=>{d.clearTimeout(l),t.removeEventListener("animationstart",m),t.removeEventListener("animationcancel",p),t.removeEventListener("animationend",p)}}else u("ANIMATION_END")},[t,u]),{isPresent:["mounted","unmountSuspended"].includes(a),ref:c.useCallback(l=>{l&&(o.current=getComputedStyle(l)),n(l)},[])}}function Qe(e){return(e==null?void 0:e.animationName)||"none"}function Ri(e){var o,r;let t=(o=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(r=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var Nt=0;function Zt(){c.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??In()),document.body.insertAdjacentElement("beforeend",e[1]??In()),Nt++,()=>{Nt===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),Nt--}},[])}function In(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var oe=function(){return oe=Object.assign||function(t){for(var n,o=1,r=arguments.length;o<r;o++){n=arguments[o];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i])}return t},oe.apply(this,arguments)};function Xn(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n}function gc(e,t,n,o){function r(i){return i instanceof n?i:new n(function(s){s(i)})}return new(n||(n=Promise))(function(i,s){function a(d){try{l(o.next(d))}catch(p){s(p)}}function u(d){try{l(o.throw(d))}catch(p){s(p)}}function l(d){d.done?i(d.value):r(d.value).then(a,u)}l((o=o.apply(e,t||[])).next())})}function Pi(e,t,n){if(n||arguments.length===2)for(var o=0,r=t.length,i;o<r;o++)(i||!(o in t))&&(i||(i=Array.prototype.slice.call(t,0,o)),i[o]=t[o]);return e.concat(i||Array.prototype.slice.call(t))}var it="right-scroll-bar-position",st="width-before-scroll-bar",Ai="with-scroll-bars-hidden",Ti="--removed-body-scroll-bar-size";function _t(e,t){return typeof e=="function"?e(t):e&&(e.current=t),e}function Oi(e,t){var n=c.useState(function(){return{value:e,callback:t,facade:{get current(){return n.value},set current(o){var r=n.value;r!==o&&(n.value=o,n.callback(o,r))}}}})[0];return n.callback=t,n.facade}var Ii=typeof window<"u"?c.useLayoutEffect:c.useEffect,Nn=new WeakMap;function Ni(e,t){var n=Oi(null,function(o){return e.forEach(function(r){return _t(r,o)})});return Ii(function(){var o=Nn.get(n);if(o){var r=new Set(o),i=new Set(e),s=n.current;r.forEach(function(a){i.has(a)||_t(a,null)}),i.forEach(function(a){r.has(a)||_t(a,s)})}Nn.set(n,e)},[e]),n}function _i(e){return e}function Mi(e,t){t===void 0&&(t=_i);var n=[],o=!1,r={read:function(){if(o)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(i){var s=t(i,o);return n.push(s),function(){n=n.filter(function(a){return a!==s})}},assignSyncMedium:function(i){for(o=!0;n.length;){var s=n;n=[],s.forEach(i)}n={push:function(a){return i(a)},filter:function(){return n}}},assignMedium:function(i){o=!0;var s=[];if(n.length){var a=n;n=[],a.forEach(i),s=n}var u=function(){var d=s;s=[],d.forEach(i)},l=function(){return Promise.resolve().then(u)};l(),n={push:function(d){s.push(d),l()},filter:function(d){return s=s.filter(d),n}}}};return r}function Di(e){e===void 0&&(e={});var t=Mi(null);return t.options=oe({async:!0,ssr:!1},e),t}var qn=function(e){var t=e.sideCar,n=Xn(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var o=t.read();if(!o)throw new Error("Sidecar medium not found");return c.createElement(o,oe({},n))};qn.isSideCarExport=!0;function Fi(e,t){return e.useMedium(t),qn}var Zn=Di(),Mt=function(){},wt=c.forwardRef(function(e,t){var n=c.useRef(null),o=c.useState({onScrollCapture:Mt,onWheelCapture:Mt,onTouchMoveCapture:Mt}),r=o[0],i=o[1],s=e.forwardProps,a=e.children,u=e.className,l=e.removeScrollBar,d=e.enabled,p=e.shards,m=e.sideCar,h=e.noIsolation,w=e.inert,f=e.allowPinchZoom,g=e.as,y=g===void 0?"div":g,x=e.gapMode,b=Xn(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),C=m,S=Ni([n,t]),P=oe(oe({},b),r);return c.createElement(c.Fragment,null,d&&c.createElement(C,{sideCar:Zn,removeScrollBar:l,shards:p,noIsolation:h,inert:w,setCallbacks:i,allowPinchZoom:!!f,lockRef:n,gapMode:x}),s?c.cloneElement(c.Children.only(a),oe(oe({},P),{ref:S})):c.createElement(y,oe({},P,{className:u,ref:S}),a))});wt.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};wt.classNames={fullWidth:st,zeroRight:it};var Li=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function ki(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=Li();return t&&e.setAttribute("nonce",t),e}function ji(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function Wi(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var Bi=function(){var e=0,t=null;return{add:function(n){e==0&&(t=ki())&&(ji(t,n),Wi(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},$i=function(){var e=Bi();return function(t,n){c.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},Jn=function(){var e=$i(),t=function(n){var o=n.styles,r=n.dynamic;return e(o,r),null};return t},Vi={left:0,top:0,right:0,gap:0},Dt=function(e){return parseInt(e||"",10)||0},Hi=function(e){var t=window.getComputedStyle(document.body),n=t[e==="padding"?"paddingLeft":"marginLeft"],o=t[e==="padding"?"paddingTop":"marginTop"],r=t[e==="padding"?"paddingRight":"marginRight"];return[Dt(n),Dt(o),Dt(r)]},Ui=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return Vi;var t=Hi(e),n=document.documentElement.clientWidth,o=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,o-n+t[2]-t[0])}},Ki=Jn(),_e="data-scroll-locked",zi=function(e,t,n,o){var r=e.left,i=e.top,s=e.right,a=e.gap;return n===void 0&&(n="margin"),`
  .`.concat(Ai,` {
   overflow: hidden `).concat(o,`;
   padding-right: `).concat(a,"px ").concat(o,`;
  }
  body[`).concat(_e,`] {
    overflow: hidden `).concat(o,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(o,";"),n==="margin"&&`
    padding-left: `.concat(r,`px;
    padding-top: `).concat(i,`px;
    padding-right: `).concat(s,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(a,"px ").concat(o,`;
    `),n==="padding"&&"padding-right: ".concat(a,"px ").concat(o,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(it,` {
    right: `).concat(a,"px ").concat(o,`;
  }
  
  .`).concat(st,` {
    margin-right: `).concat(a,"px ").concat(o,`;
  }
  
  .`).concat(it," .").concat(it,` {
    right: 0 `).concat(o,`;
  }
  
  .`).concat(st," .").concat(st,` {
    margin-right: 0 `).concat(o,`;
  }
  
  body[`).concat(_e,`] {
    `).concat(Ti,": ").concat(a,`px;
  }
`)},_n=function(){var e=parseInt(document.body.getAttribute(_e)||"0",10);return isFinite(e)?e:0},Gi=function(){c.useEffect(function(){return document.body.setAttribute(_e,(_n()+1).toString()),function(){var e=_n()-1;e<=0?document.body.removeAttribute(_e):document.body.setAttribute(_e,e.toString())}},[])},Yi=function(e){var t=e.noRelative,n=e.noImportant,o=e.gapMode,r=o===void 0?"margin":o;Gi();var i=c.useMemo(function(){return Ui(r)},[r]);return c.createElement(Ki,{styles:zi(i,!t,r,n?"":"!important")})},$t=!1;if(typeof window<"u")try{var et=Object.defineProperty({},"passive",{get:function(){return $t=!0,!0}});window.addEventListener("test",et,et),window.removeEventListener("test",et,et)}catch{$t=!1}var Oe=$t?{passive:!1}:!1,Xi=function(e){return e.tagName==="TEXTAREA"},Qn=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!Xi(e)&&n[t]==="visible")},qi=function(e){return Qn(e,"overflowY")},Zi=function(e){return Qn(e,"overflowX")},Mn=function(e,t){var n=t.ownerDocument,o=t;do{typeof ShadowRoot<"u"&&o instanceof ShadowRoot&&(o=o.host);var r=eo(e,o);if(r){var i=to(e,o),s=i[1],a=i[2];if(s>a)return!0}o=o.parentNode}while(o&&o!==n.body);return!1},Ji=function(e){var t=e.scrollTop,n=e.scrollHeight,o=e.clientHeight;return[t,n,o]},Qi=function(e){var t=e.scrollLeft,n=e.scrollWidth,o=e.clientWidth;return[t,n,o]},eo=function(e,t){return e==="v"?qi(t):Zi(t)},to=function(e,t){return e==="v"?Ji(t):Qi(t)},es=function(e,t){return e==="h"&&t==="rtl"?-1:1},ts=function(e,t,n,o,r){var i=es(e,window.getComputedStyle(t).direction),s=i*o,a=n.target,u=t.contains(a),l=!1,d=s>0,p=0,m=0;do{var h=to(e,a),w=h[0],f=h[1],g=h[2],y=f-g-i*w;(w||y)&&eo(e,a)&&(p+=y,m+=w),a instanceof ShadowRoot?a=a.host:a=a.parentNode}while(!u&&a!==document.body||u&&(t.contains(a)||t===a));return(d&&Math.abs(p)<1||!d&&Math.abs(m)<1)&&(l=!0),l},tt=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},Dn=function(e){return[e.deltaX,e.deltaY]},Fn=function(e){return e&&"current"in e?e.current:e},ns=function(e,t){return e[0]===t[0]&&e[1]===t[1]},os=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},rs=0,Ie=[];function is(e){var t=c.useRef([]),n=c.useRef([0,0]),o=c.useRef(),r=c.useState(rs++)[0],i=c.useState(Jn)[0],s=c.useRef(e);c.useEffect(function(){s.current=e},[e]),c.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(r));var f=Pi([e.lockRef.current],(e.shards||[]).map(Fn),!0).filter(Boolean);return f.forEach(function(g){return g.classList.add("allow-interactivity-".concat(r))}),function(){document.body.classList.remove("block-interactivity-".concat(r)),f.forEach(function(g){return g.classList.remove("allow-interactivity-".concat(r))})}}},[e.inert,e.lockRef.current,e.shards]);var a=c.useCallback(function(f,g){if("touches"in f&&f.touches.length===2||f.type==="wheel"&&f.ctrlKey)return!s.current.allowPinchZoom;var y=tt(f),x=n.current,b="deltaX"in f?f.deltaX:x[0]-y[0],C="deltaY"in f?f.deltaY:x[1]-y[1],S,P=f.target,E=Math.abs(b)>Math.abs(C)?"h":"v";if("touches"in f&&E==="h"&&P.type==="range")return!1;var R=Mn(E,P);if(!R)return!0;if(R?S=E:(S=E==="v"?"h":"v",R=Mn(E,P)),!R)return!1;if(!o.current&&"changedTouches"in f&&(b||C)&&(o.current=S),!S)return!0;var D=o.current||S;return ts(D,g,f,D==="h"?b:C)},[]),u=c.useCallback(function(f){var g=f;if(!(!Ie.length||Ie[Ie.length-1]!==i)){var y="deltaY"in g?Dn(g):tt(g),x=t.current.filter(function(S){return S.name===g.type&&(S.target===g.target||g.target===S.shadowParent)&&ns(S.delta,y)})[0];if(x&&x.should){g.cancelable&&g.preventDefault();return}if(!x){var b=(s.current.shards||[]).map(Fn).filter(Boolean).filter(function(S){return S.contains(g.target)}),C=b.length>0?a(g,b[0]):!s.current.noIsolation;C&&g.cancelable&&g.preventDefault()}}},[]),l=c.useCallback(function(f,g,y,x){var b={name:f,delta:g,target:y,should:x,shadowParent:ss(y)};t.current.push(b),setTimeout(function(){t.current=t.current.filter(function(C){return C!==b})},1)},[]),d=c.useCallback(function(f){n.current=tt(f),o.current=void 0},[]),p=c.useCallback(function(f){l(f.type,Dn(f),f.target,a(f,e.lockRef.current))},[]),m=c.useCallback(function(f){l(f.type,tt(f),f.target,a(f,e.lockRef.current))},[]);c.useEffect(function(){return Ie.push(i),e.setCallbacks({onScrollCapture:p,onWheelCapture:p,onTouchMoveCapture:m}),document.addEventListener("wheel",u,Oe),document.addEventListener("touchmove",u,Oe),document.addEventListener("touchstart",d,Oe),function(){Ie=Ie.filter(function(f){return f!==i}),document.removeEventListener("wheel",u,Oe),document.removeEventListener("touchmove",u,Oe),document.removeEventListener("touchstart",d,Oe)}},[]);var h=e.removeScrollBar,w=e.inert;return c.createElement(c.Fragment,null,w?c.createElement(i,{styles:os(r)}):null,h?c.createElement(Yi,{gapMode:e.gapMode}):null)}function ss(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const as=Fi(Zn,is);var yt=c.forwardRef(function(e,t){return c.createElement(wt,oe({},e,{ref:t,sideCar:as}))});yt.classNames=wt.classNames;var cs=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},Ne=new WeakMap,nt=new WeakMap,ot={},Ft=0,no=function(e){return e&&(e.host||no(e.parentNode))},ls=function(e,t){return t.map(function(n){if(e.contains(n))return n;var o=no(n);return o&&e.contains(o)?o:(console.error("aria-hidden",n,"in not contained inside",e,". Doing nothing"),null)}).filter(function(n){return!!n})},us=function(e,t,n,o){var r=ls(t,Array.isArray(e)?e:[e]);ot[n]||(ot[n]=new WeakMap);var i=ot[n],s=[],a=new Set,u=new Set(r),l=function(p){!p||a.has(p)||(a.add(p),l(p.parentNode))};r.forEach(l);var d=function(p){!p||u.has(p)||Array.prototype.forEach.call(p.children,function(m){if(a.has(m))d(m);else try{var h=m.getAttribute(o),w=h!==null&&h!=="false",f=(Ne.get(m)||0)+1,g=(i.get(m)||0)+1;Ne.set(m,f),i.set(m,g),s.push(m),f===1&&w&&nt.set(m,!0),g===1&&m.setAttribute(n,"true"),w||m.setAttribute(o,"true")}catch(y){console.error("aria-hidden: cannot operate on ",m,y)}})};return d(t),a.clear(),Ft++,function(){s.forEach(function(p){var m=Ne.get(p)-1,h=i.get(p)-1;Ne.set(p,m),i.set(p,h),m||(nt.has(p)||p.removeAttribute(o),nt.delete(p)),h||p.removeAttribute(n)}),Ft--,Ft||(Ne=new WeakMap,Ne=new WeakMap,nt=new WeakMap,ot={})}},Jt=function(e,t,n){n===void 0&&(n="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),r=cs(e);return r?(o.push.apply(o,Array.from(r.querySelectorAll("[aria-live]"))),us(o,r,n,"aria-hidden")):function(){return null}},Qt="Dialog",[oo,vc]=Pe(Qt),[fs,te]=oo(Qt),ro=e=>{const{__scopeDialog:t,children:n,open:o,defaultOpen:r,onOpenChange:i,modal:s=!0}=e,a=c.useRef(null),u=c.useRef(null),[l=!1,d]=De({prop:o,defaultProp:r,onChange:i});return v.jsx(fs,{scope:t,triggerRef:a,contentRef:u,contentId:le(),titleId:le(),descriptionId:le(),open:l,onOpenChange:d,onOpenToggle:c.useCallback(()=>d(p=>!p),[d]),modal:s,children:n})};ro.displayName=Qt;var io="DialogTrigger",so=c.forwardRef((e,t)=>{const{__scopeDialog:n,...o}=e,r=te(io,n),i=K(t,r.triggerRef);return v.jsx(N.button,{type:"button","aria-haspopup":"dialog","aria-expanded":r.open,"aria-controls":r.contentId,"data-state":nn(r.open),...o,ref:i,onClick:I(e.onClick,r.onOpenToggle)})});so.displayName=io;var en="DialogPortal",[ds,ao]=oo(en,{forceMount:void 0}),co=e=>{const{__scopeDialog:t,forceMount:n,children:o,container:r}=e,i=te(en,t);return v.jsx(ds,{scope:t,forceMount:n,children:c.Children.map(o,s=>v.jsx(Ae,{present:n||i.open,children:v.jsx(vt,{asChild:!0,container:r,children:s})}))})};co.displayName=en;var ct="DialogOverlay",lo=c.forwardRef((e,t)=>{const n=ao(ct,e.__scopeDialog),{forceMount:o=n.forceMount,...r}=e,i=te(ct,e.__scopeDialog);return i.modal?v.jsx(Ae,{present:o||i.open,children:v.jsx(ps,{...r,ref:t})}):null});lo.displayName=ct;var ps=c.forwardRef((e,t)=>{const{__scopeDialog:n,...o}=e,r=te(ct,n);return v.jsx(yt,{as:Ce,allowPinchZoom:!0,shards:[r.contentRef],children:v.jsx(N.div,{"data-state":nn(r.open),...o,ref:t,style:{pointerEvents:"auto",...o.style}})})}),Se="DialogContent",uo=c.forwardRef((e,t)=>{const n=ao(Se,e.__scopeDialog),{forceMount:o=n.forceMount,...r}=e,i=te(Se,e.__scopeDialog);return v.jsx(Ae,{present:o||i.open,children:i.modal?v.jsx(ms,{...r,ref:t}):v.jsx(hs,{...r,ref:t})})});uo.displayName=Se;var ms=c.forwardRef((e,t)=>{const n=te(Se,e.__scopeDialog),o=c.useRef(null),r=K(t,n.contentRef,o);return c.useEffect(()=>{const i=o.current;if(i)return Jt(i)},[]),v.jsx(fo,{...e,ref:r,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:I(e.onCloseAutoFocus,i=>{var s;i.preventDefault(),(s=n.triggerRef.current)==null||s.focus()}),onPointerDownOutside:I(e.onPointerDownOutside,i=>{const s=i.detail.originalEvent,a=s.button===0&&s.ctrlKey===!0;(s.button===2||a)&&i.preventDefault()}),onFocusOutside:I(e.onFocusOutside,i=>i.preventDefault())})}),hs=c.forwardRef((e,t)=>{const n=te(Se,e.__scopeDialog),o=c.useRef(!1),r=c.useRef(!1);return v.jsx(fo,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:i=>{var s,a;(s=e.onCloseAutoFocus)==null||s.call(e,i),i.defaultPrevented||(o.current||(a=n.triggerRef.current)==null||a.focus(),i.preventDefault()),o.current=!1,r.current=!1},onInteractOutside:i=>{var u,l;(u=e.onInteractOutside)==null||u.call(e,i),i.defaultPrevented||(o.current=!0,i.detail.originalEvent.type==="pointerdown"&&(r.current=!0));const s=i.target;((l=n.triggerRef.current)==null?void 0:l.contains(s))&&i.preventDefault(),i.detail.originalEvent.type==="focusin"&&r.current&&i.preventDefault()}})}),fo=c.forwardRef((e,t)=>{const{__scopeDialog:n,trapFocus:o,onOpenAutoFocus:r,onCloseAutoFocus:i,...s}=e,a=te(Se,n),u=c.useRef(null),l=K(t,u);return Zt(),v.jsxs(v.Fragment,{children:[v.jsx(gt,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:r,onUnmountAutoFocus:i,children:v.jsx(ht,{role:"dialog",id:a.contentId,"aria-describedby":a.descriptionId,"aria-labelledby":a.titleId,"data-state":nn(a.open),...s,ref:l,onDismiss:()=>a.onOpenChange(!1)})}),v.jsxs(v.Fragment,{children:[v.jsx(gs,{titleId:a.titleId}),v.jsx(ws,{contentRef:u,descriptionId:a.descriptionId})]})]})}),tn="DialogTitle",po=c.forwardRef((e,t)=>{const{__scopeDialog:n,...o}=e,r=te(tn,n);return v.jsx(N.h2,{id:r.titleId,...o,ref:t})});po.displayName=tn;var mo="DialogDescription",ho=c.forwardRef((e,t)=>{const{__scopeDialog:n,...o}=e,r=te(mo,n);return v.jsx(N.p,{id:r.descriptionId,...o,ref:t})});ho.displayName=mo;var go="DialogClose",vo=c.forwardRef((e,t)=>{const{__scopeDialog:n,...o}=e,r=te(go,n);return v.jsx(N.button,{type:"button",...o,ref:t,onClick:I(e.onClick,()=>r.onOpenChange(!1))})});vo.displayName=go;function nn(e){return e?"open":"closed"}var wo="DialogTitleWarning",[wc,yo]=qr(wo,{contentName:Se,titleName:tn,docsSlug:"dialog"}),gs=({titleId:e})=>{const t=yo(wo),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return c.useEffect(()=>{e&&(document.getElementById(e)||console.error(n))},[n,e]),null},vs="DialogDescriptionWarning",ws=({contentRef:e,descriptionId:t})=>{const o=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${yo(vs).contentName}}.`;return c.useEffect(()=>{var i;const r=(i=e.current)==null?void 0:i.getAttribute("aria-describedby");t&&r&&(document.getElementById(t)||console.warn(o))},[o,e,t]),null},yc=ro,xc=so,bc=co,Cc=lo,Sc=uo,Ec=po,Rc=ho,Pc=vo;const ys=["top","right","bottom","left"],me=Math.min,q=Math.max,lt=Math.round,rt=Math.floor,re=e=>({x:e,y:e}),xs={left:"right",right:"left",bottom:"top",top:"bottom"},bs={start:"end",end:"start"};function Vt(e,t,n){return q(e,me(t,n))}function ue(e,t){return typeof e=="function"?e(t):e}function fe(e){return e.split("-")[0]}function ke(e){return e.split("-")[1]}function on(e){return e==="x"?"y":"x"}function rn(e){return e==="y"?"height":"width"}function he(e){return["top","bottom"].includes(fe(e))?"y":"x"}function sn(e){return on(he(e))}function Cs(e,t,n){n===void 0&&(n=!1);const o=ke(e),r=sn(e),i=rn(r);let s=r==="x"?o===(n?"end":"start")?"right":"left":o==="start"?"bottom":"top";return t.reference[i]>t.floating[i]&&(s=ut(s)),[s,ut(s)]}function Ss(e){const t=ut(e);return[Ht(e),t,Ht(t)]}function Ht(e){return e.replace(/start|end/g,t=>bs[t])}function Es(e,t,n){const o=["left","right"],r=["right","left"],i=["top","bottom"],s=["bottom","top"];switch(e){case"top":case"bottom":return n?t?r:o:t?o:r;case"left":case"right":return t?i:s;default:return[]}}function Rs(e,t,n,o){const r=ke(e);let i=Es(fe(e),n==="start",o);return r&&(i=i.map(s=>s+"-"+r),t&&(i=i.concat(i.map(Ht)))),i}function ut(e){return e.replace(/left|right|bottom|top/g,t=>xs[t])}function Ps(e){return{top:0,right:0,bottom:0,left:0,...e}}function xo(e){return typeof e!="number"?Ps(e):{top:e,right:e,bottom:e,left:e}}function ft(e){const{x:t,y:n,width:o,height:r}=e;return{width:o,height:r,top:n,left:t,right:t+o,bottom:n+r,x:t,y:n}}function Ln(e,t,n){let{reference:o,floating:r}=e;const i=he(t),s=sn(t),a=rn(s),u=fe(t),l=i==="y",d=o.x+o.width/2-r.width/2,p=o.y+o.height/2-r.height/2,m=o[a]/2-r[a]/2;let h;switch(u){case"top":h={x:d,y:o.y-r.height};break;case"bottom":h={x:d,y:o.y+o.height};break;case"right":h={x:o.x+o.width,y:p};break;case"left":h={x:o.x-r.width,y:p};break;default:h={x:o.x,y:o.y}}switch(ke(t)){case"start":h[s]-=m*(n&&l?-1:1);break;case"end":h[s]+=m*(n&&l?-1:1);break}return h}const As=async(e,t,n)=>{const{placement:o="bottom",strategy:r="absolute",middleware:i=[],platform:s}=n,a=i.filter(Boolean),u=await(s.isRTL==null?void 0:s.isRTL(t));let l=await s.getElementRects({reference:e,floating:t,strategy:r}),{x:d,y:p}=Ln(l,o,u),m=o,h={},w=0;for(let f=0;f<a.length;f++){const{name:g,fn:y}=a[f],{x,y:b,data:C,reset:S}=await y({x:d,y:p,initialPlacement:o,placement:m,strategy:r,middlewareData:h,rects:l,platform:s,elements:{reference:e,floating:t}});d=x??d,p=b??p,h={...h,[g]:{...h[g],...C}},S&&w<=50&&(w++,typeof S=="object"&&(S.placement&&(m=S.placement),S.rects&&(l=S.rects===!0?await s.getElementRects({reference:e,floating:t,strategy:r}):S.rects),{x:d,y:p}=Ln(l,m,u)),f=-1)}return{x:d,y:p,placement:m,strategy:r,middlewareData:h}};async function Ge(e,t){var n;t===void 0&&(t={});const{x:o,y:r,platform:i,rects:s,elements:a,strategy:u}=e,{boundary:l="clippingAncestors",rootBoundary:d="viewport",elementContext:p="floating",altBoundary:m=!1,padding:h=0}=ue(t,e),w=xo(h),g=a[m?p==="floating"?"reference":"floating":p],y=ft(await i.getClippingRect({element:(n=await(i.isElement==null?void 0:i.isElement(g)))==null||n?g:g.contextElement||await(i.getDocumentElement==null?void 0:i.getDocumentElement(a.floating)),boundary:l,rootBoundary:d,strategy:u})),x=p==="floating"?{x:o,y:r,width:s.floating.width,height:s.floating.height}:s.reference,b=await(i.getOffsetParent==null?void 0:i.getOffsetParent(a.floating)),C=await(i.isElement==null?void 0:i.isElement(b))?await(i.getScale==null?void 0:i.getScale(b))||{x:1,y:1}:{x:1,y:1},S=ft(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:x,offsetParent:b,strategy:u}):x);return{top:(y.top-S.top+w.top)/C.y,bottom:(S.bottom-y.bottom+w.bottom)/C.y,left:(y.left-S.left+w.left)/C.x,right:(S.right-y.right+w.right)/C.x}}const Ts=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:o,placement:r,rects:i,platform:s,elements:a,middlewareData:u}=t,{element:l,padding:d=0}=ue(e,t)||{};if(l==null)return{};const p=xo(d),m={x:n,y:o},h=sn(r),w=rn(h),f=await s.getDimensions(l),g=h==="y",y=g?"top":"left",x=g?"bottom":"right",b=g?"clientHeight":"clientWidth",C=i.reference[w]+i.reference[h]-m[h]-i.floating[w],S=m[h]-i.reference[h],P=await(s.getOffsetParent==null?void 0:s.getOffsetParent(l));let E=P?P[b]:0;(!E||!await(s.isElement==null?void 0:s.isElement(P)))&&(E=a.floating[b]||i.floating[w]);const R=C/2-S/2,D=E/2-f[w]/2-1,_=me(p[y],D),L=me(p[x],D),B=_,F=E-f[w]-L,M=E/2-f[w]/2+R,U=Vt(B,M,F),T=!u.arrow&&ke(r)!=null&&M!==U&&i.reference[w]/2-(M<B?_:L)-f[w]/2<0,k=T?M<B?M-B:M-F:0;return{[h]:m[h]+k,data:{[h]:U,centerOffset:M-U-k,...T&&{alignmentOffset:k}},reset:T}}}),Os=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,o;const{placement:r,middlewareData:i,rects:s,initialPlacement:a,platform:u,elements:l}=t,{mainAxis:d=!0,crossAxis:p=!0,fallbackPlacements:m,fallbackStrategy:h="bestFit",fallbackAxisSideDirection:w="none",flipAlignment:f=!0,...g}=ue(e,t);if((n=i.arrow)!=null&&n.alignmentOffset)return{};const y=fe(r),x=he(a),b=fe(a)===a,C=await(u.isRTL==null?void 0:u.isRTL(l.floating)),S=m||(b||!f?[ut(a)]:Ss(a)),P=w!=="none";!m&&P&&S.push(...Rs(a,f,w,C));const E=[a,...S],R=await Ge(t,g),D=[];let _=((o=i.flip)==null?void 0:o.overflows)||[];if(d&&D.push(R[y]),p){const M=Cs(r,s,C);D.push(R[M[0]],R[M[1]])}if(_=[..._,{placement:r,overflows:D}],!D.every(M=>M<=0)){var L,B;const M=(((L=i.flip)==null?void 0:L.index)||0)+1,U=E[M];if(U)return{data:{index:M,overflows:_},reset:{placement:U}};let T=(B=_.filter(k=>k.overflows[0]<=0).sort((k,O)=>k.overflows[1]-O.overflows[1])[0])==null?void 0:B.placement;if(!T)switch(h){case"bestFit":{var F;const k=(F=_.filter(O=>{if(P){const V=he(O.placement);return V===x||V==="y"}return!0}).map(O=>[O.placement,O.overflows.filter(V=>V>0).reduce((V,Y)=>V+Y,0)]).sort((O,V)=>O[1]-V[1])[0])==null?void 0:F[0];k&&(T=k);break}case"initialPlacement":T=a;break}if(r!==T)return{reset:{placement:T}}}return{}}}};function kn(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function jn(e){return ys.some(t=>e[t]>=0)}const Is=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:o="referenceHidden",...r}=ue(e,t);switch(o){case"referenceHidden":{const i=await Ge(t,{...r,elementContext:"reference"}),s=kn(i,n.reference);return{data:{referenceHiddenOffsets:s,referenceHidden:jn(s)}}}case"escaped":{const i=await Ge(t,{...r,altBoundary:!0}),s=kn(i,n.floating);return{data:{escapedOffsets:s,escaped:jn(s)}}}default:return{}}}}};async function Ns(e,t){const{placement:n,platform:o,elements:r}=e,i=await(o.isRTL==null?void 0:o.isRTL(r.floating)),s=fe(n),a=ke(n),u=he(n)==="y",l=["left","top"].includes(s)?-1:1,d=i&&u?-1:1,p=ue(t,e);let{mainAxis:m,crossAxis:h,alignmentAxis:w}=typeof p=="number"?{mainAxis:p,crossAxis:0,alignmentAxis:null}:{mainAxis:p.mainAxis||0,crossAxis:p.crossAxis||0,alignmentAxis:p.alignmentAxis};return a&&typeof w=="number"&&(h=a==="end"?w*-1:w),u?{x:h*d,y:m*l}:{x:m*l,y:h*d}}const _s=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,o;const{x:r,y:i,placement:s,middlewareData:a}=t,u=await Ns(t,e);return s===((n=a.offset)==null?void 0:n.placement)&&(o=a.arrow)!=null&&o.alignmentOffset?{}:{x:r+u.x,y:i+u.y,data:{...u,placement:s}}}}},Ms=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:o,placement:r}=t,{mainAxis:i=!0,crossAxis:s=!1,limiter:a={fn:g=>{let{x:y,y:x}=g;return{x:y,y:x}}},...u}=ue(e,t),l={x:n,y:o},d=await Ge(t,u),p=he(fe(r)),m=on(p);let h=l[m],w=l[p];if(i){const g=m==="y"?"top":"left",y=m==="y"?"bottom":"right",x=h+d[g],b=h-d[y];h=Vt(x,h,b)}if(s){const g=p==="y"?"top":"left",y=p==="y"?"bottom":"right",x=w+d[g],b=w-d[y];w=Vt(x,w,b)}const f=a.fn({...t,[m]:h,[p]:w});return{...f,data:{x:f.x-n,y:f.y-o,enabled:{[m]:i,[p]:s}}}}}},Ds=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:o,placement:r,rects:i,middlewareData:s}=t,{offset:a=0,mainAxis:u=!0,crossAxis:l=!0}=ue(e,t),d={x:n,y:o},p=he(r),m=on(p);let h=d[m],w=d[p];const f=ue(a,t),g=typeof f=="number"?{mainAxis:f,crossAxis:0}:{mainAxis:0,crossAxis:0,...f};if(u){const b=m==="y"?"height":"width",C=i.reference[m]-i.floating[b]+g.mainAxis,S=i.reference[m]+i.reference[b]-g.mainAxis;h<C?h=C:h>S&&(h=S)}if(l){var y,x;const b=m==="y"?"width":"height",C=["top","left"].includes(fe(r)),S=i.reference[p]-i.floating[b]+(C&&((y=s.offset)==null?void 0:y[p])||0)+(C?0:g.crossAxis),P=i.reference[p]+i.reference[b]+(C?0:((x=s.offset)==null?void 0:x[p])||0)-(C?g.crossAxis:0);w<S?w=S:w>P&&(w=P)}return{[m]:h,[p]:w}}}},Fs=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,o;const{placement:r,rects:i,platform:s,elements:a}=t,{apply:u=()=>{},...l}=ue(e,t),d=await Ge(t,l),p=fe(r),m=ke(r),h=he(r)==="y",{width:w,height:f}=i.floating;let g,y;p==="top"||p==="bottom"?(g=p,y=m===(await(s.isRTL==null?void 0:s.isRTL(a.floating))?"start":"end")?"left":"right"):(y=p,g=m==="end"?"top":"bottom");const x=f-d.top-d.bottom,b=w-d.left-d.right,C=me(f-d[g],x),S=me(w-d[y],b),P=!t.middlewareData.shift;let E=C,R=S;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(R=b),(o=t.middlewareData.shift)!=null&&o.enabled.y&&(E=x),P&&!m){const _=q(d.left,0),L=q(d.right,0),B=q(d.top,0),F=q(d.bottom,0);h?R=w-2*(_!==0||L!==0?_+L:q(d.left,d.right)):E=f-2*(B!==0||F!==0?B+F:q(d.top,d.bottom))}await u({...t,availableWidth:R,availableHeight:E});const D=await s.getDimensions(a.floating);return w!==D.width||f!==D.height?{reset:{rects:!0}}:{}}}};function xt(){return typeof window<"u"}function je(e){return bo(e)?(e.nodeName||"").toLowerCase():"#document"}function Z(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function ae(e){var t;return(t=(bo(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function bo(e){return xt()?e instanceof Node||e instanceof Z(e).Node:!1}function Q(e){return xt()?e instanceof Element||e instanceof Z(e).Element:!1}function se(e){return xt()?e instanceof HTMLElement||e instanceof Z(e).HTMLElement:!1}function Wn(e){return!xt()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof Z(e).ShadowRoot}function Xe(e){const{overflow:t,overflowX:n,overflowY:o,display:r}=ee(e);return/auto|scroll|overlay|hidden|clip/.test(t+o+n)&&!["inline","contents"].includes(r)}function Ls(e){return["table","td","th"].includes(je(e))}function bt(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch{return!1}})}function an(e){const t=cn(),n=Q(e)?ee(e):e;return["transform","translate","scale","rotate","perspective"].some(o=>n[o]?n[o]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(o=>(n.willChange||"").includes(o))||["paint","layout","strict","content"].some(o=>(n.contain||"").includes(o))}function ks(e){let t=ge(e);for(;se(t)&&!Fe(t);){if(an(t))return t;if(bt(t))return null;t=ge(t)}return null}function cn(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function Fe(e){return["html","body","#document"].includes(je(e))}function ee(e){return Z(e).getComputedStyle(e)}function Ct(e){return Q(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function ge(e){if(je(e)==="html")return e;const t=e.assignedSlot||e.parentNode||Wn(e)&&e.host||ae(e);return Wn(t)?t.host:t}function Co(e){const t=ge(e);return Fe(t)?e.ownerDocument?e.ownerDocument.body:e.body:se(t)&&Xe(t)?t:Co(t)}function Ye(e,t,n){var o;t===void 0&&(t=[]),n===void 0&&(n=!0);const r=Co(e),i=r===((o=e.ownerDocument)==null?void 0:o.body),s=Z(r);if(i){const a=Ut(s);return t.concat(s,s.visualViewport||[],Xe(r)?r:[],a&&n?Ye(a):[])}return t.concat(r,Ye(r,[],n))}function Ut(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function So(e){const t=ee(e);let n=parseFloat(t.width)||0,o=parseFloat(t.height)||0;const r=se(e),i=r?e.offsetWidth:n,s=r?e.offsetHeight:o,a=lt(n)!==i||lt(o)!==s;return a&&(n=i,o=s),{width:n,height:o,$:a}}function ln(e){return Q(e)?e:e.contextElement}function Me(e){const t=ln(e);if(!se(t))return re(1);const n=t.getBoundingClientRect(),{width:o,height:r,$:i}=So(t);let s=(i?lt(n.width):n.width)/o,a=(i?lt(n.height):n.height)/r;return(!s||!Number.isFinite(s))&&(s=1),(!a||!Number.isFinite(a))&&(a=1),{x:s,y:a}}const js=re(0);function Eo(e){const t=Z(e);return!cn()||!t.visualViewport?js:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function Ws(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==Z(e)?!1:t}function Ee(e,t,n,o){t===void 0&&(t=!1),n===void 0&&(n=!1);const r=e.getBoundingClientRect(),i=ln(e);let s=re(1);t&&(o?Q(o)&&(s=Me(o)):s=Me(e));const a=Ws(i,n,o)?Eo(i):re(0);let u=(r.left+a.x)/s.x,l=(r.top+a.y)/s.y,d=r.width/s.x,p=r.height/s.y;if(i){const m=Z(i),h=o&&Q(o)?Z(o):o;let w=m,f=Ut(w);for(;f&&o&&h!==w;){const g=Me(f),y=f.getBoundingClientRect(),x=ee(f),b=y.left+(f.clientLeft+parseFloat(x.paddingLeft))*g.x,C=y.top+(f.clientTop+parseFloat(x.paddingTop))*g.y;u*=g.x,l*=g.y,d*=g.x,p*=g.y,u+=b,l+=C,w=Z(f),f=Ut(w)}}return ft({width:d,height:p,x:u,y:l})}function un(e,t){const n=Ct(e).scrollLeft;return t?t.left+n:Ee(ae(e)).left+n}function Ro(e,t,n){n===void 0&&(n=!1);const o=e.getBoundingClientRect(),r=o.left+t.scrollLeft-(n?0:un(e,o)),i=o.top+t.scrollTop;return{x:r,y:i}}function Bs(e){let{elements:t,rect:n,offsetParent:o,strategy:r}=e;const i=r==="fixed",s=ae(o),a=t?bt(t.floating):!1;if(o===s||a&&i)return n;let u={scrollLeft:0,scrollTop:0},l=re(1);const d=re(0),p=se(o);if((p||!p&&!i)&&((je(o)!=="body"||Xe(s))&&(u=Ct(o)),se(o))){const h=Ee(o);l=Me(o),d.x=h.x+o.clientLeft,d.y=h.y+o.clientTop}const m=s&&!p&&!i?Ro(s,u,!0):re(0);return{width:n.width*l.x,height:n.height*l.y,x:n.x*l.x-u.scrollLeft*l.x+d.x+m.x,y:n.y*l.y-u.scrollTop*l.y+d.y+m.y}}function $s(e){return Array.from(e.getClientRects())}function Vs(e){const t=ae(e),n=Ct(e),o=e.ownerDocument.body,r=q(t.scrollWidth,t.clientWidth,o.scrollWidth,o.clientWidth),i=q(t.scrollHeight,t.clientHeight,o.scrollHeight,o.clientHeight);let s=-n.scrollLeft+un(e);const a=-n.scrollTop;return ee(o).direction==="rtl"&&(s+=q(t.clientWidth,o.clientWidth)-r),{width:r,height:i,x:s,y:a}}function Hs(e,t){const n=Z(e),o=ae(e),r=n.visualViewport;let i=o.clientWidth,s=o.clientHeight,a=0,u=0;if(r){i=r.width,s=r.height;const l=cn();(!l||l&&t==="fixed")&&(a=r.offsetLeft,u=r.offsetTop)}return{width:i,height:s,x:a,y:u}}function Us(e,t){const n=Ee(e,!0,t==="fixed"),o=n.top+e.clientTop,r=n.left+e.clientLeft,i=se(e)?Me(e):re(1),s=e.clientWidth*i.x,a=e.clientHeight*i.y,u=r*i.x,l=o*i.y;return{width:s,height:a,x:u,y:l}}function Bn(e,t,n){let o;if(t==="viewport")o=Hs(e,n);else if(t==="document")o=Vs(ae(e));else if(Q(t))o=Us(t,n);else{const r=Eo(e);o={x:t.x-r.x,y:t.y-r.y,width:t.width,height:t.height}}return ft(o)}function Po(e,t){const n=ge(e);return n===t||!Q(n)||Fe(n)?!1:ee(n).position==="fixed"||Po(n,t)}function Ks(e,t){const n=t.get(e);if(n)return n;let o=Ye(e,[],!1).filter(a=>Q(a)&&je(a)!=="body"),r=null;const i=ee(e).position==="fixed";let s=i?ge(e):e;for(;Q(s)&&!Fe(s);){const a=ee(s),u=an(s);!u&&a.position==="fixed"&&(r=null),(i?!u&&!r:!u&&a.position==="static"&&!!r&&["absolute","fixed"].includes(r.position)||Xe(s)&&!u&&Po(e,s))?o=o.filter(d=>d!==s):r=a,s=ge(s)}return t.set(e,o),o}function zs(e){let{element:t,boundary:n,rootBoundary:o,strategy:r}=e;const s=[...n==="clippingAncestors"?bt(t)?[]:Ks(t,this._c):[].concat(n),o],a=s[0],u=s.reduce((l,d)=>{const p=Bn(t,d,r);return l.top=q(p.top,l.top),l.right=me(p.right,l.right),l.bottom=me(p.bottom,l.bottom),l.left=q(p.left,l.left),l},Bn(t,a,r));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}}function Gs(e){const{width:t,height:n}=So(e);return{width:t,height:n}}function Ys(e,t,n){const o=se(t),r=ae(t),i=n==="fixed",s=Ee(e,!0,i,t);let a={scrollLeft:0,scrollTop:0};const u=re(0);if(o||!o&&!i)if((je(t)!=="body"||Xe(r))&&(a=Ct(t)),o){const m=Ee(t,!0,i,t);u.x=m.x+t.clientLeft,u.y=m.y+t.clientTop}else r&&(u.x=un(r));const l=r&&!o&&!i?Ro(r,a):re(0),d=s.left+a.scrollLeft-u.x-l.x,p=s.top+a.scrollTop-u.y-l.y;return{x:d,y:p,width:s.width,height:s.height}}function Lt(e){return ee(e).position==="static"}function $n(e,t){if(!se(e)||ee(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return ae(e)===n&&(n=n.ownerDocument.body),n}function Ao(e,t){const n=Z(e);if(bt(e))return n;if(!se(e)){let r=ge(e);for(;r&&!Fe(r);){if(Q(r)&&!Lt(r))return r;r=ge(r)}return n}let o=$n(e,t);for(;o&&Ls(o)&&Lt(o);)o=$n(o,t);return o&&Fe(o)&&Lt(o)&&!an(o)?n:o||ks(e)||n}const Xs=async function(e){const t=this.getOffsetParent||Ao,n=this.getDimensions,o=await n(e.floating);return{reference:Ys(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:o.width,height:o.height}}};function qs(e){return ee(e).direction==="rtl"}const Zs={convertOffsetParentRelativeRectToViewportRelativeRect:Bs,getDocumentElement:ae,getClippingRect:zs,getOffsetParent:Ao,getElementRects:Xs,getClientRects:$s,getDimensions:Gs,getScale:Me,isElement:Q,isRTL:qs};function To(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function Js(e,t){let n=null,o;const r=ae(e);function i(){var a;clearTimeout(o),(a=n)==null||a.disconnect(),n=null}function s(a,u){a===void 0&&(a=!1),u===void 0&&(u=1),i();const l=e.getBoundingClientRect(),{left:d,top:p,width:m,height:h}=l;if(a||t(),!m||!h)return;const w=rt(p),f=rt(r.clientWidth-(d+m)),g=rt(r.clientHeight-(p+h)),y=rt(d),b={rootMargin:-w+"px "+-f+"px "+-g+"px "+-y+"px",threshold:q(0,me(1,u))||1};let C=!0;function S(P){const E=P[0].intersectionRatio;if(E!==u){if(!C)return s();E?s(!1,E):o=setTimeout(()=>{s(!1,1e-7)},1e3)}E===1&&!To(l,e.getBoundingClientRect())&&s(),C=!1}try{n=new IntersectionObserver(S,{...b,root:r.ownerDocument})}catch{n=new IntersectionObserver(S,b)}n.observe(e)}return s(!0),i}function Qs(e,t,n,o){o===void 0&&(o={});const{ancestorScroll:r=!0,ancestorResize:i=!0,elementResize:s=typeof ResizeObserver=="function",layoutShift:a=typeof IntersectionObserver=="function",animationFrame:u=!1}=o,l=ln(e),d=r||i?[...l?Ye(l):[],...Ye(t)]:[];d.forEach(y=>{r&&y.addEventListener("scroll",n,{passive:!0}),i&&y.addEventListener("resize",n)});const p=l&&a?Js(l,n):null;let m=-1,h=null;s&&(h=new ResizeObserver(y=>{let[x]=y;x&&x.target===l&&h&&(h.unobserve(t),cancelAnimationFrame(m),m=requestAnimationFrame(()=>{var b;(b=h)==null||b.observe(t)})),n()}),l&&!u&&h.observe(l),h.observe(t));let w,f=u?Ee(e):null;u&&g();function g(){const y=Ee(e);f&&!To(f,y)&&n(),f=y,w=requestAnimationFrame(g)}return n(),()=>{var y;d.forEach(x=>{r&&x.removeEventListener("scroll",n),i&&x.removeEventListener("resize",n)}),p==null||p(),(y=h)==null||y.disconnect(),h=null,u&&cancelAnimationFrame(w)}}const ea=_s,ta=Ms,na=Os,oa=Fs,ra=Is,Vn=Ts,ia=Ds,sa=(e,t,n)=>{const o=new Map,r={platform:Zs,...n},i={...r.platform,_c:o};return As(e,t,{...r,platform:i})};var at=typeof document<"u"?c.useLayoutEffect:c.useEffect;function dt(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,o,r;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(o=n;o--!==0;)if(!dt(e[o],t[o]))return!1;return!0}if(r=Object.keys(e),n=r.length,n!==Object.keys(t).length)return!1;for(o=n;o--!==0;)if(!{}.hasOwnProperty.call(t,r[o]))return!1;for(o=n;o--!==0;){const i=r[o];if(!(i==="_owner"&&e.$$typeof)&&!dt(e[i],t[i]))return!1}return!0}return e!==e&&t!==t}function Oo(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function Hn(e,t){const n=Oo(e);return Math.round(t*n)/n}function kt(e){const t=c.useRef(e);return at(()=>{t.current=e}),t}function aa(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:o=[],platform:r,elements:{reference:i,floating:s}={},transform:a=!0,whileElementsMounted:u,open:l}=e,[d,p]=c.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[m,h]=c.useState(o);dt(m,o)||h(o);const[w,f]=c.useState(null),[g,y]=c.useState(null),x=c.useCallback(O=>{O!==P.current&&(P.current=O,f(O))},[]),b=c.useCallback(O=>{O!==E.current&&(E.current=O,y(O))},[]),C=i||w,S=s||g,P=c.useRef(null),E=c.useRef(null),R=c.useRef(d),D=u!=null,_=kt(u),L=kt(r),B=kt(l),F=c.useCallback(()=>{if(!P.current||!E.current)return;const O={placement:t,strategy:n,middleware:m};L.current&&(O.platform=L.current),sa(P.current,E.current,O).then(V=>{const Y={...V,isPositioned:B.current!==!1};M.current&&!dt(R.current,Y)&&(R.current=Y,mt.flushSync(()=>{p(Y)}))})},[m,t,n,L,B]);at(()=>{l===!1&&R.current.isPositioned&&(R.current.isPositioned=!1,p(O=>({...O,isPositioned:!1})))},[l]);const M=c.useRef(!1);at(()=>(M.current=!0,()=>{M.current=!1}),[]),at(()=>{if(C&&(P.current=C),S&&(E.current=S),C&&S){if(_.current)return _.current(C,S,F);F()}},[C,S,F,_,D]);const U=c.useMemo(()=>({reference:P,floating:E,setReference:x,setFloating:b}),[x,b]),T=c.useMemo(()=>({reference:C,floating:S}),[C,S]),k=c.useMemo(()=>{const O={position:n,left:0,top:0};if(!T.floating)return O;const V=Hn(T.floating,d.x),Y=Hn(T.floating,d.y);return a?{...O,transform:"translate("+V+"px, "+Y+"px)",...Oo(T.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:V,top:Y}},[n,a,T.floating,d.x,d.y]);return c.useMemo(()=>({...d,update:F,refs:U,elements:T,floatingStyles:k}),[d,F,U,T,k])}const ca=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:o,padding:r}=typeof e=="function"?e(n):e;return o&&t(o)?o.current!=null?Vn({element:o.current,padding:r}).fn(n):{}:o?Vn({element:o,padding:r}).fn(n):{}}}},la=(e,t)=>({...ea(e),options:[e,t]}),ua=(e,t)=>({...ta(e),options:[e,t]}),fa=(e,t)=>({...ia(e),options:[e,t]}),da=(e,t)=>({...na(e),options:[e,t]}),pa=(e,t)=>({...oa(e),options:[e,t]}),ma=(e,t)=>({...ra(e),options:[e,t]}),ha=(e,t)=>({...ca(e),options:[e,t]});var ga="Arrow",Io=c.forwardRef((e,t)=>{const{children:n,width:o=10,height:r=5,...i}=e;return v.jsx(N.svg,{...i,ref:t,width:o,height:r,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:v.jsx("polygon",{points:"0,0 30,0 15,10"})})});Io.displayName=ga;var va=Io;function wa(e){const[t,n]=c.useState(void 0);return G(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const o=new ResizeObserver(r=>{if(!Array.isArray(r)||!r.length)return;const i=r[0];let s,a;if("borderBoxSize"in i){const u=i.borderBoxSize,l=Array.isArray(u)?u[0]:u;s=l.inlineSize,a=l.blockSize}else s=e.offsetWidth,a=e.offsetHeight;n({width:s,height:a})});return o.observe(e,{box:"border-box"}),()=>o.unobserve(e)}else n(void 0)},[e]),t}var fn="Popper",[No,St]=Pe(fn),[ya,_o]=No(fn),Mo=e=>{const{__scopePopper:t,children:n}=e,[o,r]=c.useState(null);return v.jsx(ya,{scope:t,anchor:o,onAnchorChange:r,children:n})};Mo.displayName=fn;var Do="PopperAnchor",Fo=c.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:o,...r}=e,i=_o(Do,n),s=c.useRef(null),a=K(t,s);return c.useEffect(()=>{i.onAnchorChange((o==null?void 0:o.current)||s.current)}),o?null:v.jsx(N.div,{...r,ref:a})});Fo.displayName=Do;var dn="PopperContent",[xa,ba]=No(dn),Lo=c.forwardRef((e,t)=>{var A,H,z,$,j,W;const{__scopePopper:n,side:o="bottom",sideOffset:r=0,align:i="center",alignOffset:s=0,arrowPadding:a=0,avoidCollisions:u=!0,collisionBoundary:l=[],collisionPadding:d=0,sticky:p="partial",hideWhenDetached:m=!1,updatePositionStrategy:h="optimized",onPlaced:w,...f}=e,g=_o(dn,n),[y,x]=c.useState(null),b=K(t,X=>x(X)),[C,S]=c.useState(null),P=wa(C),E=(P==null?void 0:P.width)??0,R=(P==null?void 0:P.height)??0,D=o+(i!=="center"?"-"+i:""),_=typeof d=="number"?d:{top:0,right:0,bottom:0,left:0,...d},L=Array.isArray(l)?l:[l],B=L.length>0,F={padding:_,boundary:L.filter(Sa),altBoundary:B},{refs:M,floatingStyles:U,placement:T,isPositioned:k,middlewareData:O}=aa({strategy:"fixed",placement:D,whileElementsMounted:(...X)=>Qs(...X,{animationFrame:h==="always"}),elements:{reference:g.anchor},middleware:[la({mainAxis:r+R,alignmentAxis:s}),u&&ua({mainAxis:!0,crossAxis:!1,limiter:p==="partial"?fa():void 0,...F}),u&&da({...F}),pa({...F,apply:({elements:X,rects:ne,availableWidth:Ve,availableHeight:He})=>{const{width:Ue,height:Kr}=ne.reference,Je=X.floating.style;Je.setProperty("--radix-popper-available-width",`${Ve}px`),Je.setProperty("--radix-popper-available-height",`${He}px`),Je.setProperty("--radix-popper-anchor-width",`${Ue}px`),Je.setProperty("--radix-popper-anchor-height",`${Kr}px`)}}),C&&ha({element:C,padding:a}),Ea({arrowWidth:E,arrowHeight:R}),m&&ma({strategy:"referenceHidden",...F})]}),[V,Y]=Wo(T),ce=ie(w);G(()=>{k&&(ce==null||ce())},[k,ce]);const Be=(A=O.arrow)==null?void 0:A.x,$e=(H=O.arrow)==null?void 0:H.y,de=((z=O.arrow)==null?void 0:z.centerOffset)!==0,[Te,xe]=c.useState();return G(()=>{y&&xe(window.getComputedStyle(y).zIndex)},[y]),v.jsx("div",{ref:M.setFloating,"data-radix-popper-content-wrapper":"",style:{...U,transform:k?U.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:Te,"--radix-popper-transform-origin":[($=O.transformOrigin)==null?void 0:$.x,(j=O.transformOrigin)==null?void 0:j.y].join(" "),...((W=O.hide)==null?void 0:W.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:v.jsx(xa,{scope:n,placedSide:V,onArrowChange:S,arrowX:Be,arrowY:$e,shouldHideArrow:de,children:v.jsx(N.div,{"data-side":V,"data-align":Y,...f,ref:b,style:{...f.style,animation:k?void 0:"none"}})})})});Lo.displayName=dn;var ko="PopperArrow",Ca={top:"bottom",right:"left",bottom:"top",left:"right"},jo=c.forwardRef(function(t,n){const{__scopePopper:o,...r}=t,i=ba(ko,o),s=Ca[i.placedSide];return v.jsx("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[s]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:v.jsx(va,{...r,ref:n,style:{...r.style,display:"block"}})})});jo.displayName=ko;function Sa(e){return e!==null}var Ea=e=>({name:"transformOrigin",options:e,fn(t){var g,y,x;const{placement:n,rects:o,middlewareData:r}=t,s=((g=r.arrow)==null?void 0:g.centerOffset)!==0,a=s?0:e.arrowWidth,u=s?0:e.arrowHeight,[l,d]=Wo(n),p={start:"0%",center:"50%",end:"100%"}[d],m=(((y=r.arrow)==null?void 0:y.x)??0)+a/2,h=(((x=r.arrow)==null?void 0:x.y)??0)+u/2;let w="",f="";return l==="bottom"?(w=s?p:`${m}px`,f=`${-u}px`):l==="top"?(w=s?p:`${m}px`,f=`${o.floating.height+u}px`):l==="right"?(w=`${-u}px`,f=s?p:`${h}px`):l==="left"&&(w=`${o.floating.width+u}px`,f=s?p:`${h}px`),{data:{x:w,y:f}}}});function Wo(e){const[t,n="center"]=e.split("-");return[t,n]}var Bo=Mo,pn=Fo,$o=Lo,Vo=jo,Ra="VisuallyHidden",mn=c.forwardRef((e,t)=>v.jsx(N.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));mn.displayName=Ra;var Ac=mn,hn="Popover",[Ho,Tc]=Pe(hn,[St]),qe=St(),[Pa,ve]=Ho(hn),Uo=e=>{const{__scopePopover:t,children:n,open:o,defaultOpen:r,onOpenChange:i,modal:s=!1}=e,a=qe(t),u=c.useRef(null),[l,d]=c.useState(!1),[p=!1,m]=De({prop:o,defaultProp:r,onChange:i});return v.jsx(Bo,{...a,children:v.jsx(Pa,{scope:t,contentId:le(),triggerRef:u,open:p,onOpenChange:m,onOpenToggle:c.useCallback(()=>m(h=>!h),[m]),hasCustomAnchor:l,onCustomAnchorAdd:c.useCallback(()=>d(!0),[]),onCustomAnchorRemove:c.useCallback(()=>d(!1),[]),modal:s,children:n})})};Uo.displayName=hn;var Ko="PopoverAnchor",Aa=c.forwardRef((e,t)=>{const{__scopePopover:n,...o}=e,r=ve(Ko,n),i=qe(n),{onCustomAnchorAdd:s,onCustomAnchorRemove:a}=r;return c.useEffect(()=>(s(),()=>a()),[s,a]),v.jsx(pn,{...i,...o,ref:t})});Aa.displayName=Ko;var zo="PopoverTrigger",Go=c.forwardRef((e,t)=>{const{__scopePopover:n,...o}=e,r=ve(zo,n),i=qe(n),s=K(t,r.triggerRef),a=v.jsx(N.button,{type:"button","aria-haspopup":"dialog","aria-expanded":r.open,"aria-controls":r.contentId,"data-state":Jo(r.open),...o,ref:s,onClick:I(e.onClick,r.onOpenToggle)});return r.hasCustomAnchor?a:v.jsx(pn,{asChild:!0,...i,children:a})});Go.displayName=zo;var gn="PopoverPortal",[Ta,Oa]=Ho(gn,{forceMount:void 0}),Yo=e=>{const{__scopePopover:t,forceMount:n,children:o,container:r}=e,i=ve(gn,t);return v.jsx(Ta,{scope:t,forceMount:n,children:v.jsx(Ae,{present:n||i.open,children:v.jsx(vt,{asChild:!0,container:r,children:o})})})};Yo.displayName=gn;var Le="PopoverContent",Xo=c.forwardRef((e,t)=>{const n=Oa(Le,e.__scopePopover),{forceMount:o=n.forceMount,...r}=e,i=ve(Le,e.__scopePopover);return v.jsx(Ae,{present:o||i.open,children:i.modal?v.jsx(Ia,{...r,ref:t}):v.jsx(Na,{...r,ref:t})})});Xo.displayName=Le;var Ia=c.forwardRef((e,t)=>{const n=ve(Le,e.__scopePopover),o=c.useRef(null),r=K(t,o),i=c.useRef(!1);return c.useEffect(()=>{const s=o.current;if(s)return Jt(s)},[]),v.jsx(yt,{as:Ce,allowPinchZoom:!0,children:v.jsx(qo,{...e,ref:r,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:I(e.onCloseAutoFocus,s=>{var a;s.preventDefault(),i.current||(a=n.triggerRef.current)==null||a.focus()}),onPointerDownOutside:I(e.onPointerDownOutside,s=>{const a=s.detail.originalEvent,u=a.button===0&&a.ctrlKey===!0,l=a.button===2||u;i.current=l},{checkForDefaultPrevented:!1}),onFocusOutside:I(e.onFocusOutside,s=>s.preventDefault(),{checkForDefaultPrevented:!1})})})}),Na=c.forwardRef((e,t)=>{const n=ve(Le,e.__scopePopover),o=c.useRef(!1),r=c.useRef(!1);return v.jsx(qo,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:i=>{var s,a;(s=e.onCloseAutoFocus)==null||s.call(e,i),i.defaultPrevented||(o.current||(a=n.triggerRef.current)==null||a.focus(),i.preventDefault()),o.current=!1,r.current=!1},onInteractOutside:i=>{var u,l;(u=e.onInteractOutside)==null||u.call(e,i),i.defaultPrevented||(o.current=!0,i.detail.originalEvent.type==="pointerdown"&&(r.current=!0));const s=i.target;((l=n.triggerRef.current)==null?void 0:l.contains(s))&&i.preventDefault(),i.detail.originalEvent.type==="focusin"&&r.current&&i.preventDefault()}})}),qo=c.forwardRef((e,t)=>{const{__scopePopover:n,trapFocus:o,onOpenAutoFocus:r,onCloseAutoFocus:i,disableOutsidePointerEvents:s,onEscapeKeyDown:a,onPointerDownOutside:u,onFocusOutside:l,onInteractOutside:d,...p}=e,m=ve(Le,n),h=qe(n);return Zt(),v.jsx(gt,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:r,onUnmountAutoFocus:i,children:v.jsx(ht,{asChild:!0,disableOutsidePointerEvents:s,onInteractOutside:d,onEscapeKeyDown:a,onPointerDownOutside:u,onFocusOutside:l,onDismiss:()=>m.onOpenChange(!1),children:v.jsx($o,{"data-state":Jo(m.open),role:"dialog",id:m.contentId,...h,...p,ref:t,style:{...p.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),Zo="PopoverClose",_a=c.forwardRef((e,t)=>{const{__scopePopover:n,...o}=e,r=ve(Zo,n);return v.jsx(N.button,{type:"button",...o,ref:t,onClick:I(e.onClick,()=>r.onOpenChange(!1))})});_a.displayName=Zo;var Ma="PopoverArrow",Da=c.forwardRef((e,t)=>{const{__scopePopover:n,...o}=e,r=qe(n);return v.jsx(Vo,{...r,...o,ref:t})});Da.displayName=Ma;function Jo(e){return e?"open":"closed"}var Oc=Uo,Ic=Go,Nc=Yo,_c=Xo;function Un(e,[t,n]){return Math.min(n,Math.max(t,e))}function Qo(e){const t=e+"CollectionProvider",[n,o]=Pe(t),[r,i]=n(t,{collectionRef:{current:null},itemMap:new Map}),s=h=>{const{scope:w,children:f}=h,g=be.useRef(null),y=be.useRef(new Map).current;return v.jsx(r,{scope:w,itemMap:y,collectionRef:g,children:f})};s.displayName=t;const a=e+"CollectionSlot",u=be.forwardRef((h,w)=>{const{scope:f,children:g}=h,y=i(a,f),x=K(w,y.collectionRef);return v.jsx(Ce,{ref:x,children:g})});u.displayName=a;const l=e+"CollectionItemSlot",d="data-radix-collection-item",p=be.forwardRef((h,w)=>{const{scope:f,children:g,...y}=h,x=be.useRef(null),b=K(w,x),C=i(l,f);return be.useEffect(()=>(C.itemMap.set(x,{ref:x,...y}),()=>void C.itemMap.delete(x))),v.jsx(Ce,{[d]:"",ref:b,children:g})});p.displayName=l;function m(h){const w=i(e+"CollectionConsumer",h);return be.useCallback(()=>{const g=w.collectionRef.current;if(!g)return[];const y=Array.from(g.querySelectorAll(`[${d}]`));return Array.from(w.itemMap.values()).sort((C,S)=>y.indexOf(C.ref.current)-y.indexOf(S.ref.current))},[w.collectionRef,w.itemMap])}return[{Provider:s,Slot:u,ItemSlot:p},m,o]}var Fa=c.createContext(void 0);function vn(e){const t=c.useContext(Fa);return e||t||"ltr"}function La(e){const t=c.useRef({value:e,previous:e});return c.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}var ka=[" ","Enter","ArrowUp","ArrowDown"],ja=[" ","Enter"],Ze="Select",[Et,Rt,Wa]=Qo(Ze),[We,Mc]=Pe(Ze,[Wa,St]),Pt=St(),[Ba,we]=We(Ze),[$a,Va]=We(Ze),er=e=>{const{__scopeSelect:t,children:n,open:o,defaultOpen:r,onOpenChange:i,value:s,defaultValue:a,onValueChange:u,dir:l,name:d,autoComplete:p,disabled:m,required:h,form:w}=e,f=Pt(t),[g,y]=c.useState(null),[x,b]=c.useState(null),[C,S]=c.useState(!1),P=vn(l),[E=!1,R]=De({prop:o,defaultProp:r,onChange:i}),[D,_]=De({prop:s,defaultProp:a,onChange:u}),L=c.useRef(null),B=g?w||!!g.closest("form"):!0,[F,M]=c.useState(new Set),U=Array.from(F).map(T=>T.props.value).join(";");return v.jsx(Bo,{...f,children:v.jsxs(Ba,{required:h,scope:t,trigger:g,onTriggerChange:y,valueNode:x,onValueNodeChange:b,valueNodeHasChildren:C,onValueNodeHasChildrenChange:S,contentId:le(),value:D,onValueChange:_,open:E,onOpenChange:R,dir:P,triggerPointerDownPosRef:L,disabled:m,children:[v.jsx(Et.Provider,{scope:t,children:v.jsx($a,{scope:e.__scopeSelect,onNativeOptionAdd:c.useCallback(T=>{M(k=>new Set(k).add(T))},[]),onNativeOptionRemove:c.useCallback(T=>{M(k=>{const O=new Set(k);return O.delete(T),O})},[]),children:n})}),B?v.jsxs(Pr,{"aria-hidden":!0,required:h,tabIndex:-1,name:d,autoComplete:p,value:D,onChange:T=>_(T.target.value),disabled:m,form:w,children:[D===void 0?v.jsx("option",{value:""}):null,Array.from(F)]},U):null]})})};er.displayName=Ze;var tr="SelectTrigger",nr=c.forwardRef((e,t)=>{const{__scopeSelect:n,disabled:o=!1,...r}=e,i=Pt(n),s=we(tr,n),a=s.disabled||o,u=K(t,s.onTriggerChange),l=Rt(n),d=c.useRef("touch"),[p,m,h]=Ar(f=>{const g=l().filter(b=>!b.disabled),y=g.find(b=>b.value===s.value),x=Tr(g,f,y);x!==void 0&&s.onValueChange(x.value)}),w=f=>{a||(s.onOpenChange(!0),h()),f&&(s.triggerPointerDownPosRef.current={x:Math.round(f.pageX),y:Math.round(f.pageY)})};return v.jsx(pn,{asChild:!0,...i,children:v.jsx(N.button,{type:"button",role:"combobox","aria-controls":s.contentId,"aria-expanded":s.open,"aria-required":s.required,"aria-autocomplete":"none",dir:s.dir,"data-state":s.open?"open":"closed",disabled:a,"data-disabled":a?"":void 0,"data-placeholder":Rr(s.value)?"":void 0,...r,ref:u,onClick:I(r.onClick,f=>{f.currentTarget.focus(),d.current!=="mouse"&&w(f)}),onPointerDown:I(r.onPointerDown,f=>{d.current=f.pointerType;const g=f.target;g.hasPointerCapture(f.pointerId)&&g.releasePointerCapture(f.pointerId),f.button===0&&f.ctrlKey===!1&&f.pointerType==="mouse"&&(w(f),f.preventDefault())}),onKeyDown:I(r.onKeyDown,f=>{const g=p.current!=="";!(f.ctrlKey||f.altKey||f.metaKey)&&f.key.length===1&&m(f.key),!(g&&f.key===" ")&&ka.includes(f.key)&&(w(),f.preventDefault())})})})});nr.displayName=tr;var or="SelectValue",rr=c.forwardRef((e,t)=>{const{__scopeSelect:n,className:o,style:r,children:i,placeholder:s="",...a}=e,u=we(or,n),{onValueNodeHasChildrenChange:l}=u,d=i!==void 0,p=K(t,u.onValueNodeChange);return G(()=>{l(d)},[l,d]),v.jsx(N.span,{...a,ref:p,style:{pointerEvents:"none"},children:Rr(u.value)?v.jsx(v.Fragment,{children:s}):i})});rr.displayName=or;var Ha="SelectIcon",ir=c.forwardRef((e,t)=>{const{__scopeSelect:n,children:o,...r}=e;return v.jsx(N.span,{"aria-hidden":!0,...r,ref:t,children:o||"▼"})});ir.displayName=Ha;var Ua="SelectPortal",sr=e=>v.jsx(vt,{asChild:!0,...e});sr.displayName=Ua;var Re="SelectContent",ar=c.forwardRef((e,t)=>{const n=we(Re,e.__scopeSelect),[o,r]=c.useState();if(G(()=>{r(new DocumentFragment)},[]),!n.open){const i=o;return i?mt.createPortal(v.jsx(cr,{scope:e.__scopeSelect,children:v.jsx(Et.Slot,{scope:e.__scopeSelect,children:v.jsx("div",{children:e.children})})}),i):null}return v.jsx(lr,{...e,ref:t})});ar.displayName=Re;var J=10,[cr,ye]=We(Re),Ka="SelectContentImpl",lr=c.forwardRef((e,t)=>{const{__scopeSelect:n,position:o="item-aligned",onCloseAutoFocus:r,onEscapeKeyDown:i,onPointerDownOutside:s,side:a,sideOffset:u,align:l,alignOffset:d,arrowPadding:p,collisionBoundary:m,collisionPadding:h,sticky:w,hideWhenDetached:f,avoidCollisions:g,...y}=e,x=we(Re,n),[b,C]=c.useState(null),[S,P]=c.useState(null),E=K(t,A=>C(A)),[R,D]=c.useState(null),[_,L]=c.useState(null),B=Rt(n),[F,M]=c.useState(!1),U=c.useRef(!1);c.useEffect(()=>{if(b)return Jt(b)},[b]),Zt();const T=c.useCallback(A=>{const[H,...z]=B().map(W=>W.ref.current),[$]=z.slice(-1),j=document.activeElement;for(const W of A)if(W===j||(W==null||W.scrollIntoView({block:"nearest"}),W===H&&S&&(S.scrollTop=0),W===$&&S&&(S.scrollTop=S.scrollHeight),W==null||W.focus(),document.activeElement!==j))return},[B,S]),k=c.useCallback(()=>T([R,b]),[T,R,b]);c.useEffect(()=>{F&&k()},[F,k]);const{onOpenChange:O,triggerPointerDownPosRef:V}=x;c.useEffect(()=>{if(b){let A={x:0,y:0};const H=$=>{var j,W;A={x:Math.abs(Math.round($.pageX)-(((j=V.current)==null?void 0:j.x)??0)),y:Math.abs(Math.round($.pageY)-(((W=V.current)==null?void 0:W.y)??0))}},z=$=>{A.x<=10&&A.y<=10?$.preventDefault():b.contains($.target)||O(!1),document.removeEventListener("pointermove",H),V.current=null};return V.current!==null&&(document.addEventListener("pointermove",H),document.addEventListener("pointerup",z,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",H),document.removeEventListener("pointerup",z,{capture:!0})}}},[b,O,V]),c.useEffect(()=>{const A=()=>O(!1);return window.addEventListener("blur",A),window.addEventListener("resize",A),()=>{window.removeEventListener("blur",A),window.removeEventListener("resize",A)}},[O]);const[Y,ce]=Ar(A=>{const H=B().filter(j=>!j.disabled),z=H.find(j=>j.ref.current===document.activeElement),$=Tr(H,A,z);$&&setTimeout(()=>$.ref.current.focus())}),Be=c.useCallback((A,H,z)=>{const $=!U.current&&!z;(x.value!==void 0&&x.value===H||$)&&(D(A),$&&(U.current=!0))},[x.value]),$e=c.useCallback(()=>b==null?void 0:b.focus(),[b]),de=c.useCallback((A,H,z)=>{const $=!U.current&&!z;(x.value!==void 0&&x.value===H||$)&&L(A)},[x.value]),Te=o==="popper"?Kt:ur,xe=Te===Kt?{side:a,sideOffset:u,align:l,alignOffset:d,arrowPadding:p,collisionBoundary:m,collisionPadding:h,sticky:w,hideWhenDetached:f,avoidCollisions:g}:{};return v.jsx(cr,{scope:n,content:b,viewport:S,onViewportChange:P,itemRefCallback:Be,selectedItem:R,onItemLeave:$e,itemTextRefCallback:de,focusSelectedItem:k,selectedItemText:_,position:o,isPositioned:F,searchRef:Y,children:v.jsx(yt,{as:Ce,allowPinchZoom:!0,children:v.jsx(gt,{asChild:!0,trapped:x.open,onMountAutoFocus:A=>{A.preventDefault()},onUnmountAutoFocus:I(r,A=>{var H;(H=x.trigger)==null||H.focus({preventScroll:!0}),A.preventDefault()}),children:v.jsx(ht,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:s,onFocusOutside:A=>A.preventDefault(),onDismiss:()=>x.onOpenChange(!1),children:v.jsx(Te,{role:"listbox",id:x.contentId,"data-state":x.open?"open":"closed",dir:x.dir,onContextMenu:A=>A.preventDefault(),...y,...xe,onPlaced:()=>M(!0),ref:E,style:{display:"flex",flexDirection:"column",outline:"none",...y.style},onKeyDown:I(y.onKeyDown,A=>{const H=A.ctrlKey||A.altKey||A.metaKey;if(A.key==="Tab"&&A.preventDefault(),!H&&A.key.length===1&&ce(A.key),["ArrowUp","ArrowDown","Home","End"].includes(A.key)){let $=B().filter(j=>!j.disabled).map(j=>j.ref.current);if(["ArrowUp","End"].includes(A.key)&&($=$.slice().reverse()),["ArrowUp","ArrowDown"].includes(A.key)){const j=A.target,W=$.indexOf(j);$=$.slice(W+1)}setTimeout(()=>T($)),A.preventDefault()}})})})})})})});lr.displayName=Ka;var za="SelectItemAlignedPosition",ur=c.forwardRef((e,t)=>{const{__scopeSelect:n,onPlaced:o,...r}=e,i=we(Re,n),s=ye(Re,n),[a,u]=c.useState(null),[l,d]=c.useState(null),p=K(t,E=>d(E)),m=Rt(n),h=c.useRef(!1),w=c.useRef(!0),{viewport:f,selectedItem:g,selectedItemText:y,focusSelectedItem:x}=s,b=c.useCallback(()=>{if(i.trigger&&i.valueNode&&a&&l&&f&&g&&y){const E=i.trigger.getBoundingClientRect(),R=l.getBoundingClientRect(),D=i.valueNode.getBoundingClientRect(),_=y.getBoundingClientRect();if(i.dir!=="rtl"){const j=_.left-R.left,W=D.left-j,X=E.left-W,ne=E.width+X,Ve=Math.max(ne,R.width),He=window.innerWidth-J,Ue=Un(W,[J,Math.max(J,He-Ve)]);a.style.minWidth=ne+"px",a.style.left=Ue+"px"}else{const j=R.right-_.right,W=window.innerWidth-D.right-j,X=window.innerWidth-E.right-W,ne=E.width+X,Ve=Math.max(ne,R.width),He=window.innerWidth-J,Ue=Un(W,[J,Math.max(J,He-Ve)]);a.style.minWidth=ne+"px",a.style.right=Ue+"px"}const L=m(),B=window.innerHeight-J*2,F=f.scrollHeight,M=window.getComputedStyle(l),U=parseInt(M.borderTopWidth,10),T=parseInt(M.paddingTop,10),k=parseInt(M.borderBottomWidth,10),O=parseInt(M.paddingBottom,10),V=U+T+F+O+k,Y=Math.min(g.offsetHeight*5,V),ce=window.getComputedStyle(f),Be=parseInt(ce.paddingTop,10),$e=parseInt(ce.paddingBottom,10),de=E.top+E.height/2-J,Te=B-de,xe=g.offsetHeight/2,A=g.offsetTop+xe,H=U+T+A,z=V-H;if(H<=de){const j=L.length>0&&g===L[L.length-1].ref.current;a.style.bottom="0px";const W=l.clientHeight-f.offsetTop-f.offsetHeight,X=Math.max(Te,xe+(j?$e:0)+W+k),ne=H+X;a.style.height=ne+"px"}else{const j=L.length>0&&g===L[0].ref.current;a.style.top="0px";const X=Math.max(de,U+f.offsetTop+(j?Be:0)+xe)+z;a.style.height=X+"px",f.scrollTop=H-de+f.offsetTop}a.style.margin=`${J}px 0`,a.style.minHeight=Y+"px",a.style.maxHeight=B+"px",o==null||o(),requestAnimationFrame(()=>h.current=!0)}},[m,i.trigger,i.valueNode,a,l,f,g,y,i.dir,o]);G(()=>b(),[b]);const[C,S]=c.useState();G(()=>{l&&S(window.getComputedStyle(l).zIndex)},[l]);const P=c.useCallback(E=>{E&&w.current===!0&&(b(),x==null||x(),w.current=!1)},[b,x]);return v.jsx(Ya,{scope:n,contentWrapper:a,shouldExpandOnScrollRef:h,onScrollButtonChange:P,children:v.jsx("div",{ref:u,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:C},children:v.jsx(N.div,{...r,ref:p,style:{boxSizing:"border-box",maxHeight:"100%",...r.style}})})})});ur.displayName=za;var Ga="SelectPopperPosition",Kt=c.forwardRef((e,t)=>{const{__scopeSelect:n,align:o="start",collisionPadding:r=J,...i}=e,s=Pt(n);return v.jsx($o,{...s,...i,ref:t,align:o,collisionPadding:r,style:{boxSizing:"border-box",...i.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});Kt.displayName=Ga;var[Ya,wn]=We(Re,{}),zt="SelectViewport",fr=c.forwardRef((e,t)=>{const{__scopeSelect:n,nonce:o,...r}=e,i=ye(zt,n),s=wn(zt,n),a=K(t,i.onViewportChange),u=c.useRef(0);return v.jsxs(v.Fragment,{children:[v.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),v.jsx(Et.Slot,{scope:n,children:v.jsx(N.div,{"data-radix-select-viewport":"",role:"presentation",...r,ref:a,style:{position:"relative",flex:1,overflow:"hidden auto",...r.style},onScroll:I(r.onScroll,l=>{const d=l.currentTarget,{contentWrapper:p,shouldExpandOnScrollRef:m}=s;if(m!=null&&m.current&&p){const h=Math.abs(u.current-d.scrollTop);if(h>0){const w=window.innerHeight-J*2,f=parseFloat(p.style.minHeight),g=parseFloat(p.style.height),y=Math.max(f,g);if(y<w){const x=y+h,b=Math.min(w,x),C=x-b;p.style.height=b+"px",p.style.bottom==="0px"&&(d.scrollTop=C>0?C:0,p.style.justifyContent="flex-end")}}}u.current=d.scrollTop})})})]})});fr.displayName=zt;var dr="SelectGroup",[Xa,qa]=We(dr),pr=c.forwardRef((e,t)=>{const{__scopeSelect:n,...o}=e,r=le();return v.jsx(Xa,{scope:n,id:r,children:v.jsx(N.div,{role:"group","aria-labelledby":r,...o,ref:t})})});pr.displayName=dr;var mr="SelectLabel",hr=c.forwardRef((e,t)=>{const{__scopeSelect:n,...o}=e,r=qa(mr,n);return v.jsx(N.div,{id:r.id,...o,ref:t})});hr.displayName=mr;var pt="SelectItem",[Za,gr]=We(pt),vr=c.forwardRef((e,t)=>{const{__scopeSelect:n,value:o,disabled:r=!1,textValue:i,...s}=e,a=we(pt,n),u=ye(pt,n),l=a.value===o,[d,p]=c.useState(i??""),[m,h]=c.useState(!1),w=K(t,x=>{var b;return(b=u.itemRefCallback)==null?void 0:b.call(u,x,o,r)}),f=le(),g=c.useRef("touch"),y=()=>{r||(a.onValueChange(o),a.onOpenChange(!1))};if(o==="")throw new Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return v.jsx(Za,{scope:n,value:o,disabled:r,textId:f,isSelected:l,onItemTextChange:c.useCallback(x=>{p(b=>b||((x==null?void 0:x.textContent)??"").trim())},[]),children:v.jsx(Et.ItemSlot,{scope:n,value:o,disabled:r,textValue:d,children:v.jsx(N.div,{role:"option","aria-labelledby":f,"data-highlighted":m?"":void 0,"aria-selected":l&&m,"data-state":l?"checked":"unchecked","aria-disabled":r||void 0,"data-disabled":r?"":void 0,tabIndex:r?void 0:-1,...s,ref:w,onFocus:I(s.onFocus,()=>h(!0)),onBlur:I(s.onBlur,()=>h(!1)),onClick:I(s.onClick,()=>{g.current!=="mouse"&&y()}),onPointerUp:I(s.onPointerUp,()=>{g.current==="mouse"&&y()}),onPointerDown:I(s.onPointerDown,x=>{g.current=x.pointerType}),onPointerMove:I(s.onPointerMove,x=>{var b;g.current=x.pointerType,r?(b=u.onItemLeave)==null||b.call(u):g.current==="mouse"&&x.currentTarget.focus({preventScroll:!0})}),onPointerLeave:I(s.onPointerLeave,x=>{var b;x.currentTarget===document.activeElement&&((b=u.onItemLeave)==null||b.call(u))}),onKeyDown:I(s.onKeyDown,x=>{var C;((C=u.searchRef)==null?void 0:C.current)!==""&&x.key===" "||(ja.includes(x.key)&&y(),x.key===" "&&x.preventDefault())})})})})});vr.displayName=pt;var ze="SelectItemText",wr=c.forwardRef((e,t)=>{const{__scopeSelect:n,className:o,style:r,...i}=e,s=we(ze,n),a=ye(ze,n),u=gr(ze,n),l=Va(ze,n),[d,p]=c.useState(null),m=K(t,y=>p(y),u.onItemTextChange,y=>{var x;return(x=a.itemTextRefCallback)==null?void 0:x.call(a,y,u.value,u.disabled)}),h=d==null?void 0:d.textContent,w=c.useMemo(()=>v.jsx("option",{value:u.value,disabled:u.disabled,children:h},u.value),[u.disabled,u.value,h]),{onNativeOptionAdd:f,onNativeOptionRemove:g}=l;return G(()=>(f(w),()=>g(w)),[f,g,w]),v.jsxs(v.Fragment,{children:[v.jsx(N.span,{id:u.textId,...i,ref:m}),u.isSelected&&s.valueNode&&!s.valueNodeHasChildren?mt.createPortal(i.children,s.valueNode):null]})});wr.displayName=ze;var yr="SelectItemIndicator",xr=c.forwardRef((e,t)=>{const{__scopeSelect:n,...o}=e;return gr(yr,n).isSelected?v.jsx(N.span,{"aria-hidden":!0,...o,ref:t}):null});xr.displayName=yr;var Gt="SelectScrollUpButton",br=c.forwardRef((e,t)=>{const n=ye(Gt,e.__scopeSelect),o=wn(Gt,e.__scopeSelect),[r,i]=c.useState(!1),s=K(t,o.onScrollButtonChange);return G(()=>{if(n.viewport&&n.isPositioned){let a=function(){const l=u.scrollTop>0;i(l)};const u=n.viewport;return a(),u.addEventListener("scroll",a),()=>u.removeEventListener("scroll",a)}},[n.viewport,n.isPositioned]),r?v.jsx(Sr,{...e,ref:s,onAutoScroll:()=>{const{viewport:a,selectedItem:u}=n;a&&u&&(a.scrollTop=a.scrollTop-u.offsetHeight)}}):null});br.displayName=Gt;var Yt="SelectScrollDownButton",Cr=c.forwardRef((e,t)=>{const n=ye(Yt,e.__scopeSelect),o=wn(Yt,e.__scopeSelect),[r,i]=c.useState(!1),s=K(t,o.onScrollButtonChange);return G(()=>{if(n.viewport&&n.isPositioned){let a=function(){const l=u.scrollHeight-u.clientHeight,d=Math.ceil(u.scrollTop)<l;i(d)};const u=n.viewport;return a(),u.addEventListener("scroll",a),()=>u.removeEventListener("scroll",a)}},[n.viewport,n.isPositioned]),r?v.jsx(Sr,{...e,ref:s,onAutoScroll:()=>{const{viewport:a,selectedItem:u}=n;a&&u&&(a.scrollTop=a.scrollTop+u.offsetHeight)}}):null});Cr.displayName=Yt;var Sr=c.forwardRef((e,t)=>{const{__scopeSelect:n,onAutoScroll:o,...r}=e,i=ye("SelectScrollButton",n),s=c.useRef(null),a=Rt(n),u=c.useCallback(()=>{s.current!==null&&(window.clearInterval(s.current),s.current=null)},[]);return c.useEffect(()=>()=>u(),[u]),G(()=>{var d;const l=a().find(p=>p.ref.current===document.activeElement);(d=l==null?void 0:l.ref.current)==null||d.scrollIntoView({block:"nearest"})},[a]),v.jsx(N.div,{"aria-hidden":!0,...r,ref:t,style:{flexShrink:0,...r.style},onPointerDown:I(r.onPointerDown,()=>{s.current===null&&(s.current=window.setInterval(o,50))}),onPointerMove:I(r.onPointerMove,()=>{var l;(l=i.onItemLeave)==null||l.call(i),s.current===null&&(s.current=window.setInterval(o,50))}),onPointerLeave:I(r.onPointerLeave,()=>{u()})})}),Ja="SelectSeparator",Er=c.forwardRef((e,t)=>{const{__scopeSelect:n,...o}=e;return v.jsx(N.div,{"aria-hidden":!0,...o,ref:t})});Er.displayName=Ja;var Xt="SelectArrow",Qa=c.forwardRef((e,t)=>{const{__scopeSelect:n,...o}=e,r=Pt(n),i=we(Xt,n),s=ye(Xt,n);return i.open&&s.position==="popper"?v.jsx(Vo,{...r,...o,ref:t}):null});Qa.displayName=Xt;function Rr(e){return e===""||e===void 0}var Pr=c.forwardRef((e,t)=>{const{value:n,...o}=e,r=c.useRef(null),i=K(t,r),s=La(n);return c.useEffect(()=>{const a=r.current,u=window.HTMLSelectElement.prototype,d=Object.getOwnPropertyDescriptor(u,"value").set;if(s!==n&&d){const p=new Event("change",{bubbles:!0});d.call(a,n),a.dispatchEvent(p)}},[s,n]),v.jsx(mn,{asChild:!0,children:v.jsx("select",{...o,ref:i,defaultValue:n})})});Pr.displayName="BubbleSelect";function Ar(e){const t=ie(e),n=c.useRef(""),o=c.useRef(0),r=c.useCallback(s=>{const a=n.current+s;t(a),function u(l){n.current=l,window.clearTimeout(o.current),l!==""&&(o.current=window.setTimeout(()=>u(""),1e3))}(a)},[t]),i=c.useCallback(()=>{n.current="",window.clearTimeout(o.current)},[]);return c.useEffect(()=>()=>window.clearTimeout(o.current),[]),[n,r,i]}function Tr(e,t,n){const r=t.length>1&&Array.from(t).every(l=>l===t[0])?t[0]:t,i=n?e.indexOf(n):-1;let s=ec(e,Math.max(i,0));r.length===1&&(s=s.filter(l=>l!==n));const u=s.find(l=>l.textValue.toLowerCase().startsWith(r.toLowerCase()));return u!==n?u:void 0}function ec(e,t){return e.map((n,o)=>e[(t+o)%e.length])}var Dc=er,Fc=nr,Lc=rr,kc=ir,jc=sr,Wc=ar,Bc=fr,$c=pr,Vc=hr,Hc=vr,Uc=wr,Kc=xr,zc=br,Gc=Cr,Yc=Er,jt="rovingFocusGroup.onEntryFocus",tc={bubbles:!1,cancelable:!0},At="RovingFocusGroup",[qt,Or,nc]=Qo(At),[oc,Ir]=Pe(At,[nc]),[rc,ic]=oc(At),Nr=c.forwardRef((e,t)=>v.jsx(qt.Provider,{scope:e.__scopeRovingFocusGroup,children:v.jsx(qt.Slot,{scope:e.__scopeRovingFocusGroup,children:v.jsx(sc,{...e,ref:t})})}));Nr.displayName=At;var sc=c.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,orientation:o,loop:r=!1,dir:i,currentTabStopId:s,defaultCurrentTabStopId:a,onCurrentTabStopIdChange:u,onEntryFocus:l,preventScrollOnEntryFocus:d=!1,...p}=e,m=c.useRef(null),h=K(t,m),w=vn(i),[f=null,g]=De({prop:s,defaultProp:a,onChange:u}),[y,x]=c.useState(!1),b=ie(l),C=Or(n),S=c.useRef(!1),[P,E]=c.useState(0);return c.useEffect(()=>{const R=m.current;if(R)return R.addEventListener(jt,b),()=>R.removeEventListener(jt,b)},[b]),v.jsx(rc,{scope:n,orientation:o,dir:w,loop:r,currentTabStopId:f,onItemFocus:c.useCallback(R=>g(R),[g]),onItemShiftTab:c.useCallback(()=>x(!0),[]),onFocusableItemAdd:c.useCallback(()=>E(R=>R+1),[]),onFocusableItemRemove:c.useCallback(()=>E(R=>R-1),[]),children:v.jsx(N.div,{tabIndex:y||P===0?-1:0,"data-orientation":o,...p,ref:h,style:{outline:"none",...e.style},onMouseDown:I(e.onMouseDown,()=>{S.current=!0}),onFocus:I(e.onFocus,R=>{const D=!S.current;if(R.target===R.currentTarget&&D&&!y){const _=new CustomEvent(jt,tc);if(R.currentTarget.dispatchEvent(_),!_.defaultPrevented){const L=C().filter(T=>T.focusable),B=L.find(T=>T.active),F=L.find(T=>T.id===f),U=[B,F,...L].filter(Boolean).map(T=>T.ref.current);Dr(U,d)}}S.current=!1}),onBlur:I(e.onBlur,()=>x(!1))})})}),_r="RovingFocusGroupItem",Mr=c.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,focusable:o=!0,active:r=!1,tabStopId:i,...s}=e,a=le(),u=i||a,l=ic(_r,n),d=l.currentTabStopId===u,p=Or(n),{onFocusableItemAdd:m,onFocusableItemRemove:h}=l;return c.useEffect(()=>{if(o)return m(),()=>h()},[o,m,h]),v.jsx(qt.ItemSlot,{scope:n,id:u,focusable:o,active:r,children:v.jsx(N.span,{tabIndex:d?0:-1,"data-orientation":l.orientation,...s,ref:t,onMouseDown:I(e.onMouseDown,w=>{o?l.onItemFocus(u):w.preventDefault()}),onFocus:I(e.onFocus,()=>l.onItemFocus(u)),onKeyDown:I(e.onKeyDown,w=>{if(w.key==="Tab"&&w.shiftKey){l.onItemShiftTab();return}if(w.target!==w.currentTarget)return;const f=lc(w,l.orientation,l.dir);if(f!==void 0){if(w.metaKey||w.ctrlKey||w.altKey||w.shiftKey)return;w.preventDefault();let y=p().filter(x=>x.focusable).map(x=>x.ref.current);if(f==="last")y.reverse();else if(f==="prev"||f==="next"){f==="prev"&&y.reverse();const x=y.indexOf(w.currentTarget);y=l.loop?uc(y,x+1):y.slice(x+1)}setTimeout(()=>Dr(y))}})})})});Mr.displayName=_r;var ac={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function cc(e,t){return t!=="rtl"?e:e==="ArrowLeft"?"ArrowRight":e==="ArrowRight"?"ArrowLeft":e}function lc(e,t,n){const o=cc(e.key,n);if(!(t==="vertical"&&["ArrowLeft","ArrowRight"].includes(o))&&!(t==="horizontal"&&["ArrowUp","ArrowDown"].includes(o)))return ac[o]}function Dr(e,t=!1){const n=document.activeElement;for(const o of e)if(o===n||(o.focus({preventScroll:t}),document.activeElement!==n))return}function uc(e,t){return e.map((n,o)=>e[(t+o)%e.length])}var fc=Nr,dc=Mr,yn="Tabs",[pc,Xc]=Pe(yn,[Ir]),Fr=Ir(),[mc,xn]=pc(yn),Lr=c.forwardRef((e,t)=>{const{__scopeTabs:n,value:o,onValueChange:r,defaultValue:i,orientation:s="horizontal",dir:a,activationMode:u="automatic",...l}=e,d=vn(a),[p,m]=De({prop:o,onChange:r,defaultProp:i});return v.jsx(mc,{scope:n,baseId:le(),value:p,onValueChange:m,orientation:s,dir:d,activationMode:u,children:v.jsx(N.div,{dir:d,"data-orientation":s,...l,ref:t})})});Lr.displayName=yn;var kr="TabsList",jr=c.forwardRef((e,t)=>{const{__scopeTabs:n,loop:o=!0,...r}=e,i=xn(kr,n),s=Fr(n);return v.jsx(fc,{asChild:!0,...s,orientation:i.orientation,dir:i.dir,loop:o,children:v.jsx(N.div,{role:"tablist","aria-orientation":i.orientation,...r,ref:t})})});jr.displayName=kr;var Wr="TabsTrigger",Br=c.forwardRef((e,t)=>{const{__scopeTabs:n,value:o,disabled:r=!1,...i}=e,s=xn(Wr,n),a=Fr(n),u=Hr(s.baseId,o),l=Ur(s.baseId,o),d=o===s.value;return v.jsx(dc,{asChild:!0,...a,focusable:!r,active:d,children:v.jsx(N.button,{type:"button",role:"tab","aria-selected":d,"aria-controls":l,"data-state":d?"active":"inactive","data-disabled":r?"":void 0,disabled:r,id:u,...i,ref:t,onMouseDown:I(e.onMouseDown,p=>{!r&&p.button===0&&p.ctrlKey===!1?s.onValueChange(o):p.preventDefault()}),onKeyDown:I(e.onKeyDown,p=>{[" ","Enter"].includes(p.key)&&s.onValueChange(o)}),onFocus:I(e.onFocus,()=>{const p=s.activationMode!=="manual";!d&&!r&&p&&s.onValueChange(o)})})})});Br.displayName=Wr;var $r="TabsContent",Vr=c.forwardRef((e,t)=>{const{__scopeTabs:n,value:o,forceMount:r,children:i,...s}=e,a=xn($r,n),u=Hr(a.baseId,o),l=Ur(a.baseId,o),d=o===a.value,p=c.useRef(d);return c.useEffect(()=>{const m=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(m)},[]),v.jsx(Ae,{present:r||d,children:({present:m})=>v.jsx(N.div,{"data-state":d?"active":"inactive","data-orientation":a.orientation,role:"tabpanel","aria-labelledby":u,hidden:!m,id:l,tabIndex:0,...s,ref:t,style:{...e.style,animationDuration:p.current?"0s":void 0},children:m&&i})})});Vr.displayName=$r;function Hr(e,t){return`${e}-trigger-${t}`}function Ur(e,t){return`${e}-content-${t}`}var qc=Lr,Zc=jr,Jc=Br,Qc=Vr;export{Zc as $,pn as A,Un as B,$o as C,ht as D,Fc as E,zc as F,Gc as G,jc as H,kc as I,Wc as J,Hc as K,Vc as L,Kc as M,Uc as N,Cc as O,Ae as P,Yc as Q,Ac as R,ti as S,Ec as T,Dc as U,Bc as V,Lc as W,$c as X,vc as Y,wc as Z,gc as _,St as a,Jc as a0,Qc as a1,qc as a2,le as b,Pe as c,De as d,Bo as e,N as f,I as g,Vo as h,Ce as i,v as j,bc as k,Sc as l,Pc as m,Rc as n,yc as o,xc as p,Nc as q,_c as r,Oc as s,Ic as t,K as u,La as v,wa as w,vn as x,ie as y,G as z};
